<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.activity.dao.ActivityTemplateMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.activity.pojo.entity.ActivityTemplate">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="available" jdbcType="BIT" property="available" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, name, channel, file_key, url, available, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from activity_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from activity_template
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from activity_template
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplateExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from activity_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplate">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into activity_template (id, name, channel, 
      file_key, url, available, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{channel,jdbcType=INTEGER}, 
      #{fileKey,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{available,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplate">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into activity_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="available != null">
        available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        #{available,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from activity_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    update activity_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.available != null">
        available = #{record.available,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    update activity_template
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      available = #{record.available,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplate">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    update activity_template
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        available = #{available,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.activity.pojo.entity.ActivityTemplate">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    update activity_template
    set name = #{name,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      file_key = #{fileKey,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      available = #{available,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into activity_template
    (id, name, channel, file_key, url, available, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.channel,jdbcType=INTEGER}, 
        #{item.fileKey,jdbcType=VARCHAR}, #{item.url,jdbcType=VARCHAR}, #{item.available,jdbcType=BIT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 17 14:55:48 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into activity_template (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=INTEGER}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'available'.toString() == column.value">
          #{item.available,jdbcType=BIT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>