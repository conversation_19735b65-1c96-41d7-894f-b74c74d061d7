<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.activity.dao.ext.ActivityMapperExt">

  <select id="pageQuery" resultType="com.chinamobile.activity.pojo.mapper.ActivityListDO"
          parameterType="com.chinamobile.activity.pojo.param.ActivityListParam">
  SELECT
      a.id,
      a.activity_name activityName,
      a.channel_type channelType,
      a.template_name templateName,
      a.`status`,
      a.create_time createTime,
      c.mall_name provinceName,
      a.province_code provinceCode,
      u.`name` userName
  FROM
      activity a
      LEFT JOIN `user` u ON a.user_id = u.user_id
      LEFT JOIN contract_province_info c ON a.province_code = c.mall_code
  where 1=1
  <if test="channel != null and channel != ''">
      and a.channel_type = #{channel}
  </if>
  <if test="name != null and name != ''">
      and a.activity_name like concat ('%',#{name},'%')
  </if>
  <if test="provinceCode != null and provinceCode.size() != 0 ">
      and a.province_code in
      <foreach item='item' index='index' collection='provinceCode' open='(' separator=',' close=')'>
          #{item}
      </foreach>
  </if>
  ORDER BY a.update_time DESC
  </select>


</mapper>