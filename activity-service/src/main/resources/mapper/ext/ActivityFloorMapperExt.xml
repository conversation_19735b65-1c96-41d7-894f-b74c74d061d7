<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.activity.dao.ext.ActivityFloorMapperExt">

<!--  <select id="getActivityFloorAdvertising" parameterType="java.lang.String" resultType="com.chinamobile.activity.pojo.mapper.ActivityFloorAdvertisingDO">
    SELECT
      f.id floorId,
      f.`name` floorName,
      f.is_show isShow,
      f.is_show_second isShowSecond,
      f.is_show_tag isShowTag,
      f.show_num showNum,
      f.num_in_a_line numInALine,
      spu.id spuId,
      spu.offering_name `name`,
      spu.url,
      spu.img_url imgUrl ,
      CASE
        WHEN a.province_code IS NULL THEN (
          SELECT MAX(price)
          FROM sku_offering_info sku
          WHERE sku.spu_code = spu.offering_code and sku.offering_status = '1' AND sku.delete_time IS NULL
        ) ELSE (
        SELECT MAX(price)
        FROM sku_offering_info sku
        LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code
        WHERE sku.spu_code = spu.offering_code AND sku.offering_status = '1' AND sku.delete_time IS NULL AND (srt.province_code = '000' OR srt.province_code = a.province_code)
      ) END minSkuPrice,
      spu.tag,
      IFNULL(SUM(orderTemp.sku_quantity), 0) skuQuantity
    FROM activity_floor_spu s
    LEFT JOIN activity_floor f ON f.activity_id = s.activity_id AND f.id = s.activity_floor_id
    LEFT JOIN activity a ON s.activity_id = a.id
    JOIN spu_offering_info spu ON spu.id = s.spu_id AND spu.offering_status = '1' AND spu.delete_time IS NULL
    LEFT JOIN (
      SELECT
        atom.spu_offering_code,
        atom.sku_quantity,
        (
            SELECT create_time
            FROM order_2c_atom_history history
            WHERE history.operate_type = 1 AND history.inner_status = 2 AND history.atom_order_id = atom.id
            limit 1
        ) receiveTime
      FROM order_2c_atom_info atom
      WHERE atom.order_status in (2, 7)
      GROUP BY atom.order_id
      HAVING ( receiveTime IS NULL OR receiveTime <![CDATA[ < ]]> #{deadlineTime} )
    ) orderTemp ON orderTemp.spu_offering_code = spu.offering_code
    WHERE s.activity_id = #{activityId}
    GROUP BY s.activity_floor_id, s.spu_id
    HAVING minSkuPrice is not NULL
    order by f.sort,s.sort
  </select>-->

<!--  <select id="getActivityFloorAdvertising" parameterType="java.lang.String" resultType="com.chinamobile.activity.pojo.mapper.ActivityFloorAdvertisingDO">-->
<!--    SELECT-->
<!--      f.id floorId,-->
<!--      f.`name` floorName,-->
<!--      f.is_show isShow,-->
<!--      f.is_show_second isShowSecond,-->
<!--      f.is_show_tag isShowTag,-->
<!--      f.show_num showNum,-->
<!--      f.num_in_a_line numInALine,-->
<!--      spu.id spuId,-->
<!--      spu.offering_name `name`,-->
<!--      spu.url,-->
<!--      spu.img_url imgUrl ,-->
<!--      CASE-->
<!--        WHEN a.province_code IS NULL THEN (-->
<!--          SELECT MAX(price)-->
<!--          FROM sku_offering_info sku-->
<!--          WHERE sku.spu_code = spu.offering_code and sku.offering_status = '1' AND sku.delete_time IS NULL-->
<!--        ) ELSE (-->
<!--        SELECT MAX(price)-->
<!--        FROM sku_offering_info sku-->
<!--        LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code-->
<!--        WHERE sku.spu_code = spu.offering_code AND sku.offering_status = '1' AND sku.delete_time IS NULL AND (srt.province_code = '000' OR srt.province_code = a.province_code)-->
<!--      ) END minSkuPrice,-->
<!--      spu.tag,-->
<!--      IFNULL(SUM(orderTemp.sku_quantity), 0) skuQuantity-->
<!--    FROM activity_floor_spu s-->
<!--    LEFT JOIN activity_floor f ON f.activity_id = s.activity_id AND f.id = s.activity_floor_id-->
<!--    LEFT JOIN activity a ON s.activity_id = a.id-->
<!--    JOIN spu_offering_info spu ON spu.id = s.spu_id AND spu.offering_status = '1' AND spu.delete_time IS NULL-->
<!--    LEFT JOIN (-->
<!--      SELECT-->
<!--        atom.spu_offering_code,-->
<!--        atom.sku_quantity,-->
<!--        (-->
<!--            SELECT create_time-->
<!--            FROM order_2c_atom_history history-->
<!--            WHERE history.operate_type = 1 AND history.inner_status = 2 AND history.atom_order_id = atom.id-->
<!--            limit 1-->
<!--        ) receiveTime-->
<!--      FROM order_2c_atom_info atom-->
<!--      WHERE atom.order_status in (2, 7)-->
<!--      GROUP BY atom.order_id-->
<!--      HAVING ( receiveTime IS NULL OR receiveTime <![CDATA[ < ]]> #{deadlineTime} )-->
<!--    ) orderTemp ON orderTemp.spu_offering_code = spu.offering_code-->
<!--    WHERE s.activity_id = #{activityId}-->
<!--    GROUP BY s.activity_floor_id, s.spu_id-->
<!--    HAVING minSkuPrice is not NULL-->
<!--    order by f.sort,s.sort-->
<!--  </select>-->
  <select id="getActivityFloorAdvertising" parameterType="java.lang.String" resultType="com.chinamobile.activity.pojo.mapper.ActivityFloorAdvertisingDO">
    SELECT
      f.id floorId,
      f.`name` floorName,
      f.is_show isShow,
      f.is_show_second isShowSecond,
      f.is_show_tag isShowTag,
      f.show_num showNum,
      f.num_in_a_line numInALine,
      s.sort sort,
      s.spu_sort spuSort,
      spu.id spuId,
      spu.offering_name `name`,
      spu.offering_code spuCode,
      spu.url,
      spu.img_url imgUrl ,
      (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
      (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel,
      spu.product_description as productDescription,
      CASE
        WHEN a.province_code IS NULL THEN (
          SELECT MAX(price)
          FROM sku_offering_info sku
          WHERE sku.spu_code = spu.offering_code and sku.offering_status = '1' AND sku.delete_time IS NULL
        ) ELSE (
        SELECT MAX(price)
        FROM sku_offering_info sku
               LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code
        WHERE sku.spu_code = spu.offering_code AND sku.offering_status = '1' AND sku.delete_time IS NULL AND (srt.province_code = '000' OR srt.province_code = a.province_code)
      ) END minSkuPrice,
      spu.tag
    FROM activity_floor_spu s
           LEFT JOIN activity_floor f ON f.activity_id = s.activity_id AND f.id = s.activity_floor_id
           LEFT JOIN activity a ON s.activity_id = a.id
           JOIN spu_offering_info spu ON spu.id = s.spu_id AND spu.offering_status = '1' AND spu.delete_time IS NULL
    WHERE s.activity_id = #{activityId}
    GROUP BY s.activity_floor_id, s.spu_id
    HAVING minSkuPrice is not NULL
    order by  f.sort, if(s.sort_mode ='1',s.sort,s.spu_sort);
  </select>
</mapper>
