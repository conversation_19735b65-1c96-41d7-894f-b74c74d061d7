<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.activity.dao.ActivityFloorSpuMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.activity.pojo.entity.ActivityFloorSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="activity_floor_id" jdbcType="VARCHAR" property="activityFloorId" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="sort_mode" jdbcType="VARCHAR" property="sortMode" />
    <result column="spu_sort" jdbcType="INTEGER" property="spuSort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, activity_id, activity_floor_id, spu_id, sort, sort_mode, spu_sort, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpuExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from activity_floor_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from activity_floor_spu
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from activity_floor_spu
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpuExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from activity_floor_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into activity_floor_spu (id, activity_id, activity_floor_id, 
      spu_id, sort, sort_mode, 
      spu_sort, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{activityFloorId,jdbcType=VARCHAR}, 
      #{spuId,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{sortMode,jdbcType=VARCHAR}, 
      #{spuSort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into activity_floor_spu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="activityFloorId != null">
        activity_floor_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="sortMode != null">
        sort_mode,
      </if>
      <if test="spuSort != null">
        spu_sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityFloorId != null">
        #{activityFloorId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="sortMode != null">
        #{sortMode,jdbcType=VARCHAR},
      </if>
      <if test="spuSort != null">
        #{spuSort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpuExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from activity_floor_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    update activity_floor_spu
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityFloorId != null">
        activity_floor_id = #{record.activityFloorId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.sortMode != null">
        sort_mode = #{record.sortMode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuSort != null">
        spu_sort = #{record.spuSort,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    update activity_floor_spu
    set id = #{record.id,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      activity_floor_id = #{record.activityFloorId,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      sort_mode = #{record.sortMode,jdbcType=VARCHAR},
      spu_sort = #{record.spuSort,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    update activity_floor_spu
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityFloorId != null">
        activity_floor_id = #{activityFloorId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="sortMode != null">
        sort_mode = #{sortMode,jdbcType=VARCHAR},
      </if>
      <if test="spuSort != null">
        spu_sort = #{spuSort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.activity.pojo.entity.ActivityFloorSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    update activity_floor_spu
    set activity_id = #{activityId,jdbcType=VARCHAR},
      activity_floor_id = #{activityFloorId,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      sort_mode = #{sortMode,jdbcType=VARCHAR},
      spu_sort = #{spuSort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into activity_floor_spu
    (id, activity_id, activity_floor_id, spu_id, sort, sort_mode, spu_sort, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, #{item.activityFloorId,jdbcType=VARCHAR}, 
        #{item.spuId,jdbcType=VARCHAR}, #{item.sort,jdbcType=INTEGER}, #{item.sortMode,jdbcType=VARCHAR}, 
        #{item.spuSort,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 30 16:12:30 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into activity_floor_spu (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'activity_floor_id'.toString() == column.value">
          #{item.activityFloorId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=INTEGER}
        </if>
        <if test="'sort_mode'.toString() == column.value">
          #{item.sortMode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_sort'.toString() == column.value">
          #{item.spuSort,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>