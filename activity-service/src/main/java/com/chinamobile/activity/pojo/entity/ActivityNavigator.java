package com.chinamobile.activity.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 活动专区的导航
 *
 * <AUTHOR>
public class ActivityNavigator implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private String id;

    /**
     * 活动id
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private String activityId;

    /**
     * 导航名称
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private String name;

    /**
     * 链接名称
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private String linkName;

    /**
     * 链接地址
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private String linkUrl;

    /**
     * 排序，数字越小越靠前
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private Integer sort;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.id
     *
     * @return the value of supply_chain..activity_navigator.id
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.id
     *
     * @param id the value for supply_chain..activity_navigator.id
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.activity_id
     *
     * @return the value of supply_chain..activity_navigator.activity_id
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.activity_id
     *
     * @param activityId the value for supply_chain..activity_navigator.activity_id
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.name
     *
     * @return the value of supply_chain..activity_navigator.name
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.name
     *
     * @param name the value for supply_chain..activity_navigator.name
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.link_name
     *
     * @return the value of supply_chain..activity_navigator.link_name
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public String getLinkName() {
        return linkName;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withLinkName(String linkName) {
        this.setLinkName(linkName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.link_name
     *
     * @param linkName the value for supply_chain..activity_navigator.link_name
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.link_url
     *
     * @return the value of supply_chain..activity_navigator.link_url
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public String getLinkUrl() {
        return linkUrl;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withLinkUrl(String linkUrl) {
        this.setLinkUrl(linkUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.link_url
     *
     * @param linkUrl the value for supply_chain..activity_navigator.link_url
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.sort
     *
     * @return the value of supply_chain..activity_navigator.sort
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public Integer getSort() {
        return sort;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withSort(Integer sort) {
        this.setSort(sort);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.sort
     *
     * @param sort the value for supply_chain..activity_navigator.sort
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.create_time
     *
     * @return the value of supply_chain..activity_navigator.create_time
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.create_time
     *
     * @param createTime the value for supply_chain..activity_navigator.create_time
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_navigator.update_time
     *
     * @return the value of supply_chain..activity_navigator.update_time
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public ActivityNavigator withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_navigator.update_time
     *
     * @param updateTime the value for supply_chain..activity_navigator.update_time
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", name=").append(name);
        sb.append(", linkName=").append(linkName);
        sb.append(", linkUrl=").append(linkUrl);
        sb.append(", sort=").append(sort);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ActivityNavigator other = (ActivityNavigator) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getLinkName() == null ? other.getLinkName() == null : this.getLinkName().equals(other.getLinkName()))
            && (this.getLinkUrl() == null ? other.getLinkUrl() == null : this.getLinkUrl().equals(other.getLinkUrl()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getLinkName() == null) ? 0 : getLinkName().hashCode());
        result = prime * result + ((getLinkUrl() == null) ? 0 : getLinkUrl().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:12:01 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        linkName("link_name", "linkName", "VARCHAR", false),
        linkUrl("link_url", "linkUrl", "VARCHAR", false),
        sort("sort", "sort", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:12:01 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}