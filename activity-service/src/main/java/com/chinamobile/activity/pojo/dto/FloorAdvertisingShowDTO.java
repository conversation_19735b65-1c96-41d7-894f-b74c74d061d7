package com.chinamobile.activity.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/8/9 16:15
 */
@Data
public class FloorAdvertisingShowDTO {

    //展示所有
    @NotNull(message = "是否展示所有不能为空")
    private Boolean isAll;

    //商品展示个数,-1或者null表示全部
    private Integer showNum;

    //每行展示个数
    @NotNull(message = "每行展示个数不能为空")
    private Integer numInALine;

}
