package com.chinamobile.activity.controller;

import com.chinamobile.activity.pojo.dto.ActivityListItemDTO;
import com.chinamobile.activity.pojo.dto.ActivityTemplateDTO;
import com.chinamobile.activity.pojo.entity.ActivityTemplate;
import com.chinamobile.activity.service.TemplateService;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.github.pagehelper.PageInfo;
import org.hibernate.validator.constraints.Range;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 模板缩略图controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/template")
public class TemplateController {

    @Resource
    private TemplateService templateService;

    /**
     * 上传模板缩略图
     */
    @PostMapping(value = "/upload",consumes = {"multipart/form-data"})
    @Auth(authCode = {BaseConstant.ACTIVITY})
    public BaseAnswer<Void> uploadThumbnail(@RequestParam("file") MultipartFile file,
                                            @RequestParam("name") String name,
                                            @RequestParam("channel") @Range(min = 1,max = 2) Integer channel){
        return templateService.uploadThumbnail(file,name,channel);
    }

    /**
     * 删除模板缩略图
     */
    @DeleteMapping("/delete")
    @Auth(authCode = {BaseConstant.ACTIVITY})
    public BaseAnswer<Void> delete(@RequestParam("id") String id){
        return templateService.deleteThumbnail(id);
    }

    /**
     * 查询模板列表
     */
    @GetMapping("/list")
    @Auth(authCode = {BaseConstant.ACTIVITY})
    public BaseAnswer<List<ActivityTemplateDTO>> getList(@RequestParam("channel")  @Range(min = 1,max = 2) Integer channel){
        return templateService.getList(channel);
    }


}
