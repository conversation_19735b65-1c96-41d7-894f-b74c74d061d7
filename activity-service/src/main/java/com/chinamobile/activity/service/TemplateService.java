package com.chinamobile.activity.service;


import com.chinamobile.activity.pojo.dto.ActivityTemplateDTO;
import com.chinamobile.iot.sc.common.BaseAnswer;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 对象存储接口
 * <AUTHOR>
 */
public interface TemplateService {
    /**
     * 上传模板缩略图
     */
     BaseAnswer<Void> uploadThumbnail(MultipartFile file,String name,Integer channel)  ;

    /**
     * 删除模板缩略图
     */
    BaseAnswer<Void> deleteThumbnail(String id)  ;

    /**
     * 获取模板列表
     */
    BaseAnswer<List<ActivityTemplateDTO>> getList(Integer channel)  ;


}
