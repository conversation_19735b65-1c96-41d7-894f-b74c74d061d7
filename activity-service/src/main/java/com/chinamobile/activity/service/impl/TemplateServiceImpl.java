package com.chinamobile.activity.service.impl;

import com.chinamobile.activity.dao.*;
import com.chinamobile.activity.pojo.dto.ActivityTemplateDTO;
import com.chinamobile.activity.pojo.entity.*;
import com.chinamobile.activity.service.ActivityService;
import com.chinamobile.activity.service.IStorageService;
import com.chinamobile.activity.service.TemplateService;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.commons.util.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模板缩略图service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private ActivityFloorMapper activityFloorMapper;

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private ActivityFloorSpuMapper activityFloorSpuMapper;

    @Resource
    private ActivityNavigatorMapper activityNavigatorMapper;

    @Resource
    private ActivitySliderMapper activitySliderMapper;

    @Resource
    private IStorageService iStorageService;

    @Resource
    private ActivityTemplateMapper activityTemplateMapper;

    @Resource
    private ActivityService activityService;

    @Override
    public BaseAnswer<Void> uploadThumbnail(MultipartFile file, String name, Integer channel) {
        Date time = new Date();
        String extention = file.getOriginalFilename().split("\\.")[1].toLowerCase();
        String rename = name + channel + DateTimeUtil.formatDate(time, DateTimeUtil.DB_TIME_STR) + extention;
        BaseAnswer<UpResult> answer = activityService.uploadPictureInner(file, rename);

        ActivityTemplateExample example = new ActivityTemplateExample();
        ActivityTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(name);
        criteria.andChannelEqualTo(channel);
        List<ActivityTemplate> templates = activityTemplateMapper.selectByExample(example);
        ActivityTemplate template = null;
        if (CollectionUtils.isNotEmpty(templates)) {
            template = templates.get(0);
            try {
                iStorageService.delete(template.getFileKey());
            } catch (Exception exception) {
                log.error("删除图片发生异常", exception);
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
            }
            template.setUrl(answer.getData().getOuterUrl());
            template.setFileKey(answer.getData().getKey());
            template.setChannel(channel);
            template.setUpdateTime(time);
            activityTemplateMapper.updateByExample(template, example);
        } else {
            template = new ActivityTemplate();
            template.setId(BaseServiceUtils.getId());
            template.setUrl(answer.getData().getOuterUrl());
            template.setFileKey(answer.getData().getKey());
            template.setName(name);
            template.setChannel(channel);
            template.setAvailable(true);
            template.setCreateTime(time);
            activityTemplateMapper.insertSelective(template);
        }

        return new BaseAnswer<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> deleteThumbnail(String id) {
        ActivityTemplate template = activityTemplateMapper.selectByPrimaryKey(id);
        if (template != null) {
            try {
                activityTemplateMapper.deleteByPrimaryKey(id);
                iStorageService.delete(template.getFileKey());
            } catch (Exception exception) {
                log.error("删除图片发生异常", exception);
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
            }


        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<List<ActivityTemplateDTO>> getList(Integer channel) {
        ActivityTemplateExample example = new ActivityTemplateExample();
        ActivityTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andChannelEqualTo(channel);
        List<ActivityTemplate> templates = activityTemplateMapper.selectByExample(example);
        BaseAnswer<List<ActivityTemplateDTO>> answer = new BaseAnswer<>();
        if (CollectionUtils.isNotEmpty(templates)) {
            List<ActivityTemplateDTO> dtos = templates.stream().map(item -> {
                ActivityTemplateDTO dto = new ActivityTemplateDTO();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toList());
            answer.setData(dtos);
        }
        return answer;
    }
}
