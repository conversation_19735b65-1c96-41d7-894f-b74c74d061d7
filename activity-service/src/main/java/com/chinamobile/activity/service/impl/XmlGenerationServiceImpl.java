package com.chinamobile.activity.service.impl;

import com.chinamobile.activity.dao.OperateRecordMapper;
import com.chinamobile.activity.pojo.entity.OperateRecord;
import com.chinamobile.activity.pojo.entity.OperateRecordExample;
import com.chinamobile.activity.service.XmlGenerationService;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * XML文件生成服务实现类
 */
@Service
@Slf4j
public class XmlGenerationServiceImpl implements XmlGenerationService {
    @Resource
    private OperateRecordMapper operateRecordMapper;

    @Value("${4A.resourceCode:CQIOTMALLOS}")
    private String resourceCode;

    @Value("${4A.identityName:CQIOTMALLOS}")
    private String identityName;

    @Value("${4A.ip:xxx}")
    private String ip;
    @Value("${4A.port:xxx}")
    private String port;
    @Value("${4A.mac:xxx}")
    private String mac;

    @Value("${4A.ftp.name}")
    private String sftpUserName;
    @Value("${4A.ftp.password}")
    private String sftpPassword;
    @Value("${4A.ftp.host}")
    private String sftpHost;
    @Value("${4A.ftp.port}")
    private Integer sftpPort;

    @Value("${4A.ftp.upload}")
    private String sftpUpload;
    @Override
    public BaseAnswer<Void> generateXmlFromDatabase() {
        // 获取前一天的开始与结束时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startTime = calendar.getTime();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endTime = calendar.getTime();

        // 查询指定时间范围内的记录
        List<OperateRecord> records = operateRecordMapper.selectByExampleWithBLOBs(
                new OperateRecordExample().createCriteria()
                        .andTimeBetween(startTime, endTime)
                        .example()
        );

        // 生成XML内容
        String xmlContent = generateXmlContent(records);

        // 将XML内容写入临时文件
        String tempFileName = identityName + "-LOG4A-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xml";
        File tempFile = new File(tempFileName);
        try {
            FileUtils.writeStringToFile(tempFile, xmlContent, "UTF-8");
        } catch (IOException e) {
            log.error("写入临时文件失败: {}", e.getMessage());
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }

        // 上传到SFTP
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接4Asftp上传日志文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpUpload);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件:{}", tempFileName);
                sftpUtil.upload(sftpUpload, tempFileName, new FileInputStream(tempFile));
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        } finally {
            sftpUtil.logout();
            log.info("登出sftp服务！");
            // 删除临时文件
            if (tempFile.exists()) {
//                tempFile.delete();
            }
        }

        return BaseAnswer.success(null);
    }

    private String generateXmlContent(List<OperateRecord> records) {
        StringBuilder xmlBuilder = new StringBuilder();
        xmlBuilder.append("<?xml version='1.0' encoding='utf-8'?>\n");
        xmlBuilder.append("<ROOT>\n");
        String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        for (OperateRecord record : records) {
            log.info("record:{}", record.getContent());
            xmlBuilder.append("  <LOG4A>\n");
            xmlBuilder.append("    <APP_LOG_ID>").append(StringUtils.isNotEmpty(record.getId()) ? record.getId() : "").append("</APP_LOG_ID>\n");
            xmlBuilder.append("    <SESSION_ID>").append(StringUtils.isNotEmpty(record.getId()) ? record.getId() : "").append("</SESSION_ID>\n");
            xmlBuilder.append("    <IDENTITY_NAME>").append(StringUtils.isNotEmpty(resourceCode) ? resourceCode : "").append("</IDENTITY_NAME>\n");
            xmlBuilder.append("    <RESOURCE_KIND>").append(1).append("</RESOURCE_KIND>\n");
            xmlBuilder.append("    <RESOURCE_CODE>").append(StringUtils.isNotEmpty(identityName) ? identityName : "").append("</RESOURCE_CODE>\n");
            xmlBuilder.append("    <IDR_CREATION_TIME>").append(timestamp).append("</IDR_CREATION_TIME>\n");
            xmlBuilder.append("    <MAIN_ACCOUNT_NAME>").append(StringUtils.isNotEmpty(record.getMainAcctId()) ? record.getMainAcctId() : "").append("</MAIN_ACCOUNT_NAME>\n");
            xmlBuilder.append("    <SUB_ACCOUNT_NAME>").append(StringUtils.isNotEmpty(record.getOperatorName()) ? record.getOperatorName() : "").append("</SUB_ACCOUNT_NAME>\n");
            xmlBuilder.append("    <OPERATE_TIME>").append(record.getTime()).append("</OPERATE_TIME>\n");
            xmlBuilder.append("    <OPERATE_CONTENT>").append(StringUtils.isNotEmpty(record.getContent()) ? record.getContent() : "").append("</OPERATE_CONTENT>\n");
            xmlBuilder.append("    <OPERATE_RESULT>").append(record.getResult()).append("</OPERATE_RESULT>\n");
            xmlBuilder.append("    <CLIENT_NETWORK_ADDRESS>").append(StringUtils.isNotEmpty(record.getIp()) ? record.getIp() : "").append("</CLIENT_NETWORK_ADDRESS>\n");
            xmlBuilder.append("    <CLIENT_ADDRESS>").append(StringUtils.isNotEmpty(record.getIp()) ? record.getIp() : "").append("</CLIENT_ADDRESS>\n");
            xmlBuilder.append("    <CLIENT_PORT>").append("").append("</CLIENT_PORT>\n");
            xmlBuilder.append("    <SERVER_ADDRESS>").append(ip).append("</SERVER_ADDRESS>\n");
            xmlBuilder.append("    <SERVER_PORT>").append(port).append("</SERVER_PORT>\n");
            xmlBuilder.append("    <SERVER_MAC>").append(mac).append("</SERVER_MAC>\n");
            xmlBuilder.append("  </LOG4A>\n");
        }

        xmlBuilder.append("</ROOT>");
        return xmlBuilder.toString();
    }
}