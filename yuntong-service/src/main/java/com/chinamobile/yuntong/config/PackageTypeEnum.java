package com.chinamobile.yuntong.config;

/**
 * 套餐类型枚举类
 */
public enum PackageTypeEnum {

    NEW(1,"新订购一次性套餐"),
    EXISTED_ONECE(2,"存量客户一次性订购套餐"),
    EXISTED_CONTINUE(3,"存量客户连续包月套餐"),
    MALL(4,"电商渠道订购套餐"),
    ;

    public Integer code;
    public String type;

    PackageTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public static boolean contains(Integer code){
        if(code == null){
            return false;
        }
        PackageTypeEnum[] values = PackageTypeEnum.values();
        for (PackageTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }
}
