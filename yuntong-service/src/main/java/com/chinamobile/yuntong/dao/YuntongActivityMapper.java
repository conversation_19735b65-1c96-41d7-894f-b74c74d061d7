package com.chinamobile.yuntong.dao;

import com.chinamobile.yuntong.pojo.entity.YuntongActivity;
import com.chinamobile.yuntong.pojo.entity.YuntongActivityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface YuntongActivityMapper {
    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    long countByExample(YuntongActivityExample example);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int deleteByExample(YuntongActivityExample example);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int insert(YuntongActivity record);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int insertSelective(YuntongActivity record);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    List<YuntongActivity> selectByExample(YuntongActivityExample example);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    YuntongActivity selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int updateByExampleSelective(@Param("record") YuntongActivity record, @Param("example") YuntongActivityExample example);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int updateByExample(@Param("record") YuntongActivity record, @Param("example") YuntongActivityExample example);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int updateByPrimaryKeySelective(YuntongActivity record);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int updateByPrimaryKey(YuntongActivity record);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int batchInsert(@Param("list") List<YuntongActivity> list);

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    int batchInsertSelective(@Param("list") List<YuntongActivity> list, @Param("selective") YuntongActivity.Column ... selective);
}