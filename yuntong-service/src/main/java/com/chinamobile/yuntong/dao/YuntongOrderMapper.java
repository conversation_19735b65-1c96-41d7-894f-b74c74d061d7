package com.chinamobile.yuntong.dao;

import com.chinamobile.yuntong.pojo.entity.YuntongOrder;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface YuntongOrderMapper {
    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    long countByExample(YuntongOrderExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int deleteByExample(YuntongOrderExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int insert(YuntongOrder record);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int insertSelective(YuntongOrder record);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    List<YuntongOrder> selectByExample(YuntongOrderExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    YuntongOrder selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int updateByExampleSelective(@Param("record") YuntongOrder record, @Param("example") YuntongOrderExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int updateByExample(@Param("record") YuntongOrder record, @Param("example") YuntongOrderExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int updateByPrimaryKeySelective(YuntongOrder record);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int updateByPrimaryKey(YuntongOrder record);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int batchInsert(@Param("list") List<YuntongOrder> list);

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    int batchInsertSelective(@Param("list") List<YuntongOrder> list, @Param("selective") YuntongOrder.Column ... selective);
}