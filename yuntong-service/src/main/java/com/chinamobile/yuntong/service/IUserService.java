package com.chinamobile.yuntong.service;

import com.chinamobile.yuntong.pojo.param.AddAddressParam;
import com.chinamobile.yuntong.pojo.param.UpdateAddressParam;
import com.chinamobile.yuntong.pojo.vo.YuntongUserAddressVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IUserService {

    List<YuntongUserAddressVO> getAddressList(String userId);

    void addAddress(AddAddressParam param, String userId);

    void updateAddress(UpdateAddressParam param, String userId);

    void deleteAddress(String id, String userId);

    void importYuntongAppUsers(MultipartFile excel);

}
