package com.chinamobile.yuntong.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.yuntong.config.RestTemplateConfig;
import com.chinamobile.yuntong.dao.YuntongApplyInvoiceInfoMapper;
import com.chinamobile.yuntong.dao.YuntongApplyInvoiceRecordMapper;
import com.chinamobile.yuntong.dao.YuntongOrderInfoMapper;
import com.chinamobile.yuntong.exception.ServicePowerException;
import com.chinamobile.yuntong.exception.StatusContant;
import com.chinamobile.yuntong.pojo.entity.YuntongApplyInvoiceInfo;
import com.chinamobile.yuntong.pojo.entity.YuntongApplyInvoiceRecord;
import com.chinamobile.yuntong.pojo.entity.YuntongApplyInvoiceRecordExample;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderInfo;
import com.chinamobile.yuntong.pojo.param.InvoiceApplyParam;
import com.chinamobile.yuntong.request.SyncCommonRequest;
import com.chinamobile.yuntong.request.revenue.GetInvoiceRequest;
import com.chinamobile.yuntong.request.revenue.GetTokenRequest;
import com.chinamobile.yuntong.request.revenue.InvoiceApplyRequest;
import com.chinamobile.yuntong.request.revenue.YuntongInvoiceRequest;
import com.chinamobile.yuntong.response.revenue.GetInvoiceResponse;
import com.chinamobile.yuntong.response.revenue.GetTokenResponse;
import com.chinamobile.yuntong.response.revenue.InvoiceApplyResponse;
import com.chinamobile.yuntong.response.revenue.YuntongInvoiceResponse;
import com.chinamobile.yuntong.service.IInvoiceService;
import com.chinamobile.yuntong.util.YuntongSignUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URL;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;


/**
 **/
@Slf4j
@Service
public class YuntongInvoiceServiceImpl implements IInvoiceService {


    @Value("${revenue.token.url}")
    private String revenueTokenUrl;

    @Value("${revenue.token.appKey}")
    private String revenueAppKey;

    @Value("${revenue.token.appSecret}")
    private String revenueAppSecret;

    @Value("${revenue.invoiceApply.url}")
    private String revenueInvoiceApplyUrl;

    @Value("${revenue.invoiceApply.isTest}")
    private String revenueInvoiceApplyIsTest;

    @Value("${revenue.getInvoice.url}")
    private String revenueGetInvoiceUrl;


    @Value("${yuntong.invoice.url}")
    private String yuntongInvoiceUrl;
    @Value("${yuntong.invoice.path}")
    private String yuntongInvoicePath;
    @Value("${yuntong.invoice.secret}")
    private String yuntongInvoiceSecret;
    @Value("${yuntong.invoice.appid}")
    private String yuntongInvoiceAppid;

    @Value("${supply.des.key}")
    private String desKey;
    @Resource
    private YuntongApplyInvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private YuntongApplyInvoiceRecordMapper invoiceRecMapper;

    @Resource
    private YuntongOrderInfoMapper orderInfoMapper;

    @Resource
    private RedisTemplate redisTemplate;

    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));



    /**
     *@Description: 云瞳同步开票申请至OS系统
     *@param syncCommonRequest:
     *@return: IOTAnswer<Void>
     *@Author: zyj
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> InvoiceRequest2OS(SyncCommonRequest syncCommonRequest) {
        log.info("云瞳同步发票申请信息请求:{}", JSON.toJSONString(syncCommonRequest));
        Date now = new Date();
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
//      sign验签
        try {
            YuntongSignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }
        InvoiceApplyParam invoiceApply = null;;
        try {
            invoiceApply = JSON.parseObject(input, InvoiceApplyParam.class);
        } catch (Exception e) {
            log.error("解析异常:" + e);
            baseAnswer.setMessage("input解析错误");
            baseAnswer.setStateCode("10004");
            return baseAnswer;
        }
        //
        if(ObjectUtils.isNotEmpty(invoiceApply) && ObjectUtils.isNotEmpty(invoiceApply.getOrderNumber())){
            List<YuntongApplyInvoiceRecord> applyInvoiceRecordExist = invoiceRecMapper.selectByExample(
              new YuntongApplyInvoiceRecordExample().createCriteria()
                      .andOrderIdEqualTo(invoiceApply.getOrderNumber())
                      .example()
            );
            if(!applyInvoiceRecordExist.isEmpty()){
                baseAnswer.setMessage("订单已提交开票请求");
                baseAnswer.setStateCode("10004");

                return baseAnswer;
            }
            YuntongOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(invoiceApply.getOrderNumber());
            if(orderInfo == null){
                baseAnswer.setMessage("对应订单不存在");
                baseAnswer.setStateCode("10004");

                return baseAnswer;
            }else{
                YuntongApplyInvoiceRecord applyInvoiceRecord = new YuntongApplyInvoiceRecord();
                applyInvoiceRecord.setId(BaseServiceUtils.getId());
                applyInvoiceRecord.setOrderId(invoiceApply.getOrderNumber());
                applyInvoiceRecord.setFrank("1");
                applyInvoiceRecord.setpName(invoiceApply.getInvoiceTitle());
                applyInvoiceRecord.setIdentifyNum(invoiceApply.getTaxpayerNumber());
                applyInvoiceRecord.setAddressInfo(aesDecrypt(invoiceApply.getClientAddress(), desKey));
                applyInvoiceRecord.setPhoneNumber(aesDecrypt(invoiceApply.getClientPhone(), desKey));
                applyInvoiceRecord.setBankName(invoiceApply.getClientOpeningBank());
                applyInvoiceRecord.setBankId(invoiceApply.getClientAccount());
                applyInvoiceRecord.setOrderPrice(orderInfo.getValoremTotal());
                applyInvoiceRecord.setStatus(0);
                applyInvoiceRecord.setCreateTime(now);
                applyInvoiceRecord.setUpdateTime(now);
                applyInvoiceRecord.setVoucherSum(orderInfo.getValoremTotal());

                invoiceRecMapper.insert(applyInvoiceRecord);
                log.info("成功记录开具发票申请：{}", JSON.toJSONString(applyInvoiceRecord));
                executor.execute(() -> revenueInvoiceApply(applyInvoiceRecord.getId()));
            }
        }else{
            //解析请求为null，则返回失败
            baseAnswer.setMessage("input解析错误");
            baseAnswer.setStateCode("10004");

            return baseAnswer;
        }
        return baseAnswer;
    }



    /**
     * @return
     * @Description: 请求应收系统返回发票信息和链接；并同步至IOT商城
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void invoicingResult2IOTMall1(String orderId) {

        List<YuntongApplyInvoiceRecord> applyInvoiceRecs = invoiceRecMapper.selectByExample(
          new YuntongApplyInvoiceRecordExample().createCriteria()
                  .andOrderIdEqualTo(orderId).example()
        );
        if (CollectionUtil.isEmpty(applyInvoiceRecs)){
            throw new BusinessException(StatusContant.INTERNAL_ERROR);
        }

        for (YuntongApplyInvoiceRecord applyInvoiceRec : applyInvoiceRecs){
            // 请求应收系统获取信息
            GetInvoiceRequest getInvoiceRequest = new GetInvoiceRequest();
            getInvoiceRequest.setCurrentPage(1);
            getInvoiceRequest.setPageSize(5);
            getInvoiceRequest.setAccessToken(getRevenueToken());
            getInvoiceRequest.setApplyDocumentNumber(applyInvoiceRec.getApplyDocumentNumber());
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(getInvoiceRequest), headers);

                log.info("云瞳请求应收系统获取开票信息 request:{}", JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<GetInvoiceResponse> response = restTemplateHttps.postForEntity(revenueGetInvoiceUrl, requestEntity, GetInvoiceResponse.class);
                log.info("云瞳请求应收系统获取开票信息 response:{}", JSON.toJSONString(response));
                GetInvoiceResponse getInvoiceResponse = response.getBody();
                if (!getInvoiceResponse.getSuccessFlag().equals("Y")) {
                    log.info("云瞳请求应收系统获取开票信息失败");
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "云瞳请求应收系统获取开票信息");
                }else{
                    // 记录发票录入信息
                    if(ObjectUtils.isNotEmpty(getInvoiceResponse.getList())){
                        for(GetInvoiceResponse.invoiceList invoiceInfo : getInvoiceResponse.getList()){
                            if(invoiceInfo.getInvoiceStatus() == 1){
                                for(int i=0; i<invoiceInfo.getSalesInvoiceAddressList().size(); i++){
                                    // 封装发票文件名
//                                    String voucherFileName = String.format("%s_Voucher_%s_%s_%s_%s.pdf", applyInvoiceRec.getBeId(), InvoiceConstant.MONTH_INVOICE
//                                            ,custCode, applyInvoiceRec.getOrderSeq(), ShareCodeUtil.getRandomNum(6));
                                    YuntongApplyInvoiceInfo applyInvoiceInfo = new YuntongApplyInvoiceInfo();

                                    applyInvoiceInfo.setId(BaseServiceUtils.getId());
                                    applyInvoiceInfo.setOrderId(applyInvoiceRec.getOrderId());
                                    applyInvoiceInfo.setVoucherNum(invoiceInfo.getInvoiceNumber());
                                    applyInvoiceInfo.setVoucherId(invoiceInfo.getInvoiceCode());
                                    applyInvoiceInfo.setVoucherSum(invoiceInfo.getValoremTotal().toString());
                                    applyInvoiceInfo.setBillingDate(invoiceInfo.getLogInfoList().get(0).getCreateTime().toString());
                                    applyInvoiceInfo.setCreateTime(new Date());
                                    //记录发票信息
//                                    invoiceInfos.add(applyInvoiceInfo);
//                                    invoiceInfoMapper.insert(applyInvoiceInfo);
                                    String voucherUrl = invoiceInfo.getSalesInvoiceAddressList().get(i).getUrl();
                                    applyInvoiceInfo.setRevenueUrl(voucherUrl);

                                    invoiceInfoMapper.insert(applyInvoiceInfo);

                                    // 4.3 同步至IOT商城-电子发票开具结果反馈接口（业务订单id下所有子订单完成发票录入后进行反馈）(原子订单状态更新后是否能马上查到)
//                                    invoice2IOTService.invoicingResult2IOTMall(applyInvoiceRec, "1");
                                    YuntongInvoiceRequest yuntongInvoiceRequest = new YuntongInvoiceRequest();
                                    yuntongInvoiceRequest.setInvoiceUrl(voucherUrl);
                                    // 定义日期格式
                                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                                    // 解析为 LocalDateTime（无时区信息）
                                    LocalDateTime localDateTime = LocalDateTime.parse(invoiceInfo.getLogInfoList().get(0).getCreateTime(), formatter);

                                    // 将 LocalDateTime 转换为系统默认时区的 ZonedDateTime
                                    ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());

                                    // 转换为 13位时间戳（毫秒）
                                    long timestamp = zonedDateTime.toInstant().toEpochMilli();
                                    yuntongInvoiceRequest.setInvoicingTime(String.valueOf(timestamp));
                                    yuntongInvoiceRequest.setOrderNumber(orderId);
                                    executor.execute(() -> yuntongInvoice(yuntongInvoiceRequest));

                                }
                            }else if (invoiceInfo.getInvoiceStatus() == 4 || invoiceInfo.getInvoiceStatus() == -1){
                                // 开票失败
                                applyInvoiceRec.setStatus(-1);
                                applyInvoiceRec.setRemark(invoiceInfo.getInvoiceStatus() == 4 ? "开票失败":"未找到对应订单");
                                invoiceRecMapper.updateByPrimaryKeySelective(applyInvoiceRec);
                            }else{
                                //开票中 存一哈留个底
//                                applyInvoiceRec.setStatus(invoiceInfo.getInvoiceStatus());
//                                invoiceRecMapper.updateById(applyInvoiceRec);
                            }
                        }
                    }else{
                        //列表为空直接过
                    }
                }
            }
            catch (Exception e) {
                log.info("请求应收系统获取开票信息失败: {}",e.getMessage());
            }
        }
        return null;
    }

    public static byte[] downloadBytesFromUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            try (InputStream inputStream = url.openStream();
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                }
                return byteArrayOutputStream.toByteArray();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *@Description: 应收系统返回token
     */
    @Override
    public String getRevenueToken() {
        String token = (String) redisTemplate.opsForValue().get(Constant.REDIS_KEY_REVENUE_TOKEN);
        if (null == token) {
            String timeStamp = DateTimeUtil.formatDate(new Date(), DateTimeUtil.DEFAULT_DATE_DEFAULT);
            GetTokenRequest request = new GetTokenRequest();
            request.setAppKey(revenueAppKey);
            request.setAppSecret(revenueAppSecret);
            request.setTimestamp(timeStamp);
            String md5Str = revenueAppKey + revenueAppSecret + timeStamp;
            String sign = getMD5ForString(md5Str);
            request.setSign(sign);
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(request), headers);

                log.info("请求应收系统token request:{}", JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<GetTokenResponse> response = restTemplateHttps.postForEntity(revenueTokenUrl, requestEntity, GetTokenResponse.class);
                log.info("请求应收系统token response:{}", JSON.toJSONString(response));
                GetTokenResponse getTokenResponse = response.getBody();
                if(getTokenResponse.getSuccessFlag().equals("Y")){
                    token = getTokenResponse.getAccessToken();
                    redisTemplate.opsForValue().setIfAbsent(Constant.REDIS_KEY_REVENUE_TOKEN, token, 120,TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
            }
        }
        return token;
    }

    /**
     * 字符串MD5计算
     *
     * @param str
     *            字符串
     * @return 计算后的字符串
     */
    private String getMD5ForString(String str) {
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(str.getBytes());
            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
            String md5 = new BigInteger(1, md.digest()).toString(16);
            // BigInteger会把0省略掉，需补全至32位
            return fillMD5(md5);
        } catch (Exception e) {
            throw new RuntimeException("MD5加密错误:" + e.getMessage(), e);
        }
    }

    /**
     * 将字符串填补为32位
     *
     * @param md5
     *            MD5字符串
     * @return 填补后的字符串
     */
    private  String fillMD5(String md5) {
        return md5.length() == 32 ? md5 : fillMD5("0" + md5);
    }


    /**
     *@Description: 开票
     */
    @Override
    public BaseAnswer<Void> revenueInvoiceApply(String recId) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        YuntongApplyInvoiceRecord applyInvoiceRec = invoiceRecMapper.selectByPrimaryKey(recId);
        if(applyInvoiceRec == null){
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "发票申请不存在");
        }
        InvoiceApplyRequest invoiceApplyRequest = new InvoiceApplyRequest();
        invoiceApplyRequest.setAccessToken(getRevenueToken());
        invoiceApplyRequest.setOrderNumber(applyInvoiceRec.getOrderId());
        invoiceApplyRequest.setClientProperty("0");//先传0
        if(revenueInvoiceApplyIsTest.equals("Y")){
            invoiceApplyRequest.setInvoiceTitle("中移物联网有限公司虚拟单位");
        }else{
            invoiceApplyRequest.setInvoiceTitle(applyInvoiceRec.getpName());
        }
        if(StringUtils.isNotEmpty(applyInvoiceRec.getIdentifyNum())){
            invoiceApplyRequest.setTaxpayerNumber(applyInvoiceRec.getIdentifyNum());
        }else{
            if(revenueInvoiceApplyIsTest.equals("Y")){
                invoiceApplyRequest.setTaxpayerNumber("919901080000289272");
            }else{
                invoiceApplyRequest.setTaxpayerNumber("00000");
            }
        }
        invoiceApplyRequest.setClientOpeningBank(applyInvoiceRec.getBankName());
        invoiceApplyRequest.setClientAccount(applyInvoiceRec.getBankId());
        invoiceApplyRequest.setClientAddress(applyInvoiceRec.getAddressInfo());
        invoiceApplyRequest.setClientPhone(applyInvoiceRec.getPhoneNumber());
        if(applyInvoiceRec.getFrank().equals("1")){
            invoiceApplyRequest.setInvoiceType(0);
        }else{
            invoiceApplyRequest.setInvoiceType(1);
        }
        //有税号传0 无税号传1
        if(StringUtils.isNotEmpty(applyInvoiceRec.getIdentifyNum())){
            invoiceApplyRequest.setIsGmfzrrbz(0);
        }else{
            invoiceApplyRequest.setIsGmfzrrbz(1);
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(invoiceApplyRequest), headers);

            log.info("请求应收系统开票 request:{}", JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<InvoiceApplyResponse> response = restTemplateHttps.postForEntity(revenueInvoiceApplyUrl, requestEntity, InvoiceApplyResponse.class);
            log.info("请求应收系统开票 response:{}", JSON.toJSONString(response));
            InvoiceApplyResponse invoiceApplyResponse = response.getBody();
            if (!invoiceApplyResponse.getCode().equals("200")) {
                log.info("请求应收系统开票失败");
                applyInvoiceRec.setStatus(-1);
                applyInvoiceRec.setRemark(invoiceApplyResponse.getMsg());
                invoiceRecMapper.updateByPrimaryKeySelective(applyInvoiceRec);
                throw new BusinessException(StatusContant.INTERNAL_ERROR, invoiceApplyResponse.getMsg());
            }else{
                applyInvoiceRec.setApplyDocumentNumber(invoiceApplyResponse.getData().getApplyDocumentNumber());
                applyInvoiceRec.setStatus(1);
                invoiceRecMapper.updateByPrimaryKeySelective(applyInvoiceRec);
            }
        }
        catch (Exception e) {
            log.info("请求应收系统开票失败");
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }
        return  baseAnswer;
    }

    public BaseAnswer<Void> yuntongInvoice(YuntongInvoiceRequest request) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        try {
            // 2. 生成时间戳和流水号
            String timestamp = String.valueOf(System.currentTimeMillis());
            String msgSeq = "REQ_" + timestamp; // 示例流水号

            // 3. 生成请求Body的JSON和MD5
            ObjectMapper mapper = new ObjectMapper();
            mapper.enable(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS);
            String requestBody = mapper.writeValueAsString(request);
            log.info("云瞳回传发票url接口body待md5加密:{}", requestBody);
            String md5 = YuntongSignUtils.md532bit(requestBody);

            // 4. 生成签名
            String messageToSign = String.format("%s,%s,%s,%s,%s",
                    yuntongInvoiceSecret, yuntongInvoiceAppid, timestamp, yuntongInvoicePath, md5);
            log.info("云瞳回传发票url接口待加密签名:{}", JSON.toJSONString(messageToSign));
            String signature = YuntongSignUtils.hmacSha256(yuntongInvoiceSecret, messageToSign);
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");
            headers.add("appid", yuntongInvoiceAppid);
            headers.add("msgSeq", msgSeq);
            headers.add("timestamp", timestamp);
            headers.add("signature", signature);
            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(request), headers);

            log.info("返回发票url给云瞳 request:{}", JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<YuntongInvoiceResponse> response = restTemplateHttps.postForEntity(yuntongInvoiceUrl, requestEntity, YuntongInvoiceResponse.class);
            log.info("返回发票url给云瞳 response:{}", JSON.toJSONString(response));
            YuntongInvoiceResponse yuntongInvoiceResponse = response.getBody();
            if (!yuntongInvoiceResponse.getResultCode().equals("00000")) {
                log.info("返回发票url给云瞳失败");
                throw new BusinessException(StatusContant.INTERNAL_ERROR, yuntongInvoiceResponse.getResultMsg());
            }else{
            }
        }
        catch (Exception e) {
            log.info("返回发票url给云瞳失败");
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }
        return baseAnswer;
    }

    public static String aesDecrypt(String input, String key) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(input));
            return new String(decryptedBytes);
        } catch (Exception e) {
            log.error("解密失败，解密原文: {}", input, e);
            return input; // 返回原文
        }

    }

}
