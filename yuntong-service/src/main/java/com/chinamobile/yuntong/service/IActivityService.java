package com.chinamobile.yuntong.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.yuntong.pojo.param.*;
import com.chinamobile.yuntong.pojo.vo.*;
import com.chinamobile.iot.sc.mode.PageData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IActivityService {

    PageData<YuntongActivityVO> pageActivities(PageActivitiesParam param);

    void updateActivity(UpdateActivityParam param);

    PageData<YuntongActivityAwardRecordVO> pageAwardsRecord(PageAwardRecordParam param);

    void configTrackingNumber(List<ConfigAwardLogisticsTrackingNumberParam> params);

    void configTrackingNumberBatch(MultipartFile file);

    YuntongActivityAwardRecordDetailVO awardRecordDetail(String id);

    void exportAwardsRecord(String userId);

    void offline(ActivityOfflineParam param);

    YuntongActivityH5VO getH5ActivityById(String activityId, LoginIfo4Redis loginIfo4Redis);

    String raffleByActivityId(RaffleParam param, LoginIfo4Redis loginIfo4Redis);

    PageData<YuntongUserAwardH5VO> getAwardRecordsByUser(PageUserAwardRecordParam param, String userId);

    void bindAwardAddress(BindAwardAddressParam param);

}
