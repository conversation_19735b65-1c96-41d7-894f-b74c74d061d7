package com.chinamobile.yuntong.pojo.param;

import lombok.Data;

import java.util.List;

@Data
public class OrderInfoParam  {


    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 业务日期
     */
    private String businessDate;

    /**
     * 审核人
     */
    private String approveId;

    /**
     * 价税合计
     */
    private String valoremTotal;

    /**
     * 客户编码
     */
    private String client;

    /**
     * 销售组织
     */
    private String saleOrgId;

    /**
     * 销售组织名称
     */
    private String saleOrgName;

    /**
     * 销售部门成本中心名称
     */
    private String costCenterName;

    /**
     * 销售部门成本中心编码
     */
    private String costCenterCode;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 销售员名称
     */
    private String salesmanName;

    /**
     * 收款组织
     */
    private String payOrgId;

    /**
     */
    private String redBlue;

    /**
     * 回款责任人
     */
    private String responsiblePerson;

    /**
     * 税额
     */
    private String taxAmountFor;

    /**
     * 不含税金额
     */
    private String noTaxAmountFor;

    /**
     * 销售部门编码
     */
    private String saleDeptId;

    /**
     * 物料List
     *
     */
    private List<OrderMaterialParam> materialList;

}
