package com.chinamobile.yuntong.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 和目/云瞳订单表，包含一些套餐信息
 *
 * <AUTHOR>
public class YuntongOrder implements Serializable {
    /**
     * 订单编号
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String id;

    /**
     * spu编码，新订购套餐才有此值
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String spuCode;

    /**
     * spu名称，新订购套餐才有此值
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String spuName;

    /**
     * sku编码，新订购套餐才有此值
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String skuCode;

    /**
     * sku名称；存量客户订单存套餐名称，便于订单列表查询
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String skuName;

    /**
     * 原子编码，新订购套餐才有此值
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String atomCode;

    /**
     * 原子名称，新订购套餐才有此值
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String atomName;

    /**
     * 订单金额，单位厘
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Long amount;

    /**
     * 订购数量
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Integer skuQuantity;

    /**
     * 订单状态，新订购订单取商城同步的订单状态，存量订单默认为3-交易成功
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Integer orderStatus;

    /**
     * 云瞳用户id,user_yuntong表主键
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private String userId;

    /**
     * 订单类型 1-新订购一次性订单 2-存量客户一次性订单 3-存量客户连续包月订单
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Integer orderType;

    /**
     * 支付时间，取商城同步订单状态0时的order_status_time
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Date payTime;

    /**
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.id
     *
     * @return the value of supply_chain..yuntong_order.id
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.id
     *
     * @param id the value for supply_chain..yuntong_order.id
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.spu_code
     *
     * @return the value of supply_chain..yuntong_order.spu_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.spu_code
     *
     * @param spuCode the value for supply_chain..yuntong_order.spu_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.spu_name
     *
     * @return the value of supply_chain..yuntong_order.spu_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getSpuName() {
        return spuName;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withSpuName(String spuName) {
        this.setSpuName(spuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.spu_name
     *
     * @param spuName the value for supply_chain..yuntong_order.spu_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.sku_code
     *
     * @return the value of supply_chain..yuntong_order.sku_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.sku_code
     *
     * @param skuCode the value for supply_chain..yuntong_order.sku_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.sku_name
     *
     * @return the value of supply_chain..yuntong_order.sku_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withSkuName(String skuName) {
        this.setSkuName(skuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.sku_name
     *
     * @param skuName the value for supply_chain..yuntong_order.sku_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.atom_code
     *
     * @return the value of supply_chain..yuntong_order.atom_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getAtomCode() {
        return atomCode;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withAtomCode(String atomCode) {
        this.setAtomCode(atomCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.atom_code
     *
     * @param atomCode the value for supply_chain..yuntong_order.atom_code
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setAtomCode(String atomCode) {
        this.atomCode = atomCode;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.atom_name
     *
     * @return the value of supply_chain..yuntong_order.atom_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getAtomName() {
        return atomName;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withAtomName(String atomName) {
        this.setAtomName(atomName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.atom_name
     *
     * @param atomName the value for supply_chain..yuntong_order.atom_name
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setAtomName(String atomName) {
        this.atomName = atomName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.amount
     *
     * @return the value of supply_chain..yuntong_order.amount
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Long getAmount() {
        return amount;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withAmount(Long amount) {
        this.setAmount(amount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.amount
     *
     * @param amount the value for supply_chain..yuntong_order.amount
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setAmount(Long amount) {
        this.amount = amount;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.sku_quantity
     *
     * @return the value of supply_chain..yuntong_order.sku_quantity
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Integer getSkuQuantity() {
        return skuQuantity;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withSkuQuantity(Integer skuQuantity) {
        this.setSkuQuantity(skuQuantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.sku_quantity
     *
     * @param skuQuantity the value for supply_chain..yuntong_order.sku_quantity
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setSkuQuantity(Integer skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.order_status
     *
     * @return the value of supply_chain..yuntong_order.order_status
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Integer getOrderStatus() {
        return orderStatus;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.order_status
     *
     * @param orderStatus the value for supply_chain..yuntong_order.order_status
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.user_id
     *
     * @return the value of supply_chain..yuntong_order.user_id
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.user_id
     *
     * @param userId the value for supply_chain..yuntong_order.user_id
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.order_type
     *
     * @return the value of supply_chain..yuntong_order.order_type
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Integer getOrderType() {
        return orderType;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withOrderType(Integer orderType) {
        this.setOrderType(orderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.order_type
     *
     * @param orderType the value for supply_chain..yuntong_order.order_type
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.pay_time
     *
     * @return the value of supply_chain..yuntong_order.pay_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withPayTime(Date payTime) {
        this.setPayTime(payTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.pay_time
     *
     * @param payTime the value for supply_chain..yuntong_order.pay_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.create_time
     *
     * @return the value of supply_chain..yuntong_order.create_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.create_time
     *
     * @param createTime the value for supply_chain..yuntong_order.create_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_order.update_time
     *
     * @return the value of supply_chain..yuntong_order.update_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public YuntongOrder withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_order.update_time
     *
     * @param updateTime the value for supply_chain..yuntong_order.update_time
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", spuName=").append(spuName);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", skuName=").append(skuName);
        sb.append(", atomCode=").append(atomCode);
        sb.append(", atomName=").append(atomName);
        sb.append(", amount=").append(amount);
        sb.append(", skuQuantity=").append(skuQuantity);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", userId=").append(userId);
        sb.append(", orderType=").append(orderType);
        sb.append(", payTime=").append(payTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YuntongOrder other = (YuntongOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSpuName() == null ? other.getSpuName() == null : this.getSpuName().equals(other.getSpuName()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getSkuName() == null ? other.getSkuName() == null : this.getSkuName().equals(other.getSkuName()))
            && (this.getAtomCode() == null ? other.getAtomCode() == null : this.getAtomCode().equals(other.getAtomCode()))
            && (this.getAtomName() == null ? other.getAtomName() == null : this.getAtomName().equals(other.getAtomName()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getSkuQuantity() == null ? other.getSkuQuantity() == null : this.getSkuQuantity().equals(other.getSkuQuantity()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getPayTime() == null ? other.getPayTime() == null : this.getPayTime().equals(other.getPayTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSpuName() == null) ? 0 : getSpuName().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getSkuName() == null) ? 0 : getSkuName().hashCode());
        result = prime * result + ((getAtomCode() == null) ? 0 : getAtomCode().hashCode());
        result = prime * result + ((getAtomName() == null) ? 0 : getAtomName().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getSkuQuantity() == null) ? 0 : getSkuQuantity().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getPayTime() == null) ? 0 : getPayTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 16:19:33 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        spuName("spu_name", "spuName", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        skuName("sku_name", "skuName", "VARCHAR", false),
        atomCode("atom_code", "atomCode", "VARCHAR", false),
        atomName("atom_name", "atomName", "VARCHAR", false),
        amount("amount", "amount", "BIGINT", false),
        skuQuantity("sku_quantity", "skuQuantity", "INTEGER", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        userId("user_id", "userId", "VARCHAR", false),
        orderType("order_type", "orderType", "INTEGER", false),
        payTime("pay_time", "payTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jan 15 16:19:33 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}