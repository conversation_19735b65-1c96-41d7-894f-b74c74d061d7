package com.chinamobile.yuntong.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class YuntongActivityExample {
    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public static Criteria newAndCreateCriteria() {
        YuntongActivityExample example = new YuntongActivityExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public YuntongActivityExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("`name` <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andWebUrlIsNull() {
            addCriterion("web_url is null");
            return (Criteria) this;
        }

        public Criteria andWebUrlIsNotNull() {
            addCriterion("web_url is not null");
            return (Criteria) this;
        }

        public Criteria andWebUrlEqualTo(String value) {
            addCriterion("web_url =", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlNotEqualTo(String value) {
            addCriterion("web_url <>", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlGreaterThan(String value) {
            addCriterion("web_url >", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlGreaterThanOrEqualTo(String value) {
            addCriterion("web_url >=", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlLessThan(String value) {
            addCriterion("web_url <", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlLessThanOrEqualTo(String value) {
            addCriterion("web_url <=", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("web_url <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWebUrlLike(String value) {
            addCriterion("web_url like", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlNotLike(String value) {
            addCriterion("web_url not like", value, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlIn(List<String> values) {
            addCriterion("web_url in", values, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlNotIn(List<String> values) {
            addCriterion("web_url not in", values, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlBetween(String value1, String value2) {
            addCriterion("web_url between", value1, value2, "webUrl");
            return (Criteria) this;
        }

        public Criteria andWebUrlNotBetween(String value1, String value2) {
            addCriterion("web_url not between", value1, value2, "webUrl");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("start_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeIsNull() {
            addCriterion("stop_time is null");
            return (Criteria) this;
        }

        public Criteria andStopTimeIsNotNull() {
            addCriterion("stop_time is not null");
            return (Criteria) this;
        }

        public Criteria andStopTimeEqualTo(Date value) {
            addCriterion("stop_time =", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeNotEqualTo(Date value) {
            addCriterion("stop_time <>", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeGreaterThan(Date value) {
            addCriterion("stop_time >", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("stop_time >=", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeLessThan(Date value) {
            addCriterion("stop_time <", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeLessThanOrEqualTo(Date value) {
            addCriterion("stop_time <=", value, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("stop_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStopTimeIn(List<Date> values) {
            addCriterion("stop_time in", values, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeNotIn(List<Date> values) {
            addCriterion("stop_time not in", values, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeBetween(Date value1, Date value2) {
            addCriterion("stop_time between", value1, value2, "stopTime");
            return (Criteria) this;
        }

        public Criteria andStopTimeNotBetween(Date value1, Date value2) {
            addCriterion("stop_time not between", value1, value2, "stopTime");
            return (Criteria) this;
        }

        public Criteria andCreatorUidIsNull() {
            addCriterion("creator_uid is null");
            return (Criteria) this;
        }

        public Criteria andCreatorUidIsNotNull() {
            addCriterion("creator_uid is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorUidEqualTo(String value) {
            addCriterion("creator_uid =", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidNotEqualTo(String value) {
            addCriterion("creator_uid <>", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidGreaterThan(String value) {
            addCriterion("creator_uid >", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidGreaterThanOrEqualTo(String value) {
            addCriterion("creator_uid >=", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidLessThan(String value) {
            addCriterion("creator_uid <", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidLessThanOrEqualTo(String value) {
            addCriterion("creator_uid <=", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_uid <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorUidLike(String value) {
            addCriterion("creator_uid like", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidNotLike(String value) {
            addCriterion("creator_uid not like", value, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidIn(List<String> values) {
            addCriterion("creator_uid in", values, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidNotIn(List<String> values) {
            addCriterion("creator_uid not in", values, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidBetween(String value1, String value2) {
            addCriterion("creator_uid between", value1, value2, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorUidNotBetween(String value1, String value2) {
            addCriterion("creator_uid not between", value1, value2, "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("creator_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudIsNull() {
            addCriterion("coupon_url_with_cloud is null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudIsNotNull() {
            addCriterion("coupon_url_with_cloud is not null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudEqualTo(String value) {
            addCriterion("coupon_url_with_cloud =", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudNotEqualTo(String value) {
            addCriterion("coupon_url_with_cloud <>", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudGreaterThan(String value) {
            addCriterion("coupon_url_with_cloud >", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_url_with_cloud >=", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLessThan(String value) {
            addCriterion("coupon_url_with_cloud <", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLessThanOrEqualTo(String value) {
            addCriterion("coupon_url_with_cloud <=", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_with_cloud <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLike(String value) {
            addCriterion("coupon_url_with_cloud like", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudNotLike(String value) {
            addCriterion("coupon_url_with_cloud not like", value, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudIn(List<String> values) {
            addCriterion("coupon_url_with_cloud in", values, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudNotIn(List<String> values) {
            addCriterion("coupon_url_with_cloud not in", values, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudBetween(String value1, String value2) {
            addCriterion("coupon_url_with_cloud between", value1, value2, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudNotBetween(String value1, String value2) {
            addCriterion("coupon_url_with_cloud not between", value1, value2, "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudIsNull() {
            addCriterion("coupon_url_without_cloud is null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudIsNotNull() {
            addCriterion("coupon_url_without_cloud is not null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudEqualTo(String value) {
            addCriterion("coupon_url_without_cloud =", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudNotEqualTo(String value) {
            addCriterion("coupon_url_without_cloud <>", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudGreaterThan(String value) {
            addCriterion("coupon_url_without_cloud >", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_url_without_cloud >=", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLessThan(String value) {
            addCriterion("coupon_url_without_cloud <", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLessThanOrEqualTo(String value) {
            addCriterion("coupon_url_without_cloud <=", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_without_cloud <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLike(String value) {
            addCriterion("coupon_url_without_cloud like", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudNotLike(String value) {
            addCriterion("coupon_url_without_cloud not like", value, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudIn(List<String> values) {
            addCriterion("coupon_url_without_cloud in", values, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudNotIn(List<String> values) {
            addCriterion("coupon_url_without_cloud not in", values, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudBetween(String value1, String value2) {
            addCriterion("coupon_url_without_cloud between", value1, value2, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudNotBetween(String value1, String value2) {
            addCriterion("coupon_url_without_cloud not between", value1, value2, "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserIsNull() {
            addCriterion("coupon_url_new_user is null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserIsNotNull() {
            addCriterion("coupon_url_new_user is not null");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserEqualTo(String value) {
            addCriterion("coupon_url_new_user =", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserNotEqualTo(String value) {
            addCriterion("coupon_url_new_user <>", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserGreaterThan(String value) {
            addCriterion("coupon_url_new_user >", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_url_new_user >=", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLessThan(String value) {
            addCriterion("coupon_url_new_user <", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLessThanOrEqualTo(String value) {
            addCriterion("coupon_url_new_user <=", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("coupon_url_new_user <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLike(String value) {
            addCriterion("coupon_url_new_user like", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserNotLike(String value) {
            addCriterion("coupon_url_new_user not like", value, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserIn(List<String> values) {
            addCriterion("coupon_url_new_user in", values, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserNotIn(List<String> values) {
            addCriterion("coupon_url_new_user not in", values, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserBetween(String value1, String value2) {
            addCriterion("coupon_url_new_user between", value1, value2, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserNotBetween(String value1, String value2) {
            addCriterion("coupon_url_new_user not between", value1, value2, "couponUrlNewUser");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusIsNull() {
            addCriterion("offline_status is null");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusIsNotNull() {
            addCriterion("offline_status is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusEqualTo(Boolean value) {
            addCriterion("offline_status =", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusNotEqualTo(Boolean value) {
            addCriterion("offline_status <>", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusGreaterThan(Boolean value) {
            addCriterion("offline_status >", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("offline_status >=", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusLessThan(Boolean value) {
            addCriterion("offline_status <", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("offline_status <=", value, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("offline_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineStatusIn(List<Boolean> values) {
            addCriterion("offline_status in", values, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusNotIn(List<Boolean> values) {
            addCriterion("offline_status not in", values, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("offline_status between", value1, value2, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andOfflineStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("offline_status not between", value1, value2, "offlineStatus");
            return (Criteria) this;
        }

        public Criteria andWheelPartsIsNull() {
            addCriterion("wheel_parts is null");
            return (Criteria) this;
        }

        public Criteria andWheelPartsIsNotNull() {
            addCriterion("wheel_parts is not null");
            return (Criteria) this;
        }

        public Criteria andWheelPartsEqualTo(Integer value) {
            addCriterion("wheel_parts =", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotEqualTo(Integer value) {
            addCriterion("wheel_parts <>", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThan(Integer value) {
            addCriterion("wheel_parts >", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanOrEqualTo(Integer value) {
            addCriterion("wheel_parts >=", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThan(Integer value) {
            addCriterion("wheel_parts <", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanOrEqualTo(Integer value) {
            addCriterion("wheel_parts <=", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("wheel_parts <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsIn(List<Integer> values) {
            addCriterion("wheel_parts in", values, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotIn(List<Integer> values) {
            addCriterion("wheel_parts not in", values, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsBetween(Integer value1, Integer value2) {
            addCriterion("wheel_parts between", value1, value2, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotBetween(Integer value1, Integer value2) {
            addCriterion("wheel_parts not between", value1, value2, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(YuntongActivity.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(`name`) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andWebUrlLikeInsensitive(String value) {
            addCriterion("upper(web_url) like", value.toUpperCase(), "webUrl");
            return (Criteria) this;
        }

        public Criteria andCreatorUidLikeInsensitive(String value) {
            addCriterion("upper(creator_uid) like", value.toUpperCase(), "creatorUid");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLikeInsensitive(String value) {
            addCriterion("upper(creator_name) like", value.toUpperCase(), "creatorName");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithCloudLikeInsensitive(String value) {
            addCriterion("upper(coupon_url_with_cloud) like", value.toUpperCase(), "couponUrlWithCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlWithoutCloudLikeInsensitive(String value) {
            addCriterion("upper(coupon_url_without_cloud) like", value.toUpperCase(), "couponUrlWithoutCloud");
            return (Criteria) this;
        }

        public Criteria andCouponUrlNewUserLikeInsensitive(String value) {
            addCriterion("upper(coupon_url_new_user) like", value.toUpperCase(), "couponUrlNewUser");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon May 26 17:24:08 GMT+08:00 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        private YuntongActivityExample example;

        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        protected Criteria(YuntongActivityExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        public YuntongActivityExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon May 26 17:24:08 GMT+08:00 2025
         */
        void example(com.chinamobile.yuntong.pojo.entity.YuntongActivityExample example);
    }
}