package com.chinamobile.yuntong.pojo.mapper;

import lombok.Data;

@Data
public class OrderInfoListDO {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 业务日期
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    private String businessDate;

    /**
     * 同步给市场销售系统的时间
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    private String billNoTime;

    /**
     * 计收状态，0-待计收，1-计收成功， 2-计收失败
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    private String settleStatus;

    /**
     * 开票申请状态，-1-失败，0-待发票，1-开票中，2-开票成功
     *
     */
    private String invoiceStatus;

    /**
     * 价税合计
     *
     *
     * @mbg.generated Mon Feb 10 15:47:55 CST 2025
     */
    private String valoremTotal;

    /**
     * 开票记录id
     *
     */
    private String recId;
}
