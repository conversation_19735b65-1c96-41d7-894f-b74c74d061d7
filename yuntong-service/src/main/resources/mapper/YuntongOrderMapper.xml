<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.YuntongOrderMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.yuntong.pojo.entity.YuntongOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="spu_name" jdbcType="VARCHAR" property="spuName" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="atom_code" jdbcType="VARCHAR" property="atomCode" />
    <result column="atom_name" jdbcType="VARCHAR" property="atomName" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="sku_quantity" jdbcType="INTEGER" property="skuQuantity" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_code, spu_name, sku_code, sku_name, atom_code, atom_name, amount, sku_quantity, 
    order_status, user_id, order_type, pay_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yuntong_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from yuntong_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order (id, spu_code, spu_name, 
      sku_code, sku_name, atom_code, 
      atom_name, amount, sku_quantity, 
      order_status, user_id, order_type, 
      pay_time, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{spuName,jdbcType=VARCHAR}, 
      #{skuCode,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{atomCode,jdbcType=VARCHAR}, 
      #{atomName,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{skuQuantity,jdbcType=INTEGER}, 
      #{orderStatus,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{payTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="spuName != null">
        spu_name,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="atomCode != null">
        atom_code,
      </if>
      <if test="atomName != null">
        atom_name,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="skuQuantity != null">
        sku_quantity,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="atomCode != null">
        #{atomCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="skuQuantity != null">
        #{skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from yuntong_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuName != null">
        spu_name = #{record.spuName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.atomCode != null">
        atom_code = #{record.atomCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomName != null">
        atom_name = #{record.atomName,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.skuQuantity != null">
        sku_quantity = #{record.skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order
    set id = #{record.id,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      spu_name = #{record.spuName,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      atom_code = #{record.atomCode,jdbcType=VARCHAR},
      atom_name = #{record.atomName,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      sku_quantity = #{record.skuQuantity,jdbcType=INTEGER},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order
    <set>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        spu_name = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="atomCode != null">
        atom_code = #{atomCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        atom_name = #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="skuQuantity != null">
        sku_quantity = #{skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order
    set spu_code = #{spuCode,jdbcType=VARCHAR},
      spu_name = #{spuName,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      atom_code = #{atomCode,jdbcType=VARCHAR},
      atom_name = #{atomName,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      sku_quantity = #{skuQuantity,jdbcType=INTEGER},
      order_status = #{orderStatus,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order
    (id, spu_code, spu_name, sku_code, sku_name, atom_code, atom_name, amount, sku_quantity, 
      order_status, user_id, order_type, pay_time, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.spuName,jdbcType=VARCHAR}, 
        #{item.skuCode,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, #{item.atomCode,jdbcType=VARCHAR}, 
        #{item.atomName,jdbcType=VARCHAR}, #{item.amount,jdbcType=BIGINT}, #{item.skuQuantity,jdbcType=INTEGER}, 
        #{item.orderStatus,jdbcType=INTEGER}, #{item.userId,jdbcType=VARCHAR}, #{item.orderType,jdbcType=INTEGER}, 
        #{item.payTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_name'.toString() == column.value">
          #{item.spuName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_name'.toString() == column.value">
          #{item.skuName,jdbcType=VARCHAR}
        </if>
        <if test="'atom_code'.toString() == column.value">
          #{item.atomCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_name'.toString() == column.value">
          #{item.atomName,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=BIGINT}
        </if>
        <if test="'sku_quantity'.toString() == column.value">
          #{item.skuQuantity,jdbcType=INTEGER}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=INTEGER}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=INTEGER}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>