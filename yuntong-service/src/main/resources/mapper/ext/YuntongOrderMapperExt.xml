<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.ext.YuntongOrderPackageMapperExt">
    <select id="orderList" resultType="com.chinamobile.yuntong.pojo.mapper.OrderListDO">
        SELECT
        o.id,
        o.pay_time payTime,
        o.sku_name skuName,
        o.amount amount,
        o.sku_quantity skuQuantity,
        o.order_status orderStatus,
        o.order_type orderType
        FROM
        yuntong_order o
        where  1=1
        <if test="param.userId != null and param.userId != ''">
            and o.userId = #{param.userId}
        </if>
        <if test="param.orderType != null and param.orderType != ''">
            and o.order_type = #{param.orderType}
        </if>

        order by o.pay_time DESC
    </select>
</mapper>