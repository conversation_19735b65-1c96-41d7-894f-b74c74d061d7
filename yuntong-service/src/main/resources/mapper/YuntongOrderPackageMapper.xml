<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.YuntongOrderPackageMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="yuntong_order_id" jdbcType="VARCHAR" property="yuntongOrderId" />
    <result column="device_mac" jdbcType="VARCHAR" property="deviceMac" />
    <result column="package_number" jdbcType="VARCHAR" property="packageNumber" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="valid_time" jdbcType="TIMESTAMP" property="validTime" />
    <result column="invalid_time" jdbcType="TIMESTAMP" property="invalidTime" />
    <result column="switched" jdbcType="BIT" property="switched" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, yuntong_order_id, device_mac, package_number, status, user_id, valid_time, invalid_time, 
    switched, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yuntong_order_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from yuntong_order_package
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order_package
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackageExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_package (id, yuntong_order_id, device_mac, 
      package_number, status, user_id, 
      valid_time, invalid_time, switched, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{yuntongOrderId,jdbcType=VARCHAR}, #{deviceMac,jdbcType=VARCHAR}, 
      #{packageNumber,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, 
      #{validTime,jdbcType=TIMESTAMP}, #{invalidTime,jdbcType=TIMESTAMP}, #{switched,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="yuntongOrderId != null">
        yuntong_order_id,
      </if>
      <if test="deviceMac != null">
        device_mac,
      </if>
      <if test="packageNumber != null">
        package_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="validTime != null">
        valid_time,
      </if>
      <if test="invalidTime != null">
        invalid_time,
      </if>
      <if test="switched != null">
        switched,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="yuntongOrderId != null">
        #{yuntongOrderId,jdbcType=VARCHAR},
      </if>
      <if test="deviceMac != null">
        #{deviceMac,jdbcType=VARCHAR},
      </if>
      <if test="packageNumber != null">
        #{packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidTime != null">
        #{invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="switched != null">
        #{switched,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from yuntong_order_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.yuntongOrderId != null">
        yuntong_order_id = #{record.yuntongOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceMac != null">
        device_mac = #{record.deviceMac,jdbcType=VARCHAR},
      </if>
      <if test="record.packageNumber != null">
        package_number = #{record.packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.validTime != null">
        valid_time = #{record.validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invalidTime != null">
        invalid_time = #{record.invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.switched != null">
        switched = #{record.switched,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_package
    set id = #{record.id,jdbcType=VARCHAR},
      yuntong_order_id = #{record.yuntongOrderId,jdbcType=VARCHAR},
      device_mac = #{record.deviceMac,jdbcType=VARCHAR},
      package_number = #{record.packageNumber,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=VARCHAR},
      valid_time = #{record.validTime,jdbcType=TIMESTAMP},
      invalid_time = #{record.invalidTime,jdbcType=TIMESTAMP},
      switched = #{record.switched,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_package
    <set>
      <if test="yuntongOrderId != null">
        yuntong_order_id = #{yuntongOrderId,jdbcType=VARCHAR},
      </if>
      <if test="deviceMac != null">
        device_mac = #{deviceMac,jdbcType=VARCHAR},
      </if>
      <if test="packageNumber != null">
        package_number = #{packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="validTime != null">
        valid_time = #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidTime != null">
        invalid_time = #{invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="switched != null">
        switched = #{switched,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_package
    set yuntong_order_id = #{yuntongOrderId,jdbcType=VARCHAR},
      device_mac = #{deviceMac,jdbcType=VARCHAR},
      package_number = #{packageNumber,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=VARCHAR},
      valid_time = #{validTime,jdbcType=TIMESTAMP},
      invalid_time = #{invalidTime,jdbcType=TIMESTAMP},
      switched = #{switched,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_package
    (id, yuntong_order_id, device_mac, package_number, status, user_id, valid_time, invalid_time, 
      switched, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.yuntongOrderId,jdbcType=VARCHAR}, #{item.deviceMac,jdbcType=VARCHAR}, 
        #{item.packageNumber,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.validTime,jdbcType=TIMESTAMP}, #{item.invalidTime,jdbcType=TIMESTAMP}, #{item.switched,jdbcType=BIT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 15 16:19:30 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_package (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'yuntong_order_id'.toString() == column.value">
          #{item.yuntongOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'device_mac'.toString() == column.value">
          #{item.deviceMac,jdbcType=VARCHAR}
        </if>
        <if test="'package_number'.toString() == column.value">
          #{item.packageNumber,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'valid_time'.toString() == column.value">
          #{item.validTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'invalid_time'.toString() == column.value">
          #{item.invalidTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'switched'.toString() == column.value">
          #{item.switched,jdbcType=BIT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>