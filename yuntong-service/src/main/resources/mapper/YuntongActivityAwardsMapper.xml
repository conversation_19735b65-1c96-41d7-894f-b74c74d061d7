<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.YuntongActivityAwardsMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwards">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="max_count" jdbcType="INTEGER" property="maxCount" />
    <result column="max_count_daily" jdbcType="INTEGER" property="maxCountDaily" />
    <result column="probability" jdbcType="INTEGER" property="probability" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    id, activity_id, `name`, max_count, max_count_daily, probability, image, sort
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwardsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from `yuntong_activity_awards`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from `yuntong_activity_awards`
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    delete from `yuntong_activity_awards`
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwardsExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    delete from `yuntong_activity_awards`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwards">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `yuntong_activity_awards` (id, activity_id, `name`, 
      max_count, max_count_daily, probability, 
      image, sort)
    values (#{id,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{maxCount,jdbcType=INTEGER}, #{maxCountDaily,jdbcType=INTEGER}, #{probability,jdbcType=INTEGER}, 
      #{image,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwards">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `yuntong_activity_awards`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="maxCount != null">
        max_count,
      </if>
      <if test="maxCountDaily != null">
        max_count_daily,
      </if>
      <if test="probability != null">
        probability,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="maxCount != null">
        #{maxCount,jdbcType=INTEGER},
      </if>
      <if test="maxCountDaily != null">
        #{maxCountDaily,jdbcType=INTEGER},
      </if>
      <if test="probability != null">
        #{probability,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwardsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from `yuntong_activity_awards`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `yuntong_activity_awards`
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.maxCount != null">
        max_count = #{record.maxCount,jdbcType=INTEGER},
      </if>
      <if test="record.maxCountDaily != null">
        max_count_daily = #{record.maxCountDaily,jdbcType=INTEGER},
      </if>
      <if test="record.probability != null">
        probability = #{record.probability,jdbcType=INTEGER},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `yuntong_activity_awards`
    set id = #{record.id,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      max_count = #{record.maxCount,jdbcType=INTEGER},
      max_count_daily = #{record.maxCountDaily,jdbcType=INTEGER},
      probability = #{record.probability,jdbcType=INTEGER},
      image = #{record.image,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwards">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `yuntong_activity_awards`
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="maxCount != null">
        max_count = #{maxCount,jdbcType=INTEGER},
      </if>
      <if test="maxCountDaily != null">
        max_count_daily = #{maxCountDaily,jdbcType=INTEGER},
      </if>
      <if test="probability != null">
        probability = #{probability,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongActivityAwards">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `yuntong_activity_awards`
    set activity_id = #{activityId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      max_count = #{maxCount,jdbcType=INTEGER},
      max_count_daily = #{maxCountDaily,jdbcType=INTEGER},
      probability = #{probability,jdbcType=INTEGER},
      image = #{image,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `yuntong_activity_awards`
    (id, activity_id, `name`, max_count, max_count_daily, probability, image, sort)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.maxCount,jdbcType=INTEGER}, #{item.maxCountDaily,jdbcType=INTEGER}, #{item.probability,jdbcType=INTEGER}, 
        #{item.image,jdbcType=VARCHAR}, #{item.sort,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jan 22 09:01:34 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `yuntong_activity_awards` (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'max_count'.toString() == column.value">
          #{item.maxCount,jdbcType=INTEGER}
        </if>
        <if test="'max_count_daily'.toString() == column.value">
          #{item.maxCountDaily,jdbcType=INTEGER}
        </if>
        <if test="'probability'.toString() == column.value">
          #{item.probability,jdbcType=INTEGER}
        </if>
        <if test="'image'.toString() == column.value">
          #{item.image,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>