#spring:
#  cloud:
#    nacos:
#      discovery:
#        namespace: test
#        server-addr: 10.12.57.2:8848
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    hikari:
#      idle-timeout: 180000
#      max-lifetime: 30000
#      maximum-pool-size: 8
#      minimum-idle: 4
#    password: app_!QAZxsw2
#    url: ************************************************************************************************************************************
#    username: supply
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#  redis:
#    cluster:
#      nodes: **********:6381,**********:6381,**********:6381
#    password: app_!QAZxsw2
#    pool:
#      max-active: 5
#      max-idle: 5
#      max-wait: -1
#      min-idle: 0
#    timeout: 10000
#  servlet:
#    multipart:
#      max-file-size: 10MB
#      max-request-size: 10MB
##oneNET 对象存储
#onenet-storage:
#  queryHttpInner: http://*************:9092/mallos/oss/ #访问代理地址
#  queryHttpOuter: http://**********/mallos/oss/
#  endpoint: http://s3-qos.iot-st-armtest.qiniu-solutions.com
#  bucketName: mallos-test
#  accessKey: W1aowUWsredwHbsuCeLUbI_wXI8_eNJtSWelhbxD
#  secretKey: zht2qc8vrIdCrL50PB5EdFrZSNAApdlOxQ7wZIFD
