# dockerfile配置
#FROM hub.iot.chinamobile.com/runtime/jre:8
#解决验证码的API用到了AWT的东西，openjdk基础镜像无AWT组件
#增加skywalking agent 基于镜像hub.iot.chinamobile.com/ai_team/iotmall-jdk:v1.0.1
FROM hub.iot.chinamobile.com/stqdyy/skywalking-agent:8.15

# 创建字体目录
RUN mkdir -p /usr/share/fonts/yahei
# 将字体文件复制到镜像中
COPY ./iot-service/font/msyh.ttc /usr/share/fonts/yahei

#时区设置
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
&& echo "Asia/Shanghai" > /etc/timezone
COPY ./iot-service/target/iot-service-1.0.0-SNAPSHOT.jar /opt/java/iot-service.jar
WORKDIR /opt/java/
ENTRYPOINT ["java","-XX:+UnlockExperimentalVMOptions", "-XX:+UseCGroupMemoryLimitForHeap","-Dfile.encoding=utf-8","-jar","iot-service.jar"]
EXPOSE 8416
