package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.vo.ProductConfigVO;
import com.chinamobile.iot.sc.request.ProductInfoRequest;
import com.chinamobile.iot.sc.request.SkuInfoRequest;
import com.chinamobile.iot.sc.request.product.*;
import com.chinamobile.iot.sc.request.sku.SkuOfferingInfoMDTO;
import com.chinamobile.iot.sc.service.IProductService;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/8 16:57
 * @Description: 构建一个同步产品json
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class ProductTest {

    @Autowired
    private IProductService iProductService;


    @Test
    public void getProduct(){
        ProductInfoRequest productInfoRequest = new ProductInfoRequest();
        ManagerInfoDTO managerInfoDTO = new ManagerInfoDTO();
        managerInfoDTO.setOperId("杨思陈");
        productInfoRequest.setManagerInfo(managerInfoDTO);
        SpuOfferingInfoDTO spuOfferingInfoDTO=new SpuOfferingInfoDTO();
        spuOfferingInfoDTO.setOfferingCode("1000009002");
        spuOfferingInfoDTO.setOfferingName("新建协议商品");
        spuOfferingInfoDTO.setOfferingStatus("0");
//        spuOfferingInfoDTO.setSaleObject("G");
        spuOfferingInfoDTO.setOperType("A");
        SkuOfferingInfoDTO skuOfferingInfoDTO=new SkuOfferingInfoDTO();
        skuOfferingInfoDTO.setOfferingCode("1000009003");
        skuOfferingInfoDTO.setOfferingName("规格1");
        skuOfferingInfoDTO.setQuantity(1L);
        skuOfferingInfoDTO.setPrice(100L);
        skuOfferingInfoDTO.setUnit("件");
        AtomOfferingInfoDTO atomOfferingInfoDTO=new AtomOfferingInfoDTO();
        atomOfferingInfoDTO.setOfferingClass("S");
        atomOfferingInfoDTO.setOfferingCode("1000009004");
        atomOfferingInfoDTO.setOfferingName("原子商品1");
        atomOfferingInfoDTO.setQuantity(2L);
        atomOfferingInfoDTO.setSettlePrice(100L);
        atomOfferingInfoDTO.setExtSoftOfferingCode("007");
        List<AtomOfferingInfoDTO> atomList=new ArrayList<>();
        atomList.add(atomOfferingInfoDTO);
        skuOfferingInfoDTO.setAtomOfferingInfo(atomList);
        List<SkuOfferingInfoDTO> skuList=new ArrayList<>();
        skuList.add(skuOfferingInfoDTO);
        spuOfferingInfoDTO.setSkuOfferingInfo(skuList);
        CategoryInfoDTO categoryInfoDTO=new CategoryInfoDTO();
        categoryInfoDTO.setOfferingClass("A01");
        spuOfferingInfoDTO.setCategoryInfo(categoryInfoDTO);
        productInfoRequest.setSpuOfferingInfo(spuOfferingInfoDTO);

        IOTRequest iotRequest=new IOTRequest();
        iotRequest.setMessageSeq("0001000009591");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("000");
        iotRequest.setLoginSystemCode("");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("1fa8cda8b94eb4cc87199082b706f6a3");
        iotRequest.setChannelId("30");
        iotRequest.setContent(JSON.toJSONString(productInfoRequest));
        System.out.println(JSON.toJSONString(iotRequest));
    }

    @Test
    public void getSkuInfo(){
        SkuInfoRequest request=new SkuInfoRequest();
        ManagerInfoDTO managerInfoDTO = new ManagerInfoDTO();
        managerInfoDTO.setOperId("杨思陈");
        request.setManagerInfo(managerInfoDTO);
        SkuOfferingInfoMDTO skuOfferingInfoMDTO=new SkuOfferingInfoMDTO();
        skuOfferingInfoMDTO.setOfferingCode("1000009003");
        skuOfferingInfoMDTO.setOfferingName("规格1");
        skuOfferingInfoMDTO.setQuantity(1L);
        skuOfferingInfoMDTO.setPrice(100L);
        skuOfferingInfoMDTO.setUnit("件");
        List<SkuOfferingInfoMDTO> list=new ArrayList<>();
        list.add(skuOfferingInfoMDTO);
        request.setSkuOfferingInfo(list);
        IOTRequest iotRequest=new IOTRequest();
        iotRequest.setMessageSeq("0001000009591");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("000");
        iotRequest.setLoginSystemCode("");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("1fa8cda8b94eb4cc87199082b706f6a3");
        iotRequest.setChannelId("30");
        iotRequest.setContent(JSON.toJSONString(request));
        System.out.println(JSON.toJSONString(iotRequest));
    }

    @Test
    public void testDecrypt(){
        String msg = "ODk0MkJFM0M4NTY1OTU0RDJCMkM2MTI5OTlCNDIzRkQ=";
        System.out.println("原始字符串："+ msg +",解密之后："+ IOTEncodeUtils.decryptIOTMessage(msg,"3D88F1C1AAE7"));
    }

    @Test
    public void syncSkuCooperatorId(){
        iProductService.syncSkuCooperatorId();
    }

    @Test
    public void getProductConfigInfo(){
        String skuId = "1352239864914886656";
        BaseAnswer<ProductConfigVO> productConfigInfo = iProductService.getProductConfigInfo(skuId);
        System.out.println(JSON.toJSONString(productConfigInfo.getData()));
    }

}
