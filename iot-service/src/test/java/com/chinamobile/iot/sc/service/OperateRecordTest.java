package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.pojo.param.OperateRecordQueryParam;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Author: dgj
 * @Description: 日志管理测试
 */
@SpringBootTest
public class OperateRecordTest {
    @Resource
    private IOperateRecordService operateRecordService;

    @Test
    public void getModules(){
        System.out.println(JSON.toJSONString(ModuleEnum.getAllModules()));
    }

    @Test
    public void getSubModule(){
        System.out.println(JSON.toJSONString(ModuleEnum.subModuleName(1,0)));
    }

    @Test
    public void testGetRecords(){
        OperateRecordQueryParam param = new OperateRecordQueryParam();
        param.setPageSize(10);
        param.setPageNum(1);
        System.out.println(JSON.toJSONString(operateRecordService.getLogList(param)));
    }
}
