package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.IOrder2CService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class CardInfoServiceTest {

    @Resource
    private CardInfoService cardInfoService;

    @Autowired
    private IOrder2CService order2CService;

    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;

    @Test
    public void orderProductTemplateCardList(){
        BaseAnswer<List<String>> listBaseAnswer = cardInfoService.orderProductTemplateCardList("1240726790587604992",null);
        System.out.println(JSON.toJSONString(listBaseAnswer));
    }

    @Test
    public void getOrderCardType(){
        BaseAnswer<String> orderCardType = order2CService.getOrderCardType("1240726790587604992");

        System.out.println(JSON.toJSONString(orderCardType));
    }

    @Test
    public void testBatchInsert(){
        List<Order2cAtomInfo> atomInfos = new ArrayList<>();
        Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
        order2cAtomInfo.setId(BaseServiceUtils.getId());
        order2cAtomInfo.setOrderId("123");
        order2cAtomInfo.setOrderType("00");
        order2cAtomInfo.setSpuOfferingCode("123");
        order2cAtomInfo.setSkuOfferingCode("456");
        order2cAtomInfo.setAtomOfferingCode("789");
        order2cAtomInfo.setSkuQuantity(1L);
        order2cAtomInfo.setAtomQuantity(2L);
        order2cAtomInfo.setOrderStatus(1);
        order2cAtomInfo.setCooperatorId("1");
        order2cAtomInfo.setCreateTime("20240514000000");

        Order2cAtomInfo order2cAtomInfo2 = new Order2cAtomInfo();
        order2cAtomInfo2.setId(BaseServiceUtils.getId());
        order2cAtomInfo2.setOrderId("123");
        order2cAtomInfo2.setOrderType("00");
        order2cAtomInfo2.setSpuOfferingCode("123");
        order2cAtomInfo2.setSkuOfferingCode("456");
        order2cAtomInfo2.setAtomOfferingCode("789");
        order2cAtomInfo2.setSkuQuantity(1L);
        order2cAtomInfo2.setAtomQuantity(2L);
        order2cAtomInfo2.setOrderStatus(1);
        order2cAtomInfo2.setCooperatorId("1");
        order2cAtomInfo2.setCreateTime("20240514000000");
        atomInfos.add(order2cAtomInfo);
        atomInfos.add(order2cAtomInfo2);
        atomOrderInfoMapper.batchInsert(atomInfos);
    }

    @Test
    public void orderProductTemplateName(){
        BaseAnswer<String> stringBaseAnswer = cardInfoService.orderProductTemplateName("1240607090771746816");
        System.out.println(stringBaseAnswer.getData());
    }


    @Test
    public void getOrderList(){
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId("907998143042379889");
        loginIfo4Redis.setRoleId("907921766251245568");
       // order2CService.getOrderList("190000001546012",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null);
    }

}
