package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.iot.sc.request.ProductNavigationInfoMigrateRequest;
import com.chinamobile.iot.sc.service.IProductNavigationDirectoryService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class ProductNavigationDirectoryServiceTest {

    @Resource
    private IProductNavigationDirectoryService productNavigationDirectoryService;

    @Test
    void listAll() {
        List<ProductNavigationDirectoryVO> list = productNavigationDirectoryService.listAll();
        System.out.println(JSON.toJSON(list));
    }

    @Test
    void migrate() {
        String content = "{\"data\":[{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"和对讲\",\"level2NavigationCode\":\"6010303201023130002\"},{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"阿带测试\",\"level2NavigationCode\":\"100000000000002356\"},{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"智能单品\",\"level2NavigationCode\":\"100000000000002300\"},{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"云视讯\",\"level2NavigationCode\":\"100000000000001960\"},{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"和商务\",\"level2NavigationCode\":\"100000000000000700\"},{\"menu\":\"1\",\"level1NavigationName\":\"语音视频\",\"level1NavigationCode\":\"6010303201023110033\",\"level2NavigationName\":\"千里眼\",\"level2NavigationCode\":\"100000000000000320\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智能出入\",\"level2NavigationCode\":\"6010303201109930004\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧酒店\",\"level2NavigationCode\":\"100000000000002364\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧楼宇\",\"level2NavigationCode\":\"100000000000002363\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧金融\",\"level2NavigationCode\":\"100000000000002362\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧安防\",\"level2NavigationCode\":\"100000000000002361\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧工地\",\"level2NavigationCode\":\"100000000000002360\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智能阿带\",\"level2NavigationCode\":\"100000000000002357\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"企业办公\",\"level2NavigationCode\":\"100000000000002350\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧园区\",\"level2NavigationCode\":\"100000000000002349\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智能健康\",\"level2NavigationCode\":\"100000000000002348\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧市政\",\"level2NavigationCode\":\"100000000000002347\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧消防\",\"level2NavigationCode\":\"100000000000000990\"},{\"menu\":\"1\",\"level1NavigationName\":\"行业应用\",\"level1NavigationCode\":\"6010303201023110034\",\"level2NavigationName\":\"智慧出行\",\"level2NavigationCode\":\"100000000000000681\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"智能健康\",\"level2NavigationCode\":\"6010303201023130005\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"学习教育\",\"level2NavigationCode\":\"100000000000002365\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"阿带游戏\",\"level2NavigationCode\":\"100000000000002358\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"热销产品\",\"level2NavigationCode\":\"100000000000002355\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"网络设备\",\"level2NavigationCode\":\"100000000000002354\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"生活电器\",\"level2NavigationCode\":\"100000000000002353\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"智能安防\",\"level2NavigationCode\":\"100000000000002352\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"智能音箱\",\"level2NavigationCode\":\"100000000000002351\"},{\"menu\":\"1\",\"level1NavigationName\":\"个人消费\",\"level1NavigationCode\":\"6010303201023110036\",\"level2NavigationName\":\"智能穿戴\",\"level2NavigationCode\":\"100000000000001140\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"装维服务\",\"level2NavigationCode\":\"6010303201108150081\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"阿带零帧\",\"level2NavigationCode\":\"100000000000002359\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"官方小可爱\",\"level2NavigationCode\":\"100000000000000900\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"工业网关\",\"level2NavigationCode\":\"100000000000000880\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"平台服务\",\"level2NavigationCode\":\"100000000000000680\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"定位服务\",\"level2NavigationCode\":\"100000000000000660\"},{\"menu\":\"1\",\"level1NavigationName\":\"基础产品\",\"level1NavigationCode\":\"6010303201108150080\",\"level2NavigationName\":\"模组芯片\",\"level2NavigationCode\":\"100000000000000380\"},{\"menu\":\"2\",\"level1NavigationName\":\"千里眼\",\"level1NavigationCode\":\"6010303202406130010\",\"level2NavigationName\":\"视联网\",\"level2NavigationCode\":\"100000000000002200\"},{\"menu\":\"2\",\"level1NavigationName\":\"千里眼\",\"level1NavigationCode\":\"6010303202406130010\",\"level2NavigationName\":\"千里眼修改\",\"level2NavigationCode\":\"100000000000002141\"},{\"menu\":\"2\",\"level1NavigationName\":\"云视讯\",\"level1NavigationCode\":\"6010303202406130020\",\"level2NavigationName\":\"视联网云视讯二级\",\"level2NavigationCode\":\"100000000000002343\"},{\"menu\":\"2\",\"level1NavigationName\":\"和对讲\",\"level1NavigationCode\":\"6010303202406130030\",\"level2NavigationName\":\"和对讲卡X\",\"level2NavigationCode\":\"100000000000002283\"},{\"menu\":\"2\",\"level1NavigationName\":\"和对讲\",\"level1NavigationCode\":\"6010303202406130030\",\"level2NavigationName\":\"顶顶顶\",\"level2NavigationCode\":\"100000000000002181\"},{\"menu\":\"2\",\"level1NavigationName\":\"基础服务\",\"level1NavigationCode\":\"6010303202406130040\",\"level2NavigationName\":\"视联网基础二级\",\"level2NavigationCode\":\"100000000000002344\"}]}";
        ProductNavigationInfoMigrateRequest request = JSON.parseObject(content, ProductNavigationInfoMigrateRequest.class);
        productNavigationDirectoryService.migrateProductNavigation(request);
    }

    @Test
    void sync() {
        String content = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"menu\\\":\\\"1\\\",\\\"level1Navigation\\\":[{\\\"level1NavigationCode\\\":\\\"6010303201023110034\\\",\\\"level1NavigationName\\\":\\\"行业应用\\\",\\\"level2Navigation\\\":[{\\\"level2NavigationCode\\\":\\\"6010303201109930004\\\",\\\"level2NavigationName\\\":\\\"智能出入\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000681\\\",\\\"level2NavigationName\\\":\\\"智慧出行\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000990\\\",\\\"level2NavigationName\\\":\\\"智慧消防\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002347\\\",\\\"level2NavigationName\\\":\\\"智慧市政\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002348\\\",\\\"level2NavigationName\\\":\\\"智能健康\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002349\\\",\\\"level2NavigationName\\\":\\\"智慧园区\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002350\\\",\\\"level2NavigationName\\\":\\\"企业办公\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002357\\\",\\\"level2NavigationName\\\":\\\"智能阿带\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002360\\\",\\\"level2NavigationName\\\":\\\"智慧工地\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002361\\\",\\\"level2NavigationName\\\":\\\"智慧安防\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002362\\\",\\\"level2NavigationName\\\":\\\"智慧金融\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002363\\\",\\\"level2NavigationName\\\":\\\"智慧楼宇\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002364\\\",\\\"level2NavigationName\\\":\\\"智慧酒店\\\"}]},{\\\"level1NavigationCode\\\":\\\"6010303201023110033\\\",\\\"level1NavigationName\\\":\\\"语音视频\\\",\\\"level2Navigation\\\":[{\\\"level2NavigationCode\\\":\\\"6010303201023130002\\\",\\\"level2NavigationName\\\":\\\"和对讲\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000320\\\",\\\"level2NavigationName\\\":\\\"千里眼\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000700\\\",\\\"level2NavigationName\\\":\\\"和商务\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000001960\\\",\\\"level2NavigationName\\\":\\\"云视讯\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002300\\\",\\\"level2NavigationName\\\":\\\"智能单品\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002356\\\",\\\"level2NavigationName\\\":\\\"阿带测试\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002366\\\",\\\"level2NavigationName\\\":\\\"二级目录新增\\\"}]},{\\\"level1NavigationCode\\\":\\\"6010303201023110036\\\",\\\"level1NavigationName\\\":\\\"个人消费\\\",\\\"level2Navigation\\\":[{\\\"level2NavigationCode\\\":\\\"6010303201023130005\\\",\\\"level2NavigationName\\\":\\\"智能健康\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000001140\\\",\\\"level2NavigationName\\\":\\\"智能穿戴\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002351\\\",\\\"level2NavigationName\\\":\\\"智能音箱\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002352\\\",\\\"level2NavigationName\\\":\\\"智能安防\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002353\\\",\\\"level2NavigationName\\\":\\\"生活电器\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002354\\\",\\\"level2NavigationName\\\":\\\"网络设备\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002355\\\",\\\"level2NavigationName\\\":\\\"热销产品\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002358\\\",\\\"level2NavigationName\\\":\\\"阿带游戏\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002365\\\",\\\"level2NavigationName\\\":\\\"学习教育\\\"}]},{\\\"level1NavigationCode\\\":\\\"6010303201108150080\\\",\\\"level1NavigationName\\\":\\\"基础产品\\\",\\\"level2Navigation\\\":[{\\\"level2NavigationCode\\\":\\\"6010303201108150081\\\",\\\"level2NavigationName\\\":\\\"装维服务\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000380\\\",\\\"level2NavigationName\\\":\\\"模组芯片\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000660\\\",\\\"level2NavigationName\\\":\\\"定位服务\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000680\\\",\\\"level2NavigationName\\\":\\\"平台服务\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000880\\\",\\\"level2NavigationName\\\":\\\"工业网关\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000000900\\\",\\\"level2NavigationName\\\":\\\"官方小可爱\\\"},{\\\"level2NavigationCode\\\":\\\"100000000000002359\\\",\\\"level2NavigationName\\\":\\\"阿带零帧\\\"}]}]}\",\"messageSeq\":\"100010127451\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"01443a1ad25b6a8b6fdceff647ce578d\"}";
        IOTRequest request = JSON.parseObject(content, IOTRequest.class);
        productNavigationDirectoryService.SyncNavigationInfo(request);
    }
}
