package com.chinamobile.iot.sc;

import com.chinamobile.iot.sc.config.GrowingIOConfig;
import com.chinamobile.iot.sc.dao.UserMiniProgramMapper;
import com.chinamobile.iot.sc.pojo.entity.UserMiniProgram;
import com.chinamobile.iot.sc.service.impl.GioBurialPointServiceImpl;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import io.growing.sdk.java.dto.GioCdpEventMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@SpringBootTest
public class GioServiceTest {
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Autowired
    private GioBurialPointServiceImpl gioBurialPointService;
    @Autowired
    private GrowingIOConfig growingIOConfig;
    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Test
    void testSendH5Userregistration() {
        // 创建测试用的UserMiniProgram对象
       UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey("1355147669657477120");
        Date date = new Date();
        GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
//                        .eventTime(userMiniProgram.getCreateTime()==null?DateTimeUtil.toTimestamp(new Date()): DateTimeUtil.toTimestamp(userMiniProgram.getCreateTime())) // 默认为系统当前时间 (选填)
                .eventTime(DateTimeUtil.toTimestampMs(date))
                .eventKey("H5_userregistration") // 埋点事件标识 (必填)
                .loginUserId(userMiniProgram.getUserId())
                .build();

        growingIOConfig.getProject().send(eventMessage);
        // 调用被测试的方法
//        gioBurialPointService.sendH5Userregistration(userMiniProgram);
        
        // 这里可以添加断言来验证预期行为
        // 由于方法是异步执行的，可能需要等待一段时间或者使用Mockito来验证交互
    }
    @Test
    void decryptIotMsg() {
        String encryptedMsg = "Q+y2OA0IlGjHBRU8XZ5qloRGnoSjQlBdiv4WVmfit74=";
        IOTEncodeUtils.decryptSM4(encryptedMsg, iotSm4Key, iotSm4Iv);
        System.out.println(IOTEncodeUtils.decryptSM4(encryptedMsg, iotSm4Key, iotSm4Iv));
    }
}