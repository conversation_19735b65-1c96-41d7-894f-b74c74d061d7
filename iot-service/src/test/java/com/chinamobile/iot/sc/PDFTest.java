package com.chinamobile.iot.sc;


import com.chinamobile.iot.sc.util.PdfUtils;
import java.util.ArrayList;
import java.util.List;


/**
 * created by l<PERSON><PERSON><PERSON> on 2024/9/3 16:16
 * https://www.cnblogs.com/fonks/p/15090635.html
 * https://www.cnblogs.com/zhangzhixi/p/18010726
 * https://blog.csdn.net/weixin_42596407/article/details/120035922
 * 根据模板生成PDF： https://blog.csdn.net/u011628753/article/details/131377253
 */
public class PDFTest {

//    public static void main(String args[]) throws Exception {
//        // 1、Creating a PdfWriter
//        String dest = "sample.pdf";
//        PdfWriter writer = new PdfWriter(dest);
//        // 2、Creating a PdfDocument
//        PdfDocument pdfDoc = new PdfDocument(writer);
//
//        // 4、Creating a Document
//        Document document = new Document(pdfDoc);
//        Paragraph hello = new Paragraph("hello");
//        hello.setFixedPosition(40,5,5);
//        document.add(hello);
//        document.add(new Paragraph("mike"));
//
//
//
//        document.add(new AreaBreak());
//        document.add(new Paragraph("hi"));
//        document.add(new AreaBreak());
//        document.add(new Paragraph("haha"));
//
//
//
//        // 5、Closing the document
//        document.close();
//        System.out.println("PDF Created");
//    }

    /**
     * https://blog.csdn.net/weixin_42553047/article/details/137602538 （第三方jar包）
     * https://www.cnblogs.com/Yesi/p/12036332.html (spire方案，可用)
     * @param args
     */
    public static void main(String[] args) throws Exception {
        PdfUtils.excel2Pdf("C:\\Users\\<USER>\\Desktop\\locationBeIdFromDoc.xlsx","测试pdf.pdf");
        System.out.println("完成");
        //
/*        String excelFile = "C:\\Users\\<USER>\\Desktop\\0911产品割接\\改表头后文件.xlsx";
        String pdfFile = "excel转pdf导出测试.pdf";
        PdfUtils.excel2Pdf(excelFile,pdfFile);*/

        //pdf合并
        /*List<String> list = new ArrayList<>();
        list.add("excel转pdf导出测试.pdf");
        list.add("excel转pdf导出测试2.pdf");
        String[] pdfNeedMergeList = list.toArray(new String[list.size()]);
        PdfUtils.mergePdf(pdfNeedMergeList,"合并的pdf.pdf");*/

        //文件压缩为zip
/*
        List<String> fileNameList = new ArrayList<>();
        fileNameList.add("excel转pdf导出测试.pdf");
        fileNameList.add("excel转pdf导出测试2.pdf");
        String zipFileName = "压缩的结果.zip";
        FileUtils.fileList2Zip(fileNameList,zipFileName);
*/

    }

/*    public static void main(String[] args) {
        File file = new File("从模板导出的pdf文件.pdf");
        file.delete();
    }*/

    /*public static void main(String[] args) {
        String templateFile1 = "pdf/word改造的pdf模板.pdf";//模板
        String outputFileName1 = "从模板导出的pdf文件.pdf";//生成文件

        String templateFile2 = "pdf/word改造的pdf模板2_wrapper.pdf";//模板
        String outputFileName2 = "从模板导出的pdf文件2.pdf";//生成文件

        BaseFont bf = null;
        try {
            bf = BaseFont.createFont("font/SIMYOU.TTF", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 填充表单的数据(文本)
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("orderId", "123");
        data.put("skuName", "sku1");
        data.put("atomName1", "atom1");
        data.put("atomName2", "atom2");
        data.put("skuQuantity", "100");
        data.put("remark", "这是备注");
        data.put("date", "2020-01-01");

        Map<String, Object> data2 = new HashMap<String, Object>();
        data2.put("orderId", "456");
        data2.put("skuName", "sku1");
        data2.put("atomName", "atom");
        data2.put("skuQuantity", "200");
        data2.put("remark", "这是备注");
        data2.put("date", "2099-01-01");

        fillData(templateFile1, outputFileName1, bf, data);
        fillData(templateFile2, outputFileName2, bf, data2);


        //合并文件
        List<String> fileNames = new ArrayList<>();
        fileNames.add(outputFileName1);
        fileNames.add(outputFileName2);

        String mergedFileName = "合并的文件.pdf";
        File mergedFile = new File(mergedFileName);
        mergePdf(fileNames,mergedFile);

        //删除生成的单个pdf
        if(CollectionUtils.isNotEmpty(fileNames)){
            for (String fileName : fileNames) {
                File file = new File(fileName);
                if(file.exists()){
                    file.delete();
                }
            }
        }

    }*/


/*
    需要加入此依赖：
            <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
    才能使用下面的两个方法

    //根据PDF模板填充数据
    private static void fillData(String templateFileName, String outputFileNameName, BaseFont font, Map<String, Object> data) {
        OutputStream os = null;
        PdfStamper ps = null;
        PdfReader reader = null;
        try {
            File file = new File(outputFileNameName);
            // 1 获取文件的输出流
            os = new FileOutputStream(file);
            // 2 读取pdf模板
            reader = new PdfReader(templateFileName);
            // 3 根据表单生成一个新的pdf
            ps = new PdfStamper(reader, os);
            // 4 获取pdf表单
            AcroFields form = ps.getAcroFields();
            // 5 给表单添加中文字体
            form.addSubstitutionFont(font);

            // 6遍历data赋值到form
            for (String key : data.keySet()) {
                form.setField(key, data.get(key).toString());
            }
            ps.setFormFlattening(true);
            System.out.println("生成pdf成功");

        } catch (Exception e) {
            System.out.println("生成pdf失败");
            e.printStackTrace();
        } finally {
            try {
                ps.close();
                reader.close();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    //根据PDF模板填充数据,合并pdf
    private static void mergePdf(List<String> fileNameList, File mergedFile){

        List<byte[]> fileList = new ArrayList<>();
        for (String fileNmae : fileNameList) {
            ByteArrayOutputStream bos = null;
            FileInputStream fis = null;
            try {
                File file = new File(fileNmae);
                fis = new FileInputStream(file);
                bos = new ByteArrayOutputStream();
                int byteContent;
                while ((byteContent = fis.read()) != -1) {
                    bos.write(byteContent);
                }
                byte[] bytes = bos.toByteArray();
                fileList.add(bytes);
            } catch (Exception e) {
                System.out.println("读取待合并文件出错");
            } finally {
                if(bos != null){
                    try {
                        bos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if(fis != null){
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfCopy copy = null;
        try {
            copy = new PdfCopy(document, bos);
            document.open();
            for (byte[] bs : fileList) {
                PdfReader reader = new PdfReader(bs);
                int pageTotal = reader.getNumberOfPages();
                System.out.println("pdf的页码数是 ==>"+pageTotal );
                for (int pageNo = 1; pageNo <= pageTotal; pageNo++) {
                    document.newPage();
                    PdfImportedPage page = copy.getImportedPage(reader, pageNo);
                    copy.addPage(page);
                }
                reader.close();
            }
            document.close();

            FileOutputStream fileOutputStream = new FileOutputStream(mergedFile);
            bos.writeTo(fileOutputStream);
            System.out.println("合并完毕");
        } catch (Exception e) {
            System.out.println("合并过程出错");
        } finally {
            try {
                bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if(copy != null){
                copy.close();
            }
        }
    }*/

}
