<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NavigationInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NavigationInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="level1_navigation_code" jdbcType="VARCHAR" property="level1NavigationCode" />
    <result column="level2_navigation_code" jdbcType="VARCHAR" property="level2NavigationCode" />
    <result column="level3_navigation_code" jdbcType="VARCHAR" property="level3NavigationCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_offering_code, level1_navigation_code, level2_navigation_code, level3_navigation_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from navigation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from navigation_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from navigation_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from navigation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into navigation_info (id, spu_offering_code, level1_navigation_code, 
      level2_navigation_code, level3_navigation_code
      )
    values (#{id,jdbcType=VARCHAR}, #{spuOfferingCode,jdbcType=VARCHAR}, #{level1NavigationCode,jdbcType=VARCHAR}, 
      #{level2NavigationCode,jdbcType=VARCHAR}, #{level3NavigationCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into navigation_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="level1NavigationCode != null">
        level1_navigation_code,
      </if>
      <if test="level2NavigationCode != null">
        level2_navigation_code,
      </if>
      <if test="level3NavigationCode != null">
        level3_navigation_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="level1NavigationCode != null">
        #{level1NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="level2NavigationCode != null">
        #{level2NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="level3NavigationCode != null">
        #{level3NavigationCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from navigation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    update navigation_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.level1NavigationCode != null">
        level1_navigation_code = #{record.level1NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.level2NavigationCode != null">
        level2_navigation_code = #{record.level2NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.level3NavigationCode != null">
        level3_navigation_code = #{record.level3NavigationCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    update navigation_info
    set id = #{record.id,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      level1_navigation_code = #{record.level1NavigationCode,jdbcType=VARCHAR},
      level2_navigation_code = #{record.level2NavigationCode,jdbcType=VARCHAR},
      level3_navigation_code = #{record.level3NavigationCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    update navigation_info
    <set>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="level1NavigationCode != null">
        level1_navigation_code = #{level1NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="level2NavigationCode != null">
        level2_navigation_code = #{level2NavigationCode,jdbcType=VARCHAR},
      </if>
      <if test="level3NavigationCode != null">
        level3_navigation_code = #{level3NavigationCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NavigationInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    update navigation_info
    set spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      level1_navigation_code = #{level1NavigationCode,jdbcType=VARCHAR},
      level2_navigation_code = #{level2NavigationCode,jdbcType=VARCHAR},
      level3_navigation_code = #{level3NavigationCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into navigation_info
    (id, spu_offering_code, level1_navigation_code, level2_navigation_code, level3_navigation_code
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.level1NavigationCode,jdbcType=VARCHAR}, 
        #{item.level2NavigationCode,jdbcType=VARCHAR}, #{item.level3NavigationCode,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 10:54:17 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into navigation_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'level1_navigation_code'.toString() == column.value">
          #{item.level1NavigationCode,jdbcType=VARCHAR}
        </if>
        <if test="'level2_navigation_code'.toString() == column.value">
          #{item.level2NavigationCode,jdbcType=VARCHAR}
        </if>
        <if test="'level3_navigation_code'.toString() == column.value">
          #{item.level3NavigationCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>