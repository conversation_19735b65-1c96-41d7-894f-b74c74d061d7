<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CarSecurityOrderMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.CarSecurityOrder">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="subResult" jdbcType="INTEGER" property="subresult" />
    <result column="sub_time" jdbcType="VARCHAR" property="subTime" />
    <result column="unsub_time" jdbcType="VARCHAR" property="unsubTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, phone, product_code, subResult, sub_time, unsub_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_security_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_security_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from car_security_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrderExample">
    delete from car_security_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrder">
    insert into car_security_order (id, order_id, phone, 
      product_code, subResult, sub_time, 
      unsub_time, create_time)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{productCode,jdbcType=VARCHAR}, #{subresult,jdbcType=INTEGER}, #{subTime,jdbcType=VARCHAR}, 
      #{unsubTime,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrder">
    insert into car_security_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="subresult != null">
        subResult,
      </if>
      <if test="subTime != null">
        sub_time,
      </if>
      <if test="unsubTime != null">
        unsub_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="subresult != null">
        #{subresult,jdbcType=INTEGER},
      </if>
      <if test="subTime != null">
        #{subTime,jdbcType=VARCHAR},
      </if>
      <if test="unsubTime != null">
        #{unsubTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrderExample" resultType="java.lang.Long">
    select count(*) from car_security_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_security_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.subresult != null">
        subResult = #{record.subresult,jdbcType=INTEGER},
      </if>
      <if test="record.subTime != null">
        sub_time = #{record.subTime,jdbcType=VARCHAR},
      </if>
      <if test="record.unsubTime != null">
        unsub_time = #{record.unsubTime,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_security_order
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      subResult = #{record.subresult,jdbcType=INTEGER},
      sub_time = #{record.subTime,jdbcType=VARCHAR},
      unsub_time = #{record.unsubTime,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrder">
    update car_security_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="subresult != null">
        subResult = #{subresult,jdbcType=INTEGER},
      </if>
      <if test="subTime != null">
        sub_time = #{subTime,jdbcType=VARCHAR},
      </if>
      <if test="unsubTime != null">
        unsub_time = #{unsubTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.CarSecurityOrder">
    update car_security_order
    set order_id = #{orderId,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      subResult = #{subresult,jdbcType=INTEGER},
      sub_time = #{subTime,jdbcType=VARCHAR},
      unsub_time = #{unsubTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_security_order
    (id, order_id, phone, product_code, subResult, sub_time, unsub_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.productCode,jdbcType=VARCHAR}, #{item.subresult,jdbcType=INTEGER}, #{item.subTime,jdbcType=VARCHAR}, 
        #{item.unsubTime,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into car_security_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'subResult'.toString() == column.value">
          #{item.subresult,jdbcType=INTEGER}
        </if>
        <if test="'sub_time'.toString() == column.value">
          #{item.subTime,jdbcType=VARCHAR}
        </if>
        <if test="'unsub_time'.toString() == column.value">
          #{item.unsubTime,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>