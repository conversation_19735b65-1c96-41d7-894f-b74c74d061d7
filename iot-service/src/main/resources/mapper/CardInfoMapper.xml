<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.CardInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="req_file_name" jdbcType="VARCHAR" property="reqFileName" />
    <result column="open_card_time" jdbcType="VARCHAR" property="openCardTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, template_name, be_id, region_id, cust_code, cust_name, project_id, 
    req_file_name, open_card_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.CardInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from card_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from card_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.CardInfoExample">
    delete from card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.CardInfo">
    insert into card_info (id, template_id, template_name, 
      be_id, region_id, cust_code, 
      cust_name, project_id, req_file_name, 
      open_card_time, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, 
      #{beId,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, 
      #{custName,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{reqFileName,jdbcType=VARCHAR}, 
      #{openCardTime,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.CardInfo">
    insert into card_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="reqFileName != null">
        req_file_name,
      </if>
      <if test="openCardTime != null">
        open_card_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="reqFileName != null">
        #{reqFileName,jdbcType=VARCHAR},
      </if>
      <if test="openCardTime != null">
        #{openCardTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.CardInfoExample" resultType="java.lang.Long">
    select count(*) from card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update card_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.reqFileName != null">
        req_file_name = #{record.reqFileName,jdbcType=VARCHAR},
      </if>
      <if test="record.openCardTime != null">
        open_card_time = #{record.openCardTime,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update card_info
    set id = #{record.id,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      req_file_name = #{record.reqFileName,jdbcType=VARCHAR},
      open_card_time = #{record.openCardTime,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.CardInfo">
    update card_info
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="reqFileName != null">
        req_file_name = #{reqFileName,jdbcType=VARCHAR},
      </if>
      <if test="openCardTime != null">
        open_card_time = #{openCardTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.CardInfo">
    update card_info
    set template_id = #{templateId,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      req_file_name = #{reqFileName,jdbcType=VARCHAR},
      open_card_time = #{openCardTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into card_info
    (id, template_id, template_name, be_id, region_id, cust_code, cust_name, project_id, 
      req_file_name, open_card_time, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR}, #{item.templateName,jdbcType=VARCHAR}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, 
        #{item.custName,jdbcType=VARCHAR}, #{item.projectId,jdbcType=VARCHAR}, #{item.reqFileName,jdbcType=VARCHAR}, 
        #{item.openCardTime,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into card_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'template_name'.toString() == column.value">
          #{item.templateName,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'req_file_name'.toString() == column.value">
          #{item.reqFileName,jdbcType=VARCHAR}
        </if>
        <if test="'open_card_time'.toString() == column.value">
          #{item.openCardTime,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>