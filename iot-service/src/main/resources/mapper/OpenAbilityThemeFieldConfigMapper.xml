<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OpenAbilityThemeFieldConfigMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="field_id" jdbcType="VARCHAR" property="fieldId" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="rule_value_obj" jdbcType="VARCHAR" property="ruleValueObj" />
    <result column="rule_range_list" jdbcType="VARCHAR" property="ruleRangeList" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, app_id, organization_id, rule_id, field_id, rule_type, rule_value_obj, rule_range_list, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from open_ability_theme_field_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from open_ability_theme_field_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_field_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfigExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_field_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_config (id, app_id, organization_id, 
      rule_id, field_id, rule_type, 
      rule_value_obj, rule_range_list, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, 
      #{ruleId,jdbcType=VARCHAR}, #{fieldId,jdbcType=VARCHAR}, #{ruleType,jdbcType=VARCHAR}, 
      #{ruleValueObj,jdbcType=VARCHAR}, #{ruleRangeList,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="fieldId != null">
        field_id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="ruleValueObj != null">
        rule_value_obj,
      </if>
      <if test="ruleRangeList != null">
        rule_range_list,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="fieldId != null">
        #{fieldId,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleValueObj != null">
        #{ruleValueObj,jdbcType=VARCHAR},
      </if>
      <if test="ruleRangeList != null">
        #{ruleRangeList,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from open_ability_theme_field_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleId != null">
        rule_id = #{record.ruleId,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldId != null">
        field_id = #{record.fieldId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleValueObj != null">
        rule_value_obj = #{record.ruleValueObj,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleRangeList != null">
        rule_range_list = #{record.ruleRangeList,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_config
    set id = #{record.id,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      organization_id = #{record.organizationId,jdbcType=VARCHAR},
      rule_id = #{record.ruleId,jdbcType=VARCHAR},
      field_id = #{record.fieldId,jdbcType=VARCHAR},
      rule_type = #{record.ruleType,jdbcType=VARCHAR},
      rule_value_obj = #{record.ruleValueObj,jdbcType=VARCHAR},
      rule_range_list = #{record.ruleRangeList,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_config
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="fieldId != null">
        field_id = #{fieldId,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleValueObj != null">
        rule_value_obj = #{ruleValueObj,jdbcType=VARCHAR},
      </if>
      <if test="ruleRangeList != null">
        rule_range_list = #{ruleRangeList,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_config
    set app_id = #{appId,jdbcType=VARCHAR},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      rule_id = #{ruleId,jdbcType=VARCHAR},
      field_id = #{fieldId,jdbcType=VARCHAR},
      rule_type = #{ruleType,jdbcType=VARCHAR},
      rule_value_obj = #{ruleValueObj,jdbcType=VARCHAR},
      rule_range_list = #{ruleRangeList,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_config
    (id, app_id, organization_id, rule_id, field_id, rule_type, rule_value_obj, rule_range_list, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.appId,jdbcType=VARCHAR}, #{item.organizationId,jdbcType=VARCHAR}, 
        #{item.ruleId,jdbcType=VARCHAR}, #{item.fieldId,jdbcType=VARCHAR}, #{item.ruleType,jdbcType=VARCHAR}, 
        #{item.ruleValueObj,jdbcType=VARCHAR}, #{item.ruleRangeList,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 16:08:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'app_id'.toString() == column.value">
          #{item.appId,jdbcType=VARCHAR}
        </if>
        <if test="'organization_id'.toString() == column.value">
          #{item.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="'rule_id'.toString() == column.value">
          #{item.ruleId,jdbcType=VARCHAR}
        </if>
        <if test="'field_id'.toString() == column.value">
          #{item.fieldId,jdbcType=VARCHAR}
        </if>
        <if test="'rule_type'.toString() == column.value">
          #{item.ruleType,jdbcType=VARCHAR}
        </if>
        <if test="'rule_value_obj'.toString() == column.value">
          #{item.ruleValueObj,jdbcType=VARCHAR}
        </if>
        <if test="'rule_range_list'.toString() == column.value">
          #{item.ruleRangeList,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>