<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AnnouncementMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="popup" jdbcType="BIT" property="popup" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, title, status, creator_id, popup, end_time, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.chinamobile.iot.sc.pojo.AnnouncementExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.AnnouncementExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from announcement
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from announcement
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.AnnouncementExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into announcement (id, title, status, 
      creator_id, popup, end_time, 
      create_time, update_time, content
      )
    values (#{id,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{creatorId,jdbcType=VARCHAR}, #{popup,jdbcType=BIT}, #{endTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into announcement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="popup != null">
        popup,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="popup != null">
        #{popup,jdbcType=BIT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.AnnouncementExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.popup != null">
        popup = #{record.popup,jdbcType=BIT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    set id = #{record.id,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=VARCHAR},
      popup = #{record.popup,jdbcType=BIT},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    set id = #{record.id,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=VARCHAR},
      popup = #{record.popup,jdbcType=BIT},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="popup != null">
        popup = #{popup,jdbcType=BIT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    set title = #{title,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      popup = #{popup,jdbcType=BIT},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Announcement">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update announcement
    set title = #{title,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      popup = #{popup,jdbcType=BIT},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into announcement
    (id, title, status, creator_id, popup, end_time, create_time, update_time, content
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.creatorId,jdbcType=VARCHAR}, #{item.popup,jdbcType=BIT}, #{item.endTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.content,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 15 16:19:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into announcement (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'title'.toString() == column.value">
          #{item.title,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'creator_id'.toString() == column.value">
          #{item.creatorId,jdbcType=VARCHAR}
        </if>
        <if test="'popup'.toString() == column.value">
          #{item.popup,jdbcType=BIT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>