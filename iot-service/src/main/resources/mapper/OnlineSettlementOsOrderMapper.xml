<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OnlineSettlementOsOrderMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="sku_quantity" jdbcType="BIGINT" property="skuQuantity" />
    <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass" />
    <result column="total_price" jdbcType="BIGINT" property="totalPrice" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime" />
    <result column="order_success_time" jdbcType="TIMESTAMP" property="orderSuccessTime" />
    <result column="purchase_status" jdbcType="INTEGER" property="purchaseStatus" />
    <result column="online_purchase_status" jdbcType="INTEGER" property="onlinePurchaseStatus" />
    <result column="online_settlement_purchase_order_id" jdbcType="VARCHAR" property="onlineSettlementPurchaseOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_type, order_id, spu_offering_code, sku_offering_code, sku_offering_name, 
    sku_quantity, spu_offering_class, total_price, be_id, location, order_create_time, 
    order_success_time, purchase_status, online_purchase_status, online_settlement_purchase_order_id, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from online_settlement_os_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from online_settlement_os_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from online_settlement_os_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample">
    delete from online_settlement_os_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder">
    insert into online_settlement_os_order (id, order_type, order_id, 
      spu_offering_code, sku_offering_code, sku_offering_name, 
      sku_quantity, spu_offering_class, total_price, 
      be_id, location, order_create_time, 
      order_success_time, purchase_status, online_purchase_status, 
      online_settlement_purchase_order_id, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{spuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingName,jdbcType=VARCHAR}, 
      #{skuQuantity,jdbcType=BIGINT}, #{spuOfferingClass,jdbcType=VARCHAR}, #{totalPrice,jdbcType=BIGINT}, 
      #{beId,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{orderCreateTime,jdbcType=TIMESTAMP}, 
      #{orderSuccessTime,jdbcType=TIMESTAMP}, #{purchaseStatus,jdbcType=INTEGER}, #{onlinePurchaseStatus,jdbcType=INTEGER}, 
      #{onlineSettlementPurchaseOrderId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder">
    insert into online_settlement_os_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name,
      </if>
      <if test="skuQuantity != null">
        sku_quantity,
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="orderCreateTime != null">
        order_create_time,
      </if>
      <if test="orderSuccessTime != null">
        order_success_time,
      </if>
      <if test="purchaseStatus != null">
        purchase_status,
      </if>
      <if test="onlinePurchaseStatus != null">
        online_purchase_status,
      </if>
      <if test="onlineSettlementPurchaseOrderId != null">
        online_settlement_purchase_order_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuQuantity != null">
        #{skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="spuOfferingClass != null">
        #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateTime != null">
        #{orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSuccessTime != null">
        #{orderSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="onlinePurchaseStatus != null">
        #{onlinePurchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineSettlementPurchaseOrderId != null">
        #{onlineSettlementPurchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample" resultType="java.lang.Long">
    select count(*) from online_settlement_os_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update online_settlement_os_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingName != null">
        sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuQuantity != null">
        sku_quantity = #{record.skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.spuOfferingClass != null">
        spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCreateTime != null">
        order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderSuccessTime != null">
        order_success_time = #{record.orderSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseStatus != null">
        purchase_status = #{record.purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="record.onlinePurchaseStatus != null">
        online_purchase_status = #{record.onlinePurchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="record.onlineSettlementPurchaseOrderId != null">
        online_settlement_purchase_order_id = #{record.onlineSettlementPurchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update online_settlement_os_order
    set id = #{record.id,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      sku_quantity = #{record.skuQuantity,jdbcType=BIGINT},
      spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      total_price = #{record.totalPrice,jdbcType=BIGINT},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
      order_success_time = #{record.orderSuccessTime,jdbcType=TIMESTAMP},
      purchase_status = #{record.purchaseStatus,jdbcType=INTEGER},
      online_purchase_status = #{record.onlinePurchaseStatus,jdbcType=INTEGER},
      online_settlement_purchase_order_id = #{record.onlineSettlementPurchaseOrderId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder">
    update online_settlement_os_order
    <set>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuQuantity != null">
        sku_quantity = #{skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateTime != null">
        order_create_time = #{orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSuccessTime != null">
        order_success_time = #{orderSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseStatus != null">
        purchase_status = #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="onlinePurchaseStatus != null">
        online_purchase_status = #{onlinePurchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineSettlementPurchaseOrderId != null">
        online_settlement_purchase_order_id = #{onlineSettlementPurchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder">
    update online_settlement_os_order
    set order_type = #{orderType,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      sku_quantity = #{skuQuantity,jdbcType=BIGINT},
      spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=BIGINT},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      order_create_time = #{orderCreateTime,jdbcType=TIMESTAMP},
      order_success_time = #{orderSuccessTime,jdbcType=TIMESTAMP},
      purchase_status = #{purchaseStatus,jdbcType=INTEGER},
      online_purchase_status = #{onlinePurchaseStatus,jdbcType=INTEGER},
      online_settlement_purchase_order_id = #{onlineSettlementPurchaseOrderId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into online_settlement_os_order
    (id, order_type, order_id, spu_offering_code, sku_offering_code, sku_offering_name, 
      sku_quantity, spu_offering_class, total_price, be_id, location, order_create_time, 
      order_success_time, purchase_status, online_purchase_status, online_settlement_purchase_order_id, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderType,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.skuOfferingCode,jdbcType=VARCHAR}, 
        #{item.skuOfferingName,jdbcType=VARCHAR}, #{item.skuQuantity,jdbcType=BIGINT}, 
        #{item.spuOfferingClass,jdbcType=VARCHAR}, #{item.totalPrice,jdbcType=BIGINT}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, #{item.orderCreateTime,jdbcType=TIMESTAMP}, 
        #{item.orderSuccessTime,jdbcType=TIMESTAMP}, #{item.purchaseStatus,jdbcType=INTEGER}, 
        #{item.onlinePurchaseStatus,jdbcType=INTEGER}, #{item.onlineSettlementPurchaseOrderId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into online_settlement_os_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_name'.toString() == column.value">
          #{item.skuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_quantity'.toString() == column.value">
          #{item.skuQuantity,jdbcType=BIGINT}
        </if>
        <if test="'spu_offering_class'.toString() == column.value">
          #{item.spuOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'total_price'.toString() == column.value">
          #{item.totalPrice,jdbcType=BIGINT}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'order_create_time'.toString() == column.value">
          #{item.orderCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_success_time'.toString() == column.value">
          #{item.orderSuccessTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'purchase_status'.toString() == column.value">
          #{item.purchaseStatus,jdbcType=INTEGER}
        </if>
        <if test="'online_purchase_status'.toString() == column.value">
          #{item.onlinePurchaseStatus,jdbcType=INTEGER}
        </if>
        <if test="'online_settlement_purchase_order_id'.toString() == column.value">
          #{item.onlineSettlementPurchaseOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>