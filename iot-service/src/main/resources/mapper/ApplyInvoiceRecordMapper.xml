<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ApplyInvoiceRecordMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="order_seq" jdbcType="VARCHAR" property="orderSeq" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="print_date" jdbcType="VARCHAR" property="printDate" />
    <result column="frank" jdbcType="VARCHAR" property="frank" />
    <result column="p_name" jdbcType="VARCHAR" property="pName" />
    <result column="identify_num" jdbcType="VARCHAR" property="identifyNum" />
    <result column="address_info" jdbcType="VARCHAR" property="addressInfo" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_iD" jdbcType="VARCHAR" property="bankId" />
    <result column="order_price" jdbcType="BIGINT" property="orderPrice" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="finish_cooperator_id" jdbcType="VARCHAR" property="finishCooperatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="voucher_sum" jdbcType="BIGINT" property="voucherSum" />
    <result column="apply_document_number" jdbcType="VARCHAR" property="applyDocumentNumber" />
    <result column="reminder_count" jdbcType="INTEGER" property="reminderCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, atom_order_id, order_seq, order_id, be_id, cust_code, print_date, frank, p_name, 
    identify_num, address_info, phone_number, bank_name, bank_iD, order_price, status, 
    remark, cooperator_id, finish_cooperator_id, create_time, update_time, voucher_sum, 
    apply_document_number, reminder_count
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_invoice_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_invoice_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from apply_invoice_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecordExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from apply_invoice_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into apply_invoice_record (id, atom_order_id, order_seq, 
      order_id, be_id, cust_code, 
      print_date, frank, p_name, 
      identify_num, address_info, phone_number, 
      bank_name, bank_iD, order_price, 
      status, remark, cooperator_id, 
      finish_cooperator_id, create_time, update_time, 
      voucher_sum, apply_document_number, reminder_count
      )
    values (#{id,jdbcType=VARCHAR}, #{atomOrderId,jdbcType=VARCHAR}, #{orderSeq,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, 
      #{printDate,jdbcType=VARCHAR}, #{frank,jdbcType=VARCHAR}, #{pName,jdbcType=VARCHAR}, 
      #{identifyNum,jdbcType=VARCHAR}, #{addressInfo,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{bankId,jdbcType=VARCHAR}, #{orderPrice,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{cooperatorId,jdbcType=VARCHAR}, 
      #{finishCooperatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{voucherSum,jdbcType=BIGINT}, #{applyDocumentNumber,jdbcType=VARCHAR}, #{reminderCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into apply_invoice_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="orderSeq != null">
        order_seq,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="printDate != null">
        print_date,
      </if>
      <if test="frank != null">
        frank,
      </if>
      <if test="pName != null">
        p_name,
      </if>
      <if test="identifyNum != null">
        identify_num,
      </if>
      <if test="addressInfo != null">
        address_info,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankId != null">
        bank_iD,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="voucherSum != null">
        voucher_sum,
      </if>
      <if test="applyDocumentNumber != null">
        apply_document_number,
      </if>
      <if test="reminderCount != null">
        reminder_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="printDate != null">
        #{printDate,jdbcType=VARCHAR},
      </if>
      <if test="frank != null">
        #{frank,jdbcType=VARCHAR},
      </if>
      <if test="pName != null">
        #{pName,jdbcType=VARCHAR},
      </if>
      <if test="identifyNum != null">
        #{identifyNum,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null">
        #{bankId,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherSum != null">
        #{voucherSum,jdbcType=BIGINT},
      </if>
      <if test="applyDocumentNumber != null">
        #{applyDocumentNumber,jdbcType=VARCHAR},
      </if>
      <if test="reminderCount != null">
        #{reminderCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_invoice_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    update apply_invoice_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSeq != null">
        order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.printDate != null">
        print_date = #{record.printDate,jdbcType=VARCHAR},
      </if>
      <if test="record.frank != null">
        frank = #{record.frank,jdbcType=VARCHAR},
      </if>
      <if test="record.pName != null">
        p_name = #{record.pName,jdbcType=VARCHAR},
      </if>
      <if test="record.identifyNum != null">
        identify_num = #{record.identifyNum,jdbcType=VARCHAR},
      </if>
      <if test="record.addressInfo != null">
        address_info = #{record.addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankId != null">
        bank_iD = #{record.bankId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPrice != null">
        order_price = #{record.orderPrice,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.finishCooperatorId != null">
        finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherSum != null">
        voucher_sum = #{record.voucherSum,jdbcType=BIGINT},
      </if>
      <if test="record.applyDocumentNumber != null">
        apply_document_number = #{record.applyDocumentNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.reminderCount != null">
        reminder_count = #{record.reminderCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    update apply_invoice_record
    set id = #{record.id,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      print_date = #{record.printDate,jdbcType=VARCHAR},
      frank = #{record.frank,jdbcType=VARCHAR},
      p_name = #{record.pName,jdbcType=VARCHAR},
      identify_num = #{record.identifyNum,jdbcType=VARCHAR},
      address_info = #{record.addressInfo,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_iD = #{record.bankId,jdbcType=VARCHAR},
      order_price = #{record.orderPrice,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      voucher_sum = #{record.voucherSum,jdbcType=BIGINT},
      apply_document_number = #{record.applyDocumentNumber,jdbcType=VARCHAR},
      reminder_count = #{record.reminderCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    update apply_invoice_record
    <set>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="printDate != null">
        print_date = #{printDate,jdbcType=VARCHAR},
      </if>
      <if test="frank != null">
        frank = #{frank,jdbcType=VARCHAR},
      </if>
      <if test="pName != null">
        p_name = #{pName,jdbcType=VARCHAR},
      </if>
      <if test="identifyNum != null">
        identify_num = #{identifyNum,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        address_info = #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null">
        bank_iD = #{bankId,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherSum != null">
        voucher_sum = #{voucherSum,jdbcType=BIGINT},
      </if>
      <if test="applyDocumentNumber != null">
        apply_document_number = #{applyDocumentNumber,jdbcType=VARCHAR},
      </if>
      <if test="reminderCount != null">
        reminder_count = #{reminderCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ApplyInvoiceRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    update apply_invoice_record
    set atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      print_date = #{printDate,jdbcType=VARCHAR},
      frank = #{frank,jdbcType=VARCHAR},
      p_name = #{pName,jdbcType=VARCHAR},
      identify_num = #{identifyNum,jdbcType=VARCHAR},
      address_info = #{addressInfo,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_iD = #{bankId,jdbcType=VARCHAR},
      order_price = #{orderPrice,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      voucher_sum = #{voucherSum,jdbcType=BIGINT},
      apply_document_number = #{applyDocumentNumber,jdbcType=VARCHAR},
      reminder_count = #{reminderCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into apply_invoice_record
    (id, atom_order_id, order_seq, order_id, be_id, cust_code, print_date, frank, p_name, 
      identify_num, address_info, phone_number, bank_name, bank_iD, order_price, status, 
      remark, cooperator_id, finish_cooperator_id, create_time, update_time, voucher_sum, 
      apply_document_number, reminder_count)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomOrderId,jdbcType=VARCHAR}, #{item.orderSeq,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, 
        #{item.printDate,jdbcType=VARCHAR}, #{item.frank,jdbcType=VARCHAR}, #{item.pName,jdbcType=VARCHAR}, 
        #{item.identifyNum,jdbcType=VARCHAR}, #{item.addressInfo,jdbcType=VARCHAR}, #{item.phoneNumber,jdbcType=VARCHAR}, 
        #{item.bankName,jdbcType=VARCHAR}, #{item.bankId,jdbcType=VARCHAR}, #{item.orderPrice,jdbcType=BIGINT}, 
        #{item.status,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR}, #{item.cooperatorId,jdbcType=VARCHAR}, 
        #{item.finishCooperatorId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.voucherSum,jdbcType=BIGINT}, #{item.applyDocumentNumber,jdbcType=VARCHAR}, 
        #{item.reminderCount,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 10 17:46:15 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into apply_invoice_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_seq'.toString() == column.value">
          #{item.orderSeq,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'print_date'.toString() == column.value">
          #{item.printDate,jdbcType=VARCHAR}
        </if>
        <if test="'frank'.toString() == column.value">
          #{item.frank,jdbcType=VARCHAR}
        </if>
        <if test="'p_name'.toString() == column.value">
          #{item.pName,jdbcType=VARCHAR}
        </if>
        <if test="'identify_num'.toString() == column.value">
          #{item.identifyNum,jdbcType=VARCHAR}
        </if>
        <if test="'address_info'.toString() == column.value">
          #{item.addressInfo,jdbcType=VARCHAR}
        </if>
        <if test="'phone_number'.toString() == column.value">
          #{item.phoneNumber,jdbcType=VARCHAR}
        </if>
        <if test="'bank_name'.toString() == column.value">
          #{item.bankName,jdbcType=VARCHAR}
        </if>
        <if test="'bank_iD'.toString() == column.value">
          #{item.bankId,jdbcType=VARCHAR}
        </if>
        <if test="'order_price'.toString() == column.value">
          #{item.orderPrice,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'finish_cooperator_id'.toString() == column.value">
          #{item.finishCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'voucher_sum'.toString() == column.value">
          #{item.voucherSum,jdbcType=BIGINT}
        </if>
        <if test="'apply_document_number'.toString() == column.value">
          #{item.applyDocumentNumber,jdbcType=VARCHAR}
        </if>
        <if test="'reminder_count'.toString() == column.value">
          #{item.reminderCount,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>