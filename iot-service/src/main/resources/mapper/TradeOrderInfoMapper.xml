<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.TradeOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.TradeOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="trade_price" jdbcType="BIGINT" property="tradePrice" />
    <result column="max_financing_price" jdbcType="DECIMAL" property="maxFinancingPrice" />
    <result column="request_financing_price" jdbcType="BIGINT" property="requestFinancingPrice" />
    <result column="erwartetes_wareneingangsdatum" jdbcType="VARCHAR" property="erwartetesWareneingangsdatum" />
    <result column="invoice_num" jdbcType="VARCHAR" property="invoiceNum" />
    <result column="invoice_price" jdbcType="BIGINT" property="invoicePrice" />
    <result column="can_use_bank" jdbcType="VARCHAR" property="canUseBank" />
    <result column="baoli_status" jdbcType="INTEGER" property="baoliStatus" />
    <result column="advice" jdbcType="VARCHAR" property="advice" />
    <result column="pay_date" jdbcType="VARCHAR" property="payDate" />
    <result column="pay_amount" jdbcType="BIGINT" property="payAmount" />
    <result column="pay_bank" jdbcType="VARCHAR" property="payBank" />
    <result column="due_date" jdbcType="VARCHAR" property="dueDate" />
    <result column="finance_code" jdbcType="VARCHAR" property="financeCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, trade_no, contract_num, buyer_name, seller_name, cooperator_id, trade_price, 
    max_financing_price, request_financing_price, erwartetes_wareneingangsdatum, invoice_num, 
    invoice_price, can_use_bank, baoli_status, advice, pay_date, pay_amount, pay_bank, 
    due_date, finance_code, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from trade_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from trade_order_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from trade_order_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from trade_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into trade_order_info (id, trade_no, contract_num, 
      buyer_name, seller_name, cooperator_id, 
      trade_price, max_financing_price, request_financing_price, 
      erwartetes_wareneingangsdatum, invoice_num, 
      invoice_price, can_use_bank, baoli_status, 
      advice, pay_date, pay_amount, 
      pay_bank, due_date, finance_code, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, #{contractNum,jdbcType=VARCHAR}, 
      #{buyerName,jdbcType=VARCHAR}, #{sellerName,jdbcType=VARCHAR}, #{cooperatorId,jdbcType=VARCHAR}, 
      #{tradePrice,jdbcType=BIGINT}, #{maxFinancingPrice,jdbcType=DECIMAL}, #{requestFinancingPrice,jdbcType=BIGINT}, 
      #{erwartetesWareneingangsdatum,jdbcType=VARCHAR}, #{invoiceNum,jdbcType=VARCHAR}, 
      #{invoicePrice,jdbcType=BIGINT}, #{canUseBank,jdbcType=VARCHAR}, #{baoliStatus,jdbcType=INTEGER}, 
      #{advice,jdbcType=VARCHAR}, #{payDate,jdbcType=VARCHAR}, #{payAmount,jdbcType=BIGINT}, 
      #{payBank,jdbcType=VARCHAR}, #{dueDate,jdbcType=VARCHAR}, #{financeCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into trade_order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="contractNum != null">
        contract_num,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="sellerName != null">
        seller_name,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="tradePrice != null">
        trade_price,
      </if>
      <if test="maxFinancingPrice != null">
        max_financing_price,
      </if>
      <if test="requestFinancingPrice != null">
        request_financing_price,
      </if>
      <if test="erwartetesWareneingangsdatum != null">
        erwartetes_wareneingangsdatum,
      </if>
      <if test="invoiceNum != null">
        invoice_num,
      </if>
      <if test="invoicePrice != null">
        invoice_price,
      </if>
      <if test="canUseBank != null">
        can_use_bank,
      </if>
      <if test="baoliStatus != null">
        baoli_status,
      </if>
      <if test="advice != null">
        advice,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payBank != null">
        pay_bank,
      </if>
      <if test="dueDate != null">
        due_date,
      </if>
      <if test="financeCode != null">
        finance_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNum != null">
        #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="tradePrice != null">
        #{tradePrice,jdbcType=BIGINT},
      </if>
      <if test="maxFinancingPrice != null">
        #{maxFinancingPrice,jdbcType=DECIMAL},
      </if>
      <if test="requestFinancingPrice != null">
        #{requestFinancingPrice,jdbcType=BIGINT},
      </if>
      <if test="erwartetesWareneingangsdatum != null">
        #{erwartetesWareneingangsdatum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null">
        #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrice != null">
        #{invoicePrice,jdbcType=BIGINT},
      </if>
      <if test="canUseBank != null">
        #{canUseBank,jdbcType=VARCHAR},
      </if>
      <if test="baoliStatus != null">
        #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="advice != null">
        #{advice,jdbcType=VARCHAR},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="payBank != null">
        #{payBank,jdbcType=VARCHAR},
      </if>
      <if test="dueDate != null">
        #{dueDate,jdbcType=VARCHAR},
      </if>
      <if test="financeCode != null">
        #{financeCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from trade_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update trade_order_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNum != null">
        contract_num = #{record.contractNum,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerName != null">
        buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerName != null">
        seller_name = #{record.sellerName,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.tradePrice != null">
        trade_price = #{record.tradePrice,jdbcType=BIGINT},
      </if>
      <if test="record.maxFinancingPrice != null">
        max_financing_price = #{record.maxFinancingPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.requestFinancingPrice != null">
        request_financing_price = #{record.requestFinancingPrice,jdbcType=BIGINT},
      </if>
      <if test="record.erwartetesWareneingangsdatum != null">
        erwartetes_wareneingangsdatum = #{record.erwartetesWareneingangsdatum,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNum != null">
        invoice_num = #{record.invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="record.invoicePrice != null">
        invoice_price = #{record.invoicePrice,jdbcType=BIGINT},
      </if>
      <if test="record.canUseBank != null">
        can_use_bank = #{record.canUseBank,jdbcType=VARCHAR},
      </if>
      <if test="record.baoliStatus != null">
        baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="record.advice != null">
        advice = #{record.advice,jdbcType=VARCHAR},
      </if>
      <if test="record.payDate != null">
        pay_date = #{record.payDate,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=BIGINT},
      </if>
      <if test="record.payBank != null">
        pay_bank = #{record.payBank,jdbcType=VARCHAR},
      </if>
      <if test="record.dueDate != null">
        due_date = #{record.dueDate,jdbcType=VARCHAR},
      </if>
      <if test="record.financeCode != null">
        finance_code = #{record.financeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update trade_order_info
    set id = #{record.id,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      contract_num = #{record.contractNum,jdbcType=VARCHAR},
      buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      seller_name = #{record.sellerName,jdbcType=VARCHAR},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      trade_price = #{record.tradePrice,jdbcType=BIGINT},
      max_financing_price = #{record.maxFinancingPrice,jdbcType=DECIMAL},
      request_financing_price = #{record.requestFinancingPrice,jdbcType=BIGINT},
      erwartetes_wareneingangsdatum = #{record.erwartetesWareneingangsdatum,jdbcType=VARCHAR},
      invoice_num = #{record.invoiceNum,jdbcType=VARCHAR},
      invoice_price = #{record.invoicePrice,jdbcType=BIGINT},
      can_use_bank = #{record.canUseBank,jdbcType=VARCHAR},
      baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      advice = #{record.advice,jdbcType=VARCHAR},
      pay_date = #{record.payDate,jdbcType=VARCHAR},
      pay_amount = #{record.payAmount,jdbcType=BIGINT},
      pay_bank = #{record.payBank,jdbcType=VARCHAR},
      due_date = #{record.dueDate,jdbcType=VARCHAR},
      finance_code = #{record.financeCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update trade_order_info
    <set>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNum != null">
        contract_num = #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        seller_name = #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="tradePrice != null">
        trade_price = #{tradePrice,jdbcType=BIGINT},
      </if>
      <if test="maxFinancingPrice != null">
        max_financing_price = #{maxFinancingPrice,jdbcType=DECIMAL},
      </if>
      <if test="requestFinancingPrice != null">
        request_financing_price = #{requestFinancingPrice,jdbcType=BIGINT},
      </if>
      <if test="erwartetesWareneingangsdatum != null">
        erwartetes_wareneingangsdatum = #{erwartetesWareneingangsdatum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null">
        invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrice != null">
        invoice_price = #{invoicePrice,jdbcType=BIGINT},
      </if>
      <if test="canUseBank != null">
        can_use_bank = #{canUseBank,jdbcType=VARCHAR},
      </if>
      <if test="baoliStatus != null">
        baoli_status = #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="advice != null">
        advice = #{advice,jdbcType=VARCHAR},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="payBank != null">
        pay_bank = #{payBank,jdbcType=VARCHAR},
      </if>
      <if test="dueDate != null">
        due_date = #{dueDate,jdbcType=VARCHAR},
      </if>
      <if test="financeCode != null">
        finance_code = #{financeCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.TradeOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update trade_order_info
    set trade_no = #{tradeNo,jdbcType=VARCHAR},
      contract_num = #{contractNum,jdbcType=VARCHAR},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      seller_name = #{sellerName,jdbcType=VARCHAR},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      trade_price = #{tradePrice,jdbcType=BIGINT},
      max_financing_price = #{maxFinancingPrice,jdbcType=DECIMAL},
      request_financing_price = #{requestFinancingPrice,jdbcType=BIGINT},
      erwartetes_wareneingangsdatum = #{erwartetesWareneingangsdatum,jdbcType=VARCHAR},
      invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      invoice_price = #{invoicePrice,jdbcType=BIGINT},
      can_use_bank = #{canUseBank,jdbcType=VARCHAR},
      baoli_status = #{baoliStatus,jdbcType=INTEGER},
      advice = #{advice,jdbcType=VARCHAR},
      pay_date = #{payDate,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=BIGINT},
      pay_bank = #{payBank,jdbcType=VARCHAR},
      due_date = #{dueDate,jdbcType=VARCHAR},
      finance_code = #{financeCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into trade_order_info
    (id, trade_no, contract_num, buyer_name, seller_name, cooperator_id, trade_price, 
      max_financing_price, request_financing_price, erwartetes_wareneingangsdatum, invoice_num, 
      invoice_price, can_use_bank, baoli_status, advice, pay_date, pay_amount, pay_bank, 
      due_date, finance_code, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR}, #{item.contractNum,jdbcType=VARCHAR}, 
        #{item.buyerName,jdbcType=VARCHAR}, #{item.sellerName,jdbcType=VARCHAR}, #{item.cooperatorId,jdbcType=VARCHAR}, 
        #{item.tradePrice,jdbcType=BIGINT}, #{item.maxFinancingPrice,jdbcType=DECIMAL}, 
        #{item.requestFinancingPrice,jdbcType=BIGINT}, #{item.erwartetesWareneingangsdatum,jdbcType=VARCHAR}, 
        #{item.invoiceNum,jdbcType=VARCHAR}, #{item.invoicePrice,jdbcType=BIGINT}, #{item.canUseBank,jdbcType=VARCHAR}, 
        #{item.baoliStatus,jdbcType=INTEGER}, #{item.advice,jdbcType=VARCHAR}, #{item.payDate,jdbcType=VARCHAR}, 
        #{item.payAmount,jdbcType=BIGINT}, #{item.payBank,jdbcType=VARCHAR}, #{item.dueDate,jdbcType=VARCHAR}, 
        #{item.financeCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 16 17:03:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into trade_order_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'contract_num'.toString() == column.value">
          #{item.contractNum,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_name'.toString() == column.value">
          #{item.buyerName,jdbcType=VARCHAR}
        </if>
        <if test="'seller_name'.toString() == column.value">
          #{item.sellerName,jdbcType=VARCHAR}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'trade_price'.toString() == column.value">
          #{item.tradePrice,jdbcType=BIGINT}
        </if>
        <if test="'max_financing_price'.toString() == column.value">
          #{item.maxFinancingPrice,jdbcType=DECIMAL}
        </if>
        <if test="'request_financing_price'.toString() == column.value">
          #{item.requestFinancingPrice,jdbcType=BIGINT}
        </if>
        <if test="'erwartetes_wareneingangsdatum'.toString() == column.value">
          #{item.erwartetesWareneingangsdatum,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_num'.toString() == column.value">
          #{item.invoiceNum,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_price'.toString() == column.value">
          #{item.invoicePrice,jdbcType=BIGINT}
        </if>
        <if test="'can_use_bank'.toString() == column.value">
          #{item.canUseBank,jdbcType=VARCHAR}
        </if>
        <if test="'baoli_status'.toString() == column.value">
          #{item.baoliStatus,jdbcType=INTEGER}
        </if>
        <if test="'advice'.toString() == column.value">
          #{item.advice,jdbcType=VARCHAR}
        </if>
        <if test="'pay_date'.toString() == column.value">
          #{item.payDate,jdbcType=VARCHAR}
        </if>
        <if test="'pay_amount'.toString() == column.value">
          #{item.payAmount,jdbcType=BIGINT}
        </if>
        <if test="'pay_bank'.toString() == column.value">
          #{item.payBank,jdbcType=VARCHAR}
        </if>
        <if test="'due_date'.toString() == column.value">
          #{item.dueDate,jdbcType=VARCHAR}
        </if>
        <if test="'finance_code'.toString() == column.value">
          #{item.financeCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>