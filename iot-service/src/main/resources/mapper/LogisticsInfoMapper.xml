<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.LogisticsInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.LogisticsInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_atom_info_id" jdbcType="VARCHAR" property="orderAtomInfoId" />
    <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
    <result column="logistics_type" jdbcType="INTEGER" property="logisticsType" />
    <result column="logis_code" jdbcType="VARCHAR" property="logisCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="sign_receipt_name" jdbcType="VARCHAR" property="signReceiptName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="logistics_state" jdbcType="INTEGER" property="logisticsState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, order_id, order_atom_info_id, refund_order_id, logistics_type, logis_code, supplier_name, 
    sign_receipt_name, description, create_time, update_time, logistics_state
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from logistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from logistics_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from logistics_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from logistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into logistics_info (id, order_id, order_atom_info_id, 
      refund_order_id, logistics_type, logis_code, 
      supplier_name, sign_receipt_name, description, 
      create_time, update_time, logistics_state
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{orderAtomInfoId,jdbcType=VARCHAR}, 
      #{refundOrderId,jdbcType=VARCHAR}, #{logisticsType,jdbcType=INTEGER}, #{logisCode,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{signReceiptName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{logisticsState,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into logistics_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id,
      </if>
      <if test="refundOrderId != null">
        refund_order_id,
      </if>
      <if test="logisticsType != null">
        logistics_type,
      </if>
      <if test="logisCode != null">
        logis_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="signReceiptName != null">
        sign_receipt_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="logisticsState != null">
        logistics_state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="logisticsType != null">
        #{logisticsType,jdbcType=INTEGER},
      </if>
      <if test="logisCode != null">
        #{logisCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="signReceiptName != null">
        #{signReceiptName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logisticsState != null">
        #{logisticsState,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from logistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    update logistics_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAtomInfoId != null">
        order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundOrderId != null">
        refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.logisticsType != null">
        logistics_type = #{record.logisticsType,jdbcType=INTEGER},
      </if>
      <if test="record.logisCode != null">
        logis_code = #{record.logisCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.signReceiptName != null">
        sign_receipt_name = #{record.signReceiptName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.logisticsState != null">
        logistics_state = #{record.logisticsState,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    update logistics_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      logistics_type = #{record.logisticsType,jdbcType=INTEGER},
      logis_code = #{record.logisCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      sign_receipt_name = #{record.signReceiptName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      logistics_state = #{record.logisticsState,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    update logistics_info
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="logisticsType != null">
        logistics_type = #{logisticsType,jdbcType=INTEGER},
      </if>
      <if test="logisCode != null">
        logis_code = #{logisCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="signReceiptName != null">
        sign_receipt_name = #{signReceiptName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logisticsState != null">
        logistics_state = #{logisticsState,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.LogisticsInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    update logistics_info
    set order_id = #{orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      logistics_type = #{logisticsType,jdbcType=INTEGER},
      logis_code = #{logisCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      sign_receipt_name = #{signReceiptName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      logistics_state = #{logisticsState,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into logistics_info
    (id, order_id, order_atom_info_id, refund_order_id, logistics_type, logis_code, supplier_name, 
      sign_receipt_name, description, create_time, update_time, logistics_state)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.orderAtomInfoId,jdbcType=VARCHAR}, 
        #{item.refundOrderId,jdbcType=VARCHAR}, #{item.logisticsType,jdbcType=INTEGER}, 
        #{item.logisCode,jdbcType=VARCHAR}, #{item.supplierName,jdbcType=VARCHAR}, #{item.signReceiptName,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.logisticsState,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 27 17:12:00 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into logistics_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_atom_info_id'.toString() == column.value">
          #{item.orderAtomInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'refund_order_id'.toString() == column.value">
          #{item.refundOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'logistics_type'.toString() == column.value">
          #{item.logisticsType,jdbcType=INTEGER}
        </if>
        <if test="'logis_code'.toString() == column.value">
          #{item.logisCode,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_name'.toString() == column.value">
          #{item.supplierName,jdbcType=VARCHAR}
        </if>
        <if test="'sign_receipt_name'.toString() == column.value">
          #{item.signReceiptName,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'logistics_state'.toString() == column.value">
          #{item.logisticsState,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>