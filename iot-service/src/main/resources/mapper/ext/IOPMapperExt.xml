<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.IOPMapperExt">
  <select id="IOPUploadList"  resultType="com.chinamobile.iot.sc.pojo.dto.IOPUploadDTO">
      SELECT
      oc.order_id  order_id,
      oc.sku_price * oc.sku_quantity  totalPrice,
      oc.order_status  order_status,
      IF(oi.cust_mg_name is null ,agent.agent_name, oi.cust_mg_name) name,
      IF(oi.cust_mg_phone is null ,agent.agent_phone, oi.cust_mg_phone) phone,
      oc.be_id be_id,
      oc.sku_offering_code,
      oc.sku_offering_name,
      oc.sku_quantity,
      oc.atom_offering_code,
      oc.atom_offering_name,
      oc.atom_quantity,
      CASE
      when oi.order_type = '01' then DATE_FORMAT(oi.create_time, '%Y%m%d%H%i%s')
      when (oi.order_type = '00' or oi.order_type = '02' or oi.order_type = '03') then DATE_FORMAT(oi.valet_order_complete_time, '%Y%m%d%H%i%s')
      ELSE DATE_FORMAT(oi.create_time, '%Y%m%d%H%i%s')
      end
      as timeStr
      FROM
      order_2c_atom_info oc
      left JOIN order_2c_info oi	ON oi.order_id = oc.order_id
      LEFT JOIN order_2c_agent_info agent ON agent.order_id = oc.order_id
      WHERE
      ((oc.order_type = '01' <if test="type == 'z'.toString()"> and oc.order_status != 8 </if>)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
      AND oc.sku_price * oc.sku_quantity > 80000
      AND ((oi.cust_mg_phone is not null and oi.cust_mg_phone != '') or (agent.agent_phone is not null and agent.agent_phone != ''))
      <if test="type == 'z'.toString()">
          <if test="startTime != null and startTime !=''">
              and
              CASE
              when oi.order_type ='01' then oi.create_time <![CDATA[ >= ]]> #{startTime}
              when (oi.order_type = '00' or oi.order_type = '02' or oi.order_type = '03') then oi.valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
              ELSE oi.create_time <![CDATA[ >= ]]> #{startTime}
              END
          </if>
          <if test="endTime != null and endTime !=''">
              and
              CASE
              when oi.order_type ='01' then oi.create_time <![CDATA[ <= ]]> #{endTime}
              when (oi.order_type = '00' or oi.order_type = '02' or oi.order_type = '03') then oi.valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
              ELSE oi.create_time <![CDATA[ <= ]]> #{endTime}
              END
          </if>
      </if>
      <if test="type == 'a'.toString()">
          <if test="startTime != null and startTime !=''">
              and  oi.order_status_time <![CDATA[ >= ]]> #{startTime}
          </if>
          <if test="endTime != null and endTime !=''">
              and oi.order_status_time <![CDATA[ <= ]]> #{endTime}
          </if>
      </if>
  </select>
</mapper>