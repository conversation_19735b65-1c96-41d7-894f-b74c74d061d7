<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OrderBaoliMapperExt">
  
  <select id="listBaoliOrder"
          resultType="com.chinamobile.iot.sc.pojo.vo.BaoliOrderVO">
      select
          o2ai.id orderAtomInfoId,
          o2ai.order_id orderId,
          o2ai.sku_offering_name skuOfferingName,
          oi.total_price totalPrice,
          kpm.buyer_name buyerName,
          o2ai.baoli_status baoliStatus ,
          case
              when o2ai.baoli_status = 1 then '可用'
              when o2ai.baoli_status = 2 then '不可用'
          else '未知状态'
          end baoliStatusName,
          o2ai.cooperator_id cooperatorId,
          up.partner_name partnerName
      from
          order_2c_atom_info o2ai
          inner join (
          select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
          ocr.atom_order_id,ocr.order_id
          from order_2c_atom_info o2aii
          inner join order_cooperator_relation ocr on o2aii.id = ocr.atom_order_id and o2aii.order_id = ocr.order_id
          inner JOIN user_partner up on up.user_id = ocr.cooperator_id
          where 1=1
          <if test="baoliOrderParam.downUserIdList != null and baoliOrderParam.downUserIdList.size() != 0">
              and ocr.cooperator_id in
              <foreach collection="baoliOrderParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
                  #{downUserId}
              </foreach>
          </if>
          group by o2aii.id
          ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id,
          order_2c_info oi,
          atom_offering_info aoi,
          k3_product_material kpm,
          category_info ci,
          user_partner up,
          user_partner up2,
          (select distinct
              order_id,cooperator_id
          from
              order_2c_atom_info
          where
              cooperator_id != '-1'
          group by order_id
          having count(order_id) = 1) oai
      where
          o2ai.order_id = oi.order_id
      and oi.special_after_market_handle != 1
      and o2ai.order_status = 7
      and o2ai.atom_offering_code = aoi.offering_code
      and o2ai.spu_offering_code = aoi.spu_code
      and o2ai.sku_offering_code = aoi.sku_code
      and aoi.id = kpm.atom_id
      and kpm.contract_type = 2
      and kpm.seller_name = '物联网公司'
      and kpm.contract_effective = 1
      and aoi.spu_id = ci.spu_id
      and ci.offering_class in ('A04','A08','A09')
      and o2ai.cooperator_id = up.user_id
      and up.partner_name = up2.partner_name
      and up2.cj_status = 'activated'
      and o2ai.order_id = oai.order_id
      and o2ai.cooperator_id = oai.cooperator_id
    <if test="baoliOrderParam.orderId != null and baoliOrderParam.orderId != ''">
      and o2ai.order_id like '%${baoliOrderParam.orderId}%'
    </if>
    <if test="baoliOrderParam.skuOfferingName != null and baoliOrderParam.skuOfferingName != ''">
      and o2ai.sku_offering_name like '%${baoliOrderParam.skuOfferingName}%'
    </if>
    <if test="baoliOrderParam.baoliStatus != null">
      and o2ai.baoli_status = #{baoliOrderParam.baoliStatus}
    </if>
    <if test="baoliOrderParam.buyerName != null and baoliOrderParam.buyerName != ''">
      and kpm.buyer_name like '%${baoliOrderParam.buyerName}%'
    </if>
    <if test="baoliOrderParam.partnerName != null and baoliOrderParam.partnerName != ''">
      and up.partner_name like '%${baoliOrderParam.partnerName}%'
    </if>
    <if test="baoliOrderParam.userId != null and baoliOrderParam.userId != ''">
      and up2.user_id = #{baoliOrderParam.userId}
    </if>
      <!--<if test="baoliOrderParam.downUserIdList != null and baoliOrderParam.downUserIdList.size() != 0">
          and o2ai.cooperator_id in
          <foreach collection="baoliOrderParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
              #{downUserId}
          </foreach>
      </if>-->
    order by o2ai.baoli_status asc,oi.order_status_time desc
  </select>

  <select id="exportBaoliOrderCanNotUse"
          resultType="com.chinamobile.iot.sc.pojo.dto.BaoliOrderExportDTO">
          select
              o2ai.order_id orderId,
              DATE_FORMAT(oi.order_status_time,'%Y-%m-%d %H:%i:%s') orderFinishTime,
              o2ai.sku_offering_name skuOfferingName,
              o2ai.atom_offering_class atomOfferingClass,
              oi.spu_offering_class spuOfferingClass,
              o2ai.deduct_price deductPriceStr,
              case
                  when (o2ai.order_type = '00' or o2ai.order_type = '02' or o2ai.order_type = '03') then '代客下单'
                  when o2ai.order_type = '01' then '自主下单'
              else '未定义的类型'
              end orderTypeName,
              kpm.buyer_name buyerName,
              up2.name cooperatorName,
              up.partner_name partnerName
          from
              order_2c_atom_info o2ai,
              order_2c_info oi,
              atom_offering_info aoi,
              k3_product_material kpm,
              category_info ci,
              user_partner up,
              user_partner up2,
              (select distinct
                order_id,cooperator_id
              from
                order_2c_atom_info
              where
                cooperator_id != '-1'
              group by order_id
              having count(order_id) = 1) oai
          where
              o2ai.order_id = oi.order_id
          and oi.special_after_market_handle != 1
          and o2ai.order_status = 7
          and o2ai.atom_offering_code = aoi.offering_code
          and o2ai.spu_offering_code = aoi.spu_code
          and o2ai.sku_offering_code = aoi.sku_code
          and aoi.id = kpm.atom_id
          and kpm.contract_type = 2
          and kpm.seller_name = '物联网公司'
          and kpm.contract_effective = 1
          and aoi.spu_id = ci.spu_id
          and ci.offering_class in ('A04','A08','A09')
          and o2ai.baoli_status = 2
          and o2ai.cooperator_id = up.user_id
          and up.partner_name = up2.partner_name
          and up2.cj_status = 'activated'
          and o2ai.order_id = oai.order_id
          and o2ai.cooperator_id = oai.cooperator_id
        <if test="baoliOrderExportParam.beginDate != null and baoliOrderExportParam.beginDate !=  ''">
          and DATE_FORMAT(oi.order_status_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{baoliOrderExportParam.beginDate}
        </if>
        <if test="baoliOrderExportParam.endDate != null and baoliOrderExportParam.endDate !=  ''">
          and DATE_FORMAT(oi.order_status_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{baoliOrderExportParam.endDate}
        </if>
        <if test="baoliOrderExportParam.skuOfferingName != null and baoliOrderExportParam.skuOfferingName != ''">
          and o2ai.sku_offering_name like '%${baoliOrderExportParam.skuOfferingName}%'
        </if>
        <if test="baoliOrderExportParam.buyerName != null and baoliOrderExportParam.buyerName != ''">
          and kpm.buyer_name like '%${baoliOrderExportParam.buyerName}%'
        </if>
        <if test="baoliOrderExportParam.partnerName != null and baoliOrderExportParam.partnerName != ''">
          and up.partner_name like '%${baoliOrderExportParam.partnerName}%'
        </if>
        order by oi.order_status_time desc
  </select>


    <select id="getChangeUseBaoliOrder" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.dto.ChangeUseBaoliOrderDTO">
        select
            o2ai.order_id orderId,
            o2ai.sku_offering_name skuOfferingName,
            sum(kpm.material_count*kpm.material_settle_price*o2ai.atom_quantity) totalPrice,
            kpm.contract_num contractNum,
            kpm.buyer_name buyerName,
            kpm.seller_name sellerName,
            o2ai.cooperator_id cooperatorId,
            oi.update_time orderFinishTime,
            oi.special_after_market_handle specialAfterMarketHandle,
            up.partner_name partnerName
        from
            order_2c_atom_info o2ai,
            order_2c_info oi,
            atom_offering_info aoi,
            k3_product_material kpm,
            user_partner up,
            user_partner up2,
          (select distinct
				order_id,cooperator_id
			from
				order_2c_atom_info
			 where
				cooperator_id != '-1'
			group by order_id
			having count(order_id) = 1) oai
        where
            o2ai.order_id = oi.order_id
        and oi.special_after_market_handle != 1
        and o2ai.order_status = 7
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        and aoi.id = kpm.atom_id
        and kpm.contract_type = 2
        and kpm.seller_name = '物联网公司'
        and kpm.contract_effective = 1
        and o2ai.baoli_status = 2
        and o2ai.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status = 'activated'
        and o2ai.order_id = oai.order_id
	    and o2ai.cooperator_id = oai.cooperator_id
        and o2ai.order_id = #{orderId}
        group by o2ai.order_id
    </select>
    
    <select id="getInfoToImportBaoliNotUseValid" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.dto.InfoToImportBaoliNotUseValidDTO">
        select distinct
            o2ai.id orderAtomInfoId,
            oi.special_after_market_handle specialAfterMarketHandle,
            o2ai.order_status orderStatus,
            kpm.contract_type contractType,
            kpm.seller_name sellerName,
            kpm.contract_effective contractEffective,
            o2ai.baoli_status baoliStatus,
            up2.cj_status cjStatus,
            o2ai.cooperator_id cooperatorId,
            ob.id orderBaoliId
        from
            order_2c_atom_info o2ai,
            order_2c_info oi
            left join order_baoli ob on oi.order_id = ob.order_id,
            atom_offering_info aoi,
            k3_product_material kpm,
            user_partner up,
            user_partner up2
        where
            o2ai.order_id = oi.order_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        and aoi.id = kpm.atom_id
        and o2ai.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status is not null
        and up2.cj_status != ''
        and o2ai.order_id = #{orderId}
    </select>

    <select id="listOrderBaoliNotAdmin"
            resultType="com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO">
        select
        ob.id orderBaoliId,
        ob.order_id orderId,
        ob.sku_offering_name skuOfferingName,
        ob.total_price totalPrice,
        ob.contract_num contractNum,
        ob.buyer_name buyerName,
        up.partner_name partnerName,
        ob.seller_name sellerName,
        ob.baoli_status baoliStatus,
        ob.trade_no tradeNo,
        ob.return_price returnPrice,
        toi.pay_amount payAmount
        from
        order_baoli ob
        left join trade_order_info toi on ob.trade_no = toi.trade_no
        inner join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ob.trade_no
        from
        order_baoli ob,order_cooperator_relation ocr,user_partner up
        where
        ob.order_atom_info_id = ocr.atom_order_id
        and ob.order_id = ocr.order_id
        and ocr.cooperator_id = up.user_id
        <if test="orderBaoliParam.downUserIdList != null and orderBaoliParam.downUserIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="orderBaoliParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
                #{downUserId}
            </foreach>
        </if>
        group by ob.trade_no
        ) oc on oc.trade_no = ob.trade_no,
        user_partner up,
        user_partner up2
        where
        ob.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status = 'activated'
        <if test="orderBaoliParam.skuOfferingName != null and orderBaoliParam.skuOfferingName != ''">
            and ob.sku_offering_name like '%${orderBaoliParam.skuOfferingName}%'
        </if>
        <if test="orderBaoliParam.contractNum != null and orderBaoliParam.contractNum != ''">
            and ob.contract_num like '%${orderBaoliParam.contractNum}%'
        </if>
        <if test="orderBaoliParam.buyerName != null and orderBaoliParam.buyerName != ''">
            and ob.buyer_name like '%${orderBaoliParam.buyerName}%'
        </if>
        <if test="orderBaoliParam.partnerName != null and orderBaoliParam.partnerName != ''">
            and up.partner_name like '%${orderBaoliParam.partnerName}%'
        </if>
        <if test="orderBaoliParam.sellerName != null and orderBaoliParam.sellerName != ''">
            and ob.seller_name like '%${orderBaoliParam.sellerName}%'
        </if>
        <if test="orderBaoliParam.baoliStatus != null">
            and ob.baoli_status = #{orderBaoliParam.baoliStatus}
        </if>
        <if test="orderBaoliParam.userId != null and orderBaoliParam.userId != ''">
            and up2.user_id = #{orderBaoliParam.userId}
        </if>
        <!--<if test="orderBaoliParam.downUserIdList != null and orderBaoliParam.downUserIdList.size() != 0">
            and ob.cooperator_id in
            <foreach collection="orderBaoliParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
                #{downUserId}
            </foreach>
        </if>-->
        <if test="orderBaoliParam.tabStatus != null">
            <if test="orderBaoliParam.tabStatus == 2">
                and ob.baoli_status in (0,12)
            </if>
            <if test="orderBaoliParam.tabStatus == 3">
                and ob.baoli_status in (1,2,3,4,5,6,7,8,9,10)
            </if>
            <if test="orderBaoliParam.tabStatus == 4">
                and ob.baoli_status = 11
            </if>
        </if>
        order by ob.order_finish_time desc
    </select>

    <select id="listOrderBaoli"
            resultType="com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO">
        select
            ob.id orderBaoliId,
            ob.order_id orderId,
            ob.sku_offering_name skuOfferingName,
            ob.total_price totalPrice,
            ob.contract_num contractNum,
            ob.buyer_name buyerName,
            up.partner_name partnerName,
            ob.seller_name sellerName,
            ob.baoli_status baoliStatus,
            ob.trade_no tradeNo,
            ob.return_price returnPrice,
            toi.pay_amount payAmount
        from
          order_baoli ob
        left join trade_order_info toi on ob.trade_no = toi.trade_no,
          user_partner up
        where
        ob.cooperator_id = up.user_id
        <if test="orderBaoliParam.skuOfferingName != null and orderBaoliParam.skuOfferingName != ''">
            and ob.sku_offering_name like '%${orderBaoliParam.skuOfferingName}%'
        </if>
        <if test="orderBaoliParam.contractNum != null and orderBaoliParam.contractNum != ''">
            and ob.contract_num like '%${orderBaoliParam.contractNum}%'
        </if>
        <if test="orderBaoliParam.buyerName != null and orderBaoliParam.buyerName != ''">
            and ob.buyer_name like '%${orderBaoliParam.buyerName}%'
        </if>
        <if test="orderBaoliParam.sellerName != null and orderBaoliParam.sellerName != ''">
            and ob.seller_name like '%${orderBaoliParam.sellerName}%'
        </if>
        <if test="orderBaoliParam.baoliStatus != null">
            and ob.baoli_status = #{orderBaoliParam.baoliStatus}
        </if>
        <if test="orderBaoliParam.tabStatus != null">
            <if test="orderBaoliParam.tabStatus == 2">
                and ob.baoli_status in (0,12)
            </if>
            <if test="orderBaoliParam.tabStatus == 3">
                and ob.baoli_status in (1,2,3,4,5,6,7,8,9,10)
            </if>
            <if test="orderBaoliParam.tabStatus == 4">
                and ob.baoli_status = 11
            </if>
        </if>
        order by ob.order_finish_time desc
    </select>

    <select id="listCanGenerateTradeOrder"
            resultType="com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO">
        select
            ob.id orderBaoliId,
            ob.order_id orderId,
            ob.sku_offering_name skuOfferingName,
            ob.total_price totalPrice,
            ob.contract_num contractNum,
            ob.buyer_name buyerName,
            up.partner_name partnerName,
            ob.seller_name sellerName,
            ob.baoli_status baoliStatus,
            ob.cooperator_id cooperatorId
        from
            order_baoli ob,
            user_partner up,
            user_partner up2
        where
            ob.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status = 'activated'
        <if test="generateTradeOrderInfoParam.userId != null and generateTradeOrderInfoParam.userId != ''">
            and up2.user_id = #{generateTradeOrderInfoParam.userId}
        </if>
        <if test="generateTradeOrderInfoParam.orderBaoliIdList != null and generateTradeOrderInfoParam.orderBaoliIdList.size() != 0">
            and ob.id in
            <foreach collection="generateTradeOrderInfoParam.orderBaoliIdList" item="orderBaoliId" index="index" open="(" close=")" separator=",">
                #{orderBaoliId}
            </foreach>
        </if>
    </select>

    <select id="getExportBaoliOrderList" resultType="com.chinamobile.iot.sc.pojo.dto.ExportBaoliOrderDTO">
    SELECT
        ob.order_id orderId,
        ob.baoli_status baoliStatus
    FROM
        order_baoli ob,
        user_partner up,
        user_partner up2
    where
        ob.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status = 'activated'
        and up2.user_id = #{userId}
        and ob.baoli_status in (0,12)
    </select>
</mapper>