<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.K3ProMaterialMapperExt">

    <resultMap id="configedMaterailsRet" type="com.chinamobile.iot.sc.pojo.vo.ProductMaterialsItemVO">
        <id column="materialNum" jdbcType="VARCHAR" property="materialNum"/>
        <collection property="materialArray" column="materialNum"
                    ofType="com.chinamobile.com.iot.sc.pojo.vo.ProductMaterialDetailItemVO"
                    select="getConfigedMaterialDetail">
            <result column="kpmId" jdbcType="VARCHAR" property="kpmId"/>
            <result column="materialNum" jdbcType="VARCHAR" property="materialNum"/>
            <result column="materialCount" jdbcType="VARCHAR" property="materialCount"/>
            <result column="settlePrice" jdbcType="VARCHAR" property="settlePrice"/>
            <result column="materialName" jdbcType="VARCHAR" property="materialName"/>
            <result column="materialDept" jdbcType="VARCHAR" property="materialDept"/>
            <result column="contractNum" jdbcType="VARCHAR" property="contractNum"/>
            <result column="contractName" jdbcType="VARCHAR" property="contractName"/>
            <result column="buyerName" jdbcType="VARCHAR" property="buyerName"/>
            <result column="contractPrice" jdbcType="VARCHAR" property="contractPrice"/>
            <result column="expiredDate" jdbcType="VARCHAR" property="expiredDate"/>
            <result column="contractEffective" jdbcType="VARCHAR" property="contractEffective"/>
            <result column="binded" jdbcType="VARCHAR" property="binded"/>
        </collection>
    </resultMap>


    <select id="getConfigedMaterialDetail" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO">
        select
            kpm.id as kpmId,
            cm.material_number as materialNum,
            kpm.material_count as materialCount,
            cm.settle_price as settlePrice,
            cm.name as materialName,
            kpm.material_dept as materialDept,
            cm.contract_number as contractNum,
            c.name as contractName,
            c.vendor_name as buyerName,
            c.amount_including_tax as contractPrice,
            c.end_date as expiredDate,
            c.active as contractEffective,
            case
                when kpm.atom_id is not	NULL then TRUE
                when kpm.atom_id is NULL then FALSE
                end as binded
        from
            k3_product_material	kpm
            right join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number
            left join contract c on cm.contract_number = c.number
        where
            cm.material_number = #{materialNum}
        order by cm.material_number	asc
    </select>

<!--    <select id="configedMaterails" parameterType="String"-->
<!--           resultMap="configedMaterailsRet">-->
<!--        select-->
<!--            material_num as materialNum-->
<!--        from-->
<!--            k3_product_material kpm-->
<!--        where-->
<!--            atom_id = #{atomId}-->
<!--        group by material_num-->
<!--    </select>-->

    <select id="configedMaterials" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.dto.ContractMaterialDTO">
        select
            material_num as materialNum
        from
            k3_product_material kpm
        where
            atom_id = #{atomId}
        group by material_num
    </select>

    <select id="getBindedInfo" parameterType="com.chinamobile.iot.sc.pojo.dto.ContractMaterialDTO"
            resultType="com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO">
        select
            kpm.id as kpmId,
            cm.material_number as materialNum,
            kpm.material_count as materialCount,
            cm.settle_price as settlePrice,
            cm.name as materialName,
            kpm.material_dept as materialDept,
            cm.contract_number as contractNum,
            c.name as contractName,
            c.vendor_name as buyerName,
            c.amount_including_tax as contractPrice,
            c.end_date as expiredDate,
            c.active as contractEffective,
            1 binded,
            c.contract_type contractType,
            case
            when c.contract_type = 1 then '销售合同'
            when c.contract_type = 2 then '采购合同'
            when c.contract_type = 3 then '外部合同'
            when c.contract_type = 4 then '虚拟合同'
            end contractTypeName,
            c.create_company_name sellerName,
            kpm.material_settle_price materialSettlePrice,
            pack.name servicePackName,
            pack.id servicePackId,
            cm.service_quota_used serviceQuotaUsed,
            cm.service_quota_remain serviceQuotaRemain,
            cm.material_quota_used materialQuotaUsed,
            cm.material_quota_remain materialQuotaRemain,
            cm.label_tax labelTax
        from
            k3_product_material	kpm
                right join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number and kpm.service_pack_id = cm.service_pack_id
                left join contract c on cm.contract_number = c.number
                left join service_pack pack on pack.id = cm.service_pack_id
        where
            kpm.atom_id = #{param.atomId}
            and cm.material_number = #{param.materialNum}
        order by cm.material_number	asc
    </select>

    <select id="getUnBindedInfo"
            resultType="com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO">
        select
            null as kpmId,
            cm.material_number as materialNum,
            null as materialCount,
            cm.settle_price as settlePrice,
            cm.name as materialName,
            cm.owner_department as materialDept,
            cm.contract_number as contractNum,
            c.name as contractName,
            c.vendor_name as buyerName,
            c.amount_including_tax as contractPrice,
            c.end_date as expiredDate,
            c.active as contractEffective,
            0 binded,
            c.contract_type contractType,
            case
            when c.contract_type = 1 then '销售合同'
            when c.contract_type = 2 then '采购合同'
            when c.contract_type = 3 then '外部合同'
            when c.contract_type = 4 then '虚拟合同'
            end contractTypeName,
            c.create_company_name sellerName,
            cm.settle_price materialSettlePrice,
            pack.name servicePackName,
            pack.id servicePackId,
            cm.service_quota_used serviceQuotaUsed,
            cm.service_quota_remain serviceQuotaRemain,
            cm.material_quota_used materialQuotaUsed,
            cm.material_quota_remain materialQuotaRemain,
            cm.label_tax labelTax
        from
                contract_material cm
                left join contract c on cm.contract_number = c.number
                left join service_pack pack on pack.id = cm.service_pack_id
        where
            cm.material_number = #{materialNum}
            and cm.contract_number not in
            <foreach item="item" collection="paramList" open="(" separator="," close=")">
                #{item.contractNum}
            </foreach>
        order by cm.material_number	asc
    </select>


    <select id="unConfigedMaterials" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO">
        select
            cm.material_number as materialNum,
            cm.settle_price as settlePrice,
            cm.name as materialName,
            cm.owner_department as materialDept,
            cm.contract_number as contractNum,
            c.name as contractName,
            c.vendor_name as buyerName,
            c.amount_including_tax as contractPrice,
            c.end_date as expiredDate,
            c.active as contractEffective,
            c.contract_type contractType,
            case
            when c.contract_type = 1 then '销售合同'
            when c.contract_type = 2 then '采购合同'
            when c.contract_type = 3 then '外部合同'
            when c.contract_type = 4 then '虚拟合同'
            end contractTypeName,
            c.create_company_name sellerName,
            cm.settle_price materialSettlePrice,
            pack.name servicePackName,
            pack.id servicePackId,
            cm.service_quota_used serviceQuotaUsed,
            cm.service_quota_remain serviceQuotaRemain,
            cm.material_quota_used materialQuotaUsed,
            cm.material_quota_remain materialQuotaRemain,
            cm.label_tax labelTax
        from
            contract_material cm
            left join contract c on cm.contract_number = c.number
            left join service_pack pack on pack.id = cm.service_pack_id
        where
            cm.material_number = #{materialNum}
        order by cm.material_number	asc
    </select>


    <select id="getSpuClass" parameterType="String"
            resultType="String">
        select
            ci.offering_class as spuClass
        from
            spu_offering_info spu
            left join category_info ci on spu.id = ci.spu_id
        where
            spu.offering_code = #{spuCode}
    </select>

    <select id="getMaterialDetailItem" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.vo.ContractMaterialDetailItemVO">
        select
            kpm.id  kpmId,
            pmi.tax_exclusive_univalence  unitPrice,
			pmi.tax_inclusive_univalence  discountedUnitPrice,
            pmi.tax_rate  taxRate,
            pmi.material_code  materialsCode,
            pmi.material_describe  materialsDesc,
            pmi.unit  materialsUnit,
            pmi.nested_code  offerSetCode,
            pmi.part_code  offerItemCode,
            pmi.part_name  offerItemName,
            kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity  materialsNumber,
            kpm.create_time  createDate,
            kpm.update_time  lastUpdateDate,
            cci.scm_code  ouCode,
            cci.mall_name  ouName
        from
            order_2c_info o2i
                left join order_2c_atom_info o2ai on o2ai.order_id = o2i.order_id
                left join k3_product_material kpm on kpm.atom_offering_code = o2ai.atom_offering_code and kpm.spu_code = o2ai.spu_offering_code and kpm.sku_code = o2ai.sku_offering_code
                left join contract_city_info cci on cci.mall_code = o2i.location
                left join province_material_info pmi on pmi.internet_material_code = kpm.material_num
                left join province_contract_info pci on pci.id = pmi.province_contract_id
        where
            o2i.sync_k3_id = #{contractId}
        and pci.Internet_contract_code = #{contractNum}
    </select>

    <select id="getContractDetail" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.vo.ContractProvinceInfoVO">
        select
            pci.buyer_name  applyName,
            pci.buyer_no  applyNum,
            pci.company_code  companyCode,
            pci.company_name  companyName,
            pci.province_contract_name  contractName,
            pci.province_contract_no  contractNo,
            pci.apply_department_no  deptCode,
            pci.apply_department  deptName,
            pci.Internet_contract_code  recordNumber
        from
            province_contract_info pci
        where
            pci.Internet_contract_code =  #{contractNum}
    </select>

    <resultMap id="getMaterialByOrder" type="com.chinamobile.iot.sc.request.supplychain.SoftwareOrderInfoSyncRequest$SaleLineInfo">
            <result column="PRODUCT_NUMBER" jdbcType="VARCHAR" property="PRODUCT_NUMBER"/>
            <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="PRODUCT_NAME"/>
            <result column="PRODUCT_DEPARTMENT_NAME" jdbcType="VARCHAR" property="PRODUCT_DEPARTMENT_NAME"/>
            <result column="PRODUCT_DEPARTMENT_CODE" jdbcType="VARCHAR" property="PRODUCT_DEPARTMENT_CODE"/>
            <result column="PRODUCT_COLLABORATIVE_NAME" jdbcType="VARCHAR" property="PRODUCT_COLLABORATIVE_NAME"/>
            <result column="MODEL" jdbcType="VARCHAR" property="MODEL"/>
            <result column="UNIT" jdbcType="VARCHAR" property="UNIT"/>
            <result column="AMOUNT" jdbcType="VARCHAR" property="AMOUNT"/>
            <result column="TAX_PRICE" jdbcType="VARCHAR" property="TAX_PRICE"/>
            <result column="TAX_RATE" jdbcType="VARCHAR" property="TAX_RATE"/>
            <result column="VALOREM_TOTAL" jdbcType="VARCHAR" property="VALOREM_TOTAL"/>
            <result column="INCOME_SUBJECT_CODE" jdbcType="VARCHAR" property="INCOME_SUBJECT_CODE"/>
            <result column="INCOME_SUBJECT_NAME" jdbcType="VARCHAR" property="INCOME_SUBJECT_NAME"/>
            <result column="GROUP_COMMITTEE_MARKET_NAME" jdbcType="VARCHAR" property="GROUP_COMMITTEE_MARKET_NAME"/>
            <result column="GROUP_COMMITTEE_PRODUCT_NAME" jdbcType="VARCHAR" property="GROUP_COMMITTEE_PRODUCT_NAME"/>
            <result column="GROUP_COMMITTEE_ACTIVITY_NAME" jdbcType="VARCHAR" property="GROUP_COMMITTEE_ACTIVITY_NAME"/>
            <result column="ORDER_NUMBER" jdbcType="VARCHAR" property="ORDER_NUMBER"/>
    </resultMap>
    <select id="getMaterialByOrder" parameterType="String"
            resultMap="getMaterialByOrder">
        select
            kpm.material_num  PRODUCT_NUMBER,
            kpm.material_name  PRODUCT_NAME,
--             cm.owner_department  PRODUCT_DEPARTMENT_NAME,
--             cm.product_id  PRODUCT_DEPARTMENT_CODE,
-- --             cm.applyer  PRODUCT_COLLABORATIVE_CODE,
--             cm.applyer  PRODUCT_COLLABORATIVE_NAME,
            kpm.material_model  MODEL,
            cm.material_unit  UNIT,
            kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity  AMOUNT,
            cm.settle_price/1000  TAX_PRICE,
--             cm.label_tax  TAX_RATE,
            (cm.settle_price * kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity)/1000  VALOREM_TOTAL,
--             cm.re_coa_num  INCOME_SUBJECT_CODE,
--             cm.re_coa_name  INCOME_SUBJECT_NAME,
--             cm.account_market_name  GROUP_COMMITTEE_MARKET_NAME,
--             cm.account_product_name  GROUP_COMMITTEE_PRODUCT_NAME,
--             cm.business_name  GROUP_COMMITTEE_ACTIVITY_NAME,
            o2ai.order_id ORDER_NUMBER,
            (o2ai.sku_price * o2ai.sku_quantity)/1000  totalPrice,
            c.f_number  TAX_RATE,
            c.number contractNumber,
            o2ai.update_time orderStatusTime,
            o2ai.create_time createTime,
            cm.label_tax  labelTax,
            o2i.bill_no_number billNoNumber
        from
            order_2c_atom_info o2ai
                left join order_2c_info o2i on o2i.order_id = o2ai.order_id
                left join k3_product_material kpm on kpm.atom_offering_code = o2ai.atom_offering_code and kpm.spu_code = o2ai.spu_offering_code and kpm.sku_code = o2ai.sku_offering_code
                left join contract_material cm on cm.contract_number = kpm.contract_num and cm.material_number = kpm.material_num
                join contract c on c.number = cm.contract_number and c.contract_type = '4'
        where
            o2ai.order_id = #{orderId}
            and o2ai.atom_offering_class = 'A'
    </select>

    <resultMap id="getMaterialByOrderB2B" type="com.chinamobile.iot.sc.request.supplychain.SoftwareOrderInfoB2BSyncRequest$SaleLineInfo">
        <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="MATERIAL_CODE"/>
        <result column="MATERIAL_NAME" jdbcType="VARCHAR" property="MATERIAL_NAME"/>
        <result column="PRODUCT_DEPARTMENT_NAME" jdbcType="VARCHAR" property="PRODUCT_DEPARTMENT_NAME"/>
        <result column="PRODUCT_DEPARTMENT_CODE" jdbcType="VARCHAR" property="PRODUCT_DEPARTMENT_CODE"/>
        <result column="SALES_VOLUMES" jdbcType="VARCHAR" property="SALES_VOLUMES"/>
        <result column="UNIT_PRICE_HAS_TAX" jdbcType="VARCHAR" property="UNIT_PRICE_HAS_TAX"/>
        <result column="OUTPUT_TAX_RATE" jdbcType="VARCHAR" property="OUTPUT_TAX_RATE"/>
        <result column="SALE_AMOUNT_HAS_TAX" jdbcType="VARCHAR" property="SALE_AMOUNT_HAS_TAX"/>
        <result column="SALE_CONTRACT_NO" jdbcType="VARCHAR" property="SALE_CONTRACT_NO"/>
        <result column="ACCEPTER" jdbcType="VARCHAR" property="ACCEPTER"/>
        <result column="ACCEPTER_CODE" jdbcType="VARCHAR" property="ACCEPTER_CODE"/>
        <result column="SALE_DEPARTMENT_NAME" jdbcType="VARCHAR" property="SALE_DEPARTMENT_NAME"/>
        <result column="SALE_DEPARTMENT_CODE" jdbcType="VARCHAR" property="SALE_DEPARTMENT_CODE"/>
        <result column="SALE_TEAM_NAME" jdbcType="VARCHAR" property="SALE_TEAM_NAME"/>
        <result column="SALE_TEAM_CODE" jdbcType="VARCHAR" property="SALE_TEAM_CODE"/>
        <result column="COST_CENTER_NAME" jdbcType="VARCHAR" property="COST_CENTER_NAME"/>
        <result column="COST_CENTER_CODE" jdbcType="VARCHAR" property="COST_CENTER_CODE"/>
        <result column="SIGN_CLIENT_NAME" jdbcType="VARCHAR" property="SIGN_CLIENT_NAME"/>
        <result column="SIGN_CLIENT_CODE" jdbcType="VARCHAR" property="SIGN_CLIENT_CODE"/>
        <result column="SALE_ORDER_CODE" jdbcType="VARCHAR" property="SALE_ORDER_CODE"/>
        <result column="SALE_ORDER_STATUS" jdbcType="VARCHAR" property="SALE_ORDER_STATUS"/>
        <result column="SALE_ORDER_TYPE" jdbcType="VARCHAR" property="SALE_ORDER_TYPE"/>
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="CLIENT_NAME"/>
        <result column="CLIENT_CODE" jdbcType="VARCHAR" property="CLIENT_CODE"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="PROVINCE_NAME"/>
        <result column="CITY_NAME" jdbcType="VARCHAR" property="CITY_NAME"/>
        <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="BUSINESS_TYPE"/>
    </resultMap>
    <select id="getMaterialByOrderB2B" parameterType="String"
            resultMap="getMaterialByOrderB2B">
        select
            kpm.material_num  MATERIAL_CODE,
            kpm.material_name  MATERIAL_NAME,
            cm.owner_department  PRODUCT_DEPARTMENT_NAME,
            dept.org_code  PRODUCT_DEPARTMENT_CODE,
--             cm.applyer  PRODUCT_COLLABORATIVE_CODE,
--             cm.applyer  PRODUCT_COLLABORATIVE_NAME,
--             cm.specification  SPECIFICATION_MODEL,
            cm.material_unit  UNIT,
            kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity  SALES_VOLUMES,
            cm.settle_price/1000  UNIT_PRICE_HAS_TAX,
            cm.label_tax  OUTPUT_TAX_RATE,
           (cm.settle_price * kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity)/1000  SALE_AMOUNT_HAS_TAX,
--             cm.re_coa_num  INCOME_SUBJECT_CODE,
--             cm.re_coa_name  INCOME_SUBJECT_NAME,
--             cm.account_market_name  GROUP_COMMITTEE_MARKET_NAME,
--             cm.account_product_name  GROUP_COMMITTEE_PRODUCT_NAME,
--             cm.business_name  GROUP_COMMITTEE_ACTIVITY_NAME,

            kpm.contract_num SALE_CONTRACT_NO,

            (o2ai.sku_price * o2ai.sku_quantity)/1000  totalPrice,
            o2ai.order_status orderStatus,
            oi.org_name orgName,
            o2ai.order_type orderType,

            k3.name ACCEPTER,
            k3.usercode ACCEPTER_CODE,
            k3.department SALE_DEPARTMENT_NAME,
            k3.sellerdept SALE_DEPARTMENT_CODE,
            k3.team SALE_TEAM_NAME,
            k3.sellerteam SALE_TEAM_CODE,
            k3.costcenterName COST_CENTER_NAME,
            k3.costcenter COST_CENTER_CODE,

            case
                when c.sale_order_type = 0 then IF((cir.region_ID is not null), cir.frame_cust_name , (IF((cil.region_ID is not null), cil.frame_cust_name , cib.frame_cust_name)))
                when c.sale_order_type = 1 then IF((cir.region_ID is not null), cir.dict_cust_name, (IF((cil.region_ID is not null), cil.dict_cust_name, cib.dict_cust_name)))
                when c.sale_order_type = 2 then IF((cir.region_ID is not null), cir.dict_cust_name, (IF((cil.region_ID is not null), cil.dict_cust_name, cib.dict_cust_name)))
                else '对应区域不存在'
                end SIGN_CLIENT_NAME,

            case
                when c.sale_order_type = 0 then IF((cir.region_ID is not null), cir.frame_cust_code, (IF((cil.region_ID is not null), cil.frame_cust_code, cib.frame_cust_code)))
                when c.sale_order_type = 1 then IF((cir.region_ID is not null), cir.dict_cust_code, (IF((cil.region_ID is not null), cil.dict_cust_code, cib.dict_cust_code)))
                when c.sale_order_type = 2 then IF((cir.region_ID is not null), cir.dict_cust_code, (IF((cil.region_ID is not null), cil.dict_cust_code, cib.dict_cust_code)))
                else '对应区域编码不存在'
                end SIGN_CLIENT_CODE,

            o2ai.order_id SALE_ORDER_CODE,
            case
                when o2ai.order_status = 0 then '0'
                when o2ai.order_status in (7,12) then '1'
                when o2ai.order_status = 8 then '5'
                else '未知状态'
                end SALE_ORDER_STATUS,

            case
                when c.sale_order_type = 0 then '60'
                when c.sale_order_type = 1 then '50'
                when c.sale_order_type = 2 then '80'
                else '未知状态'
            end SALE_ORDER_TYPE,

            case
                when c.sale_order_type = 0 then '购销类有采购成本'
                when c.sale_order_type = 1 then '购销类有采购成本'
                when c.sale_order_type = 2 then '自研类无采购成本'
                else '未知状态'
            end BUSINESS_TYPE,

            case
                when c.sale_order_type = 0 then IF((cir.region_ID is not null), cir.frame_cust_name, (IF((cil.region_ID is not null), cil.frame_cust_name, cib.frame_cust_name)))
                when c.sale_order_type = 1 then IF((cir.region_ID is not null), cir.dict_cust_name, (IF((cil.region_ID is not null), cil.dict_cust_name, cib.dict_cust_name)))
                when c.sale_order_type = 2 then IF((cir.region_ID is not null), cir.dict_cust_name, (IF((cil.region_ID is not null), cil.dict_cust_name, cib.dict_cust_name)))
                else '对应区域不存在'
                end CLIENT_NAME,

            case
                when c.sale_order_type = 0 then IF((cir.region_ID is not null), cir.frame_iden_code, (IF((cil.region_ID is not null), cil.frame_iden_code, cib.frame_iden_code)))
                when c.sale_order_type = 1 then IF((cir.region_ID is not null), cir.dict_iden_code, (IF((cil.region_ID is not null), cil.dict_iden_code, cib.dict_iden_code)))
                when c.sale_order_type = 2 then IF((cir.region_ID is not null), cir.dict_iden_code, (IF((cil.region_ID is not null), cil.dict_iden_code, cib.dict_iden_code)))
                else '对应区域编码不存在'
                end CLIENT_CODE,

            cib.sale_be_name  PROVINCE_NAME,
            IF((cil.region_ID is not null), cil.sale_location_name, cib.location) as CITY_NAME
        from
            order_2c_atom_info o2ai
                left join order_2c_info oi ON o2ai.order_id = oi.order_id
                left join k3_product_material kpm on kpm.atom_offering_code = o2ai.atom_offering_code and kpm.spu_code = o2ai.spu_offering_code and kpm.sku_code = o2ai.sku_offering_code
                left join contract_material cm on cm.contract_number = kpm.contract_num and cm.material_number = kpm.material_num
                join contract c on cm.contract_number = c.number
                left join user_k3 k3 on k3.provincecode = oi.be_id
                left join contract_city_info cci on cci.mall_code = oi.location
                left join department dept on dept.short_name = cm.owner_department
                left join cust_iden cir on cir.region_ID = oi.region_ID
                left join cust_iden cil on cil.region_ID = (select region_ID from cust_iden where location = oi.location limit 1)
                left join cust_iden cib on cib.region_ID = (select region_ID from cust_iden where be_id = oi.be_id limit 1)
        where
            o2ai.order_id = #{orderId}
          and o2ai.atom_offering_class not in ('C','A')
    </select>
    <select id="getOrderIdLastMonth"
            resultType="String">
        select
            o2ai.order_id as orderId
        from
            order_2c_atom_info o2ai
            left join order_2c_info o2c on o2c.order_id = o2ai.order_id
        where
            o2ai.order_status in (2,7)
          and o2c.bill_no_number is null
          and o2ai.atom_offering_class = 'A'
          and o2c.order_status_time <![CDATA[ >= ]]> #{startTime}
          and o2c.order_status_time <![CDATA[ <= ]]> #{endTime}
    </select>
</mapper>