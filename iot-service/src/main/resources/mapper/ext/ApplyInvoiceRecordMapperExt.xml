<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ApplyInvoiceRecordMapperExt">

    <resultMap id="applyInvoiceRecMap" type="com.chinamobile.iot.sc.pojo.mapper.ApplyInvoiceRecDO">
        <result column="id" property="id"/>
        <result column="atom_order_id" property="atomOrderId"/>
        <result column="order_seq" property="orderSeq"/>
        <result column="order_id" property="orderId"/>
        <result column="be_id" property="beId"/>
        <result column="cust_code" property="custCode"/>
        <result column="print_date" property="printDate"/>
        <result column="frank" property="frank"/>
        <result column="p_name" property="pName"/>
        <result column="identify_num" property="identifyNum"/>
        <result column="address_info" property="addressInfo"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_iD" property="bankId"/>
        <result column="order_price" property="orderPrice"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="cooperator_id" property="cooperatorId"/>
        <result column="partner_name" property="partnerName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="cooperatorName" property="cooperatorName"/>
        <result column="reminder_count" property="reminderCount"/>
        <result column="spuOfferingClass" property="spuOfferingClass"/>
    </resultMap>

    <resultMap id="applyInvoiceRecDetailMap" type="com.chinamobile.iot.sc.pojo.mapper.ApplyInvoiceRecDetailDO">
        <result column="id" property="id"/>
        <result column="atom_order_id" property="atomOrderId"/>
        <result column="order_seq" property="orderSeq"/>
        <result column="order_id" property="orderId"/>
        <result column="be_id" property="beId"/>
        <result column="cust_code" property="custCode"/>
        <result column="print_date" property="printDate"/>
        <result column="frank" property="frank"/>
        <result column="p_name" property="pName"/>
        <result column="identify_num" property="identifyNum"/>
        <result column="address_info" property="addressInfo"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_iD" property="bankId"/>
        <result column="order_price" property="orderPrice"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="reminder_count" property="reminderCount"/>
        <result column="primary_cooperator_id" property="primaryCooperatorId"/>
       <!-- <result column="cooperator_id" property="cooperatorId"/>
        <result column="partner_name" property="partnerName"/>-->
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
       <!-- <result column="cooperatorName" property="cooperatorName"/>-->
    </resultMap>
    <select id="page4InvoiceRec" resultMap="applyInvoiceRecMap">
        select
        a.id,
        a.atom_order_id,
        a.order_seq,
        a.order_id,
        a.be_id,
        a.cust_code,
        a.print_date,
        a.frank,
        a.p_name,
        a.identify_num,
        a.address_info,
        a.phone_number,
        a.bank_name,
        a.bank_iD,
        a.order_price,
        a.status,
        a.remark,
        a.reminder_count,
        cp.cooperator_id,
        cph.cooperator_id finish_cooperator_id,
        cp.partner_name,
        a.create_time,
        a.update_time,
        cp.user_name as cooperatorName,
        cph.user_name as finishCooperatorName,
        o.order_status,
        o.atom_offering_name,
        o.atom_offering_class as offering_class,
        o.model,
        (o.sku_quantity * o.atom_quantity) as quantity,
        o2i.spu_offering_class spuOfferingClass
        from
        apply_invoice_record a
        inner join order_2c_atom_info o on a.atom_order_id = o.id
        inner join order_2c_info o2i on a.order_id = o2i.order_id
        <if test="userIdList==null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>
        <if test="userIdList!=null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = o.id and cph.order_id = o.order_id
        where  1=1
            <if test="status!=null">and a.status = #{status}</if>
            <!--<if test="userIdList!=null">
                and a.cooperator_id in
                <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            </if>-->
            <if test="invoiceApplyId!=null and invoiceApplyId!=''">
                and a.id like concat('%',#{invoiceApplyId},'%')
            </if>
            <if test="orderId != null and orderId != ''">
                and a.order_id like concat('%',#{orderId},'%')
            </if>
            <if test="orderSeq != null and orderSeq != ''">
                and a.order_seq like concat('%',#{orderSeq},'%')
            </if>
        order by update_time desc
        limit #{pageIndex},#{num}
    </select>

    <select id="pageCount4InvoiceRec" resultType="java.lang.Long">
        select  count(*)
        from
        apply_invoice_record a
     inner join order_2c_atom_info o on a.atom_order_id = o.id
        inner join order_2c_info o2i on a.order_id = o2i.order_id
        <if test="userIdList!=null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>
        where  1=1
        <if test="status!=null">and a.status = #{status}</if>
        <!--<if test="userIdList!=null">and a.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
        </if>-->
        <if test="invoiceApplyId!=null and invoiceApplyId!=''">
            and a.id like concat('%',#{invoiceApplyId},'%')
        </if>
        <if test="orderId != null and orderId !=''">
            and a.order_id like concat('%',#{orderId},'%')
        </if>
        <if test="orderSeq != null and orderSeq != ''">
            and a.order_seq like concat('%',#{orderSeq},'%')
        </if>
    </select>

<!--    <select id="getUnInvoiceRec" resultMap="applyInvoiceRecMap">-->
<!--        select-->
<!--        a.id,-->
<!--        a.order_id,-->
<!--        a.oper_type,-->
<!--        a.create_time,-->
<!--        a.frank,-->
<!--        a.order_price,-->
<!--        a.status,-->
<!--        a.pname,-->
<!--        u.partner_name,-->
<!--        u.name as cooperatorName-->
<!--        FROM-->
<!--        invoice_reverse_record a-->
<!--        left join user u on a.cooperator_id=u.user_id-->
<!--        <where>-->
<!--            <if test="status!=null">AND status = #{status}</if>-->
<!--            <if test="userIdList!=null">AND a.cooperator_id in-->
<!--                <foreach item="item" collection="#{userIdList}" open="(" separator="," close=")">#{item}</foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY update_time DESC-->
<!--        limit #{page},#{num}-->
<!--    </select>-->

    <select id="getUnInvoiceRec" resultType="com.chinamobile.iot.sc.response.web.invoice.Data4UnInvoice">
        select
        a.id,
        a.order_id,
        a.frank,
        a.p_name,
        a.order_price,
        a.status,
        cp.cooperator_id,
        u.partner_name,
        a.create_time,
        u.name as cooperatorName
        from
        apply_invoice_record a
        left join order_2c_atom_info o on a.atom_order_id = o.id
        <if test="userIdList==null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>
        <if test="userIdList!=null">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>
        <where>
            <if test="status!=null">and status = #{status}</if>
            <!--<if test="userIdList!=null">and a.cooperator_id in
                <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            </if>-->
        </where>
        order by a.update_time desc
    </select>

    <select id="getApplyInvoiceRecordDetailByOrderId" resultMap="applyInvoiceRecDetailMap">
        select
        air.id,
        air.atom_order_id,
        air.order_seq,
        air.order_id,
        air.be_id,
        air.cust_code,
        air.print_date,
        air.frank,
        air.p_name,
        air.identify_num,
        air.address_info,
        air.phone_number,
        air.bank_name,
        air.bank_iD,
        air.order_price,
        air.status,
        air.remark,
        air.reminder_count,
        o2ai.cooperator_id primary_cooperator_id,
        <!--cp.cooperator_id,
        cph.cooperator_id finish_cooperator_id,
        cp.partner_name,-->
        air.create_time,
        air.update_time,
        <!--cp.user_name as cooperatorName,
        cph.user_name as finishCooperatorName,-->
        o2ai.order_status,
        o2ai.atom_offering_name,
        o2ai.atom_offering_class as offering_class,
        o2ai.model,
        (o2ai.sku_quantity * o2ai.atom_quantity) as quantity
        from
        apply_invoice_record air
        inner join order_2c_atom_info o2ai on air.atom_order_id = o2ai.id
        <!--<if test="userIdList==null || userIdList.size == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>-->
        <!--<if test="userIdList!=null and userIdList.size != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o.id and cp.order_id = o.order_id
        </if>-->
        <!--<if test="userIdList!=null and userIdList.size() != 0">
            inner join order_cooperator_relation ocr on ocr.atom_order_id = o.id and ocr.order_id = o.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        </if>-->
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = o2ai.id and cph.order_id = o2ai.order_id
        where air.order_id = #{orderId}
        <!--<if test="userIdList!=null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
        </if>-->
        order by air.create_time desc
        limit 1
    </select>

</mapper>
