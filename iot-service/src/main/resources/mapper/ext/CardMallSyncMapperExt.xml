<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.CardMallSyncMapperExt">

  <update id="updateCardMallOrderToNull">
    update
      card_mall_sync
    set
        order_id = null,
        atom_order_id = null,
        card_status = 1,
        update_time = now()
    where
        1=1
    and msisdn = #{updateCardMallOrderToNullParam.msisdn}
    and order_id = #{updateCardMallOrderToNullParam.orderId}
  </update>

    <select id="listNullCardHandle" resultType="com.chinamobile.iot.sc.pojo.dto.NullCardHandleDTO">
        select
            o2ai.order_status orderStatus,
            cms.id,
            o2ai.order_id orderId,
            o2ai.id atomOrderId,
            cms.msisdn
        from
            card_mall_sync cms,
            sku_msisdn_relation smr,
            order_2c_atom_info o2ai
        where
            cms.msisdn = smr.msisdn
        and smr.order_atom_info_id = o2ai.id
        and smr.order_id = o2ai.order_id
        and cms.card_type = 3
        order by o2ai.create_time asc
    </select>

</mapper>