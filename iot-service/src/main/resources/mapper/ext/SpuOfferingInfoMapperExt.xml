<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.SpuOfferingInfoMapperExt">
  <select id="getSpuOfferingClassFromSpuCode" parameterType="java.lang.String" resultType="java.lang.String">
  SELECT
      ca.offering_class
  FROM
      category_info ca
  JOIN spu_offering_info sp ON ca.spu_id = sp.id
  WHERE
      sp.offering_code = #{spuCode}
  </select>

    <select id="getSpuCodeClassBySkuCode" resultType="com.chinamobile.iot.sc.pojo.mapper.SpuCodeClassDO">
    SELECT
        spu.offering_code spuOfferingCode,
        ca.offering_class spuOfferingClass,
        spu.spu_offering_version spuOfferingVersion
    FROM
        sku_offering_info_history sku
    JOIN spu_offering_info_history spu ON sku.spu_code = spu.offering_code and sku.spu_offering_version = spu.spu_offering_version
    JOIN category_info ca on ca.spu_id = spu.spu_id
    WHERE sku.offering_code = #{skuCode} and sku.sku_offering_version = #{skuVersion}
    order by CAST(SUBSTRING_INDEX(sku.sku_offering_version,'V',-1) as UNSIGNED) desc, CAST(SUBSTRING_INDEX(sku.spu_offering_version,'V',-1) as UNSIGNED) desc
        limit 1
    </select>

    <select id="getSpuCodeClassByAtomCode" resultType="com.chinamobile.iot.sc.pojo.mapper.SpuCodeClassDO">
    SELECT
        spu.offering_code spuOfferingCode,
        ca.offering_class spuOfferingClass
    FROM
        atom_offering_info atom
    JOIN spu_offering_info spu ON atom.spu_code = spu.offering_code
    JOIN category_info ca on ca.spu_id = spu.id
    WHERE atom.offering_code = #{atomCode}
    </select>
</mapper>