<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceDirectoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="first_directory_id" jdbcType="VARCHAR" property="firstDirectoryId" />
    <result column="first_directory_name" jdbcType="VARCHAR" property="firstDirectoryName" />
    <result column="second_directory_id" jdbcType="VARCHAR" property="secondDirectoryId" />
    <result column="second_directory_name" jdbcType="VARCHAR" property="secondDirectoryName" />
    <result column="third_directory_id" jdbcType="VARCHAR" property="thirdDirectoryId" />
    <result column="third_directory_name" jdbcType="VARCHAR" property="thirdDirectoryName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, first_directory_id, first_directory_name, second_directory_id, 
    second_directory_name, third_directory_id, third_directory_name, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_directory
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_directory
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_directory (id, flow_instance_id, first_directory_id, 
      first_directory_name, second_directory_id, 
      second_directory_name, third_directory_id, 
      third_directory_name, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{firstDirectoryId,jdbcType=VARCHAR}, 
      #{firstDirectoryName,jdbcType=VARCHAR}, #{secondDirectoryId,jdbcType=VARCHAR}, 
      #{secondDirectoryName,jdbcType=VARCHAR}, #{thirdDirectoryId,jdbcType=VARCHAR}, 
      #{thirdDirectoryName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_directory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="firstDirectoryId != null">
        first_directory_id,
      </if>
      <if test="firstDirectoryName != null">
        first_directory_name,
      </if>
      <if test="secondDirectoryId != null">
        second_directory_id,
      </if>
      <if test="secondDirectoryName != null">
        second_directory_name,
      </if>
      <if test="thirdDirectoryId != null">
        third_directory_id,
      </if>
      <if test="thirdDirectoryName != null">
        third_directory_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryId != null">
        #{firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryName != null">
        #{firstDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryId != null">
        #{secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryName != null">
        #{secondDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="thirdDirectoryId != null">
        #{thirdDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="thirdDirectoryName != null">
        #{thirdDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_directory
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.firstDirectoryId != null">
        first_directory_id = #{record.firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.firstDirectoryName != null">
        first_directory_name = #{record.firstDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.secondDirectoryId != null">
        second_directory_id = #{record.secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondDirectoryName != null">
        second_directory_name = #{record.secondDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdDirectoryId != null">
        third_directory_id = #{record.thirdDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdDirectoryName != null">
        third_directory_name = #{record.thirdDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_directory
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      first_directory_id = #{record.firstDirectoryId,jdbcType=VARCHAR},
      first_directory_name = #{record.firstDirectoryName,jdbcType=VARCHAR},
      second_directory_id = #{record.secondDirectoryId,jdbcType=VARCHAR},
      second_directory_name = #{record.secondDirectoryName,jdbcType=VARCHAR},
      third_directory_id = #{record.thirdDirectoryId,jdbcType=VARCHAR},
      third_directory_name = #{record.thirdDirectoryName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_directory
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryId != null">
        first_directory_id = #{firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryName != null">
        first_directory_name = #{firstDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryId != null">
        second_directory_id = #{secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryName != null">
        second_directory_name = #{secondDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="thirdDirectoryId != null">
        third_directory_id = #{thirdDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="thirdDirectoryName != null">
        third_directory_name = #{thirdDirectoryName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_directory
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      first_directory_id = #{firstDirectoryId,jdbcType=VARCHAR},
      first_directory_name = #{firstDirectoryName,jdbcType=VARCHAR},
      second_directory_id = #{secondDirectoryId,jdbcType=VARCHAR},
      second_directory_name = #{secondDirectoryName,jdbcType=VARCHAR},
      third_directory_id = #{thirdDirectoryId,jdbcType=VARCHAR},
      third_directory_name = #{thirdDirectoryName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_directory
    (id, flow_instance_id, first_directory_id, first_directory_name, second_directory_id, 
      second_directory_name, third_directory_id, third_directory_name, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.firstDirectoryId,jdbcType=VARCHAR}, 
        #{item.firstDirectoryName,jdbcType=VARCHAR}, #{item.secondDirectoryId,jdbcType=VARCHAR}, 
        #{item.secondDirectoryName,jdbcType=VARCHAR}, #{item.thirdDirectoryId,jdbcType=VARCHAR}, 
        #{item.thirdDirectoryName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 16:41:11 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_directory (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'first_directory_id'.toString() == column.value">
          #{item.firstDirectoryId,jdbcType=VARCHAR}
        </if>
        <if test="'first_directory_name'.toString() == column.value">
          #{item.firstDirectoryName,jdbcType=VARCHAR}
        </if>
        <if test="'second_directory_id'.toString() == column.value">
          #{item.secondDirectoryId,jdbcType=VARCHAR}
        </if>
        <if test="'second_directory_name'.toString() == column.value">
          #{item.secondDirectoryName,jdbcType=VARCHAR}
        </if>
        <if test="'third_directory_id'.toString() == column.value">
          #{item.thirdDirectoryId,jdbcType=VARCHAR}
        </if>
        <if test="'third_directory_name'.toString() == column.value">
          #{item.thirdDirectoryName,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>