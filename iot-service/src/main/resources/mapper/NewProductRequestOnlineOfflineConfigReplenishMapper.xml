<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineConfigReplenishMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="new_product_request_id" jdbcType="VARCHAR" property="newProductRequestId" />
    <result column="combo_info_id" jdbcType="VARCHAR" property="comboInfoId" />
    <result column="store_first_catalog" jdbcType="VARCHAR" property="storeFirstCatalog" />
    <result column="store_second_catalog" jdbcType="VARCHAR" property="storeSecondCatalog" />
    <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass" />
    <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName" />
    <result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
    <result column="atom_hardware_settle_price" jdbcType="DECIMAL" property="atomHardwareSettlePrice" />
    <result column="atom_hardware_sale_price" jdbcType="DECIMAL" property="atomHardwareSalePrice" />
    <result column="atom_software_sale_price" jdbcType="DECIMAL" property="atomSoftwareSalePrice" />
    <result column="province_accrual" jdbcType="DECIMAL" property="provinceAccrual" />
    <result column="department_id" jdbcType="INTEGER" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="product_property" jdbcType="VARCHAR" property="productProperty" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, new_product_request_id, combo_info_id, store_first_catalog, store_second_catalog, 
    spu_offering_class, atom_offering_name, sale_price, atom_hardware_settle_price, atom_hardware_sale_price, 
    atom_software_sale_price, province_accrual, department_id, department_name, product_property, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenishExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_config_replenish
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_config_replenish
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_online_offline_config_replenish
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenishExample">
    delete from new_product_request_online_offline_config_replenish
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish">
    insert into new_product_request_online_offline_config_replenish (id, new_product_request_id, combo_info_id, 
      store_first_catalog, store_second_catalog, 
      spu_offering_class, atom_offering_name, sale_price, 
      atom_hardware_settle_price, atom_hardware_sale_price, 
      atom_software_sale_price, province_accrual, 
      department_id, department_name, product_property, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{newProductRequestId,jdbcType=VARCHAR}, #{comboInfoId,jdbcType=VARCHAR}, 
      #{storeFirstCatalog,jdbcType=VARCHAR}, #{storeSecondCatalog,jdbcType=VARCHAR}, 
      #{spuOfferingClass,jdbcType=VARCHAR}, #{atomOfferingName,jdbcType=VARCHAR}, #{salePrice,jdbcType=DECIMAL}, 
      #{atomHardwareSettlePrice,jdbcType=DECIMAL}, #{atomHardwareSalePrice,jdbcType=DECIMAL}, 
      #{atomSoftwareSalePrice,jdbcType=DECIMAL}, #{provinceAccrual,jdbcType=DECIMAL}, 
      #{departmentId,jdbcType=INTEGER}, #{departmentName,jdbcType=VARCHAR}, #{productProperty,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish">
    insert into new_product_request_online_offline_config_replenish
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="newProductRequestId != null">
        new_product_request_id,
      </if>
      <if test="comboInfoId != null">
        combo_info_id,
      </if>
      <if test="storeFirstCatalog != null">
        store_first_catalog,
      </if>
      <if test="storeSecondCatalog != null">
        store_second_catalog,
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class,
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="atomHardwareSettlePrice != null">
        atom_hardware_settle_price,
      </if>
      <if test="atomHardwareSalePrice != null">
        atom_hardware_sale_price,
      </if>
      <if test="atomSoftwareSalePrice != null">
        atom_software_sale_price,
      </if>
      <if test="provinceAccrual != null">
        province_accrual,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="productProperty != null">
        product_property,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="newProductRequestId != null">
        #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="storeFirstCatalog != null">
        #{storeFirstCatalog,jdbcType=VARCHAR},
      </if>
      <if test="storeSecondCatalog != null">
        #{storeSecondCatalog,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomHardwareSettlePrice != null">
        #{atomHardwareSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomHardwareSalePrice != null">
        #{atomHardwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomSoftwareSalePrice != null">
        #{atomSoftwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="provinceAccrual != null">
        #{provinceAccrual,jdbcType=DECIMAL},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenishExample" resultType="java.lang.Long">
    select count(*) from new_product_request_online_offline_config_replenish
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_online_offline_config_replenish
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.newProductRequestId != null">
        new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="record.comboInfoId != null">
        combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeFirstCatalog != null">
        store_first_catalog = #{record.storeFirstCatalog,jdbcType=VARCHAR},
      </if>
      <if test="record.storeSecondCatalog != null">
        store_second_catalog = #{record.storeSecondCatalog,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingClass != null">
        spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingName != null">
        atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.salePrice != null">
        sale_price = #{record.salePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.atomHardwareSettlePrice != null">
        atom_hardware_settle_price = #{record.atomHardwareSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.atomHardwareSalePrice != null">
        atom_hardware_sale_price = #{record.atomHardwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.atomSoftwareSalePrice != null">
        atom_software_sale_price = #{record.atomSoftwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.provinceAccrual != null">
        province_accrual = #{record.provinceAccrual,jdbcType=DECIMAL},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=INTEGER},
      </if>
      <if test="record.departmentName != null">
        department_name = #{record.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.productProperty != null">
        product_property = #{record.productProperty,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_online_offline_config_replenish
    set id = #{record.id,jdbcType=VARCHAR},
      new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      store_first_catalog = #{record.storeFirstCatalog,jdbcType=VARCHAR},
      store_second_catalog = #{record.storeSecondCatalog,jdbcType=VARCHAR},
      spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      sale_price = #{record.salePrice,jdbcType=DECIMAL},
      atom_hardware_settle_price = #{record.atomHardwareSettlePrice,jdbcType=DECIMAL},
      atom_hardware_sale_price = #{record.atomHardwareSalePrice,jdbcType=DECIMAL},
      atom_software_sale_price = #{record.atomSoftwareSalePrice,jdbcType=DECIMAL},
      province_accrual = #{record.provinceAccrual,jdbcType=DECIMAL},
      department_id = #{record.departmentId,jdbcType=INTEGER},
      department_name = #{record.departmentName,jdbcType=VARCHAR},
      product_property = #{record.productProperty,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish">
    update new_product_request_online_offline_config_replenish
    <set>
      <if test="newProductRequestId != null">
        new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="storeFirstCatalog != null">
        store_first_catalog = #{storeFirstCatalog,jdbcType=VARCHAR},
      </if>
      <if test="storeSecondCatalog != null">
        store_second_catalog = #{storeSecondCatalog,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomHardwareSettlePrice != null">
        atom_hardware_settle_price = #{atomHardwareSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomHardwareSalePrice != null">
        atom_hardware_sale_price = #{atomHardwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="atomSoftwareSalePrice != null">
        atom_software_sale_price = #{atomSoftwareSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="provinceAccrual != null">
        province_accrual = #{provinceAccrual,jdbcType=DECIMAL},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        product_property = #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish">
    update new_product_request_online_offline_config_replenish
    set new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      store_first_catalog = #{storeFirstCatalog,jdbcType=VARCHAR},
      store_second_catalog = #{storeSecondCatalog,jdbcType=VARCHAR},
      spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      sale_price = #{salePrice,jdbcType=DECIMAL},
      atom_hardware_settle_price = #{atomHardwareSettlePrice,jdbcType=DECIMAL},
      atom_hardware_sale_price = #{atomHardwareSalePrice,jdbcType=DECIMAL},
      atom_software_sale_price = #{atomSoftwareSalePrice,jdbcType=DECIMAL},
      province_accrual = #{provinceAccrual,jdbcType=DECIMAL},
      department_id = #{departmentId,jdbcType=INTEGER},
      department_name = #{departmentName,jdbcType=VARCHAR},
      product_property = #{productProperty,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_online_offline_config_replenish
    (id, new_product_request_id, combo_info_id, store_first_catalog, store_second_catalog, 
      spu_offering_class, atom_offering_name, sale_price, atom_hardware_settle_price, 
      atom_hardware_sale_price, atom_software_sale_price, province_accrual, department_id, 
      department_name, product_property, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.newProductRequestId,jdbcType=VARCHAR}, #{item.comboInfoId,jdbcType=VARCHAR}, 
        #{item.storeFirstCatalog,jdbcType=VARCHAR}, #{item.storeSecondCatalog,jdbcType=VARCHAR}, 
        #{item.spuOfferingClass,jdbcType=VARCHAR}, #{item.atomOfferingName,jdbcType=VARCHAR}, 
        #{item.salePrice,jdbcType=DECIMAL}, #{item.atomHardwareSettlePrice,jdbcType=DECIMAL}, 
        #{item.atomHardwareSalePrice,jdbcType=DECIMAL}, #{item.atomSoftwareSalePrice,jdbcType=DECIMAL}, 
        #{item.provinceAccrual,jdbcType=DECIMAL}, #{item.departmentId,jdbcType=INTEGER}, 
        #{item.departmentName,jdbcType=VARCHAR}, #{item.productProperty,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_online_offline_config_replenish (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'new_product_request_id'.toString() == column.value">
          #{item.newProductRequestId,jdbcType=VARCHAR}
        </if>
        <if test="'combo_info_id'.toString() == column.value">
          #{item.comboInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'store_first_catalog'.toString() == column.value">
          #{item.storeFirstCatalog,jdbcType=VARCHAR}
        </if>
        <if test="'store_second_catalog'.toString() == column.value">
          #{item.storeSecondCatalog,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_class'.toString() == column.value">
          #{item.spuOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_name'.toString() == column.value">
          #{item.atomOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'sale_price'.toString() == column.value">
          #{item.salePrice,jdbcType=DECIMAL}
        </if>
        <if test="'atom_hardware_settle_price'.toString() == column.value">
          #{item.atomHardwareSettlePrice,jdbcType=DECIMAL}
        </if>
        <if test="'atom_hardware_sale_price'.toString() == column.value">
          #{item.atomHardwareSalePrice,jdbcType=DECIMAL}
        </if>
        <if test="'atom_software_sale_price'.toString() == column.value">
          #{item.atomSoftwareSalePrice,jdbcType=DECIMAL}
        </if>
        <if test="'province_accrual'.toString() == column.value">
          #{item.provinceAccrual,jdbcType=DECIMAL}
        </if>
        <if test="'department_id'.toString() == column.value">
          #{item.departmentId,jdbcType=INTEGER}
        </if>
        <if test="'department_name'.toString() == column.value">
          #{item.departmentName,jdbcType=VARCHAR}
        </if>
        <if test="'product_property'.toString() == column.value">
          #{item.productProperty,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>