<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cAgentInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="agent_name_wash" jdbcType="VARCHAR" property="agentNameWash" />
    <result column="agent_number" jdbcType="VARCHAR" property="agentNumber" />
    <result column="agent_number_wash" jdbcType="VARCHAR" property="agentNumberWash" />
    <result column="agent_phone" jdbcType="VARCHAR" property="agentPhone" />
    <result column="agent_user_id" jdbcType="VARCHAR" property="agentUserId" />
    <result column="agent_label_wash" jdbcType="VARCHAR" property="agentLabelWash" />
    <result column="agent_category_wash" jdbcType="VARCHAR" property="agentCategoryWash" />
    <result column="is_wash" jdbcType="VARCHAR" property="isWash" />
    <result column="operator_wash" jdbcType="VARCHAR" property="operatorWash" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, order_id, agent_name, agent_name_wash, agent_number, agent_number_wash, agent_phone, 
    agent_user_id, agent_label_wash, agent_category_wash, is_wash, operator_wash, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_agent_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_2c_agent_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_agent_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_agent_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_agent_info (id, order_id, agent_name, 
      agent_name_wash, agent_number, agent_number_wash, 
      agent_phone, agent_user_id, agent_label_wash, 
      agent_category_wash, is_wash, operator_wash, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{agentName,jdbcType=VARCHAR}, 
      #{agentNameWash,jdbcType=VARCHAR}, #{agentNumber,jdbcType=VARCHAR}, #{agentNumberWash,jdbcType=VARCHAR}, 
      #{agentPhone,jdbcType=VARCHAR}, #{agentUserId,jdbcType=VARCHAR}, #{agentLabelWash,jdbcType=VARCHAR}, 
      #{agentCategoryWash,jdbcType=VARCHAR}, #{isWash,jdbcType=VARCHAR}, #{operatorWash,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_agent_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="agentName != null">
        agent_name,
      </if>
      <if test="agentNameWash != null">
        agent_name_wash,
      </if>
      <if test="agentNumber != null">
        agent_number,
      </if>
      <if test="agentNumberWash != null">
        agent_number_wash,
      </if>
      <if test="agentPhone != null">
        agent_phone,
      </if>
      <if test="agentUserId != null">
        agent_user_id,
      </if>
      <if test="agentLabelWash != null">
        agent_label_wash,
      </if>
      <if test="agentCategoryWash != null">
        agent_category_wash,
      </if>
      <if test="isWash != null">
        is_wash,
      </if>
      <if test="operatorWash != null">
        operator_wash,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null">
        #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="agentNameWash != null">
        #{agentNameWash,jdbcType=VARCHAR},
      </if>
      <if test="agentNumber != null">
        #{agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="agentNumberWash != null">
        #{agentNumberWash,jdbcType=VARCHAR},
      </if>
      <if test="agentPhone != null">
        #{agentPhone,jdbcType=VARCHAR},
      </if>
      <if test="agentUserId != null">
        #{agentUserId,jdbcType=VARCHAR},
      </if>
      <if test="agentLabelWash != null">
        #{agentLabelWash,jdbcType=VARCHAR},
      </if>
      <if test="agentCategoryWash != null">
        #{agentCategoryWash,jdbcType=VARCHAR},
      </if>
      <if test="isWash != null">
        #{isWash,jdbcType=VARCHAR},
      </if>
      <if test="operatorWash != null">
        #{operatorWash,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_agent_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_agent_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.agentName != null">
        agent_name = #{record.agentName,jdbcType=VARCHAR},
      </if>
      <if test="record.agentNameWash != null">
        agent_name_wash = #{record.agentNameWash,jdbcType=VARCHAR},
      </if>
      <if test="record.agentNumber != null">
        agent_number = #{record.agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.agentNumberWash != null">
        agent_number_wash = #{record.agentNumberWash,jdbcType=VARCHAR},
      </if>
      <if test="record.agentPhone != null">
        agent_phone = #{record.agentPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.agentUserId != null">
        agent_user_id = #{record.agentUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.agentLabelWash != null">
        agent_label_wash = #{record.agentLabelWash,jdbcType=VARCHAR},
      </if>
      <if test="record.agentCategoryWash != null">
        agent_category_wash = #{record.agentCategoryWash,jdbcType=VARCHAR},
      </if>
      <if test="record.isWash != null">
        is_wash = #{record.isWash,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorWash != null">
        operator_wash = #{record.operatorWash,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_agent_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      agent_name = #{record.agentName,jdbcType=VARCHAR},
      agent_name_wash = #{record.agentNameWash,jdbcType=VARCHAR},
      agent_number = #{record.agentNumber,jdbcType=VARCHAR},
      agent_number_wash = #{record.agentNumberWash,jdbcType=VARCHAR},
      agent_phone = #{record.agentPhone,jdbcType=VARCHAR},
      agent_user_id = #{record.agentUserId,jdbcType=VARCHAR},
      agent_label_wash = #{record.agentLabelWash,jdbcType=VARCHAR},
      agent_category_wash = #{record.agentCategoryWash,jdbcType=VARCHAR},
      is_wash = #{record.isWash,jdbcType=VARCHAR},
      operator_wash = #{record.operatorWash,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_agent_info
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null">
        agent_name = #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="agentNameWash != null">
        agent_name_wash = #{agentNameWash,jdbcType=VARCHAR},
      </if>
      <if test="agentNumber != null">
        agent_number = #{agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="agentNumberWash != null">
        agent_number_wash = #{agentNumberWash,jdbcType=VARCHAR},
      </if>
      <if test="agentPhone != null">
        agent_phone = #{agentPhone,jdbcType=VARCHAR},
      </if>
      <if test="agentUserId != null">
        agent_user_id = #{agentUserId,jdbcType=VARCHAR},
      </if>
      <if test="agentLabelWash != null">
        agent_label_wash = #{agentLabelWash,jdbcType=VARCHAR},
      </if>
      <if test="agentCategoryWash != null">
        agent_category_wash = #{agentCategoryWash,jdbcType=VARCHAR},
      </if>
      <if test="isWash != null">
        is_wash = #{isWash,jdbcType=VARCHAR},
      </if>
      <if test="operatorWash != null">
        operator_wash = #{operatorWash,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_agent_info
    set order_id = #{orderId,jdbcType=VARCHAR},
      agent_name = #{agentName,jdbcType=VARCHAR},
      agent_name_wash = #{agentNameWash,jdbcType=VARCHAR},
      agent_number = #{agentNumber,jdbcType=VARCHAR},
      agent_number_wash = #{agentNumberWash,jdbcType=VARCHAR},
      agent_phone = #{agentPhone,jdbcType=VARCHAR},
      agent_user_id = #{agentUserId,jdbcType=VARCHAR},
      agent_label_wash = #{agentLabelWash,jdbcType=VARCHAR},
      agent_category_wash = #{agentCategoryWash,jdbcType=VARCHAR},
      is_wash = #{isWash,jdbcType=VARCHAR},
      operator_wash = #{operatorWash,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_agent_info
    (id, order_id, agent_name, agent_name_wash, agent_number, agent_number_wash, agent_phone, 
      agent_user_id, agent_label_wash, agent_category_wash, is_wash, operator_wash, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.agentName,jdbcType=VARCHAR}, 
        #{item.agentNameWash,jdbcType=VARCHAR}, #{item.agentNumber,jdbcType=VARCHAR}, #{item.agentNumberWash,jdbcType=VARCHAR}, 
        #{item.agentPhone,jdbcType=VARCHAR}, #{item.agentUserId,jdbcType=VARCHAR}, #{item.agentLabelWash,jdbcType=VARCHAR}, 
        #{item.agentCategoryWash,jdbcType=VARCHAR}, #{item.isWash,jdbcType=VARCHAR}, #{item.operatorWash,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 30 17:04:22 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_agent_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'agent_name'.toString() == column.value">
          #{item.agentName,jdbcType=VARCHAR}
        </if>
        <if test="'agent_name_wash'.toString() == column.value">
          #{item.agentNameWash,jdbcType=VARCHAR}
        </if>
        <if test="'agent_number'.toString() == column.value">
          #{item.agentNumber,jdbcType=VARCHAR}
        </if>
        <if test="'agent_number_wash'.toString() == column.value">
          #{item.agentNumberWash,jdbcType=VARCHAR}
        </if>
        <if test="'agent_phone'.toString() == column.value">
          #{item.agentPhone,jdbcType=VARCHAR}
        </if>
        <if test="'agent_user_id'.toString() == column.value">
          #{item.agentUserId,jdbcType=VARCHAR}
        </if>
        <if test="'agent_label_wash'.toString() == column.value">
          #{item.agentLabelWash,jdbcType=VARCHAR}
        </if>
        <if test="'agent_category_wash'.toString() == column.value">
          #{item.agentCategoryWash,jdbcType=VARCHAR}
        </if>
        <if test="'is_wash'.toString() == column.value">
          #{item.isWash,jdbcType=VARCHAR}
        </if>
        <if test="'operator_wash'.toString() == column.value">
          #{item.operatorWash,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>