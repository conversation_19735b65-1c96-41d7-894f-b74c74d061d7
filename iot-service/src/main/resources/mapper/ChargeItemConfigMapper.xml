<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ChargeItemConfigMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="charge_name" jdbcType="VARCHAR" property="chargeName" />
    <result column="charge_id" jdbcType="VARCHAR" property="chargeId" />
    <result column="product_caliber" jdbcType="VARCHAR" property="productCaliber" />
    <result column="product_type_name" jdbcType="VARCHAR" property="productTypeName" />
    <result column="product_type_id" jdbcType="VARCHAR" property="productTypeId" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, charge_name, charge_id, product_caliber, product_type_name, product_type_id, 
    platform_code, platform_name, creator, creat_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from charge_item_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from charge_item_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from charge_item_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from charge_item_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into charge_item_config (id, charge_name, charge_id, 
      product_caliber, product_type_name, product_type_id, 
      platform_code, platform_name, creator, 
      creat_time)
    values (#{id,jdbcType=INTEGER}, #{chargeName,jdbcType=VARCHAR}, #{chargeId,jdbcType=VARCHAR}, 
      #{productCaliber,jdbcType=VARCHAR}, #{productTypeName,jdbcType=VARCHAR}, #{productTypeId,jdbcType=VARCHAR}, 
      #{platformCode,jdbcType=VARCHAR}, #{platformName,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{creatTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into charge_item_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="chargeName != null">
        charge_name,
      </if>
      <if test="chargeId != null">
        charge_id,
      </if>
      <if test="productCaliber != null">
        product_caliber,
      </if>
      <if test="productTypeName != null">
        product_type_name,
      </if>
      <if test="productTypeId != null">
        product_type_id,
      </if>
      <if test="platformCode != null">
        platform_code,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="creatTime != null">
        creat_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="chargeName != null">
        #{chargeName,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="productCaliber != null">
        #{productCaliber,jdbcType=VARCHAR},
      </if>
      <if test="productTypeName != null">
        #{productTypeName,jdbcType=VARCHAR},
      </if>
      <if test="productTypeId != null">
        #{productTypeId,jdbcType=VARCHAR},
      </if>
      <if test="platformCode != null">
        #{platformCode,jdbcType=VARCHAR},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from charge_item_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update charge_item_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.chargeName != null">
        charge_name = #{record.chargeName,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeId != null">
        charge_id = #{record.chargeId,jdbcType=VARCHAR},
      </if>
      <if test="record.productCaliber != null">
        product_caliber = #{record.productCaliber,jdbcType=VARCHAR},
      </if>
      <if test="record.productTypeName != null">
        product_type_name = #{record.productTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.productTypeId != null">
        product_type_id = #{record.productTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformCode != null">
        platform_code = #{record.platformCode,jdbcType=VARCHAR},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.creatTime != null">
        creat_time = #{record.creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update charge_item_config
    set id = #{record.id,jdbcType=INTEGER},
      charge_name = #{record.chargeName,jdbcType=VARCHAR},
      charge_id = #{record.chargeId,jdbcType=VARCHAR},
      product_caliber = #{record.productCaliber,jdbcType=VARCHAR},
      product_type_name = #{record.productTypeName,jdbcType=VARCHAR},
      product_type_id = #{record.productTypeId,jdbcType=VARCHAR},
      platform_code = #{record.platformCode,jdbcType=VARCHAR},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      creat_time = #{record.creatTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update charge_item_config
    <set>
      <if test="chargeName != null">
        charge_name = #{chargeName,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        charge_id = #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="productCaliber != null">
        product_caliber = #{productCaliber,jdbcType=VARCHAR},
      </if>
      <if test="productTypeName != null">
        product_type_name = #{productTypeName,jdbcType=VARCHAR},
      </if>
      <if test="productTypeId != null">
        product_type_id = #{productTypeId,jdbcType=VARCHAR},
      </if>
      <if test="platformCode != null">
        platform_code = #{platformCode,jdbcType=VARCHAR},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        creat_time = #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update charge_item_config
    set charge_name = #{chargeName,jdbcType=VARCHAR},
      charge_id = #{chargeId,jdbcType=VARCHAR},
      product_caliber = #{productCaliber,jdbcType=VARCHAR},
      product_type_name = #{productTypeName,jdbcType=VARCHAR},
      product_type_id = #{productTypeId,jdbcType=VARCHAR},
      platform_code = #{platformCode,jdbcType=VARCHAR},
      platform_name = #{platformName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      creat_time = #{creatTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into charge_item_config
    (id, charge_name, charge_id, product_caliber, product_type_name, product_type_id, 
      platform_code, platform_name, creator, creat_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=INTEGER}, #{item.chargeName,jdbcType=VARCHAR}, #{item.chargeId,jdbcType=VARCHAR}, 
        #{item.productCaliber,jdbcType=VARCHAR}, #{item.productTypeName,jdbcType=VARCHAR}, 
        #{item.productTypeId,jdbcType=VARCHAR}, #{item.platformCode,jdbcType=VARCHAR}, 
        #{item.platformName,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.creatTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 30 09:59:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into charge_item_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=INTEGER}
        </if>
        <if test="'charge_name'.toString() == column.value">
          #{item.chargeName,jdbcType=VARCHAR}
        </if>
        <if test="'charge_id'.toString() == column.value">
          #{item.chargeId,jdbcType=VARCHAR}
        </if>
        <if test="'product_caliber'.toString() == column.value">
          #{item.productCaliber,jdbcType=VARCHAR}
        </if>
        <if test="'product_type_name'.toString() == column.value">
          #{item.productTypeName,jdbcType=VARCHAR}
        </if>
        <if test="'product_type_id'.toString() == column.value">
          #{item.productTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'platform_code'.toString() == column.value">
          #{item.platformCode,jdbcType=VARCHAR}
        </if>
        <if test="'platform_name'.toString() == column.value">
          #{item.platformName,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'creat_time'.toString() == column.value">
          #{item.creatTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>