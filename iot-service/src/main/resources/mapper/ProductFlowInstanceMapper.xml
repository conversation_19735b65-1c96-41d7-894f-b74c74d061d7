<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstance">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="flow_instance_number" jdbcType="VARCHAR" property="flowInstanceNumber" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="current_step_id" jdbcType="VARCHAR" property="currentStepId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="off_shelf_reason" jdbcType="VARCHAR" property="offShelfReason" />
    <result column="shelf_type" jdbcType="INTEGER" property="shelfType" />
    <result column="change_list" jdbcType="VARCHAR" property="changeList" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="can_edit" jdbcType="BIT" property="canEdit" />
    <result column="can_cancel" jdbcType="BIT" property="canCancel" />
    <result column="can_audit" jdbcType="BIT" property="canAudit" />
    <result column="can_config" jdbcType="BIT" property="canConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, flow_id, flow_instance_number, creator_id, creator_name, current_step_id, status, 
    off_shelf_reason, shelf_type, change_list, create_time, update_time, can_edit, can_cancel, 
    can_audit, can_config
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstance">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance (id, flow_id, flow_instance_number, 
      creator_id, creator_name, current_step_id, 
      status, off_shelf_reason, shelf_type, 
      change_list, create_time, update_time, 
      can_edit, can_cancel, can_audit, can_config
      )
    values (#{id,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, #{flowInstanceNumber,jdbcType=VARCHAR}, 
      #{creatorId,jdbcType=VARCHAR}, #{creatorName,jdbcType=VARCHAR}, #{currentStepId,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{offShelfReason,jdbcType=VARCHAR}, #{shelfType,jdbcType=INTEGER}, 
      #{changeList,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{canEdit,jdbcType=BIT}, #{canCancel,jdbcType=BIT}, #{canAudit,jdbcType=BIT}, #{canConfig,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstance">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="flowInstanceNumber != null">
        flow_instance_number,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="currentStepId != null">
        current_step_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="offShelfReason != null">
        off_shelf_reason,
      </if>
      <if test="shelfType != null">
        shelf_type,
      </if>
      <if test="changeList != null">
        change_list,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="canEdit != null">
        can_edit,
      </if>
      <if test="canCancel != null">
        can_cancel,
      </if>
      <if test="canAudit != null">
        can_audit,
      </if>
      <if test="canConfig != null">
        can_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceNumber != null">
        #{flowInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="currentStepId != null">
        #{currentStepId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="offShelfReason != null">
        #{offShelfReason,jdbcType=VARCHAR},
      </if>
      <if test="shelfType != null">
        #{shelfType,jdbcType=INTEGER},
      </if>
      <if test="changeList != null">
        #{changeList,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canEdit != null">
        #{canEdit,jdbcType=BIT},
      </if>
      <if test="canCancel != null">
        #{canCancel,jdbcType=BIT},
      </if>
      <if test="canAudit != null">
        #{canAudit,jdbcType=BIT},
      </if>
      <if test="canConfig != null">
        #{canConfig,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceNumber != null">
        flow_instance_number = #{record.flowInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.currentStepId != null">
        current_step_id = #{record.currentStepId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.offShelfReason != null">
        off_shelf_reason = #{record.offShelfReason,jdbcType=VARCHAR},
      </if>
      <if test="record.shelfType != null">
        shelf_type = #{record.shelfType,jdbcType=INTEGER},
      </if>
      <if test="record.changeList != null">
        change_list = #{record.changeList,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.canEdit != null">
        can_edit = #{record.canEdit,jdbcType=BIT},
      </if>
      <if test="record.canCancel != null">
        can_cancel = #{record.canCancel,jdbcType=BIT},
      </if>
      <if test="record.canAudit != null">
        can_audit = #{record.canAudit,jdbcType=BIT},
      </if>
      <if test="record.canConfig != null">
        can_config = #{record.canConfig,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance
    set id = #{record.id,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      flow_instance_number = #{record.flowInstanceNumber,jdbcType=VARCHAR},
      creator_id = #{record.creatorId,jdbcType=VARCHAR},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      current_step_id = #{record.currentStepId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      off_shelf_reason = #{record.offShelfReason,jdbcType=VARCHAR},
      shelf_type = #{record.shelfType,jdbcType=INTEGER},
      change_list = #{record.changeList,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      can_edit = #{record.canEdit,jdbcType=BIT},
      can_cancel = #{record.canCancel,jdbcType=BIT},
      can_audit = #{record.canAudit,jdbcType=BIT},
      can_config = #{record.canConfig,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstance">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance
    <set>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceNumber != null">
        flow_instance_number = #{flowInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="currentStepId != null">
        current_step_id = #{currentStepId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="offShelfReason != null">
        off_shelf_reason = #{offShelfReason,jdbcType=VARCHAR},
      </if>
      <if test="shelfType != null">
        shelf_type = #{shelfType,jdbcType=INTEGER},
      </if>
      <if test="changeList != null">
        change_list = #{changeList,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canEdit != null">
        can_edit = #{canEdit,jdbcType=BIT},
      </if>
      <if test="canCancel != null">
        can_cancel = #{canCancel,jdbcType=BIT},
      </if>
      <if test="canAudit != null">
        can_audit = #{canAudit,jdbcType=BIT},
      </if>
      <if test="canConfig != null">
        can_config = #{canConfig,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstance">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance
    set flow_id = #{flowId,jdbcType=VARCHAR},
      flow_instance_number = #{flowInstanceNumber,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      current_step_id = #{currentStepId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      off_shelf_reason = #{offShelfReason,jdbcType=VARCHAR},
      shelf_type = #{shelfType,jdbcType=INTEGER},
      change_list = #{changeList,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      can_edit = #{canEdit,jdbcType=BIT},
      can_cancel = #{canCancel,jdbcType=BIT},
      can_audit = #{canAudit,jdbcType=BIT},
      can_config = #{canConfig,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance
    (id, flow_id, flow_instance_number, creator_id, creator_name, current_step_id, status, 
      off_shelf_reason, shelf_type, change_list, create_time, update_time, can_edit, 
      can_cancel, can_audit, can_config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, #{item.flowInstanceNumber,jdbcType=VARCHAR}, 
        #{item.creatorId,jdbcType=VARCHAR}, #{item.creatorName,jdbcType=VARCHAR}, #{item.currentStepId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.offShelfReason,jdbcType=VARCHAR}, #{item.shelfType,jdbcType=INTEGER}, 
        #{item.changeList,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.canEdit,jdbcType=BIT}, #{item.canCancel,jdbcType=BIT}, #{item.canAudit,jdbcType=BIT}, 
        #{item.canConfig,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 25 17:15:18 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_number'.toString() == column.value">
          #{item.flowInstanceNumber,jdbcType=VARCHAR}
        </if>
        <if test="'creator_id'.toString() == column.value">
          #{item.creatorId,jdbcType=VARCHAR}
        </if>
        <if test="'creator_name'.toString() == column.value">
          #{item.creatorName,jdbcType=VARCHAR}
        </if>
        <if test="'current_step_id'.toString() == column.value">
          #{item.currentStepId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'off_shelf_reason'.toString() == column.value">
          #{item.offShelfReason,jdbcType=VARCHAR}
        </if>
        <if test="'shelf_type'.toString() == column.value">
          #{item.shelfType,jdbcType=INTEGER}
        </if>
        <if test="'change_list'.toString() == column.value">
          #{item.changeList,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'can_edit'.toString() == column.value">
          #{item.canEdit,jdbcType=BIT}
        </if>
        <if test="'can_cancel'.toString() == column.value">
          #{item.canCancel,jdbcType=BIT}
        </if>
        <if test="'can_audit'.toString() == column.value">
          #{item.canAudit,jdbcType=BIT}
        </if>
        <if test="'can_config'.toString() == column.value">
          #{item.canConfig,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>