package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

import java.util.List;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2024/12/30 10:22
 * @Description:
 */
@Data
public class LimitSyncInfoResponse {
    /**
     * 交易流水
     */
    private String transID;
    /**
     * 返回码
     */
    private String rspCode;
    /**
     * 错误描述
     */
    private String rspDesc;
    /**
     * 已使用额度
     */

    private List<UsedLimit> usedLimit;

    //    usedLimit
    @Data
    public static class UsedLimit {
        /**
         * 省代码
         */
        private String companyID;
        /**
         * 服务包编码
         */
        private String serviceCode;
        /**
         * 已使用额度
         */
        private String limit;

    }
}
