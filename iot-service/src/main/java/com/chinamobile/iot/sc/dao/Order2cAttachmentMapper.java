package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.Order2cAttachment;
import com.chinamobile.iot.sc.pojo.Order2cAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAttachmentMapper {
    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    long countByExample(Order2cAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int deleteByExample(Order2cAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int insert(Order2cAttachment record);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int insertSelective(Order2cAttachment record);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    List<Order2cAttachment> selectByExample(Order2cAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    Order2cAttachment selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int updateByExampleSelective(@Param("record") Order2cAttachment record, @Param("example") Order2cAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int updateByExample(@Param("record") Order2cAttachment record, @Param("example") Order2cAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int updateByPrimaryKeySelective(Order2cAttachment record);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int updateByPrimaryKey(Order2cAttachment record);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int batchInsert(@Param("list") List<Order2cAttachment> list);

    /**
     *
     * @mbg.generated Thu Oct 10 15:59:34 CST 2024
     */
    int batchInsertSelective(@Param("list") List<Order2cAttachment> list, @Param("selective") Order2cAttachment.Column ... selective);
}