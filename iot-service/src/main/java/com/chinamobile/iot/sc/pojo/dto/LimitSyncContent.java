package com.chinamobile.iot.sc.pojo.dto;

import com.chinamobile.iot.sc.response.iot.LimitSyncInfoResponse;

import java.util.List;

public class LimitSyncContent {
    private List<LimitSyncInfoResponse.UsedLimit> usedLimit;

    public List<LimitSyncInfoResponse.UsedLimit> getUsedLimit() {
        return usedLimit;
    }

    public void setUsedLimit(List<LimitSyncInfoResponse.UsedLimit> usedLimit) {
        this.usedLimit = usedLimit;
    }
}
