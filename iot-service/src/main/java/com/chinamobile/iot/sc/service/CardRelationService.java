package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.dto.CardRelationImportInfoDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.CardRelationXDTO;
import com.chinamobile.iot.sc.pojo.dto.ImportNumCardDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.mapper.OrderProductCardRelationListDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 卡相关信息service接口类
 */
public interface CardRelationService {

    /**
     * 根据需要获取卡相关信息
     *
     * @param cardRelationExample
     * @return
     */
    List<CardRelation> listCardRelationByNeed(CardRelationExample cardRelationExample);

    Long countCardRelationByNeed(CardRelationExample cardRelationExample);
    /**
     * 批量新增卡相关信息
     *
     * @param cardRelationList
     */
    void batchAddCardRelation(List<CardRelation> cardRelationList);

    /**
     * 获取卡相关信息
     *
     * @param cardRelationParam
     * @return
     */
    List<CardRelationVO> listCardRelation(CardRelationParam cardRelationParam);

    /**
     * 导出卡相关信息
     *
     * @param cardRelationParam
     */
    void exportCardRelation(CardRelationParam cardRelationParam,
                            HttpServletResponse response) throws IOException;

    /**
     * 获取订单的卡相关信息
     *
     * @param cardRelationParam
     * @return
     */
    List<OrderCardVO> listOrderCard(CardRelationParam cardRelationParam);

    /**
     * 导出订单的卡相关信息
     *
     * @param cardRelationParam
     */
    void exportOrderCard(CardRelationParam cardRelationParam,
                         LoginIfo4Redis loginIfo4Redis,
                         HttpServletResponse response);

    /**
     * 获取导出订单的卡相关信息的数量
     * @param cardRelationParam
     * @param loginIfo4Redis
     * @return
     */
    Integer getExportOrderCardCount(CardRelationParam cardRelationParam,
                                    LoginIfo4Redis loginIfo4Redis)  throws Exception;

    /**
     * 获取未使用过的卡信息
     *
     * @return
     */
    List<NotUseCardVO> listNotUseCard(NotUseCardParam notUseCardParam);

    /**
     * 根据需要更新卡信息
     *
     * @param cardRelation
     * @param cardRelationExample
     */
    void updateCardRelationByNeed(CardRelation cardRelation,
                                  CardRelationExample cardRelationExample);

    /**
     * 根据id更新终端信息
     * @param cardRelation
     */
    void updateCardRelationById(CardRelation cardRelation);

    void updateCardRelationByIdSelective(CardRelation cardRelation);

    /**
     * 导入终端imei相关信息
     *
     * @param file
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    void importCardRelationX(InputStream inputStream,
                             LoginIfo4Redis loginIfo4Redis,
                             HttpServletRequest request,
                             HttpServletResponse response) throws Exception;

    /**
     * 导入终端imei相关信息到指定的终端类型
     * @param importCardRelationToCardParam
     * @param inputStream
     * @param loginIfo4Redis
     * @param request
     * @param response
     * @throws Exception
     */
    void importCardRelationXToCard(ImportCardRelationToCardParam importCardRelationToCardParam,
                                   InputStream inputStream,
                                   DkcardxInventoryMainInfo dkcardxInventoryMainInfo,
                                   LoginIfo4Redis loginIfo4Redis,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception;

    /**
     * 新增卡+X终端数据
     * @param addCardRelationParam
     * @return
     */
    BaseAnswer addCardRelationX(AddCardRelationParam addCardRelationParam,
                                LoginIfo4Redis loginIfo4Redis);

    /**
     * 分页获取x终端信息
     *
     * @param cardRelationXParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<CardRelationXVO> pageCardInfoX(CardRelationXParam cardRelationXParam,
                                            LoginIfo4Redis loginIfo4Redis);

    /**
     * 导出x终端相关信息
     *
     * @param cardRelationXParam
     * @param response
     * @throws IOException
     */
    void exportCardRelationX(CardRelationXParam cardRelationXParam,
                             LoginIfo4Redis loginIfo4Redis,
                             HttpServletResponse response) throws Exception;

    /**
     * 获取终端信息列表
     *
     * @param cardRelationXParam
     * @return
     */
    List<CardRelationXDTO> listCardRelationX(CardRelationXParam cardRelationXParam);

    /**
     * 根据id删除未销售的x终端信息
     *
     * @param id
     */
    void deleteNotSellCardRelationX(String id);

    /**
     * 根据id删除未销售的x终端信息
     *
     * @param file
     */
    void batchDeleteNotSellCardRelationX(MultipartFile file,
                                         LoginIfo4Redis loginIfo4Redis,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws Exception;

    /**
     * 根据id获取x终端信息详情
     *
     * @param id
     * @param loginIfo4Redis
     * @return
     */
    CardRelationXDetailVO getCardRelationXDetail(String id,LoginIfo4Redis loginIfo4Redis);

    /**
     * x终端导入时判断总库存数和预警值  发送预警短信
     *
     * @param inventoryMainId
     */
    void sendKxTerminalInventorySms(String inventoryMainId);

    BaseAnswer<List<OrderProductCardRelationListVO>> orderProductCardRelationList(OrderProductCardRelationListParam param);

    List<OrderProductCardRelationListDO> orderProductCardRelationList(String spuOfferingCode,
                                                                      String skuOfferingCode,
                                                                      String atomOfferingCode,
                                                                      String reserveBeId,
                                                                      String reserveLocation,
                                                                      String searchWord);


    /**
     * 批量交付时的查询
     * @param spuOfferingCode
     * @param skuOfferingCode
     * @param atomOfferingCode
     * @param reserveBeId
     * @param reserveLocation
     * @param imei
     * @return
     */
    List<OrderProductCardRelationListDO> cardRelationDeliverList(String spuOfferingCode,
                                                                      String skuOfferingCode,
                                                                      String atomOfferingCode,
                                                                      String reserveBeId,
                                                                      String reserveLocation,
                                                                      String imei,
                                                                      String deviceVersion);

    BaseAnswer deliverCard(DeliverCardParam param, Boolean addLog,
                           String cardType,LoginIfo4Redis loginIfo4Redis);

    /**
     * 导入代客下单卡+x订单的交付信息
     *
     * @param inputStream
     * @param orderId
     * @param cardType
     * @param request
     * @param response
     * @throws Exception
     */
    void importCardXDeliver(//MultipartFile file,
            InputStream inputStream,
                            String orderId,
                            String cardType,
                            List<AtomOfferingInfo> atomOfferingInfoList,
                            List<Order2cAtomInfo> order2cAtomInfoList,
                            Order2cInfo order2cInfo,
                            LoginIfo4Redis loginIfo4Redis,
                            HttpServletRequest request,
                            HttpServletResponse response) throws Exception;

    /**
     * 导入代客下单卡+x订单的单个交付信息
     * @param inputStream
     * @param orderId
     * @param cardType
     * @param atomOfferingInfo
     * @param order2cAtomInfo
     * @param loginIfo4Redis
     * @param request
     * @param response
     * @throws Exception
     */
    void importCardXDeliverSingle(//MultipartFile file,
                            InputStream inputStream,
                            String orderId,
                            String cardType,
                            AtomOfferingInfo atomOfferingInfo,
                            Order2cAtomInfo order2cAtomInfo,
                            LoginIfo4Redis loginIfo4Redis,
                            HttpServletRequest request,
                            HttpServletResponse response,
                                  String importRedisKey) throws Exception;

    /**
     * 更新卡信息为空等
     *
     * @param updateCardOrderToNullParam
     */
    void updateCardOrderToNull(UpdateCardOrderToNullParam updateCardOrderToNullParam);

    /**
     * 获取导入批次终端明细
     *
     * @param cardRelationImportInfoDetailParam
     * @return
     */
    PageData<CardRelationImportInfoDetailDTO> listCardRelationImportInfoDetail(CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam);

    /**
     * 获取导入批次x终端详情地市下拉列表
     *
     * @param cardRelationImportInfoDetailParam
     * @return
     */
    List<ImportNumCardDetailLocationDTO> listImportNumXDetailLocation(CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam);

    /**
     * 查询卡+X的临时iccid的生命周期状态
     * @param queryTempIccidLifeStatusParam
     * @return
     */
    BaseAnswer queryTempIccidLifeStatus(QueryTempIccidLifeStatusParam queryTempIccidLifeStatusParam);

    /**
     * 处理未销售存量数据到库存中
     *
     * @param id
     */
    void handleExistCardRelationToInventory(String id);

    /**
     * 处理已销售的存量数据到库存中
     *
     * @param id
     */
    void handleExistSellOutCardRelationToInventory(String id);

    /**
     * 处理已经存在的库存信息到新的库存信息表中
     *
     * @param inventoryId
     */
    void handleExistInventoryToNewInventory(String inventoryId);

    void handleInventoryMisError(String dkDetailId, String dkAtomId, Long reserveNum);

    /**
     * 处理没有导入批次的卡+X终端
     */
    void handleNoImportNumCard();

    /*
     * 处理原子商品信息库存
     * @param atomId
     * @param reserveNum
     */
    void handleInventoryMisErrorAtomInfo(String atomId,Long reserveNum);

    /**
     * 处理终端原子库存信息表预占
     * @param dkAtomId
     * @param reserveNum
     */
    void handleInventoryMisErrorAtomCard(String dkAtomId,Long reserveNum);

    /**
     * 处理终端详情信息表预占
     * @param dkDetailId
     * @param reserveNum
     */
    void handleInventoryMisErrorCardDetail(String dkDetailId,Long reserveNum);

    /**
     * 处理错误的imei信息
     * @param inputStream
     * @param request
     * @param response
     * @throws Exception
     */
    void handleErrorImei(InputStream inputStream,
                             HttpServletRequest request,
                             HttpServletResponse response) throws Exception;

    /**
     * 处理库存详情不在库存原子表的数据
     * @param dkcardxInventoryAtomInfo
     * @return
     */
    BaseAnswer handleInventoryNotInAtomInfo(DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo);

    BaseAnswer<List<CardRalationCountByAreaVO>> cardRalationCountByArea(String atomOrderId);

    void batchDeliver(InputStream inputStream,
                      LoginIfo4Redis loginIfo4Redis,
                      HttpServletResponse response,
                      String importRedisKey) throws Exception;
}
