package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 目录数据迁移
 */
@Data
public class ProductNavigationInfoMigrateRequest implements Serializable {
    private List<ProductNavigationMigrateDTO> data;

    @Data
    public static class ProductNavigationMigrateDTO implements Serializable {
        private String menu;
        private String level1NavigationName;
        private String level1NavigationCode;
        private String level2NavigationName;
        private String level2NavigationCode;
    }
}
