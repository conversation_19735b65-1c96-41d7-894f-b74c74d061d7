package com.chinamobile.iot.sc.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.IOException;
import java.io.InputStream;

/**
 * @package: com.chinamobile.iot.sc.util
 * @ClassName: FTPUtil
 * @description: FTP上传文件至IOT商城工具类
 * @author: zyj
 * @create: 2021/12/7 16:49
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
public class FTPUtil {

    //本地字符编码
    static String LOCAL_CHARSET = "GBK";

    // FTP协议里面，规定文件名编码为iso-8859-1
    static String SERVER_CHARSET = "ISO-8859-1";

    /**
     * @Description:
     * @MethodName: uploadFile
     * @param host: FTP host地址
     * @param port: FTP 端口
     * @param username: FTP连接用户名
     * @param password: FTP连接密码
     * @param basePath: 基础路径
     * @param filePath: 文件路径
     * @param filename: 文件名
     * @param input: 输入文件流
     * @return: boolean
     * @Author: zyj
     * @Date: 2021/3/30 11:47
     */
    public static boolean uploadFile(String host, int port, String username, String password, String basePath,
                                     String filePath, String filename, InputStream input) {
        boolean result = false;
        FTPClient ftp = new FTPClient();
        try {
            int reply;
            ftp.connect(host, port);// 连接FTP服务器
            // 如果采用默认端口，可以使用ftp.connect(host)的方式直接连接FTP服务器
            ftp.login(username, password);// 登录
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return result;
            }
            // 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码，否则就使用本地编码（GBK）.
            if (FTPReply.isPositiveCompletion(ftp.sendCommand("OPTS UTF8", "ON"))) {
                LOCAL_CHARSET = "UTF-8";
            }
            ftp.setControlEncoding(LOCAL_CHARSET);
            //切换到上传目录
            if (!ftp.changeWorkingDirectory(basePath+filePath)) {
                //如果目录不存在创建目录
                String[] dirs = filePath.split("/");
                String tempPath = basePath;
                for (String dir : dirs) {
                    if (null == dir || "".equals(dir)) continue;
                    tempPath += "/" + dir;
                    if (!ftp.changeWorkingDirectory(tempPath)) {
                        if (!ftp.makeDirectory(tempPath)) {
                            return result;
                        } else {
                            ftp.changeWorkingDirectory(tempPath);
                        }
                    }
                }
            }
            //设置为被动模式
            ftp.enterLocalPassiveMode();
            //设置上传文件的类型为二进制类型
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            //上传文件
            if (!ftp.storeFile(filename, input)) {
                return result;
            }
            input.close();
            ftp.logout();
            result = true;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                }
            }
        }
        return result;
    }
    /**
     * @Description: FTP客户端-递归删除路径下所有文件夹及文件
     * @MethodName: removeFile
     * @param host:
     * @param port:
     * @param username:
     * @param password:
     * @param filePath:
     * @return: boolean
     * @Author: zyj
     * @Date: 2021/4/6 14:07
     */
    public static boolean removeFile(String host, int port, String username, String password, String basePath
            , String filePath){
        boolean result = false;
        FTPClient ftp = new FTPClient();
        try {
            ftp.connect(host, port);// 连接FTP服务器
            // 如果采用默认端口，可以使用ftp.connect(host)的方式直接连接FTP服务器
            ftp.login(username, password);// 登录
            ftp.setRemoteVerificationEnabled(false);
            delete(ftp, basePath + "/" +filePath);//递归删除目录下文件
            ftp.logout();
            result = true;
        } catch (IOException e) {
            log.error("删除FTP文件夹及文件异常，路径为{}", basePath + "/" +filePath);
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                    log.error("关闭FTP连接失败！");
                }
            }
        }
        return result;
    }
    /**
     * @Description: 递归删除文件夹及文件
     * @MethodName: delete
     * @param ftp:
     * @param pathname:
     * @return: void
     * @Author: zyj
     * @Date: 2021/4/6 14:08
     */
    public static void delete(FTPClient ftp, String pathname){
        try {
            //切换到目录
            if (ftp.changeWorkingDirectory(pathname)) {
                FTPFile[] files = ftp.listFiles(pathname);
                for (FTPFile file : files) {
                    String dirName = file.getName();
                    //组装路径
                    String nowPath = pathname+"/"+dirName;
                    if (file.isDirectory()) {
                        //递归直到目录下没有文件
                        delete(ftp, nowPath);
                    } else {
                        //删除文件
                        ftp.deleteFile(dirName);
                    }
                }
                //最后删除外层目录
                ftp.removeDirectory(pathname);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
