package com.chinamobile.iot.sc.constant;

public enum OfferingStatusEnum {

    TEST("0","测试"),
    SHELF("1","发布"),
    OFF_SHELF("2","下架");

    public String code;
    public String name;

    OfferingStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OfferingStatusEnum fromCode(String code){
        OfferingStatusEnum[] values = OfferingStatusEnum.values();
        for (OfferingStatusEnum value : values) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
