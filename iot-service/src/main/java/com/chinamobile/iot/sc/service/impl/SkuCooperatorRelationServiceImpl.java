package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.SkuCooperatorRelationMapper;
import com.chinamobile.iot.sc.pojo.entity.SkuCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.SkuCooperatorRelationExample;
import com.chinamobile.iot.sc.service.SkuCooperatorRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @description sku商品和从合作伙伴关系service实现类
 */
@Service
public class SkuCooperatorRelationServiceImpl implements SkuCooperatorRelationService {

    @Resource
    private SkuCooperatorRelationMapper skuCooperatorRelationMapper;

    @Override
    public void batchAddSkuCooperatorRelation(List<SkuCooperatorRelation> skuCooperatorRelationList) {
        skuCooperatorRelationMapper.batchInsert(skuCooperatorRelationList);
    }

    @Override
    public void deleteSkuCooperatorRelationByNeed(SkuCooperatorRelationExample skuCooperatorRelationExample) {
        skuCooperatorRelationMapper.deleteByExample(skuCooperatorRelationExample);
    }

    @Override
    public List<SkuCooperatorRelation> listSkuCooperatorRelationByNeed(SkuCooperatorRelationExample skuCooperatorRelationExample) {
        return skuCooperatorRelationMapper.selectByExample(skuCooperatorRelationExample);
    }
}
