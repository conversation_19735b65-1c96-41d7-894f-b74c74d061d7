package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.List;

public class QlyOrderSnExample {
    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public QlyOrderSnExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public QlyOrderSnExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public QlyOrderSnExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        QlyOrderSnExample example = new QlyOrderSnExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public QlyOrderSnExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public QlyOrderSnExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("sn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andQlyStatusIsNull() {
            addCriterion("qly_status is null");
            return (Criteria) this;
        }

        public Criteria andQlyStatusIsNotNull() {
            addCriterion("qly_status is not null");
            return (Criteria) this;
        }

        public Criteria andQlyStatusEqualTo(Integer value) {
            addCriterion("qly_status =", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotEqualTo(Integer value) {
            addCriterion("qly_status <>", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThan(Integer value) {
            addCriterion("qly_status >", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("qly_status >=", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThan(Integer value) {
            addCriterion("qly_status <", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("qly_status <=", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("qly_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusIn(List<Integer> values) {
            addCriterion("qly_status in", values, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotIn(List<Integer> values) {
            addCriterion("qly_status not in", values, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusBetween(Integer value1, Integer value2) {
            addCriterion("qly_status between", value1, value2, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("qly_status not between", value1, value2, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeIsNull() {
            addCriterion("turn_on_code is null");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeIsNotNull() {
            addCriterion("turn_on_code is not null");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeEqualTo(String value) {
            addCriterion("turn_on_code =", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeNotEqualTo(String value) {
            addCriterion("turn_on_code <>", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeNotEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeGreaterThan(String value) {
            addCriterion("turn_on_code >", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeGreaterThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeGreaterThanOrEqualTo(String value) {
            addCriterion("turn_on_code >=", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeGreaterThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLessThan(String value) {
            addCriterion("turn_on_code <", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLessThanColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLessThanOrEqualTo(String value) {
            addCriterion("turn_on_code <=", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLessThanOrEqualToColumn(QlyOrderSn.Column column) {
            addCriterion(new StringBuilder("turn_on_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLike(String value) {
            addCriterion("turn_on_code like", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeNotLike(String value) {
            addCriterion("turn_on_code not like", value, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeIn(List<String> values) {
            addCriterion("turn_on_code in", values, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeNotIn(List<String> values) {
            addCriterion("turn_on_code not in", values, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeBetween(String value1, String value2) {
            addCriterion("turn_on_code between", value1, value2, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeNotBetween(String value1, String value2) {
            addCriterion("turn_on_code not between", value1, value2, "turnOnCode");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andSnLikeInsensitive(String value) {
            addCriterion("upper(sn) like", value.toUpperCase(), "sn");
            return (Criteria) this;
        }

        public Criteria andTurnOnCodeLikeInsensitive(String value) {
            addCriterion("upper(turn_on_code) like", value.toUpperCase(), "turnOnCode");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Aug 29 15:54:28 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        private QlyOrderSnExample example;

        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        protected Criteria(QlyOrderSnExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        public QlyOrderSnExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Aug 29 15:54:28 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Aug 29 15:54:28 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Aug 29 15:54:28 CST 2023
         */
        void example(QlyOrderSnExample example);
    }
}