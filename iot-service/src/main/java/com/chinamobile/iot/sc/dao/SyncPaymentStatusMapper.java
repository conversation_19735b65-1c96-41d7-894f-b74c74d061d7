package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.SyncPaymentStatus;
import com.chinamobile.iot.sc.pojo.SyncPaymentStatusExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SyncPaymentStatusMapper {
    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    long countByExample(SyncPaymentStatusExample example);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int deleteByExample(SyncPaymentStatusExample example);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int insert(SyncPaymentStatus record);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int insertSelective(SyncPaymentStatus record);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    List<SyncPaymentStatus> selectByExample(SyncPaymentStatusExample example);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    SyncPaymentStatus selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int updateByExampleSelective(@Param("record") SyncPaymentStatus record, @Param("example") SyncPaymentStatusExample example);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int updateByExample(@Param("record") SyncPaymentStatus record, @Param("example") SyncPaymentStatusExample example);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int updateByPrimaryKeySelective(SyncPaymentStatus record);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int updateByPrimaryKey(SyncPaymentStatus record);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int batchInsert(@Param("list") List<SyncPaymentStatus> list);

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    int batchInsertSelective(@Param("list") List<SyncPaymentStatus> list, @Param("selective") SyncPaymentStatus.Column ... selective);
}