package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14
 * @description 新增卡+X退货人员参数类
 */
@Data
public class AddUserRefundKxParam {

    /**
     * 用户id
     */
    @NotEmpty(message = "用户id不能为空")
    private String userId;

    /**
     * 用户名称
     */
    @NotEmpty(message = "用户名称不能为空")
    private String userName;

    /**
     * 用户电话
     */
    @NotEmpty(message = "用户电话不能为空")
    private String phone;

    /**
     * 角色id
     */
    @NotEmpty(message = "角色id不能为空")
    private String roleId;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    private String roleName;

    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer noticeType;
}
