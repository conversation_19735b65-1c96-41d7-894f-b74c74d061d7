package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardInventoryAtomInfoExample {
    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public CardInventoryAtomInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public CardInventoryAtomInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public CardInventoryAtomInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        CardInventoryAtomInfoExample example = new CardInventoryAtomInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public CardInventoryAtomInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public CardInventoryAtomInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIsNull() {
            addCriterion("card_inventory_main_id is null");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIsNotNull() {
            addCriterion("card_inventory_main_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdEqualTo(String value) {
            addCriterion("card_inventory_main_id =", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotEqualTo(String value) {
            addCriterion("card_inventory_main_id <>", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThan(String value) {
            addCriterion("card_inventory_main_id >", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanOrEqualTo(String value) {
            addCriterion("card_inventory_main_id >=", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThan(String value) {
            addCriterion("card_inventory_main_id <", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanOrEqualTo(String value) {
            addCriterion("card_inventory_main_id <=", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLike(String value) {
            addCriterion("card_inventory_main_id like", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotLike(String value) {
            addCriterion("card_inventory_main_id not like", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIn(List<String> values) {
            addCriterion("card_inventory_main_id in", values, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotIn(List<String> values) {
            addCriterion("card_inventory_main_id not in", values, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdBetween(String value1, String value2) {
            addCriterion("card_inventory_main_id between", value1, value2, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotBetween(String value1, String value2) {
            addCriterion("card_inventory_main_id not between", value1, value2, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNull() {
            addCriterion("atom_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNotNull() {
            addCriterion("atom_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualTo(String value) {
            addCriterion("atom_id =", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualTo(String value) {
            addCriterion("atom_id <>", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThan(String value) {
            addCriterion("atom_id >", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_id >=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThan(String value) {
            addCriterion("atom_id <", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualTo(String value) {
            addCriterion("atom_id <=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLike(String value) {
            addCriterion("atom_id like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotLike(String value) {
            addCriterion("atom_id not like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdIn(List<String> values) {
            addCriterion("atom_id in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotIn(List<String> values) {
            addCriterion("atom_id not in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdBetween(String value1, String value2) {
            addCriterion("atom_id between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotBetween(String value1, String value2) {
            addCriterion("atom_id not between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryIsNull() {
            addCriterion("atom_inventory is null");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryIsNotNull() {
            addCriterion("atom_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryEqualTo(Integer value) {
            addCriterion("atom_inventory =", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryNotEqualTo(Integer value) {
            addCriterion("atom_inventory <>", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryGreaterThan(Integer value) {
            addCriterion("atom_inventory >", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("atom_inventory >=", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryLessThan(Integer value) {
            addCriterion("atom_inventory <", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryLessThanOrEqualTo(Integer value) {
            addCriterion("atom_inventory <=", value, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomInventoryIn(List<Integer> values) {
            addCriterion("atom_inventory in", values, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryNotIn(List<Integer> values) {
            addCriterion("atom_inventory not in", values, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryBetween(Integer value1, Integer value2) {
            addCriterion("atom_inventory between", value1, value2, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andAtomInventoryNotBetween(Integer value1, Integer value2) {
            addCriterion("atom_inventory not between", value1, value2, "atomInventory");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNull() {
            addCriterion("offering_code is null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNotNull() {
            addCriterion("offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualTo(String value) {
            addCriterion("offering_code =", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualTo(String value) {
            addCriterion("offering_code <>", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThan(String value) {
            addCriterion("offering_code >", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_code >=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThan(String value) {
            addCriterion("offering_code <", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("offering_code <=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLike(String value) {
            addCriterion("offering_code like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotLike(String value) {
            addCriterion("offering_code not like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIn(List<String> values) {
            addCriterion("offering_code in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotIn(List<String> values) {
            addCriterion("offering_code not in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeBetween(String value1, String value2) {
            addCriterion("offering_code between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("offering_code not between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(CardInventoryAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLikeInsensitive(String value) {
            addCriterion("upper(card_inventory_main_id) like", value.toUpperCase(), "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLikeInsensitive(String value) {
            addCriterion("upper(atom_id) like", value.toUpperCase(), "atomId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(offering_code) like", value.toUpperCase(), "offeringCode");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated do_not_delete_during_merge Thu Dec 19 10:21:32 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..card_inventory_atom_info
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        private CardInventoryAtomInfoExample example;

        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        protected Criteria(CardInventoryAtomInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        public CardInventoryAtomInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Dec 19 10:21:32 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_atom_info
     *
     * @mbg.generated Thu Dec 19 10:21:32 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Dec 19 10:21:32 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfoExample example);
    }
}