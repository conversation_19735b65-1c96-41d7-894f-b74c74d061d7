package com.chinamobile.iot.sc.dto;


import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.request.order2c.OrderInfoDTO;
import com.chinamobile.iot.sc.request.order2c.SpuOfferingInfoDTO;

/**
 * H5_ProductOrderCreate埋点参数类
 * 用于收集埋点所需的参数，在status==0判断分支最后统一执行埋点
 */
public class H5ProductOrderCreateBurialPointParam {
    
    private OrderInfoDTO orderInfo;
    private SpuOfferingInfoDTO spuOfferingInfoDto;
    private com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO skuInfoDto;
    private String atomVersion;
    private AtomOfferingInfo originAtomInfo;
    private Order2cAtomInfo order2cAtomInfo;
    
    public H5ProductOrderCreateBurialPointParam() {
    }
    
    public H5ProductOrderCreateBurialPointParam(OrderInfoDTO orderInfo, 
            SpuOfferingInfoDTO spuOfferingInfoDto,
            com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO skuInfoDto, 
            String atomVersion,
            AtomOfferingInfo originAtomInfo, 
            Order2cAtomInfo order2cAtomInfo) {
        this.orderInfo = orderInfo;
        this.spuOfferingInfoDto = spuOfferingInfoDto;
        this.skuInfoDto = skuInfoDto;
        this.atomVersion = atomVersion;
        this.originAtomInfo = originAtomInfo;
        this.order2cAtomInfo = order2cAtomInfo;
    }
    
    public OrderInfoDTO getOrderInfo() {
        return orderInfo;
    }
    
    public void setOrderInfo(OrderInfoDTO orderInfo) {
        this.orderInfo = orderInfo;
    }
    
    public SpuOfferingInfoDTO getSpuOfferingInfoDto() {
        return spuOfferingInfoDto;
    }
    
    public void setSpuOfferingInfoDto(SpuOfferingInfoDTO spuOfferingInfoDto) {
        this.spuOfferingInfoDto = spuOfferingInfoDto;
    }
    
    public com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO getSkuInfoDto() {
        return skuInfoDto;
    }
    
    public void setSkuInfoDto(com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO skuInfoDto) {
        this.skuInfoDto = skuInfoDto;
    }
    
    public String getAtomVersion() {
        return atomVersion;
    }
    
    public void setAtomVersion(String atomVersion) {
        this.atomVersion = atomVersion;
    }
    
    public AtomOfferingInfo getOriginAtomInfo() {
        return originAtomInfo;
    }
    
    public void setOriginAtomInfo(AtomOfferingInfo originAtomInfo) {
        this.originAtomInfo = originAtomInfo;
    }
    
    public Order2cAtomInfo getOrder2cAtomInfo() {
        return order2cAtomInfo;
    }
    
    public void setOrder2cAtomInfo(Order2cAtomInfo order2cAtomInfo) {
        this.order2cAtomInfo = order2cAtomInfo;
    }
}
