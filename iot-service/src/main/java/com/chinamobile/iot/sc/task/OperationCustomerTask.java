package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.service.IOrder2CService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.ORDER_LOCK;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/4/27 15:45
 * @description: 定时任务拉取商城分销或注册用户数据解析
 **/
@Component
@Slf4j
public class OperationCustomerTask {

    @Resource
    private IOrder2CService iOrder2CService;

    @Autowired
    private RedisUtil redisUtil;

    private String lockKey = "OperationCustomerTask";
    /**
     * 每天9点过6分开始 每25分种从FTP服务器拉取一次客户经理的数据
     */
    @Scheduled(cron = "0 6/25 9 * * ?")
    @Async("asyncTask")
//    @Scheduled(cron = "0/30 * * * * ?") // 调试用
    public void downloadOperationAccountManagerFiles() {
        redisUtil.smartLock(ORDER_LOCK+lockKey,()->{
            log.info("拉取同步商城分销或注册用户数据定时任务开始。。。");
            iOrder2CService.sftpOperationCustomerData();
            log.info("拉取同步商城分销或注册用户数据定时任务结束。。。");
            return null;
        });
    }
}
