package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceTaskMapper {
    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    long countByExample(ProductFlowInstanceTaskExample example);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int deleteByExample(ProductFlowInstanceTaskExample example);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int insert(ProductFlowInstanceTask record);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int insertSelective(ProductFlowInstanceTask record);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    List<ProductFlowInstanceTask> selectByExample(ProductFlowInstanceTaskExample example);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    ProductFlowInstanceTask selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceTask record, @Param("example") ProductFlowInstanceTaskExample example);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int updateByExample(@Param("record") ProductFlowInstanceTask record, @Param("example") ProductFlowInstanceTaskExample example);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceTask record);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int updateByPrimaryKey(ProductFlowInstanceTask record);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceTask> list);

    /**
     *
     * @mbg.generated Fri Mar 15 14:24:01 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceTask> list, @Param("selective") ProductFlowInstanceTask.Column ... selective);
}