package com.chinamobile.iot.sc.response.web;

import com.chinamobile.iot.sc.pojo.dto.AfterServiceProductConfigDTO;
import com.chinamobile.iot.sc.pojo.dto.CardXProductConfigDTO;
import com.chinamobile.iot.sc.pojo.dto.ContractProductConfigDTO;
import com.chinamobile.iot.sc.pojo.dto.DICTProductConfigDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 批量导入商品配置结果
 */
@Data
public class ImportProductConfigResultDTO {
    /**联合销售、合同履约配置失败信息*/
    private List<ContractProductConfigDTO> contracts;

    /**DICT,OneNET,OnePark配置失败信息*/
    private List<DICTProductConfigDTO> dicts;

    /**售后服务配置失败信息*/
    private List<AfterServiceProductConfigDTO> afterServices;

    /**卡+X配置失败信息*/
    private List<CardXProductConfigDTO> cardxList;


}
