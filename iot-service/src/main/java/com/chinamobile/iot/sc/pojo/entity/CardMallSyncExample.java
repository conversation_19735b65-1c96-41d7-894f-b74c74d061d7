package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardMallSyncExample {
    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSyncExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSyncExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSyncExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        CardMallSyncExample example = new CardMallSyncExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSyncExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSyncExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdIsNull() {
            addCriterion("card_info_id is null");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdIsNotNull() {
            addCriterion("card_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdEqualTo(String value) {
            addCriterion("card_info_id =", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdNotEqualTo(String value) {
            addCriterion("card_info_id <>", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdGreaterThan(String value) {
            addCriterion("card_info_id >", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdGreaterThanOrEqualTo(String value) {
            addCriterion("card_info_id >=", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLessThan(String value) {
            addCriterion("card_info_id <", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLessThanOrEqualTo(String value) {
            addCriterion("card_info_id <=", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_info_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLike(String value) {
            addCriterion("card_info_id like", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdNotLike(String value) {
            addCriterion("card_info_id not like", value, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdIn(List<String> values) {
            addCriterion("card_info_id in", values, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdNotIn(List<String> values) {
            addCriterion("card_info_id not in", values, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdBetween(String value1, String value2) {
            addCriterion("card_info_id between", value1, value2, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdNotBetween(String value1, String value2) {
            addCriterion("card_info_id not between", value1, value2, "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIsNull() {
            addCriterion("card_inventory_main_id is null");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIsNotNull() {
            addCriterion("card_inventory_main_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdEqualTo(String value) {
            addCriterion("card_inventory_main_id =", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotEqualTo(String value) {
            addCriterion("card_inventory_main_id <>", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThan(String value) {
            addCriterion("card_inventory_main_id >", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanOrEqualTo(String value) {
            addCriterion("card_inventory_main_id >=", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThan(String value) {
            addCriterion("card_inventory_main_id <", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanOrEqualTo(String value) {
            addCriterion("card_inventory_main_id <=", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_inventory_main_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLike(String value) {
            addCriterion("card_inventory_main_id like", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotLike(String value) {
            addCriterion("card_inventory_main_id not like", value, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdIn(List<String> values) {
            addCriterion("card_inventory_main_id in", values, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotIn(List<String> values) {
            addCriterion("card_inventory_main_id not in", values, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdBetween(String value1, String value2) {
            addCriterion("card_inventory_main_id between", value1, value2, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdNotBetween(String value1, String value2) {
            addCriterion("card_inventory_main_id not between", value1, value2, "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(String value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(String value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(String value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(String value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(String value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(String value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLike(String value) {
            addCriterion("card_type like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotLike(String value) {
            addCriterion("card_type not like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<String> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<String> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(String value1, String value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(String value1, String value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNull() {
            addCriterion("msisdn is null");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNotNull() {
            addCriterion("msisdn is not null");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualTo(String value) {
            addCriterion("msisdn =", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualTo(String value) {
            addCriterion("msisdn <>", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThan(String value) {
            addCriterion("msisdn >", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualTo(String value) {
            addCriterion("msisdn >=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThan(String value) {
            addCriterion("msisdn <", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualTo(String value) {
            addCriterion("msisdn <=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("msisdn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLike(String value) {
            addCriterion("msisdn like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotLike(String value) {
            addCriterion("msisdn not like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnIn(List<String> values) {
            addCriterion("msisdn in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotIn(List<String> values) {
            addCriterion("msisdn not in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnBetween(String value1, String value2) {
            addCriterion("msisdn between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotBetween(String value1, String value2) {
            addCriterion("msisdn not between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andIccidIsNull() {
            addCriterion("iccid is null");
            return (Criteria) this;
        }

        public Criteria andIccidIsNotNull() {
            addCriterion("iccid is not null");
            return (Criteria) this;
        }

        public Criteria andIccidEqualTo(String value) {
            addCriterion("iccid =", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidNotEqualTo(String value) {
            addCriterion("iccid <>", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidGreaterThan(String value) {
            addCriterion("iccid >", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidGreaterThanOrEqualTo(String value) {
            addCriterion("iccid >=", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidLessThan(String value) {
            addCriterion("iccid <", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidLessThanOrEqualTo(String value) {
            addCriterion("iccid <=", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("iccid <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIccidLike(String value) {
            addCriterion("iccid like", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidNotLike(String value) {
            addCriterion("iccid not like", value, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidIn(List<String> values) {
            addCriterion("iccid in", values, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidNotIn(List<String> values) {
            addCriterion("iccid not in", values, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidBetween(String value1, String value2) {
            addCriterion("iccid between", value1, value2, "iccid");
            return (Criteria) this;
        }

        public Criteria andIccidNotBetween(String value1, String value2) {
            addCriterion("iccid not between", value1, value2, "iccid");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNull() {
            addCriterion("card_status is null");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNotNull() {
            addCriterion("card_status is not null");
            return (Criteria) this;
        }

        public Criteria andCardStatusEqualTo(String value) {
            addCriterion("card_status =", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusNotEqualTo(String value) {
            addCriterion("card_status <>", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThan(String value) {
            addCriterion("card_status >", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThanOrEqualTo(String value) {
            addCriterion("card_status >=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThan(String value) {
            addCriterion("card_status <", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThanOrEqualTo(String value) {
            addCriterion("card_status <=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("card_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardStatusLike(String value) {
            addCriterion("card_status like", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotLike(String value) {
            addCriterion("card_status not like", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusIn(List<String> values) {
            addCriterion("card_status in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotIn(List<String> values) {
            addCriterion("card_status not in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusBetween(String value1, String value2) {
            addCriterion("card_status between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotBetween(String value1, String value2) {
            addCriterion("card_status not between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNull() {
            addCriterion("atom_order_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNotNull() {
            addCriterion("atom_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualTo(String value) {
            addCriterion("atom_order_id =", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualTo(String value) {
            addCriterion("atom_order_id <>", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThan(String value) {
            addCriterion("atom_order_id >", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_order_id >=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThan(String value) {
            addCriterion("atom_order_id <", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualTo(String value) {
            addCriterion("atom_order_id <=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("atom_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLike(String value) {
            addCriterion("atom_order_id like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotLike(String value) {
            addCriterion("atom_order_id not like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIn(List<String> values) {
            addCriterion("atom_order_id in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotIn(List<String> values) {
            addCriterion("atom_order_id not in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdBetween(String value1, String value2) {
            addCriterion("atom_order_id between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotBetween(String value1, String value2) {
            addCriterion("atom_order_id not between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(CardMallSync.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCardInfoIdLikeInsensitive(String value) {
            addCriterion("upper(card_info_id) like", value.toUpperCase(), "cardInfoId");
            return (Criteria) this;
        }

        public Criteria andCardInventoryMainIdLikeInsensitive(String value) {
            addCriterion("upper(card_inventory_main_id) like", value.toUpperCase(), "cardInventoryMainId");
            return (Criteria) this;
        }

        public Criteria andCardTypeLikeInsensitive(String value) {
            addCriterion("upper(card_type) like", value.toUpperCase(), "cardType");
            return (Criteria) this;
        }

        public Criteria andMsisdnLikeInsensitive(String value) {
            addCriterion("upper(msisdn) like", value.toUpperCase(), "msisdn");
            return (Criteria) this;
        }

        public Criteria andIccidLikeInsensitive(String value) {
            addCriterion("upper(iccid) like", value.toUpperCase(), "iccid");
            return (Criteria) this;
        }

        public Criteria andCardStatusLikeInsensitive(String value) {
            addCriterion("upper(card_status) like", value.toUpperCase(), "cardStatus");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLikeInsensitive(String value) {
            addCriterion("upper(atom_order_id) like", value.toUpperCase(), "atomOrderId");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated do_not_delete_during_merge Fri Dec 06 09:49:55 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private CardMallSyncExample example;

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        protected Criteria(CardMallSyncExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public CardMallSyncExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri Dec 06 09:49:55 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample example);
    }
}