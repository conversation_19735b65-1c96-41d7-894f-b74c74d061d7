package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.address.PartnerAddress;
import com.chinamobile.iot.sc.request.address.Request4ParAddrAdd;
import com.chinamobile.iot.sc.request.address.Request4ParAddrPage;
import com.chinamobile.iot.sc.request.address.Request4ParAddrUpdate;
import com.chinamobile.iot.sc.response.web.invoice.Data4PartnerAddress;
import com.chinamobile.iot.sc.response.web.invoice.Data4PartnerAddressDetailVO;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IPartnerAddressService
 * @description: 合作伙伴地址管理Service
 * @author: zyj
 * @create: 2021/12/15 14:16
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IPartnerAddressService {

    BaseAnswer<Void> addPartnerAddress(Request4ParAddrAdd request, String partnerId, LoginIfo4Redis loginIfo4Redis,String ip);

    BaseAnswer<Void> deletePartnerAddress(String id, String partnerId);

    BaseAnswer<Void> updatePartnerAddress(Request4ParAddrUpdate request, String partnerId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<Data4PartnerAddress>> findPage(Request4ParAddrPage request, String partnerId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<String> isLastPAddress(String id, String partnerId);

    /**
     * 获取详情
     * @param id
     * @return
     */
    Data4PartnerAddressDetailVO partnerAddrById(String id);

}
