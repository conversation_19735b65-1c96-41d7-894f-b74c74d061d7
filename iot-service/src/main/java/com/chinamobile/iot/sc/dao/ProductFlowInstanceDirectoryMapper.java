package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectory;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceDirectoryMapper {
    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    long countByExample(ProductFlowInstanceDirectoryExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int deleteByExample(ProductFlowInstanceDirectoryExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int insert(ProductFlowInstanceDirectory record);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int insertSelective(ProductFlowInstanceDirectory record);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    List<ProductFlowInstanceDirectory> selectByExample(ProductFlowInstanceDirectoryExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    ProductFlowInstanceDirectory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceDirectory record, @Param("example") ProductFlowInstanceDirectoryExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int updateByExample(@Param("record") ProductFlowInstanceDirectory record, @Param("example") ProductFlowInstanceDirectoryExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceDirectory record);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int updateByPrimaryKey(ProductFlowInstanceDirectory record);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceDirectory> list);

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceDirectory> list, @Param("selective") ProductFlowInstanceDirectory.Column ... selective);
}