package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.dao.MessageMapper;
import com.chinamobile.iot.sc.dao.ext.MessageMapperExt;
import com.chinamobile.iot.sc.pojo.Message;
import com.chinamobile.iot.sc.pojo.MessageExample;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.service.impl.OneNetObjectStorageService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2023/9/5 17:35
 * 异步导出的订单文件，有效期3天，需要定期清理
 */
@Component
@Slf4j
public class ExpiredOrderExportFileDeteleTask {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MessageMapper messageMapper;

    @Resource
    private OneNetObjectStorageService storageService;

    @Resource
    private MessageMapperExt messageMapperExt;

    @Autowired
    private ServiceConfig serviceConfig;


    /**
     * 每天1点运行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void expiredOrderExportFileDeteleTask() {
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent("expiredOrderExportFileDeteleTask", "1",30, TimeUnit.SECONDS);
            if(getLock){
                log.info("expiredOrderExportFileDeteleTask删除过期订单导出excel任务开始。。。");
                //早于此时间的文件都要删除
                Date minCreateTime = DateTimeUtil.addDay(new Date(), 0-serviceConfig.getOrderExportExcelExpireDays());
                List<String> messageTypeList = new ArrayList<>();
                messageTypeList.add("订单导出");
                messageTypeList.add("运营数据-订单导出");
                messageTypeList.add("运营数据-商品导出");
                messageTypeList.add("整体概览-导出");
                //处理OS+大屏导出成功的消息
                MessageExample example = new MessageExample().createCriteria().andCreateTimeLessThan(minCreateTime).andFileKeyIsNotNull().andTypeIn(messageTypeList).example();
                List<Message> messages = messageMapper.selectByExample(example);
                List<String> fileKeyList = messages.stream().map(m -> {
                    return m.getFileKey();
                }).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(fileKeyList)){
                    storageService.delete(fileKeyList.toArray(new String[]{}));

                    List<String> messageIdList = messages.stream().map(m -> {
                        return m.getId();
                    }).collect(Collectors.toList());
                    //清除掉消息中的链接和文件key,更新消息内容,大屏的消息自动变为已读
                    messageMapperExt.clearUrlAndFile(messageIdList);
                }
                //处理大屏导出失败的消息，变为已读
                List<String> screenMessageTypeList = new ArrayList<>();
                screenMessageTypeList.add("运营数据-订单导出");
                screenMessageTypeList.add("运营数据-商品导出");
                screenMessageTypeList.add("整体概览-导出");
                MessageExample screenExample = new MessageExample().createCriteria().andCreateTimeLessThan(minCreateTime).andSourceEqualTo(2).andTypeIn(messageTypeList).andContentLike("%失败%").example();
                Message message = new Message();
                message.setIsread(true);
                messageMapper.updateByExampleSelective(message,screenExample);

                log.info("expiredOrderExportFileDeteleTask删除过期订单导出excel任务结束。。。");
            }
        } catch (Exception e) {
            log.error("expiredOrderExportFileDeteleTask删除过期订单导出excel任务发生异常",e);
        }
    }
}
