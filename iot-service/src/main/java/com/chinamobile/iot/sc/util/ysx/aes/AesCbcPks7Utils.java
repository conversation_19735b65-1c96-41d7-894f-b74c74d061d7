/*
 * @filename: AesCbcPks7Utils
 * @All Right Reserved (C), 2019-2106, HUAWEI TECO CO
 * @brief: 7.16	AES加解密工具类
 * @author: mWX650578
 * @description: 7.16	AES加解密工具类
 * @remark: created by 美合/mWX650578 at 2020/11/19 for NGCRM XSFW V200R005C20L86AD2 OR_NX_202011_46 【集团需求】关于一证五号系统发布接口规范V3.5.0
 */
package com.chinamobile.iot.sc.util.ysx.aes;


import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import java.security.*;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * 7.16	AES加解密工具类
 */
public class AesCbcPks7Utils {

    static {
        //导入支持AES/CBC/PKCS7Padding的Provider
        //解决 java.security.NoSuchProviderException: No such provider: BC
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 固定的AES密钥KEY
     */
    public static final String sessionKey = "";

    /**
     * 固定AES密钥的IV
     */
    public static final String initVector = "";

    /**
     * 加密字符串
     *
     * @param ctx    需要加密的原始字符串
     * @param aesKey aesKey
     * @param aesIv  aesIv
     * @return 加密后的16字符串
     */
    public static String encrypt(String ctx, String aesKey, String aesIv) throws Exception {
        //增加字符集，避免中文解密乱码问题
        return byteToHexString(encrypt(ctx.getBytes("UTF-8"), aesKey, aesIv));

    }

    /**
     * 加密字节数组
     *
     * @param data   需要加密的字节数组
     * @param aesKey aesKey
     * @param aesIv  aesIv
     * @return 加密后的字节数组
     */
    public static byte[] encrypt(byte[] data, String aesKey, String aesIv) throws Exception {
        //指定算法，模式，填充方式，创建一个Cipher
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        //生成Key对象
        Key sKeySpec = new SecretKeySpec(aesKey.getBytes(), "AES");
        //把向量初始化到算法参数
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(aesIv.getBytes()));
        //指定用途，密钥，参数 初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, sKeySpec, params);
        //指定加密
        return cipher.doFinal(data);
    }

    /**
     * 解密字节数组
     *
     * @param encryptedData 加密的字节数组
     * @param aesKey        AesKey
     * @param aesIv         aesIv
     * @return 解密后的字节数组
     */
    public static byte[] decrypt(byte[] encryptedData, String aesKey, String aesIv)
            throws Exception {
        //指定算法，模式，填充方法 创建一个Cipher实例
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        //生成Key对象
        Key sKeySpec = new SecretKeySpec(aesKey.getBytes(), "AES");
        //把向量初始化到算法参数
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(aesIv.getBytes()));
        //指定用途，密钥，参数 初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, sKeySpec, params);
        //执行解密
        return cipher.doFinal(encryptedData);
    }

    /**
     * 解密字符串
     *
     * @param ctx    需要解密的字符串
     * @param aesKey aesKey
     * @param aesIv  aesIv
     * @return 返回解密后的数据
     */
    public static String decrypt(String ctx, String aesKey, String aesIv) throws Exception {
        return new String(decrypt(Hex.decode(ctx), aesKey, aesIv));
    }

    /**
     * 将BYTE转换为字符串 0－9，A－F的16进制字符串
     *
     * @param bytes
     * @return
     */
    public static String byteToHexString(byte[] bytes) throws Exception {
        byte[] hexBytes = new byte[bytes.length * 2];

        byte byTemp;
        for (int i = 0; i < bytes.length; i++) {
            byTemp = (byte) (bytes[i] & 0xF0);// 字节低四位清零
            byTemp >>= 4;// 右移四位
            byTemp &= 0x0F;// 高四位清零
            if (byTemp <= 9) {
                // [00110000]=[48]=[0x30]='0'
                // 根据字节值转换为ascii字符
                hexBytes[2 * i] = (byte) (byTemp + 0x30);
            } else {
                hexBytes[2 * i] = (byte) (byTemp + 55);
            }

            byTemp = (byte) (bytes[i] & 0x0F);// 字节高四位清零
            if (byTemp <= 9) {
                hexBytes[2 * i + 1] = (byte) (byTemp + 0x30);
            } else {
                hexBytes[2 * i + 1] = (byte) (byTemp + 55);
            }
        }
        return new String(hexBytes);
    }

    /**
     * 随机生成秘钥和iv
     */

    public static Map<String,String> getKeyAndIv(){
        Map<String,String> result = new LinkedHashMap<>();
        result.put("key",getRandom(16));
        result.put("iv",getRandom(16));
        return result;
    }

    public static String getRandom(int length){
        char[] arr = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k',
                'l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'};
        String result = String.valueOf(arr[(int)Math.floor(Math.random()*36)]);
        for(int i = 1;i<length;i++){
            result+=arr[(int)Math.floor(Math.random()*36)];
        }
        return result;
    }


    public static void main(String[] args) throws Exception {
        System.out.println(encrypt("qwqwqwqw", "87E7917AC5E60589", "0770DE88E383B447"));
        System.out.println(decrypt("F230BA005A33EB231E90052F5E1D7598", "87E7917AC5E60589", "0770DE88E383B447"));
    }
}