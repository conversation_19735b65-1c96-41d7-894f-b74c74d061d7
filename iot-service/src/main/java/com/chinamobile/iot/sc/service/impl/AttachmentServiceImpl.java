package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.QueryResult;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.service.IAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/17 11:16
 * @description TODO
 */
@Service
@Slf4j
public class AttachmentServiceImpl implements IAttachmentService {
    @Override
    public void downloadAttachment(String filename, HttpServletResponse response) {
        log.info("downloadAttachment filename = {}",filename);
        if("..".equals(filename)){
            throw new BusinessException("500","不支持下载!");
        }

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + filename);
        try {
            filename = URLDecoder.decode(filename, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码错误", e);
        }
        ClassPathResource classPathResource = new ClassPathResource("excel/" + filename);
        try {
            try (InputStream inputStream = classPathResource.getInputStream(); OutputStream out = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
                response.flushBuffer();
            }
        } catch (IOException e) {
            log.error("文件下载异常", e);
        }
    }
}
