package com.chinamobile.iot.sc.pojo.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig;
import lombok.Data;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/29 15:53
 * 标准类 合同履约产品上架excel参数
 */
@Data
public class StandardProductSheflContractParam {

    @ExcelProperty("商品上架类目")
    private String shelfCatagoryName;

    @ExcelProperty("一级导航目录")
    private String firstDirectoryName;

    @ExcelProperty("二级导航目录")
    private String secondDirectoryName;

    @ExcelProperty("三级导航目录")
    private String thirdDirectoryName;

    @ExcelProperty("商品名称（SPU）")
    private String spuName;

    @ExcelProperty("服务商")
    private String spuServiceProvider;

    @ExcelProperty("产品经理")
    private String manager;

    @ExcelProperty("售后订单管理员信息")
    private String aftermarketAdminInfo;

    @ExcelProperty("典型应用领域")
    private String applicationArea;

    @ExcelProperty("商品简介")
    private String productDesc;

    @ExcelProperty("是否隐秘上架")
    private String isHiddenShelf;

    @ExcelProperty("映射检索词")
    private String searchWord;

    @ExcelProperty("销售标签")
    private String saleTag;

    @ExcelProperty("商品规格名称")
    private String skuName;

    @ExcelProperty("商品规格简称")
    private String skuShortName;

    @ExcelProperty("省公司价格")
    private String provincePriceYuan;

    @ExcelProperty("核心部件名称")
    private String keyCompomentName;

    @ExcelProperty("核心部件及服务内容")
    private String keyComponentServiceInfo;

    @ExcelProperty("配送范围")
    private String deliveryRange;

    @ExcelProperty("游客可见")
    private String touristPartnerVisible;

    @ExcelProperty("发布订购范围")
    private String saleProvinceCity;

    @ExcelProperty("硬件原子商品名称")
    private String atomName;

    @ExcelProperty("硬件结算单价")
    private String settlePriceYuan;

    @ExcelProperty("硬件原子计量单位")
    private String unit;

    @ExcelProperty("硬件原子数量")
    private String atomQuantityStr;

    @ExcelProperty("颜色")
    private String color;

    @ExcelProperty("终端物料编码")
    private String materialNum;

    @ExcelProperty("型号")
    private String model;

    @ExcelProperty("硬件原子服务内容")
    private String serviceContent;

    @ExcelProperty("软件原子商品名称")
    private String softAtomName;

    @ExcelProperty("软件结算单价")
    private String softSettlePriceYuan;

    @ExcelProperty("软件原子计量单位")
    private String softUnit;

    @ExcelProperty("软件原子数量")
    private String softQuantityStr;

    @ExcelProperty("软件平台商品编码")
    private String softProductCode;

    @ExcelProperty("不需结算")
    private String noSettlement;

    @ExcelProperty("结算明细服务名称")
    private String settlementDetailName;

    @ExcelProperty("交付周期")
    private String deliverPeriod;

    @ExcelProperty("软件原子服务内容")
    private String softServiceContent;

    @ExcelProperty("库存数")
    private String inventoryStr;

    @ExcelProperty("标准服务名称-OS系统配置项")
    private String standardServiceName;

    @ExcelProperty("实际产品名称-OS系统配置项")
    private String realProductName;

    @ExcelProperty("产品属性-OS系统配置项")
    private String productProperty;

    @ExcelProperty("产品部门-OS系统配置项")
    private String productDepartment;

    @ExcelProperty("供应商名称")
    private String serviceProviderName;

    @ExcelProperty("商城订单处理人（主）")
    private String orderPartnerMasterAccount;

    @ExcelProperty("商城订单处理人（次）")
    private String orderPartnerSlaveAccount;

}
