package com.chinamobile.iot.sc.pojo.vo.productFlowInfo;

import lombok.Data;

@Data
public class ShelfSpuDetailAtomVO {
    private String id;

    /**
     * 原子商品名称或硬件原子商品名称
     */
    private String atomName;

    /**
     * 销售价格，单位厘
     */
    private Long salePrice;

    /**
     * 销售最低价，单位厘
     */
    private Long saleMinPrice;

    /**
     * 销售最高价，单位厘
     */
    private Long saleMaxPrice;

    /**
     * 是否允许价格区间外销售
     */
    private String saleOutOfPriceRange;


    /**
     * 结算单价或硬件结算单价或省-专结算单价，单位厘
     */
    private Long settlePrice;

    /**
     * 结算单价核对，单位厘
     */
    private Long settlePriceCheck;

    /**
     * 计量单位或硬件计量单位
     */
    private String unit;

    /**
     * 服务内容
     */
    private String serviceContent;

    /**
     * CMIOT账目项id
     */
    private String cmiotCostProjectId;

    /**
     * CMIOT账目项名称
     */
    private String cmiotCostProjectName;

    /**
     * 订购数量最小值
     */
    private Integer minPurchaseNum;

    /**
     * 省公司采购合同信息
     */
    private String provincePurchaseContract;

    /**
     * 物联网公司采购合同信息
     */
    private String iotPurchaseContract;

    /**
     * 物料编码
     */
    private String materialNum;

    /**
     * 原子信息备注
     */
    private String atomRemark;

    /**
     * 数量
     */
    private Integer atomQuantity;

    /**
     * 颜色
     */
    private String color;

    /**
     * 型号
     */
    private String model;

    /**
     * 硬件原子商品销售价格，单位厘
     */
    private Long hardwarePrice;

    /**
     * 软件原子商品名称
     */
    private String softAtomName;

    /**
     * 软件结算单价，单位厘
     */
    private Long softSettlePrice;

    /**
     * 软件计量单位
     */
    private String softUnit;

    /**
     * 软件功能费商品数量
     */
    private Integer softQuantity;

    /**
     * 软件平台商品编码
     */
    private String softProductCode;

    /**
     * 不需结算
     */
    private String noSettlement;

    /**
     * 结算明细服务名称
     */
    private String settlementDetailName;

    /**
     * 交付周期
     */
    private String deliverPeriod;

    /**
     * 软件服务内容
     */
    private String softServiceContent;

    /**
     * 软件功能费销售单价，单位厘
     */
    private Long softPrice;

    /**
     * 软件功能费商品销售价格，单位厘
     */
    private Long softTotalPrice;

    /**
     * （专-合）结算单价
     */
    private Long zhuanheSettlePrice;

    /**
     * 服务包名称
     */
    private String servicePackageName;

    /**
     * 服务产品合同信息（销售侧）
     */
    private String serviceContract;

    /**
     * 库存数
     */
    private Integer inventory;

}
