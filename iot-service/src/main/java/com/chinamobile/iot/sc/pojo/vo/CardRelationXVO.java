package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.pojo.dto.ImportNewCardRelationDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9
 * @description
 */
@Data
public class CardRelationXVO {

    private String id;

    /**
     * 设备型号
     */
    private String deviceVersion;

    /**
     * 终端类型
     */
    private String terminalTypeName;

    /**
     * 终端IMEI
     */
    private String imei;

    /**
     * 临时iccid
     */
    private String tempIccid;

    /**
     * 码号
     */
    private String msisdn;

    /**
     * 开卡模版名称
     */
    private String templateName;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 地市
     */
    private String cityName;

    /**
     * 销售状态
     */
    private String sellStatus;

    /**
     * 销售状态
     */
    private String sellStatusName;

    /**
     * 导入批次
     */
    private String importNum;

    /**
     * 操作人
     */
    private String createdUserName;

    /**
     * 导入日期
     */
    private String createTimeStr;
}
