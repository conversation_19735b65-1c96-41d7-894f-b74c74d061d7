package com.chinamobile.iot.sc.pojo.mapper;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 汇总结算单行信息
 **/
@Data
public class SupplyChainIotLineInfoDO {


    /**
     * 含税单价
     */
    private BigDecimal discountedUnitPrice;

    /**
     * 不含税金额合计
     */
    private BigDecimal lineAmount;

    /**
     * 含税金额合计
     */
    private BigDecimal lineAmountTax;

    /**
     * 行号
     */
    private String lineNum;

    /**
     * 行税额
     */
    private BigDecimal lineTax;

    /**
     * 物料编码
     */
    private String materialsCode;

    /**
     * 物料描述
     */
    private String materialsDesc;

    /**
     * 数量
     */
    private BigDecimal materialsNumber;

    /**
     * 物料单位
     */
    private String materialsUnit;

    /**
     * 配件编码
     */
    private String offerItemCode;

    /**
     * 配件名称
     */
    private String offerItemName;

    /**
     * 套编码
     */
    private String offerSetCode;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 不含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 更新日期
     */
    private Date lastUpdateDate;


    /**
     * 单行成本费用分摊明细信息
     */
    private List<SupplyChainIotCostAllocDetailsDO> scmIotCostAllocDetails;

}
