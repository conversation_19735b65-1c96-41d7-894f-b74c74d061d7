package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.dao.AtomOfferingInfoMapper;
import com.chinamobile.iot.sc.dao.DkcardxInventoryAtomInfoMapper;
import com.chinamobile.iot.sc.dao.DkcardxInventoryDetailInfoMapper;
import com.chinamobile.iot.sc.dao.ext.DkcardxInventoryDetailInfoMapperExt;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfo;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfoExample;
import com.chinamobile.iot.sc.pojo.dto.AtomDetailInventoryDTO;
import com.chinamobile.iot.sc.pojo.dto.CardRelationImportInfoDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.InventoryDetailCountDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample;
import com.chinamobile.iot.sc.pojo.param.CardRelationXParam;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryCardDetailParam;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryDetailInfoParam;
import com.chinamobile.iot.sc.service.DkcardxInventoryAtomInfoService;
import com.chinamobile.iot.sc.service.DkcardxInventoryDetailInfoService;
import com.chinamobile.iot.sc.service.DkcardxInventoryMainInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 * @description X终端库存信息详情service实现类
 */
@Service
@Slf4j
public class DkcardxInventoryDetailInfoServiceImpl implements DkcardxInventoryDetailInfoService {

    @Resource
    private DkcardxInventoryDetailInfoMapper dkcardxInventoryDetailInfoMapper;

    @Resource
    private DkcardxInventoryMainInfoService dkcardxInventoryMainInfoService;

    @Resource
    private DkcardxInventoryDetailInfoMapperExt dkcardxInventoryDetailInfoMapperExt;

    @Resource
    private DkcardxInventoryAtomInfoService dkcardxInventoryAtomInfoService;

    @Resource
    private DkcardxInventoryAtomInfoMapper dkcardxInventoryAtomInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Resource
    private UserFeignClient userFeignClient;

    @Override
    public void batchInsertDkcardxInventoryDetailInfo(List<DkcardxInventoryDetailInfo> detailInfoList) {
        dkcardxInventoryDetailInfoMapper.batchInsert(detailInfoList);
    }

    @Override
    public PageData<DkcardxInventoryCardDetailInfoDTO> listDkcardxInventoryCardDetailInfo(DkcardxInventoryCardDetailParam cardDetailParam,
                                                                                          LoginIfo4Redis loginIfo4Redis) {


        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        String locationParam = cardDetailParam.getLocation();
        if ("省级".equals(locationParam)){
            cardDetailParam.setLocation("-1");
        }

        handleDkcardxInventoryCardDetailParam(cardDetailParam,loginIfo4Redis);

        PageData<DkcardxInventoryCardDetailInfoDTO> pageData = dkcardxInventoryMainInfoService.pageCardRelationByInventory(cardDetailParam);
        List<DkcardxInventoryCardDetailInfoDTO> cardDetailInfoDTOList = pageData.getData();
        if (CollectionUtils.isNotEmpty(cardDetailInfoDTOList)) {
            cardDetailInfoDTOList.stream().forEach(cardDetailInfoDTO -> {
                cardDetailInfoDTO.setSellStatusName(SellStatusEnum.getDescByType(cardDetailInfoDTO.getSellStatus()));
                String location = cardDetailInfoDTO.getLocation();
                if (StringUtils.isEmpty(location)) {
                    cardDetailInfoDTO.setLocationName("省级");
                } else {
                    cardDetailInfoDTO.setLocationName(locationCodeNameMap.get(location) + "");
                }
            });
            pageData.setData(cardDetailInfoDTOList);
        }

        return pageData;
    }

    @Override
    public List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfo(DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam,
                                                                           LoginIfo4Redis loginIfo4Redis) {
        handleDkcardxInventoryDetailInfoParam(dkcardxInventoryDetailInfoParam,loginIfo4Redis);

        return dkcardxInventoryDetailInfoMapperExt.listDkcardxInventoryDetailInfo(dkcardxInventoryDetailInfoParam);
    }

    @Override
    public List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfoExport(DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam, LoginIfo4Redis loginIfo4Redis) {

        return dkcardxInventoryDetailInfoMapperExt.listDkcardxInventoryDetailInfo(dkcardxInventoryDetailInfoParam);
    }

    @Override
    public List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfoByNeed(DkcardxInventoryDetailInfoExample detailInfoExample) {
        return dkcardxInventoryDetailInfoMapper.selectByExample(detailInfoExample);
    }

    private void handleDkcardxInventoryDetailInfoParam(DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam,
                                      LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
        String location = dkcardxInventoryDetailInfoParam.getLocation();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);

            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser) {

            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && isProvinceUser) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {

                } else {
                    if (StringUtils.isEmpty(location)){
                        dkcardxInventoryDetailInfoParam.setLocation(userLocation);
                    }
                }

            }
        }
    }


    private void handleDkcardxInventoryCardDetailParam(DkcardxInventoryCardDetailParam dkcardxInventoryCardDetailParam,
                                      LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);

            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser) {

            }

            String location = dkcardxInventoryCardDetailParam.getLocation();
            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && isProvinceUser) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {

                } else {
                    dkcardxInventoryCardDetailParam.setCurrentLocation(userLocation);
                }

            }
        }
    }

    @Override
    public void updateDkcardxInventoryDetailInfoById(DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo) {
        dkcardxInventoryDetailInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryDetailInfo);
    }

    @Override
    public DkcardxInventoryDetailInfo getDkcardxInventoryDetailInfoById(String id) {
        return dkcardxInventoryDetailInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleKxImportHistoryToInventoryAtom() {
        // 获取原子商品绑定的库存信息
        List<AtomDetailInventoryDTO> atomDetailInventoryDTOList = dkcardxInventoryAtomInfoService.listAtomDetailInventory();
        if (CollectionUtils.isNotEmpty(atomDetailInventoryDTOList)){
            Map<String,InventoryDetailCountDTO> detailCountDTOMap = new HashMap<>();
            List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfoList = new ArrayList<>();
            Date date = new Date();
            atomDetailInventoryDTOList.forEach(atomDetailInventoryDTO -> {
                String atomId = atomDetailInventoryDTO.getAtomId();
                String inventoryMainId = atomDetailInventoryDTO.getInventoryMainId();
                Integer detailCount = atomDetailInventoryDTO.getDetailCount();

                InventoryDetailCountDTO inventoryDetailCountDTO = null;
                if (detailCountDTOMap.containsKey(inventoryMainId)){
                    inventoryDetailCountDTO = detailCountDTOMap.get(inventoryMainId);
                }else{// 获取设备型号绑定的库存详情数量
                    inventoryDetailCountDTO = dkcardxInventoryDetailInfoMapperExt.getInventoryDetailCount(inventoryMainId);
                    if (!Optional.ofNullable(inventoryDetailCountDTO).isPresent()){
                        throw new BusinessException("10004","卡+X终端库存主要信息表id为".concat(inventoryMainId).concat("未找到库存详情信息"));
                    }
                    detailCountDTOMap.put(inventoryMainId,inventoryDetailCountDTO);
                }
                // 如果原子商品绑定的库存详情数量不等于设备型号对应的库存数量，
                // 则需要把没有在原子商品库存绑定表中的数据绑定到原子商品库存关系中
                if (inventoryDetailCountDTO.getDetailCount() != detailCount) {
                    List<String> detailIdList
                            = dkcardxInventoryDetailInfoMapperExt.listNotBindIdToAtom(inventoryMainId, atomId);
                    if (CollectionUtils.isNotEmpty(detailIdList)){
                        // 获取商品和型号的绑定关系
                        DkcardxInventoryAtomInfoExample dkcardxInventoryAtomInfoExample = new DkcardxInventoryAtomInfoExample();
                        dkcardxInventoryAtomInfoExample.createCriteria()
                                        .andAtomIdEqualTo(atomId)
                                        .andInventoryMainIdEqualTo(inventoryMainId);
                        DkcardxInventoryAtomInfo inventoryAtomInfo
                                = dkcardxInventoryAtomInfoService.getInventoryAtomInfoByExample(dkcardxInventoryAtomInfoExample)
                                .get(0);

                        // 添加未绑定的库存详情到原子商品上面
                        detailIdList.forEach(detailId->{
                            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = new DkcardxInventoryAtomInfo();
                            BeanUtils.copyProperties(inventoryAtomInfo,dkcardxInventoryAtomInfo);
                            dkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
                            dkcardxInventoryAtomInfo.setAtomInventory(0l);
                            dkcardxInventoryAtomInfo.setInventoryDetailId(detailId);
                            dkcardxInventoryAtomInfo.setCreateTime(date);
                            dkcardxInventoryAtomInfo.setUpdateTime(date);
                            dkcardxInventoryAtomInfoList.add(dkcardxInventoryAtomInfo);
                        });
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(dkcardxInventoryAtomInfoList)){
                dkcardxInventoryAtomInfoService.batchAddInventoryAtomInfo(dkcardxInventoryAtomInfoList);
            }
        }
    }

    @Override
    public void handleDkcardxInventoryDetailInfo(String detailId,String inventoryAtomId,Integer reserveQuatity,Integer currentInventory,Integer totalInventory,Long atomInventory) {
        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfoMapper.selectByPrimaryKey(detailId);
        if (reserveQuatity !=null){
            Integer reserveQuatityOld = dkcardxInventoryDetailInfo.getReserveQuatity();
            dkcardxInventoryDetailInfo.setReserveQuatity(reserveQuatityOld+reserveQuatity);
        }
        if (currentInventory!=null){
            Integer currentInventoryOld = dkcardxInventoryDetailInfo.getCurrentInventory();
            dkcardxInventoryDetailInfo.setCurrentInventory(currentInventoryOld+currentInventory);
        }
        if (totalInventory!=null){
            Integer totalInventoryOld = dkcardxInventoryDetailInfo.getTotalInventory();
            dkcardxInventoryDetailInfo.setTotalInventory(totalInventoryOld+totalInventory);
        }
        dkcardxInventoryDetailInfo.setUpdateTime(new Date());
        dkcardxInventoryDetailInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryDetailInfo);
        //更新终端库存和原子关联库存表
        DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfoMapper.selectByPrimaryKey(inventoryAtomId);
        if (atomInventory!=null){
            Long atomInventoryOld = dkcardxInventoryAtomInfo.getAtomInventory();
            dkcardxInventoryAtomInfo.setAtomInventory(atomInventoryOld+atomInventory);
            dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryAtomInfo);
        }
    }

    @Override
    public void delDkcardInventoryDetail(String id) {
        int count = dkcardxInventoryDetailInfoMapper.deleteByExample(new DkcardxInventoryDetailInfoExample().createCriteria().andIdEqualTo(id).example());
        log.info("delDkcardInventoryDetail id = {}, count = {}",id,count);
    }
}
