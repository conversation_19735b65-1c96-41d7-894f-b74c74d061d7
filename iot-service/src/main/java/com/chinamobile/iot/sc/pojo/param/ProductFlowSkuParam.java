package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 * @description 产品流程sku已上架的参数查询实例
 */
@Data
public class ProductFlowSkuParam {

    private String spuCode;

    private String skuCode;

    private String skuName;

    /**
     * 多条件查询
     */
    private String spuSkuCodeOrName;

    /**
     * 运营类型 1-分省运营类 2-统一运营类
     */
    @NotNull(message = "运营类型不能为空")
    private Integer operateType;

    /**
     * 流程状态 0-进行中 1-结束 2-废止
     */
    private Integer flowInstanceStatus;

    /**
     * 上下架状态 1-上架中 2-上架取消 3-已上架 4-下架中 5-下架取消 6-已下架
     */
    private List<Integer> shelfStatusList;

    /**
     * 流程类型 1-产品上架 2-产品下架 3-销售价变更 4-结算价变更 5-非价格信息变更 6-商品所有信息变更(仅限统一运营类)
     */
    private List<Integer> flowTypeList;

}
