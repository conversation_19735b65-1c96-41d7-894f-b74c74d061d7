package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardRelationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CardRelationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public CardRelationExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public CardRelationExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        CardRelationExample example = new CardRelationExample();
        return example.createCriteria();
    }

    public CardRelationExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public CardRelationExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImeiIsNull() {
            addCriterion("imei is null");
            return (Criteria) this;
        }

        public Criteria andImeiIsNotNull() {
            addCriterion("imei is not null");
            return (Criteria) this;
        }

        public Criteria andImeiEqualTo(String value) {
            addCriterion("imei =", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiNotEqualTo(String value) {
            addCriterion("imei <>", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThan(String value) {
            addCriterion("imei >", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanOrEqualTo(String value) {
            addCriterion("imei >=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLessThan(String value) {
            addCriterion("imei <", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLessThanOrEqualTo(String value) {
            addCriterion("imei <=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("imei <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLike(String value) {
            addCriterion("imei like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotLike(String value) {
            addCriterion("imei not like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiIn(List<String> values) {
            addCriterion("imei in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotIn(List<String> values) {
            addCriterion("imei not in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiBetween(String value1, String value2) {
            addCriterion("imei between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotBetween(String value1, String value2) {
            addCriterion("imei not between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andTempIccidIsNull() {
            addCriterion("temp_iccid is null");
            return (Criteria) this;
        }

        public Criteria andTempIccidIsNotNull() {
            addCriterion("temp_iccid is not null");
            return (Criteria) this;
        }

        public Criteria andTempIccidEqualTo(String value) {
            addCriterion("temp_iccid =", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidNotEqualTo(String value) {
            addCriterion("temp_iccid <>", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThan(String value) {
            addCriterion("temp_iccid >", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanOrEqualTo(String value) {
            addCriterion("temp_iccid >=", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThan(String value) {
            addCriterion("temp_iccid <", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanOrEqualTo(String value) {
            addCriterion("temp_iccid <=", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("temp_iccid <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLike(String value) {
            addCriterion("temp_iccid like", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotLike(String value) {
            addCriterion("temp_iccid not like", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidIn(List<String> values) {
            addCriterion("temp_iccid in", values, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotIn(List<String> values) {
            addCriterion("temp_iccid not in", values, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidBetween(String value1, String value2) {
            addCriterion("temp_iccid between", value1, value2, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotBetween(String value1, String value2) {
            addCriterion("temp_iccid not between", value1, value2, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andClientNameIsNull() {
            addCriterion("client_name is null");
            return (Criteria) this;
        }

        public Criteria andClientNameIsNotNull() {
            addCriterion("client_name is not null");
            return (Criteria) this;
        }

        public Criteria andClientNameEqualTo(String value) {
            addCriterion("client_name =", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameNotEqualTo(String value) {
            addCriterion("client_name <>", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThan(String value) {
            addCriterion("client_name >", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanOrEqualTo(String value) {
            addCriterion("client_name >=", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLessThan(String value) {
            addCriterion("client_name <", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanOrEqualTo(String value) {
            addCriterion("client_name <=", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("client_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLike(String value) {
            addCriterion("client_name like", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotLike(String value) {
            addCriterion("client_name not like", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameIn(List<String> values) {
            addCriterion("client_name in", values, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotIn(List<String> values) {
            addCriterion("client_name not in", values, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameBetween(String value1, String value2) {
            addCriterion("client_name between", value1, value2, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotBetween(String value1, String value2) {
            addCriterion("client_name not between", value1, value2, "clientName");
            return (Criteria) this;
        }

        public Criteria andProductNumIsNull() {
            addCriterion("product_num is null");
            return (Criteria) this;
        }

        public Criteria andProductNumIsNotNull() {
            addCriterion("product_num is not null");
            return (Criteria) this;
        }

        public Criteria andProductNumEqualTo(String value) {
            addCriterion("product_num =", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumNotEqualTo(String value) {
            addCriterion("product_num <>", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumGreaterThan(String value) {
            addCriterion("product_num >", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumGreaterThanOrEqualTo(String value) {
            addCriterion("product_num >=", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumLessThan(String value) {
            addCriterion("product_num <", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumLessThanOrEqualTo(String value) {
            addCriterion("product_num <=", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("product_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNumLike(String value) {
            addCriterion("product_num like", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumNotLike(String value) {
            addCriterion("product_num not like", value, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumIn(List<String> values) {
            addCriterion("product_num in", values, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumNotIn(List<String> values) {
            addCriterion("product_num not in", values, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumBetween(String value1, String value2) {
            addCriterion("product_num between", value1, value2, "productNum");
            return (Criteria) this;
        }

        public Criteria andProductNumNotBetween(String value1, String value2) {
            addCriterion("product_num not between", value1, value2, "productNum");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIsNull() {
            addCriterion("order_atom_info_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIsNotNull() {
            addCriterion("order_atom_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdEqualTo(String value) {
            addCriterion("order_atom_info_id =", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotEqualTo(String value) {
            addCriterion("order_atom_info_id <>", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThan(String value) {
            addCriterion("order_atom_info_id >", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_atom_info_id >=", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThan(String value) {
            addCriterion("order_atom_info_id <", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanOrEqualTo(String value) {
            addCriterion("order_atom_info_id <=", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLike(String value) {
            addCriterion("order_atom_info_id like", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotLike(String value) {
            addCriterion("order_atom_info_id not like", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIn(List<String> values) {
            addCriterion("order_atom_info_id in", values, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotIn(List<String> values) {
            addCriterion("order_atom_info_id not in", values, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdBetween(String value1, String value2) {
            addCriterion("order_atom_info_id between", value1, value2, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotBetween(String value1, String value2) {
            addCriterion("order_atom_info_id not between", value1, value2, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNull() {
            addCriterion("msisdn is null");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNotNull() {
            addCriterion("msisdn is not null");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualTo(String value) {
            addCriterion("msisdn =", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualTo(String value) {
            addCriterion("msisdn <>", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThan(String value) {
            addCriterion("msisdn >", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualTo(String value) {
            addCriterion("msisdn >=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThan(String value) {
            addCriterion("msisdn <", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualTo(String value) {
            addCriterion("msisdn <=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("msisdn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLike(String value) {
            addCriterion("msisdn like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotLike(String value) {
            addCriterion("msisdn not like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnIn(List<String> values) {
            addCriterion("msisdn in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotIn(List<String> values) {
            addCriterion("msisdn not in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnBetween(String value1, String value2) {
            addCriterion("msisdn between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotBetween(String value1, String value2) {
            addCriterion("msisdn not between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andSellStatusIsNull() {
            addCriterion("sell_status is null");
            return (Criteria) this;
        }

        public Criteria andSellStatusIsNotNull() {
            addCriterion("sell_status is not null");
            return (Criteria) this;
        }

        public Criteria andSellStatusEqualTo(String value) {
            addCriterion("sell_status =", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusNotEqualTo(String value) {
            addCriterion("sell_status <>", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusGreaterThan(String value) {
            addCriterion("sell_status >", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusGreaterThanOrEqualTo(String value) {
            addCriterion("sell_status >=", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusLessThan(String value) {
            addCriterion("sell_status <", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusLessThanOrEqualTo(String value) {
            addCriterion("sell_status <=", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("sell_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellStatusLike(String value) {
            addCriterion("sell_status like", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusNotLike(String value) {
            addCriterion("sell_status not like", value, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusIn(List<String> values) {
            addCriterion("sell_status in", values, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusNotIn(List<String> values) {
            addCriterion("sell_status not in", values, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusBetween(String value1, String value2) {
            addCriterion("sell_status between", value1, value2, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andSellStatusNotBetween(String value1, String value2) {
            addCriterion("sell_status not between", value1, value2, "sellStatus");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeIsNull() {
            addCriterion("terminal_type is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeIsNotNull() {
            addCriterion("terminal_type is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeEqualTo(String value) {
            addCriterion("terminal_type =", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeNotEqualTo(String value) {
            addCriterion("terminal_type <>", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeGreaterThan(String value) {
            addCriterion("terminal_type >", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_type >=", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLessThan(String value) {
            addCriterion("terminal_type <", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLessThanOrEqualTo(String value) {
            addCriterion("terminal_type <=", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLike(String value) {
            addCriterion("terminal_type like", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeNotLike(String value) {
            addCriterion("terminal_type not like", value, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeIn(List<String> values) {
            addCriterion("terminal_type in", values, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeNotIn(List<String> values) {
            addCriterion("terminal_type not in", values, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeBetween(String value1, String value2) {
            addCriterion("terminal_type between", value1, value2, "terminalType");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeNotBetween(String value1, String value2) {
            addCriterion("terminal_type not between", value1, value2, "terminalType");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIsNull() {
            addCriterion("device_version is null");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIsNotNull() {
            addCriterion("device_version is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionEqualTo(String value) {
            addCriterion("device_version =", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotEqualTo(String value) {
            addCriterion("device_version <>", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThan(String value) {
            addCriterion("device_version >", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanOrEqualTo(String value) {
            addCriterion("device_version >=", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThan(String value) {
            addCriterion("device_version <", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanOrEqualTo(String value) {
            addCriterion("device_version <=", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLike(String value) {
            addCriterion("device_version like", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotLike(String value) {
            addCriterion("device_version not like", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIn(List<String> values) {
            addCriterion("device_version in", values, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotIn(List<String> values) {
            addCriterion("device_version not in", values, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionBetween(String value1, String value2) {
            addCriterion("device_version between", value1, value2, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotBetween(String value1, String value2) {
            addCriterion("device_version not between", value1, value2, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andMisNumIsNull() {
            addCriterion("mis_num is null");
            return (Criteria) this;
        }

        public Criteria andMisNumIsNotNull() {
            addCriterion("mis_num is not null");
            return (Criteria) this;
        }

        public Criteria andMisNumEqualTo(String value) {
            addCriterion("mis_num =", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumNotEqualTo(String value) {
            addCriterion("mis_num <>", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumGreaterThan(String value) {
            addCriterion("mis_num >", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumGreaterThanOrEqualTo(String value) {
            addCriterion("mis_num >=", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumLessThan(String value) {
            addCriterion("mis_num <", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumLessThanOrEqualTo(String value) {
            addCriterion("mis_num <=", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("mis_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisNumLike(String value) {
            addCriterion("mis_num like", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumNotLike(String value) {
            addCriterion("mis_num not like", value, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumIn(List<String> values) {
            addCriterion("mis_num in", values, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumNotIn(List<String> values) {
            addCriterion("mis_num not in", values, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumBetween(String value1, String value2) {
            addCriterion("mis_num between", value1, value2, "misNum");
            return (Criteria) this;
        }

        public Criteria andMisNumNotBetween(String value1, String value2) {
            addCriterion("mis_num not between", value1, value2, "misNum");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNull() {
            addCriterion("order_source is null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNotNull() {
            addCriterion("order_source is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceEqualTo(String value) {
            addCriterion("order_source =", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotEqualTo(String value) {
            addCriterion("order_source <>", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThan(String value) {
            addCriterion("order_source >", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThanOrEqualTo(String value) {
            addCriterion("order_source >=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThan(String value) {
            addCriterion("order_source <", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThanOrEqualTo(String value) {
            addCriterion("order_source <=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("order_source <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSourceLike(String value) {
            addCriterion("order_source like", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotLike(String value) {
            addCriterion("order_source not like", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIn(List<String> values) {
            addCriterion("order_source in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotIn(List<String> values) {
            addCriterion("order_source not in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceBetween(String value1, String value2) {
            addCriterion("order_source between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotBetween(String value1, String value2) {
            addCriterion("order_source not between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(String value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(String value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(String value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(String value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(String value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(String value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("channel <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLike(String value) {
            addCriterion("channel like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotLike(String value) {
            addCriterion("channel not like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<String> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<String> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(String value1, String value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(String value1, String value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeIsNull() {
            addCriterion("device_type is null");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeIsNotNull() {
            addCriterion("device_type is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeEqualTo(String value) {
            addCriterion("device_type =", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeNotEqualTo(String value) {
            addCriterion("device_type <>", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeGreaterThan(String value) {
            addCriterion("device_type >", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("device_type >=", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLessThan(String value) {
            addCriterion("device_type <", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLessThanOrEqualTo(String value) {
            addCriterion("device_type <=", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLike(String value) {
            addCriterion("device_type like", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeNotLike(String value) {
            addCriterion("device_type not like", value, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeIn(List<String> values) {
            addCriterion("device_type in", values, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeNotIn(List<String> values) {
            addCriterion("device_type not in", values, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeBetween(String value1, String value2) {
            addCriterion("device_type between", value1, value2, "deviceType");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeNotBetween(String value1, String value2) {
            addCriterion("device_type not between", value1, value2, "deviceType");
            return (Criteria) this;
        }

        public Criteria andSupplyIsNull() {
            addCriterion("supply is null");
            return (Criteria) this;
        }

        public Criteria andSupplyIsNotNull() {
            addCriterion("supply is not null");
            return (Criteria) this;
        }

        public Criteria andSupplyEqualTo(String value) {
            addCriterion("supply =", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyNotEqualTo(String value) {
            addCriterion("supply <>", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyGreaterThan(String value) {
            addCriterion("supply >", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyGreaterThanOrEqualTo(String value) {
            addCriterion("supply >=", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyLessThan(String value) {
            addCriterion("supply <", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyLessThanOrEqualTo(String value) {
            addCriterion("supply <=", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("supply <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyLike(String value) {
            addCriterion("supply like", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyNotLike(String value) {
            addCriterion("supply not like", value, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyIn(List<String> values) {
            addCriterion("supply in", values, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyNotIn(List<String> values) {
            addCriterion("supply not in", values, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyBetween(String value1, String value2) {
            addCriterion("supply between", value1, value2, "supply");
            return (Criteria) this;
        }

        public Criteria andSupplyNotBetween(String value1, String value2) {
            addCriterion("supply not between", value1, value2, "supply");
            return (Criteria) this;
        }

        public Criteria andContractTermIsNull() {
            addCriterion("contract_term is null");
            return (Criteria) this;
        }

        public Criteria andContractTermIsNotNull() {
            addCriterion("contract_term is not null");
            return (Criteria) this;
        }

        public Criteria andContractTermEqualTo(String value) {
            addCriterion("contract_term =", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermNotEqualTo(String value) {
            addCriterion("contract_term <>", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermGreaterThan(String value) {
            addCriterion("contract_term >", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermGreaterThanOrEqualTo(String value) {
            addCriterion("contract_term >=", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermLessThan(String value) {
            addCriterion("contract_term <", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermLessThanOrEqualTo(String value) {
            addCriterion("contract_term <=", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("contract_term <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTermLike(String value) {
            addCriterion("contract_term like", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermNotLike(String value) {
            addCriterion("contract_term not like", value, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermIn(List<String> values) {
            addCriterion("contract_term in", values, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermNotIn(List<String> values) {
            addCriterion("contract_term not in", values, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermBetween(String value1, String value2) {
            addCriterion("contract_term between", value1, value2, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermNotBetween(String value1, String value2) {
            addCriterion("contract_term not between", value1, value2, "contractTerm");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerIsNull() {
            addCriterion("termianl_owner is null");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerIsNotNull() {
            addCriterion("termianl_owner is not null");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerEqualTo(String value) {
            addCriterion("termianl_owner =", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerNotEqualTo(String value) {
            addCriterion("termianl_owner <>", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerGreaterThan(String value) {
            addCriterion("termianl_owner >", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("termianl_owner >=", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLessThan(String value) {
            addCriterion("termianl_owner <", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLessThanOrEqualTo(String value) {
            addCriterion("termianl_owner <=", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("termianl_owner <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLike(String value) {
            addCriterion("termianl_owner like", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerNotLike(String value) {
            addCriterion("termianl_owner not like", value, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerIn(List<String> values) {
            addCriterion("termianl_owner in", values, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerNotIn(List<String> values) {
            addCriterion("termianl_owner not in", values, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerBetween(String value1, String value2) {
            addCriterion("termianl_owner between", value1, value2, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerNotBetween(String value1, String value2) {
            addCriterion("termianl_owner not between", value1, value2, "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumIsNull() {
            addCriterion("device_carton_num is null");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumIsNotNull() {
            addCriterion("device_carton_num is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumEqualTo(String value) {
            addCriterion("device_carton_num =", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumNotEqualTo(String value) {
            addCriterion("device_carton_num <>", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumGreaterThan(String value) {
            addCriterion("device_carton_num >", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumGreaterThanOrEqualTo(String value) {
            addCriterion("device_carton_num >=", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLessThan(String value) {
            addCriterion("device_carton_num <", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLessThanOrEqualTo(String value) {
            addCriterion("device_carton_num <=", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_carton_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLike(String value) {
            addCriterion("device_carton_num like", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumNotLike(String value) {
            addCriterion("device_carton_num not like", value, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumIn(List<String> values) {
            addCriterion("device_carton_num in", values, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumNotIn(List<String> values) {
            addCriterion("device_carton_num not in", values, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumBetween(String value1, String value2) {
            addCriterion("device_carton_num between", value1, value2, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumNotBetween(String value1, String value2) {
            addCriterion("device_carton_num not between", value1, value2, "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrIsNull() {
            addCriterion("device_attr is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrIsNotNull() {
            addCriterion("device_attr is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrEqualTo(String value) {
            addCriterion("device_attr =", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrNotEqualTo(String value) {
            addCriterion("device_attr <>", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrGreaterThan(String value) {
            addCriterion("device_attr >", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrGreaterThanOrEqualTo(String value) {
            addCriterion("device_attr >=", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLessThan(String value) {
            addCriterion("device_attr <", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLessThanOrEqualTo(String value) {
            addCriterion("device_attr <=", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("device_attr <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLike(String value) {
            addCriterion("device_attr like", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrNotLike(String value) {
            addCriterion("device_attr not like", value, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrIn(List<String> values) {
            addCriterion("device_attr in", values, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrNotIn(List<String> values) {
            addCriterion("device_attr not in", values, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrBetween(String value1, String value2) {
            addCriterion("device_attr between", value1, value2, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrNotBetween(String value1, String value2) {
            addCriterion("device_attr not between", value1, value2, "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionIsNull() {
            addCriterion("terminal_condition is null");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionIsNotNull() {
            addCriterion("terminal_condition is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionEqualTo(String value) {
            addCriterion("terminal_condition =", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionNotEqualTo(String value) {
            addCriterion("terminal_condition <>", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionGreaterThan(String value) {
            addCriterion("terminal_condition >", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_condition >=", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLessThan(String value) {
            addCriterion("terminal_condition <", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLessThanOrEqualTo(String value) {
            addCriterion("terminal_condition <=", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("terminal_condition <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLike(String value) {
            addCriterion("terminal_condition like", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionNotLike(String value) {
            addCriterion("terminal_condition not like", value, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionIn(List<String> values) {
            addCriterion("terminal_condition in", values, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionNotIn(List<String> values) {
            addCriterion("terminal_condition not in", values, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionBetween(String value1, String value2) {
            addCriterion("terminal_condition between", value1, value2, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionNotBetween(String value1, String value2) {
            addCriterion("terminal_condition not between", value1, value2, "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagIsNull() {
            addCriterion("inventory_flag is null");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagIsNotNull() {
            addCriterion("inventory_flag is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagEqualTo(String value) {
            addCriterion("inventory_flag =", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagNotEqualTo(String value) {
            addCriterion("inventory_flag <>", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagGreaterThan(String value) {
            addCriterion("inventory_flag >", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_flag >=", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLessThan(String value) {
            addCriterion("inventory_flag <", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLessThanOrEqualTo(String value) {
            addCriterion("inventory_flag <=", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("inventory_flag <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLike(String value) {
            addCriterion("inventory_flag like", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagNotLike(String value) {
            addCriterion("inventory_flag not like", value, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagIn(List<String> values) {
            addCriterion("inventory_flag in", values, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagNotIn(List<String> values) {
            addCriterion("inventory_flag not in", values, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagBetween(String value1, String value2) {
            addCriterion("inventory_flag between", value1, value2, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagNotBetween(String value1, String value2) {
            addCriterion("inventory_flag not between", value1, value2, "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andUseModeIsNull() {
            addCriterion("use_mode is null");
            return (Criteria) this;
        }

        public Criteria andUseModeIsNotNull() {
            addCriterion("use_mode is not null");
            return (Criteria) this;
        }

        public Criteria andUseModeEqualTo(String value) {
            addCriterion("use_mode =", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeNotEqualTo(String value) {
            addCriterion("use_mode <>", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeGreaterThan(String value) {
            addCriterion("use_mode >", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeGreaterThanOrEqualTo(String value) {
            addCriterion("use_mode >=", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeLessThan(String value) {
            addCriterion("use_mode <", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeLessThanOrEqualTo(String value) {
            addCriterion("use_mode <=", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("use_mode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseModeLike(String value) {
            addCriterion("use_mode like", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeNotLike(String value) {
            addCriterion("use_mode not like", value, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeIn(List<String> values) {
            addCriterion("use_mode in", values, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeNotIn(List<String> values) {
            addCriterion("use_mode not in", values, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeBetween(String value1, String value2) {
            addCriterion("use_mode between", value1, value2, "useMode");
            return (Criteria) this;
        }

        public Criteria andUseModeNotBetween(String value1, String value2) {
            addCriterion("use_mode not between", value1, value2, "useMode");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagIsNull() {
            addCriterion("physics_flag is null");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagIsNotNull() {
            addCriterion("physics_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagEqualTo(String value) {
            addCriterion("physics_flag =", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagNotEqualTo(String value) {
            addCriterion("physics_flag <>", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagGreaterThan(String value) {
            addCriterion("physics_flag >", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagGreaterThanOrEqualTo(String value) {
            addCriterion("physics_flag >=", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLessThan(String value) {
            addCriterion("physics_flag <", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLessThanOrEqualTo(String value) {
            addCriterion("physics_flag <=", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("physics_flag <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLike(String value) {
            addCriterion("physics_flag like", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagNotLike(String value) {
            addCriterion("physics_flag not like", value, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagIn(List<String> values) {
            addCriterion("physics_flag in", values, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagNotIn(List<String> values) {
            addCriterion("physics_flag not in", values, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagBetween(String value1, String value2) {
            addCriterion("physics_flag between", value1, value2, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagNotBetween(String value1, String value2) {
            addCriterion("physics_flag not between", value1, value2, "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andConditionTimeIsNull() {
            addCriterion("condition_time is null");
            return (Criteria) this;
        }

        public Criteria andConditionTimeIsNotNull() {
            addCriterion("condition_time is not null");
            return (Criteria) this;
        }

        public Criteria andConditionTimeEqualTo(String value) {
            addCriterion("condition_time =", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeNotEqualTo(String value) {
            addCriterion("condition_time <>", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeGreaterThan(String value) {
            addCriterion("condition_time >", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeGreaterThanOrEqualTo(String value) {
            addCriterion("condition_time >=", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeLessThan(String value) {
            addCriterion("condition_time <", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeLessThanOrEqualTo(String value) {
            addCriterion("condition_time <=", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("condition_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConditionTimeLike(String value) {
            addCriterion("condition_time like", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeNotLike(String value) {
            addCriterion("condition_time not like", value, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeIn(List<String> values) {
            addCriterion("condition_time in", values, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeNotIn(List<String> values) {
            addCriterion("condition_time not in", values, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeBetween(String value1, String value2) {
            addCriterion("condition_time between", value1, value2, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andConditionTimeNotBetween(String value1, String value2) {
            addCriterion("condition_time not between", value1, value2, "conditionTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeIsNull() {
            addCriterion("teminal_create_time is null");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeIsNotNull() {
            addCriterion("teminal_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeEqualTo(String value) {
            addCriterion("teminal_create_time =", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeNotEqualTo(String value) {
            addCriterion("teminal_create_time <>", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeGreaterThan(String value) {
            addCriterion("teminal_create_time >", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("teminal_create_time >=", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLessThan(String value) {
            addCriterion("teminal_create_time <", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("teminal_create_time <=", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLike(String value) {
            addCriterion("teminal_create_time like", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeNotLike(String value) {
            addCriterion("teminal_create_time not like", value, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeIn(List<String> values) {
            addCriterion("teminal_create_time in", values, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeNotIn(List<String> values) {
            addCriterion("teminal_create_time not in", values, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeBetween(String value1, String value2) {
            addCriterion("teminal_create_time between", value1, value2, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeNotBetween(String value1, String value2) {
            addCriterion("teminal_create_time not between", value1, value2, "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkIsNull() {
            addCriterion("teminal_remark is null");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkIsNotNull() {
            addCriterion("teminal_remark is not null");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkEqualTo(String value) {
            addCriterion("teminal_remark =", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkNotEqualTo(String value) {
            addCriterion("teminal_remark <>", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkGreaterThan(String value) {
            addCriterion("teminal_remark >", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("teminal_remark >=", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLessThan(String value) {
            addCriterion("teminal_remark <", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLessThanOrEqualTo(String value) {
            addCriterion("teminal_remark <=", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("teminal_remark <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLike(String value) {
            addCriterion("teminal_remark like", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkNotLike(String value) {
            addCriterion("teminal_remark not like", value, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkIn(List<String> values) {
            addCriterion("teminal_remark in", values, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkNotIn(List<String> values) {
            addCriterion("teminal_remark not in", values, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkBetween(String value1, String value2) {
            addCriterion("teminal_remark between", value1, value2, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkNotBetween(String value1, String value2) {
            addCriterion("teminal_remark not between", value1, value2, "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceIsNull() {
            addCriterion("recycle_source is null");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceIsNotNull() {
            addCriterion("recycle_source is not null");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceEqualTo(String value) {
            addCriterion("recycle_source =", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceNotEqualTo(String value) {
            addCriterion("recycle_source <>", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceGreaterThan(String value) {
            addCriterion("recycle_source >", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceGreaterThanOrEqualTo(String value) {
            addCriterion("recycle_source >=", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLessThan(String value) {
            addCriterion("recycle_source <", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLessThanOrEqualTo(String value) {
            addCriterion("recycle_source <=", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("recycle_source <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLike(String value) {
            addCriterion("recycle_source like", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceNotLike(String value) {
            addCriterion("recycle_source not like", value, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceIn(List<String> values) {
            addCriterion("recycle_source in", values, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceNotIn(List<String> values) {
            addCriterion("recycle_source not in", values, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceBetween(String value1, String value2) {
            addCriterion("recycle_source between", value1, value2, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceNotBetween(String value1, String value2) {
            addCriterion("recycle_source not between", value1, value2, "recycleSource");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnIsNull() {
            addCriterion("chaba_msisdn is null");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnIsNotNull() {
            addCriterion("chaba_msisdn is not null");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnEqualTo(String value) {
            addCriterion("chaba_msisdn =", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnNotEqualTo(String value) {
            addCriterion("chaba_msisdn <>", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnGreaterThan(String value) {
            addCriterion("chaba_msisdn >", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnGreaterThanOrEqualTo(String value) {
            addCriterion("chaba_msisdn >=", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLessThan(String value) {
            addCriterion("chaba_msisdn <", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLessThanOrEqualTo(String value) {
            addCriterion("chaba_msisdn <=", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("chaba_msisdn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLike(String value) {
            addCriterion("chaba_msisdn like", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnNotLike(String value) {
            addCriterion("chaba_msisdn not like", value, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnIn(List<String> values) {
            addCriterion("chaba_msisdn in", values, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnNotIn(List<String> values) {
            addCriterion("chaba_msisdn not in", values, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnBetween(String value1, String value2) {
            addCriterion("chaba_msisdn between", value1, value2, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnNotBetween(String value1, String value2) {
            addCriterion("chaba_msisdn not between", value1, value2, "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(String value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(String value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(String value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(String value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(String value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(String value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLike(String value) {
            addCriterion("template_id like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotLike(String value) {
            addCriterion("template_id not like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<String> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<String> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(String value1, String value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(String value1, String value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNull() {
            addCriterion("template_name is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNotNull() {
            addCriterion("template_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualTo(String value) {
            addCriterion("template_name =", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualTo(String value) {
            addCriterion("template_name <>", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThan(String value) {
            addCriterion("template_name >", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualTo(String value) {
            addCriterion("template_name >=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThan(String value) {
            addCriterion("template_name <", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualTo(String value) {
            addCriterion("template_name <=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("template_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLike(String value) {
            addCriterion("template_name like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotLike(String value) {
            addCriterion("template_name not like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIn(List<String> values) {
            addCriterion("template_name in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotIn(List<String> values) {
            addCriterion("template_name not in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameBetween(String value1, String value2) {
            addCriterion("template_name between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotBetween(String value1, String value2) {
            addCriterion("template_name not between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeIsNull() {
            addCriterion("card_deliver_time is null");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeIsNotNull() {
            addCriterion("card_deliver_time is not null");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeEqualTo(Date value) {
            addCriterion("card_deliver_time =", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeNotEqualTo(Date value) {
            addCriterion("card_deliver_time <>", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeGreaterThan(Date value) {
            addCriterion("card_deliver_time >", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("card_deliver_time >=", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeLessThan(Date value) {
            addCriterion("card_deliver_time <", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeLessThanOrEqualTo(Date value) {
            addCriterion("card_deliver_time <=", value, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("card_deliver_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeIn(List<Date> values) {
            addCriterion("card_deliver_time in", values, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeNotIn(List<Date> values) {
            addCriterion("card_deliver_time not in", values, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeBetween(Date value1, Date value2) {
            addCriterion("card_deliver_time between", value1, value2, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCardDeliverTimeNotBetween(Date value1, Date value2) {
            addCriterion("card_deliver_time not between", value1, value2, "cardDeliverTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andImportNumIsNull() {
            addCriterion("import_num is null");
            return (Criteria) this;
        }

        public Criteria andImportNumIsNotNull() {
            addCriterion("import_num is not null");
            return (Criteria) this;
        }

        public Criteria andImportNumEqualTo(String value) {
            addCriterion("import_num =", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumNotEqualTo(String value) {
            addCriterion("import_num <>", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumGreaterThan(String value) {
            addCriterion("import_num >", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumGreaterThanOrEqualTo(String value) {
            addCriterion("import_num >=", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumLessThan(String value) {
            addCriterion("import_num <", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumLessThanOrEqualTo(String value) {
            addCriterion("import_num <=", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("import_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImportNumLike(String value) {
            addCriterion("import_num like", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumNotLike(String value) {
            addCriterion("import_num not like", value, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumIn(List<String> values) {
            addCriterion("import_num in", values, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumNotIn(List<String> values) {
            addCriterion("import_num not in", values, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumBetween(String value1, String value2) {
            addCriterion("import_num between", value1, value2, "importNum");
            return (Criteria) this;
        }

        public Criteria andImportNumNotBetween(String value1, String value2) {
            addCriterion("import_num not between", value1, value2, "importNum");
            return (Criteria) this;
        }

        public Criteria andCreatedUserIsNull() {
            addCriterion("created_user is null");
            return (Criteria) this;
        }

        public Criteria andCreatedUserIsNotNull() {
            addCriterion("created_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedUserEqualTo(String value) {
            addCriterion("created_user =", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNotEqualTo(String value) {
            addCriterion("created_user <>", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserGreaterThan(String value) {
            addCriterion("created_user >", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserGreaterThanOrEqualTo(String value) {
            addCriterion("created_user >=", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserLessThan(String value) {
            addCriterion("created_user <", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserLessThanOrEqualTo(String value) {
            addCriterion("created_user <=", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserLike(String value) {
            addCriterion("created_user like", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNotLike(String value) {
            addCriterion("created_user not like", value, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserIn(List<String> values) {
            addCriterion("created_user in", values, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNotIn(List<String> values) {
            addCriterion("created_user not in", values, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserBetween(String value1, String value2) {
            addCriterion("created_user between", value1, value2, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNotBetween(String value1, String value2) {
            addCriterion("created_user not between", value1, value2, "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameIsNull() {
            addCriterion("created_user_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameIsNotNull() {
            addCriterion("created_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameEqualTo(String value) {
            addCriterion("created_user_name =", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameNotEqualTo(String value) {
            addCriterion("created_user_name <>", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameGreaterThan(String value) {
            addCriterion("created_user_name >", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_user_name >=", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLessThan(String value) {
            addCriterion("created_user_name <", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLessThanOrEqualTo(String value) {
            addCriterion("created_user_name <=", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("created_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLike(String value) {
            addCriterion("created_user_name like", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameNotLike(String value) {
            addCriterion("created_user_name not like", value, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameIn(List<String> values) {
            addCriterion("created_user_name in", values, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameNotIn(List<String> values) {
            addCriterion("created_user_name not in", values, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameBetween(String value1, String value2) {
            addCriterion("created_user_name between", value1, value2, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameNotBetween(String value1, String value2) {
            addCriterion("created_user_name not between", value1, value2, "createdUserName");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(CardRelation.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andImeiLikeInsensitive(String value) {
            addCriterion("upper(imei) like", value.toUpperCase(), "imei");
            return (Criteria) this;
        }

        public Criteria andTempIccidLikeInsensitive(String value) {
            addCriterion("upper(temp_iccid) like", value.toUpperCase(), "tempIccid");
            return (Criteria) this;
        }

        public Criteria andClientNameLikeInsensitive(String value) {
            addCriterion("upper(client_name) like", value.toUpperCase(), "clientName");
            return (Criteria) this;
        }

        public Criteria andProductNumLikeInsensitive(String value) {
            addCriterion("upper(product_num) like", value.toUpperCase(), "productNum");
            return (Criteria) this;
        }

        public Criteria andSnLikeInsensitive(String value) {
            addCriterion("upper(sn) like", value.toUpperCase(), "sn");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLikeInsensitive(String value) {
            addCriterion("upper(order_atom_info_id) like", value.toUpperCase(), "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andMsisdnLikeInsensitive(String value) {
            addCriterion("upper(msisdn) like", value.toUpperCase(), "msisdn");
            return (Criteria) this;
        }

        public Criteria andSellStatusLikeInsensitive(String value) {
            addCriterion("upper(sell_status) like", value.toUpperCase(), "sellStatus");
            return (Criteria) this;
        }

        public Criteria andTerminalTypeLikeInsensitive(String value) {
            addCriterion("upper(terminal_type) like", value.toUpperCase(), "terminalType");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andLocationLikeInsensitive(String value) {
            addCriterion("upper(location) like", value.toUpperCase(), "location");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLikeInsensitive(String value) {
            addCriterion("upper(device_version) like", value.toUpperCase(), "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andMisNumLikeInsensitive(String value) {
            addCriterion("upper(mis_num) like", value.toUpperCase(), "misNum");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLikeInsensitive(String value) {
            addCriterion("upper(order_source) like", value.toUpperCase(), "orderSource");
            return (Criteria) this;
        }

        public Criteria andChannelLikeInsensitive(String value) {
            addCriterion("upper(channel) like", value.toUpperCase(), "channel");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeLikeInsensitive(String value) {
            addCriterion("upper(device_type) like", value.toUpperCase(), "deviceType");
            return (Criteria) this;
        }

        public Criteria andSupplyLikeInsensitive(String value) {
            addCriterion("upper(supply) like", value.toUpperCase(), "supply");
            return (Criteria) this;
        }

        public Criteria andContractTermLikeInsensitive(String value) {
            addCriterion("upper(contract_term) like", value.toUpperCase(), "contractTerm");
            return (Criteria) this;
        }

        public Criteria andTermianlOwnerLikeInsensitive(String value) {
            addCriterion("upper(termianl_owner) like", value.toUpperCase(), "termianlOwner");
            return (Criteria) this;
        }

        public Criteria andDeviceCartonNumLikeInsensitive(String value) {
            addCriterion("upper(device_carton_num) like", value.toUpperCase(), "deviceCartonNum");
            return (Criteria) this;
        }

        public Criteria andDeviceAttrLikeInsensitive(String value) {
            addCriterion("upper(device_attr) like", value.toUpperCase(), "deviceAttr");
            return (Criteria) this;
        }

        public Criteria andTerminalConditionLikeInsensitive(String value) {
            addCriterion("upper(terminal_condition) like", value.toUpperCase(), "terminalCondition");
            return (Criteria) this;
        }

        public Criteria andInventoryFlagLikeInsensitive(String value) {
            addCriterion("upper(inventory_flag) like", value.toUpperCase(), "inventoryFlag");
            return (Criteria) this;
        }

        public Criteria andUseModeLikeInsensitive(String value) {
            addCriterion("upper(use_mode) like", value.toUpperCase(), "useMode");
            return (Criteria) this;
        }

        public Criteria andPhysicsFlagLikeInsensitive(String value) {
            addCriterion("upper(physics_flag) like", value.toUpperCase(), "physicsFlag");
            return (Criteria) this;
        }

        public Criteria andConditionTimeLikeInsensitive(String value) {
            addCriterion("upper(condition_time) like", value.toUpperCase(), "conditionTime");
            return (Criteria) this;
        }

        public Criteria andTeminalCreateTimeLikeInsensitive(String value) {
            addCriterion("upper(teminal_create_time) like", value.toUpperCase(), "teminalCreateTime");
            return (Criteria) this;
        }

        public Criteria andTeminalRemarkLikeInsensitive(String value) {
            addCriterion("upper(teminal_remark) like", value.toUpperCase(), "teminalRemark");
            return (Criteria) this;
        }

        public Criteria andRecycleSourceLikeInsensitive(String value) {
            addCriterion("upper(recycle_source) like", value.toUpperCase(), "recycleSource");
            return (Criteria) this;
        }

        public Criteria andChabaMsisdnLikeInsensitive(String value) {
            addCriterion("upper(chaba_msisdn) like", value.toUpperCase(), "chabaMsisdn");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLikeInsensitive(String value) {
            addCriterion("upper(template_id) like", value.toUpperCase(), "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLikeInsensitive(String value) {
            addCriterion("upper(template_name) like", value.toUpperCase(), "templateName");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andImportNumLikeInsensitive(String value) {
            addCriterion("upper(import_num) like", value.toUpperCase(), "importNum");
            return (Criteria) this;
        }

        public Criteria andCreatedUserLikeInsensitive(String value) {
            addCriterion("upper(created_user) like", value.toUpperCase(), "createdUser");
            return (Criteria) this;
        }

        public Criteria andCreatedUserNameLikeInsensitive(String value) {
            addCriterion("upper(created_user_name) like", value.toUpperCase(), "createdUserName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private CardRelationExample example;

        protected Criteria(CardRelationExample example) {
            super();
            this.example = example;
        }

        public CardRelationExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.CardRelationExample example);
    }
}