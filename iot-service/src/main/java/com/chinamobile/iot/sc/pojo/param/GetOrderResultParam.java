package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9
 * @description 订购结果查询参数
 */
@Data
public class GetOrderResultParam {

    /**
     * 原子订单ID
     */
    @NotEmpty(message = "原子订单ID不能为空")
    private String id;

    /**
     * 订单ID
     */
    @NotEmpty(message = "订单ID不能为空")
    private String orderId;
}
