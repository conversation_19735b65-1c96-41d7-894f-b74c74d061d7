package com.chinamobile.iot.sc.pojo.dto;

import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/9
 * @description x终端数据扩展类
 */
@Data
public class CardRelationXDTO extends CardRelation {

    /**
     * 终端所属省
     */
    private String provinceName;

    /**
     * 终端所属地市
     */
    private String cityName;

    /**
     * 终端去向省
     */
    private String orderCardProvinceName;

    /**
     * 终端去向地市
     */
    private String orderCardCityName;

    /**
     * 开卡模板名称
     */
    private String templateName;

    private Date cardDeliverTime;


    private String logisCode;

    private String addr1;

    private String addr2;

    private String addr3;

    private String addr4;

    private String usaddr;

    private String contactPersonName;

    private String contactPhone;

}
