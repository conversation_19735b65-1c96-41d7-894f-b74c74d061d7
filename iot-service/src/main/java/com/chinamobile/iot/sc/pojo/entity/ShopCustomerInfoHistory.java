package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商城客户信息历史操作表
 *
 * <AUTHOR>
public class ShopCustomerInfoHistory implements Serializable {
    /**
     * 主键
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String id;

    /**
     * 客户编码
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.cust_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String custCode;

    /**
     * 客户ID
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.cust_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String custId;

    /**
     * 用户标识
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.user_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String userId;

    /**
     * 客户姓名
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.cust_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String custName;

    /**
     * 操作类型1--新增  2--修改
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.opr_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String oprType;

    /**
     * 变更时间operType=1时，与客户注册时间 operType=2时，取客户信息变更时间
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.cust_status_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private Date custStatusTime;

    /**
     * 枚举值：0：普通用户  1：一级分销员  2：二级分销员
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.role_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String roleType;

    /**
     * 客户省份
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.be_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String beId;

    /**
     * 客户省份
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.province_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String provinceName;

    /**
     * 客户地市
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.location
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String location;

    /**
     * 客户地市
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.city_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String cityName;

    /**
     * 客户区县
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.region_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String regionId;

    /**
     * 客户区县
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.region_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String regionName;

    /**
     * 客户注册时间
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.client_register
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private Date clientRegister;

    /**
     * 用户状态0：待激活（暂不启用） 1：激活  2：暂停（暂不启用） 3：失效
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.client_status
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String clientStatus;

    /**
     * 登录次数
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.number_logins
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private Integer numberLogins;

    /**
     * 分销员姓名
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorName;

    /**
     * 邀请注册成功数
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_invitation_register_successful_quantity
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private Integer distributorInvitationRegisterSuccessfulQuantity;

    /**
     * 渠道ID
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_channel_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorChannelId;

    /**
     * 渠道名称
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_channel_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorChannelName;

    /**
     * 推荐码
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_referral_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorReferralCode;

    /**
     * 绑定客户经理姓名
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_mrg_inf
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorMrgInf;

    /**
     * 绑定客户经理工号
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.distributor_mrg_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String distributorMrgCode;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.create_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private Date createTime;

    /**
     * 渠道商编码
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.agent_number
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String agentNumber;

    /**
     * 渠道商全称
     *
     * Corresponding to the database column supply_chain..shop_customer_info_history.agent_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private String agentName;

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.id
     *
     * @return the value of supply_chain..shop_customer_info_history.id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.id
     *
     * @param id the value for supply_chain..shop_customer_info_history.id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.cust_code
     *
     * @return the value of supply_chain..shop_customer_info_history.cust_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.cust_code
     *
     * @param custCode the value for supply_chain..shop_customer_info_history.cust_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.cust_id
     *
     * @return the value of supply_chain..shop_customer_info_history.cust_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getCustId() {
        return custId;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCustId(String custId) {
        this.setCustId(custId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.cust_id
     *
     * @param custId the value for supply_chain..shop_customer_info_history.cust_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCustId(String custId) {
        this.custId = custId == null ? null : custId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.user_id
     *
     * @return the value of supply_chain..shop_customer_info_history.user_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.user_id
     *
     * @param userId the value for supply_chain..shop_customer_info_history.user_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.cust_name
     *
     * @return the value of supply_chain..shop_customer_info_history.cust_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.cust_name
     *
     * @param custName the value for supply_chain..shop_customer_info_history.cust_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.opr_type
     *
     * @return the value of supply_chain..shop_customer_info_history.opr_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getOprType() {
        return oprType;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withOprType(String oprType) {
        this.setOprType(oprType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.opr_type
     *
     * @param oprType the value for supply_chain..shop_customer_info_history.opr_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setOprType(String oprType) {
        this.oprType = oprType == null ? null : oprType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.cust_status_time
     *
     * @return the value of supply_chain..shop_customer_info_history.cust_status_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Date getCustStatusTime() {
        return custStatusTime;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCustStatusTime(Date custStatusTime) {
        this.setCustStatusTime(custStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.cust_status_time
     *
     * @param custStatusTime the value for supply_chain..shop_customer_info_history.cust_status_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCustStatusTime(Date custStatusTime) {
        this.custStatusTime = custStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.role_type
     *
     * @return the value of supply_chain..shop_customer_info_history.role_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getRoleType() {
        return roleType;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withRoleType(String roleType) {
        this.setRoleType(roleType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.role_type
     *
     * @param roleType the value for supply_chain..shop_customer_info_history.role_type
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setRoleType(String roleType) {
        this.roleType = roleType == null ? null : roleType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.be_id
     *
     * @return the value of supply_chain..shop_customer_info_history.be_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.be_id
     *
     * @param beId the value for supply_chain..shop_customer_info_history.be_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.province_name
     *
     * @return the value of supply_chain..shop_customer_info_history.province_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.province_name
     *
     * @param provinceName the value for supply_chain..shop_customer_info_history.province_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.location
     *
     * @return the value of supply_chain..shop_customer_info_history.location
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.location
     *
     * @param location the value for supply_chain..shop_customer_info_history.location
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.city_name
     *
     * @return the value of supply_chain..shop_customer_info_history.city_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.city_name
     *
     * @param cityName the value for supply_chain..shop_customer_info_history.city_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.region_id
     *
     * @return the value of supply_chain..shop_customer_info_history.region_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.region_id
     *
     * @param regionId the value for supply_chain..shop_customer_info_history.region_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.region_name
     *
     * @return the value of supply_chain..shop_customer_info_history.region_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.region_name
     *
     * @param regionName the value for supply_chain..shop_customer_info_history.region_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName == null ? null : regionName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.client_register
     *
     * @return the value of supply_chain..shop_customer_info_history.client_register
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Date getClientRegister() {
        return clientRegister;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withClientRegister(Date clientRegister) {
        this.setClientRegister(clientRegister);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.client_register
     *
     * @param clientRegister the value for supply_chain..shop_customer_info_history.client_register
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setClientRegister(Date clientRegister) {
        this.clientRegister = clientRegister;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.client_status
     *
     * @return the value of supply_chain..shop_customer_info_history.client_status
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getClientStatus() {
        return clientStatus;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withClientStatus(String clientStatus) {
        this.setClientStatus(clientStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.client_status
     *
     * @param clientStatus the value for supply_chain..shop_customer_info_history.client_status
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setClientStatus(String clientStatus) {
        this.clientStatus = clientStatus == null ? null : clientStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.number_logins
     *
     * @return the value of supply_chain..shop_customer_info_history.number_logins
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Integer getNumberLogins() {
        return numberLogins;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withNumberLogins(Integer numberLogins) {
        this.setNumberLogins(numberLogins);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.number_logins
     *
     * @param numberLogins the value for supply_chain..shop_customer_info_history.number_logins
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setNumberLogins(Integer numberLogins) {
        this.numberLogins = numberLogins;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_name
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorName() {
        return distributorName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorName(String distributorName) {
        this.setDistributorName(distributorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_name
     *
     * @param distributorName the value for supply_chain..shop_customer_info_history.distributor_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName == null ? null : distributorName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_invitation_register_successful_quantity
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_invitation_register_successful_quantity
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Integer getDistributorInvitationRegisterSuccessfulQuantity() {
        return distributorInvitationRegisterSuccessfulQuantity;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorInvitationRegisterSuccessfulQuantity(Integer distributorInvitationRegisterSuccessfulQuantity) {
        this.setDistributorInvitationRegisterSuccessfulQuantity(distributorInvitationRegisterSuccessfulQuantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_invitation_register_successful_quantity
     *
     * @param distributorInvitationRegisterSuccessfulQuantity the value for supply_chain..shop_customer_info_history.distributor_invitation_register_successful_quantity
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorInvitationRegisterSuccessfulQuantity(Integer distributorInvitationRegisterSuccessfulQuantity) {
        this.distributorInvitationRegisterSuccessfulQuantity = distributorInvitationRegisterSuccessfulQuantity;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_channel_id
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_channel_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorChannelId() {
        return distributorChannelId;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorChannelId(String distributorChannelId) {
        this.setDistributorChannelId(distributorChannelId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_channel_id
     *
     * @param distributorChannelId the value for supply_chain..shop_customer_info_history.distributor_channel_id
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorChannelId(String distributorChannelId) {
        this.distributorChannelId = distributorChannelId == null ? null : distributorChannelId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_channel_name
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_channel_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorChannelName() {
        return distributorChannelName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorChannelName(String distributorChannelName) {
        this.setDistributorChannelName(distributorChannelName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_channel_name
     *
     * @param distributorChannelName the value for supply_chain..shop_customer_info_history.distributor_channel_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorChannelName(String distributorChannelName) {
        this.distributorChannelName = distributorChannelName == null ? null : distributorChannelName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_referral_code
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_referral_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorReferralCode() {
        return distributorReferralCode;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorReferralCode(String distributorReferralCode) {
        this.setDistributorReferralCode(distributorReferralCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_referral_code
     *
     * @param distributorReferralCode the value for supply_chain..shop_customer_info_history.distributor_referral_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorReferralCode(String distributorReferralCode) {
        this.distributorReferralCode = distributorReferralCode == null ? null : distributorReferralCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_mrg_inf
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_mrg_inf
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorMrgInf() {
        return distributorMrgInf;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorMrgInf(String distributorMrgInf) {
        this.setDistributorMrgInf(distributorMrgInf);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_mrg_inf
     *
     * @param distributorMrgInf the value for supply_chain..shop_customer_info_history.distributor_mrg_inf
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorMrgInf(String distributorMrgInf) {
        this.distributorMrgInf = distributorMrgInf == null ? null : distributorMrgInf.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.distributor_mrg_code
     *
     * @return the value of supply_chain..shop_customer_info_history.distributor_mrg_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getDistributorMrgCode() {
        return distributorMrgCode;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withDistributorMrgCode(String distributorMrgCode) {
        this.setDistributorMrgCode(distributorMrgCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.distributor_mrg_code
     *
     * @param distributorMrgCode the value for supply_chain..shop_customer_info_history.distributor_mrg_code
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistributorMrgCode(String distributorMrgCode) {
        this.distributorMrgCode = distributorMrgCode == null ? null : distributorMrgCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.create_time
     *
     * @return the value of supply_chain..shop_customer_info_history.create_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.create_time
     *
     * @param createTime the value for supply_chain..shop_customer_info_history.create_time
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.agent_number
     *
     * @return the value of supply_chain..shop_customer_info_history.agent_number
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getAgentNumber() {
        return agentNumber;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withAgentNumber(String agentNumber) {
        this.setAgentNumber(agentNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.agent_number
     *
     * @param agentNumber the value for supply_chain..shop_customer_info_history.agent_number
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setAgentNumber(String agentNumber) {
        this.agentNumber = agentNumber == null ? null : agentNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info_history.agent_name
     *
     * @return the value of supply_chain..shop_customer_info_history.agent_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getAgentName() {
        return agentName;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistory withAgentName(String agentName) {
        this.setAgentName(agentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info_history.agent_name
     *
     * @param agentName the value for supply_chain..shop_customer_info_history.agent_name
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setAgentName(String agentName) {
        this.agentName = agentName == null ? null : agentName.trim();
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", custCode=").append(custCode);
        sb.append(", custId=").append(custId);
        sb.append(", userId=").append(userId);
        sb.append(", custName=").append(custName);
        sb.append(", oprType=").append(oprType);
        sb.append(", custStatusTime=").append(custStatusTime);
        sb.append(", roleType=").append(roleType);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", location=").append(location);
        sb.append(", cityName=").append(cityName);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", clientRegister=").append(clientRegister);
        sb.append(", clientStatus=").append(clientStatus);
        sb.append(", numberLogins=").append(numberLogins);
        sb.append(", distributorName=").append(distributorName);
        sb.append(", distributorInvitationRegisterSuccessfulQuantity=").append(distributorInvitationRegisterSuccessfulQuantity);
        sb.append(", distributorChannelId=").append(distributorChannelId);
        sb.append(", distributorChannelName=").append(distributorChannelName);
        sb.append(", distributorReferralCode=").append(distributorReferralCode);
        sb.append(", distributorMrgInf=").append(distributorMrgInf);
        sb.append(", distributorMrgCode=").append(distributorMrgCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", agentNumber=").append(agentNumber);
        sb.append(", agentName=").append(agentName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShopCustomerInfoHistory other = (ShopCustomerInfoHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustId() == null ? other.getCustId() == null : this.getCustId().equals(other.getCustId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getOprType() == null ? other.getOprType() == null : this.getOprType().equals(other.getOprType()))
            && (this.getCustStatusTime() == null ? other.getCustStatusTime() == null : this.getCustStatusTime().equals(other.getCustStatusTime()))
            && (this.getRoleType() == null ? other.getRoleType() == null : this.getRoleType().equals(other.getRoleType()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getClientRegister() == null ? other.getClientRegister() == null : this.getClientRegister().equals(other.getClientRegister()))
            && (this.getClientStatus() == null ? other.getClientStatus() == null : this.getClientStatus().equals(other.getClientStatus()))
            && (this.getNumberLogins() == null ? other.getNumberLogins() == null : this.getNumberLogins().equals(other.getNumberLogins()))
            && (this.getDistributorName() == null ? other.getDistributorName() == null : this.getDistributorName().equals(other.getDistributorName()))
            && (this.getDistributorInvitationRegisterSuccessfulQuantity() == null ? other.getDistributorInvitationRegisterSuccessfulQuantity() == null : this.getDistributorInvitationRegisterSuccessfulQuantity().equals(other.getDistributorInvitationRegisterSuccessfulQuantity()))
            && (this.getDistributorChannelId() == null ? other.getDistributorChannelId() == null : this.getDistributorChannelId().equals(other.getDistributorChannelId()))
            && (this.getDistributorChannelName() == null ? other.getDistributorChannelName() == null : this.getDistributorChannelName().equals(other.getDistributorChannelName()))
            && (this.getDistributorReferralCode() == null ? other.getDistributorReferralCode() == null : this.getDistributorReferralCode().equals(other.getDistributorReferralCode()))
            && (this.getDistributorMrgInf() == null ? other.getDistributorMrgInf() == null : this.getDistributorMrgInf().equals(other.getDistributorMrgInf()))
            && (this.getDistributorMrgCode() == null ? other.getDistributorMrgCode() == null : this.getDistributorMrgCode().equals(other.getDistributorMrgCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getAgentNumber() == null ? other.getAgentNumber() == null : this.getAgentNumber().equals(other.getAgentNumber()))
            && (this.getAgentName() == null ? other.getAgentName() == null : this.getAgentName().equals(other.getAgentName()));
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustId() == null) ? 0 : getCustId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getOprType() == null) ? 0 : getOprType().hashCode());
        result = prime * result + ((getCustStatusTime() == null) ? 0 : getCustStatusTime().hashCode());
        result = prime * result + ((getRoleType() == null) ? 0 : getRoleType().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getClientRegister() == null) ? 0 : getClientRegister().hashCode());
        result = prime * result + ((getClientStatus() == null) ? 0 : getClientStatus().hashCode());
        result = prime * result + ((getNumberLogins() == null) ? 0 : getNumberLogins().hashCode());
        result = prime * result + ((getDistributorName() == null) ? 0 : getDistributorName().hashCode());
        result = prime * result + ((getDistributorInvitationRegisterSuccessfulQuantity() == null) ? 0 : getDistributorInvitationRegisterSuccessfulQuantity().hashCode());
        result = prime * result + ((getDistributorChannelId() == null) ? 0 : getDistributorChannelId().hashCode());
        result = prime * result + ((getDistributorChannelName() == null) ? 0 : getDistributorChannelName().hashCode());
        result = prime * result + ((getDistributorReferralCode() == null) ? 0 : getDistributorReferralCode().hashCode());
        result = prime * result + ((getDistributorMrgInf() == null) ? 0 : getDistributorMrgInf().hashCode());
        result = prime * result + ((getDistributorMrgCode() == null) ? 0 : getDistributorMrgCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getAgentNumber() == null) ? 0 : getAgentNumber().hashCode());
        result = prime * result + ((getAgentName() == null) ? 0 : getAgentName().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custId("cust_id", "custId", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        oprType("opr_type", "oprType", "VARCHAR", false),
        custStatusTime("cust_status_time", "custStatusTime", "TIMESTAMP", false),
        roleType("role_type", "roleType", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        clientRegister("client_register", "clientRegister", "TIMESTAMP", false),
        clientStatus("client_status", "clientStatus", "VARCHAR", false),
        numberLogins("number_logins", "numberLogins", "INTEGER", false),
        distributorName("distributor_name", "distributorName", "VARCHAR", false),
        distributorInvitationRegisterSuccessfulQuantity("distributor_invitation_register_successful_quantity", "distributorInvitationRegisterSuccessfulQuantity", "INTEGER", false),
        distributorChannelId("distributor_channel_id", "distributorChannelId", "VARCHAR", false),
        distributorChannelName("distributor_channel_name", "distributorChannelName", "VARCHAR", false),
        distributorReferralCode("distributor_referral_code", "distributorReferralCode", "VARCHAR", false),
        distributorMrgInf("distributor_mrg_inf", "distributorMrgInf", "VARCHAR", false),
        distributorMrgCode("distributor_mrg_code", "distributorMrgCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        agentNumber("agent_number", "agentNumber", "VARCHAR", false),
        agentName("agent_name", "agentName", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}