package com.chinamobile.iot.sc.pojo.mapper;

import lombok.Data;

@Data
public class UnionSellOrderExportDO {

    /**
     * 订单ID
     */
    private String id;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;

    /**
     * 商品组类型
     */
    private String spuOfferingClass;

    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    private String skuOfferingCode;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 订购数量
     */
    private Integer quantity;
    /**
     * 原子商品结算价 单价
     */
    private Long atomPrice;
    /**
     * 订单状态，0 待发货、1 待收货、2 已收货、3 开票、4 退款中、
     * 5 退货退款中、6 换货中、7 交易完成、8 交易失败
     */
    private Integer orderStatus;
    /**
     * 合作伙伴姓名
     */
    private String cooperatorName;
    /**
     * 产品部门
     */
    private String department;
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 原子结算单价
     */
    private Long settlePrice;
    /**
     * 组织机构名称，"-"分隔 ： 省-地市-区县-网格
     */
    private String orgName;
    /**
     * 订购数量（规格）
     */
    private Long skuQuantity;
    /**
     * 销售目录价（规格）
     */
    private Long price;
    /**
     * 订单总金额（接口带来的，已加密）
     */
    private String orderTotalPrice;
    /**
     * 订单完成时间
     */
    private String finishTime;
    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 支付时间
     */
    private String payTime;


}
