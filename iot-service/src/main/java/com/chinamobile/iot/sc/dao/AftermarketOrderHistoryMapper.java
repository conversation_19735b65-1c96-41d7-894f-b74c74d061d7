package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory;
import com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AftermarketOrderHistoryMapper {
    long countByExample(AftermarketOrderHistoryExample example);

    int deleteByExample(AftermarketOrderHistoryExample example);

    int deleteByPrimaryKey(String id);

    int insert(AftermarketOrderHistory record);

    int insertSelective(AftermarketOrderHistory record);

    List<AftermarketOrderHistory> selectByExample(AftermarketOrderHistoryExample example);

    AftermarketOrderHistory selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") AftermarketOrderHistory record, @Param("example") AftermarketOrderHistoryExample example);

    int updateByExample(@Param("record") AftermarketOrderHistory record, @Param("example") AftermarketOrderHistoryExample example);

    int updateByPrimaryKeySelective(AftermarketOrderHistory record);

    int updateByPrimaryKey(AftermarketOrderHistory record);

    int batchInsert(@Param("list") List<AftermarketOrderHistory> list);

    int batchInsertSelective(@Param("list") List<AftermarketOrderHistory> list, @Param("selective") AftermarketOrderHistory.Column ... selective);
}