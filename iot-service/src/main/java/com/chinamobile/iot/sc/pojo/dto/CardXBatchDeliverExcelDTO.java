package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/5 14:42
 * 卡+X批量交付读取excel数据的实体类
 */
@Data
public class CardXBatchDeliverExcelDTO {

    @ExcelProperty(value = "订单号", index = 0)
    private String orderId;

    @ExcelProperty(value = "设备型号", index = 1)
    private String deviceVersion;

    @ExcelProperty(value = "imei/sn", index = 2)
    private String imei;

    @ExcelProperty(value = "码号", index = 3)
    private String msisdn;
}
