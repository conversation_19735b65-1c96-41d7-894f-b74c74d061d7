package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/10 14:37
 */
@Data
public class DeliverCardParam {

    @NotEmpty(message = "原子订单交付信息列表不能为空")
    @Valid
    List<AtomDeliverItem> atomList;

    @Data
    public static class AtomDeliverItem{
        /*@NotEmpty(message = "原子订单id不能为空")
        private String atomOrderId;*/

        private List<String> atomOrderIdList;

        @NotEmpty(message = "订单id不能为空")
        private String orderId;


        @NotEmpty(message = "终端和号卡列表不能为空")
        @Valid
        private List<CardItem> cardList;
    }

    @Data
    public static class CardItem{
        @NotEmpty(message = "imei/sn不能为空")
        private String imei;

        private String iccid;

        private String msisdn;
    }


}
