package com.chinamobile.iot.sc.request;

import lombok.Data;

@Data
public class OneKeyLoginRequest {

    private Header header;

    private Body body;

    @Data
    public static class Header {

        /**
         * 接口版本号
         * 填2.0
         */
        private String interfaceVersion;

        /**
         * 应用 ID
         * 移动认证分配的固定值
         */
        private String appId;

        /**
         * 时间跟踪 ID
         * 业务方生成唯一标识
         */
        private String traceId;

        /**
         * 请求消息发送的系统时间
         * 精确到毫秒 共17位
         */
        private String timestamp;

        /**
         * 业务类型 传固定值8
         */
        private String businessType;


    }

    @Data
    public static class Body {

        /**
         * token 身份凭证
         * 获取token方法返回的token信息
         */
        private String token;

        /**
         * 签名
         */
        private String sign;

        /**
         * 浏览器加密指纹
         * 一键登录JSSDK返回的浏览器加密指纹
         */
        private String userInformation;

        /**
         * 扩展参数
         */
        private String expandParams;

    }
}
