package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/12
 * @description 卡+X库存详情相关的卡信息参数类
 */
@Data
public class DkcardxInventoryCardDetailParam extends BasePageQuery {

    /**
     * 卡+X终端库存主要信息表id
     */
    @NotEmpty(message = "卡+X终端库存主要信息表id不能为空")
    private String inventoryMainId;

    /**
     * 地市编码
     */
    private String location;

    /**
     * 当前用户所在地市编码
     */
    private String currentLocation;

    /**
     * 销售状态
     */
    private String sellStatus;

    private String imei;
}
