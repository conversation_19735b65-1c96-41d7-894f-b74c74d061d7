package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 订单和合作伙伴历史记录表
 *
 * <AUTHOR>
public class OrderCooperatorRelationHistory implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..order_cooperator_relation_history.id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private String id;

    /**
     * 原子订单id
     *
     * Corresponding to the database column supply_chain..order_cooperator_relation_history.atom_order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private String atomOrderId;

    /**
     * 订单id
     *
     * Corresponding to the database column supply_chain..order_cooperator_relation_history.order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private String orderId;

    /**
     * 从合作伙伴id
     *
     * Corresponding to the database column supply_chain..order_cooperator_relation_history.cooperator_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private String cooperatorId;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..order_cooperator_relation_history.create_time
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private Date createTime;

    /**
     * Corresponding to the database table supply_chain..order_cooperator_relation_history
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_cooperator_relation_history.id
     *
     * @return the value of supply_chain..order_cooperator_relation_history.id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public OrderCooperatorRelationHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_cooperator_relation_history.id
     *
     * @param id the value for supply_chain..order_cooperator_relation_history.id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_cooperator_relation_history.atom_order_id
     *
     * @return the value of supply_chain..order_cooperator_relation_history.atom_order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public OrderCooperatorRelationHistory withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_cooperator_relation_history.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..order_cooperator_relation_history.atom_order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId == null ? null : atomOrderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_cooperator_relation_history.order_id
     *
     * @return the value of supply_chain..order_cooperator_relation_history.order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public OrderCooperatorRelationHistory withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_cooperator_relation_history.order_id
     *
     * @param orderId the value for supply_chain..order_cooperator_relation_history.order_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_cooperator_relation_history.cooperator_id
     *
     * @return the value of supply_chain..order_cooperator_relation_history.cooperator_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public OrderCooperatorRelationHistory withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_cooperator_relation_history.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..order_cooperator_relation_history.cooperator_id
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_cooperator_relation_history.create_time
     *
     * @return the value of supply_chain..order_cooperator_relation_history.create_time
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public OrderCooperatorRelationHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_cooperator_relation_history.create_time
     *
     * @param createTime the value for supply_chain..order_cooperator_relation_history.create_time
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderCooperatorRelationHistory other = (OrderCooperatorRelationHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..order_cooperator_relation_history
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..order_cooperator_relation_history
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri May 30 11:19:10 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}