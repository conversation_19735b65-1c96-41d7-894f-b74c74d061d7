package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.Month;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.Constant.REDIS_KEY_BRM_UPLOAD_TXT_PARAM;

@Configuration
@EnableScheduling

@Slf4j
public class BRMUploadTask {

    public static final String lockKey = "IOPUploadTask";
    @Resource
    private BRMService brmService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${revenue.invoiceApply.isTest}")
    private String isTest;

    /**
     * 每天晚上0点执行，生成txt命名为前一天
     */
    @Scheduled(cron = "0 0 0,1 * * ?")
//    @Scheduled(cron = "0 20,30 * * * ?")
    @Async("asyncTask")
    public void work() {
        Boolean getLock = null;
        Calendar calendarPre = Calendar.getInstance();
        int currentHour = calendarPre.get(Calendar.HOUR_OF_DAY); // 获取当前小时（24 小时制）
        //零点中删除前一天的redis
        if (currentHour == 0) {
            stringRedisTemplate.delete(REDIS_KEY_BRM_UPLOAD_TXT_PARAM);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String fileName = "IOTSTOCKINFO_" + DateTimeUtil.formatDate(calendar.getTime(), "yyyyMMdd") + ".txt";
//        从redis获取值
        String redisFileName = stringRedisTemplate.opsForValue().get(REDIS_KEY_BRM_UPLOAD_TXT_PARAM);
        if (redisFileName != null && redisFileName.equals(fileName)) {
            return;
        }
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 30, TimeUnit.SECONDS);
            if (getLock) {
                LocalDate currentDate = LocalDate.now();
                // 检查当前日期是否是要跳过的日期
//                if (isSkipDate(currentDate)) {
//                    return; // 如果是要跳过的日期，则不执行任务
//                }
                IOPUploadParam iopUploadParam = new IOPUploadParam();
                // type为null时为非首次同步，定时任务都是非首次，手动调用接口传首次数据
                Date now = new Date();
                Date todayBegin = DateTimeUtil.getDayBeginDate(now);
                Date todayEnd = DateTimeUtil.getDayEndDate(now);
                Date yesterdayBegin = DateTimeUtil.addDay(todayBegin, -1);
                Date yesterdayEnd = DateTimeUtil.addDay(todayEnd, -1);
                iopUploadParam.setType("a");
                iopUploadParam.setRetry("00");
                iopUploadParam.setStartTime(yesterdayBegin);
                iopUploadParam.setEndTime(yesterdayEnd);

                log.info("上传brm接口文件定时任务开始。。。");
                // 测试环境不触发
                if (!isTest.equals("Y")) {
                    brmService.sftpUploadBRM(iopUploadParam);
                }


                log.info("上传brm接口文件定时任务结束。。。");
            }
        } catch (Exception e) {
            log.error("获取brm校验报告定时任务开始发生异常", e);
        }
    }

    // 检查当前日期是否是要跳过的日期
    public boolean isSkipDate(LocalDate currentDate) {
        int year = currentDate.getYear();
        Month month = currentDate.getMonth();
        int dayOfMonth = currentDate.getDayOfMonth();

        // 在这里添加你想要跳过执行任务的日期条件
        if (year == 2024 && month == Month.JUNE && dayOfMonth == 26) {
            return true; // 跳过任务
        }
        return false; // 不跳过任务
    }
}
