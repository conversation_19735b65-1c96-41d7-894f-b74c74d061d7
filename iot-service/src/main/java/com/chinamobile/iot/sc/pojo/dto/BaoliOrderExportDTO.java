package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/18
 * @description 导出保理不可用订单标记实体类
 */
@Data
public class BaoliOrderExportDTO {


    @Excel(name = "订单号")
    private String orderId;

    @Excel(name = "订单完成时间")
    private String orderFinishTime;

    @Excel(name = "商品规格（名称）")
    private String skuOfferingName;

    /**
     * 原子商品类型
     */
    @Excel(name = "商品类型")
    private String atomOfferingClass;

    private String spuOfferingClass;

    /**
     * 抵扣金额
     */
    private String deductPriceStr;

    @Excel(name = "抵扣金额")
    private BigDecimal deductPrice;

    @Excel(name = "订单类型")
    private String orderTypeName;

    /**
     * 合同相对方名称
     */
    private String buyerName;

    @Excel(name = "合作伙伴名称")
    private String partnerName;

    @Excel(name = "合作伙伴保理联系人姓名")
    private String cooperatorName;
}
