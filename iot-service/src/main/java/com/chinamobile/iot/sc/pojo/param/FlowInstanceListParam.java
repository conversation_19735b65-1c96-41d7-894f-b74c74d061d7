package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/2/27 17:06
 */
@Data
public class FlowInstanceListParam extends BasePageQuery {

    //流程编号
    private String flowNumber;

    //创建人
    private String creatorName;

    //创建时间起始
    private String createTimeStart;

    //创建时间结束
    private String createTimeEnd;

    //产品类别 1-省框 2-省内 3-DICT 4-合同履约 5-联合销售
    private Integer productType;

    //产品标准 1-标准类 2-方案类
    private Integer productStandard;

    //运营类型 1--分省运营 2--统一运营
    private Integer operateType;

    //spu名称
    private String spuName;

    //sku名称
    private String skuName;

    //流程类型 1-产品上架 2-产品下架 3-销售价变更 4-结算价变更 5-非价格信息变更 6-商品所有信息变更(仅限统一运营类)
    private List<Integer> flowTypeList;

}
