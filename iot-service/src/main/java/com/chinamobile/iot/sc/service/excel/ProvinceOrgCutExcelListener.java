package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @AUTHOR: HWF
 * @DATE: 2024/12/18
 */
@Slf4j
public class ProvinceOrgCutExcelListener extends AnalysisEventListener<ProvinceOrgCut> {

    private List<ProvinceOrgCut> list = new ArrayList<>();


    @Override
    public void invoke(ProvinceOrgCut provinceOrgCut, AnalysisContext analysisContext) {
        log.info("ProvinceOrgCutExcelListener invoke Enter...");
        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        log.info("解析到一条数据:{},当前行:{}", provinceOrgCut, currentRow);
        list.add(provinceOrgCut);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完毕！");
    }
}
