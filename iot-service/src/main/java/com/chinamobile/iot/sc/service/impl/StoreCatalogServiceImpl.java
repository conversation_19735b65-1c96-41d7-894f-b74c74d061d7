package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.StoreCatalogMapper;
import com.chinamobile.iot.sc.pojo.StoreCatalog;
import com.chinamobile.iot.sc.pojo.StoreCatalogExample;
import com.chinamobile.iot.sc.pojo.vo.StoreCatalogVO;
import com.chinamobile.iot.sc.service.StoreCatalogService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14
 * @description 商城目录service实现类
 */
@Service
public class StoreCatalogServiceImpl implements StoreCatalogService {

    @Resource
    private StoreCatalogMapper storeCatalogMapper;

    @Override
    public List<StoreCatalogVO> listStoreCatalog(String parentId) {
        List<StoreCatalogVO> storeCatalogVOList = new ArrayList<>();
        StoreCatalogExample example = new StoreCatalogExample();
        if (StringUtils.isEmpty(parentId)){
            example.createCriteria().andParentIdIsNull();
        }else {

            example.createCriteria().andParentIdEqualTo(parentId);
        }
        List<StoreCatalog> storeCatalogList = storeCatalogMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(storeCatalogList)){
            storeCatalogList.stream().forEach(storeCatalog -> {
                StoreCatalogVO storeCatalogVO = new StoreCatalogVO();
                BeanUtils.copyProperties(storeCatalog,storeCatalogVO);
                storeCatalogVOList.add(storeCatalogVO);
            });
        }
        return storeCatalogVOList;
    }

    @Override
    public StoreCatalog getStoreCatalogById(String id) {
        return storeCatalogMapper.selectByPrimaryKey(id);
    }
}
