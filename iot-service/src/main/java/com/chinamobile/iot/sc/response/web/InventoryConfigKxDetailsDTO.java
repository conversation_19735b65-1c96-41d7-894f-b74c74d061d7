package com.chinamobile.iot.sc.response.web;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/5/10 15:16
 * @description: 卡+x库存配置详情信息DTO
 **/
@Data
public class InventoryConfigKxDetailsDTO {

        /**
         * 主键
         */
        private String id;


        /**
         * 设备型号
         *
         */
        private String deviceVersion;

        /**
         * 设备imei号
         */
        private String imei;

        /**
         * 地市名称
         */
        private String cityName;

        /**
         * 销售状态(枚举值):1--未销售 2--销售中 3--已销售 4--销售失败 9--不可销售
         *
         */
        private String saleStatus;


}
