package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14
 * @description 新产品上线请求商品套餐信息
 */
@Data
public class NewProductOnlineRequestComboInfoParam {

    /**
     * 物联卡套餐说明
     */
    @NotEmpty(message = "物联卡套餐说明不能为空")
    private String thingsCardCombo;

    /**
     * 硬件商品发货清单
     */
    @NotEmpty(message = "硬件商品发货清单不能为空")
    private String hardwareShippingList;

    /**
     * 商品参数信息
     */
    @NotEmpty(message = "商品参数信息不能为空")
    private String productParam;

    @NotEmpty(message = "商品发货地址省编号不能为空")
    private String productShipProvince;

    @NotEmpty(message = "商品发货地址省名称不能为空")
    private String productShipProvinceName;

    @NotEmpty(message = "商品发货地址市编号不能为空")
    private String productShipCity;

    @NotEmpty(message = "商品发货地址市名称不能为空")
    private String productShipCityName;

    /**
     * 商品发货地址
     */
    @NotEmpty(message = "商品发货地址不能为空")
    private String productShipAddress;

    /**
     * 快递简称
     */
    @NotEmpty(message = "快递简称不能为空")
    private String hardwareExpressageSimpleName;

    /**
     * 快递名称
     */
    @NotEmpty(message = "快递名称不能为空")
    private String hardwareExpressageName;

    /**
     * 商品发货时间信息
     */
    @NotEmpty(message = "商品发货时间信息不能为空")
    private String productShipTime;

    /**
     * 商品使用条件
     */
    @NotEmpty(message = "商品使用条件不能为空")
    private String productWorkingCondition;

    /**
     * 软件平台名称及介绍
     */
    @NotEmpty(message = "软件平台名称及介绍不能为空")
    private String softwareInfo;

    /**
     * 软件平台下载方式及地址
     */
    @NotEmpty(message = "软件平台下载方式及地址不能为空")
    private String softwareGetWay;

    /**
     * 小程序名称及介绍
     */
    @NotEmpty(message = "小程序名称及介绍不能为空")
    private String appMiniProgramInfo;

    /**
     * 小程序下载方式及地址
     */
    @NotEmpty(message = "小程序下载方式及地址不能为空")
    private String appMiniProgramGetWay;

    /**
     * 安装服务说明
     */
    @NotEmpty(message = "安装服务说明不能为空")
    private String installationServices;
}
