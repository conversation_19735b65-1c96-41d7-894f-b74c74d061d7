package com.chinamobile.iot.sc.quartz;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sgs.quartz
 * @ClassName: QuartzJobConf
 * @description: quartz定时任务动态配置信息
 * @author: zyj
 * @create: 2021/8/18 11:44
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class QuartzJobConf implements Serializable {
    private static final long serialVersionUID = 5557217232384023441L;
    //定时任务id
    private String jobId;
    //cron表达式 1 1 1 17,18 12 ? 17 18为当前时间+两天
    private String triggerCron;
    //任务组
    private String jobGroup;
    //任务名
    private String jobName;
    //触发器组
    private String triggerGroup;
    //触发器名
    private String triggerName;
    //
    private Integer jobType;
    //手机号
    private List<String> phone;
    //订单号
    private String orderId;
    //模板ID
    private String templateId;
    //ROC订单流水号
    private String refundOrderId;
    //活动Id
    private String activityId;

}
