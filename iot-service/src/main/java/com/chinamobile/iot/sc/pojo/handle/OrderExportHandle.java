package com.chinamobile.iot.sc.pojo.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * created by liuxiang on 2022/3/30 16:28
 * 导出订单列表数据的查询时，接收数据库查询结果
 */
@Data
public class OrderExportHandle extends OrderInfoHandle {

    /**
     * 收货地区(省份)
     */
    private String deliveryArea;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 原子结算单价
     */
    private Long settlePrice;

    /**
     * 操作员编码
     */
    private String createOperCode;

    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员姓名
     */
    private String custMgName;

    /**
     * 操作员电话
     */
    private String custMgPhone;

    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 个人客户省份(后缀是移动)
     */
    private String province;

    /**
     * 个人客户所属归属地市编码
     */
    private String location;

    /**
     * 个人客户所属归属区县
     */
    private String regionID;

    /**
     * 物流单号，多个逗号分隔
     */
    private String logisCode;

    /**
     * 设备sn，多个逗号分隔
     */
    private String sn;

    /**
     * 收货人姓名
     */
    private String contactPersonName;

    /**
     * 收货人省（加密）
     */
    private String addr1;


    /**
     * 收货人市（加密）
     */
    private String addr2;

    /**
     * 收货人区（加密）
     */
    private String addr3;

    /**
     * 收货人乡镇（加密）
     */
    private String addr4;

    /**
     * 非结构地址(加密）
     */
    private String usaddr;

    /**
     * 组织机构名称，"-"分隔 ： 省-地市-区县-网格
     */
    private String orgName;

    /**
     * 订单抵扣金额(单位厘，已加密)
     */
    private String orderDeductPrice;

    /**
     * 原子订单抵扣金额(单位厘，已加密)
     */
    private String atomDeductPrice;

    /**
     * 订购数量（规格）
     */
    private Integer skuQuantity;

    /**
     * 建议零售价（规格）
     */
    private Long recommendPrice;

    /**
     * 销售目录价（规格）
     */
    private Long price;

    /**
     * 营销案名称
     */
    private String marketName;

    /**
     * 营销案编码
     */
    private String marketCode;

    /**
     * 订单总金额（接口带来的，已加密）
     */
    private String orderTotalPrice;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 订单完成时间
     */
    private Date finishTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 关联领货码
     */
    private String couponInfo;

    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;
}
