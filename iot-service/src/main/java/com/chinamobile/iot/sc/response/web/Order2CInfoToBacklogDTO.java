package com.chinamobile.iot.sc.response.web;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9
 * @description 订单待办展示实体类
 */
@Data
public class Order2CInfoToBacklogDTO {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;

    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;

    private String spuOfferingClassName;

    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDescribe;

    /**
     * 是否允许特殊售后 0-不允许 1-允许
     */
    private Integer requestSpecialAfterStatus;

    /**
     * X产品类型,1:5G CPE,2:5G 快线,3:千里眼,
     * 4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,
     * 7:OnePark独立服务,8:标准产品（OnePark）
     */
    private String productType;

    /**
     * 订单类型00：代客下单（商城直销订单） 01：自主下单 02：代客下单（省内融合订单）
     */
    private String orderType;

    private String orderTypeDesc;

    /**
     * 判断是否是湖南类合同履约
     */
    private Boolean isHunan;

    /**
     * 是否是河南的卡+X订单
     */
    private Boolean isHenanKx;

    /**
     * 千里眼订单服务开通状态
     */
    private Integer qlyStatus;

    /**
     * 云视讯订单服务开通状态
     */
    private Integer ysxStatus;

    /**
     * 商品封面图url
     *
     */
    private String imgUrl;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 行车卫士订购结果  0--开通失败  1--开通成功  2--退订失败 3--退订成功
     */
    private Integer carOpenStatus;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;

    /**
     * 是否卡+X预付费订单
     */
    private Boolean isKaXPrePay;

    /**
     * 是否是行车卫士订单
     */
    private Boolean isCarOrder;

    /**
     * 判断是不是省侧流程（目前只用于判断千里眼是否走省侧流程）
     */
    private Boolean isB2b;

    /**
     * 判断是否是河南合同履约或卡+x(1 . 2 .3类型订单，代发货状态)
     */
    private Boolean isHeNanReal;


    /**
     * 卡+X订单退款状态 0--申请退款 1--同意退款  2--不同意退款 3--取消退款
     */
    private Integer kxRefundStatus;

    /**
     * 接单状态 1--接单 2--拒单
     */
    private Integer allowOrderStatus;

    /**
     * 码号交付状态 0-未交付 1-已交付
     */
    private Integer deliverStatus;

    /**
     * 软件服务开通状态 0-开通成功，1-开通失败， 2-开通中， 3-退订成功， 4-退订失败， 5-退订中， 6-使用中
     */
    private Integer softServiceStatus;
    /**
     * 软件服务所有原子开通状态,目前业务只需要开通成功和开通失败  0-开通成功，1-开通失败
     */
    private Integer softServiceAllStatus;
    /**
     * 软件服务同步Iot状态  0--默认值，未同步；1--开通成功同步iot商城失败;2使用中同步iot商城失败
     */
    private Integer syncIotFailStatus;

    /**
     * 是否含卡终端（卡+X）
     */
    private Boolean hasCard;

    /**
     * 待发货催单次数
     */
    private Integer reminderWaitSend;

    /**
     * 待接单催单次数
     */
    private Integer reminderValetTaking;

    /**
     * 待交付催单次数
     */
    private Integer reminderWaitDeliver;

    /**
     * 是否需要合作伙伴接单,1：是,2：否
     */
    private String receiveOrder;

}
