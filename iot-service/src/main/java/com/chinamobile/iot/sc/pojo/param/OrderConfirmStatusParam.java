package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/7/6 10:42
 */
@Data
public class OrderConfirmStatusParam extends BaseFinancingParam {

    @NotEmpty(message = "订单列表不能为空")
    @Valid
    private List<OrderConfirmItem> orderList;

    /**
     * 审核状态
     * 已拒绝：reject
     * 已确认：pass
     */
    @NotEmpty(message = "审核状态不能为空")
    private String status;

    /**
     * 审核意见
     */
    private String advice;

    @Data
    public static class OrderConfirmItem{

        @NotEmpty(message = "订单编号不能为空")
        /**
         * 订单编号
         */
        private String orderCode;
        /**
         * 预计付款日期 yyyy-MM-dd
         */
        private String estimatedPayDate;
        /**
         * 预计确认金额(元)
         */
        private String estimatedConfirmAmount;
    }

}

