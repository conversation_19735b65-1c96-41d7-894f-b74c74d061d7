package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/4 14:20
 */
@Data
@ConfigurationProperties(prefix="sms")
@Configuration
public class ProductFlowSmsConfig {

    //新流程环节需要通知下一处理人审核的短信模板id
    private String productFlowAuditSms;

    //产品002：通过审核的短信模板id
    private String productFlowPassSms;

    //未通过审核的短信模板id
    private String productFlowRejectSms;

    //知悉的短信模板id
    private String productFlowKnownSms;

    //产品005：上架后全员通知
    private String productFlowShelfCompleteSms;



}
