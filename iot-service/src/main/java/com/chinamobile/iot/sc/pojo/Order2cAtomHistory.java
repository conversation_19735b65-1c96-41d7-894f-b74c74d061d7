package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class Order2cAtomHistory implements Serializable {
    private String atomOrderId;

    private String orderId;

    private String refundOrderId;

    private Integer operateType;

    private String operatorId;

    private Integer innerStatus;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public String getAtomOrderId() {
        return atomOrderId;
    }

    public Order2cAtomHistory withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId == null ? null : atomOrderId.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public Order2cAtomHistory withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getRefundOrderId() {
        return refundOrderId;
    }

    public Order2cAtomHistory withRefundOrderId(String refundOrderId) {
        this.setRefundOrderId(refundOrderId);
        return this;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId == null ? null : refundOrderId.trim();
    }

    public Integer getOperateType() {
        return operateType;
    }

    public Order2cAtomHistory withOperateType(Integer operateType) {
        this.setOperateType(operateType);
        return this;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public Order2cAtomHistory withOperatorId(String operatorId) {
        this.setOperatorId(operatorId);
        return this;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public Order2cAtomHistory withInnerStatus(Integer innerStatus) {
        this.setInnerStatus(innerStatus);
        return this;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Order2cAtomHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public Order2cAtomHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", refundOrderId=").append(refundOrderId);
        sb.append(", operateType=").append(operateType);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", innerStatus=").append(innerStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAtomHistory other = (Order2cAtomHistory) that;
        return (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
                && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
                && (this.getRefundOrderId() == null ? other.getRefundOrderId() == null : this.getRefundOrderId().equals(other.getRefundOrderId()))
                && (this.getOperateType() == null ? other.getOperateType() == null : this.getOperateType().equals(other.getOperateType()))
                && (this.getOperatorId() == null ? other.getOperatorId() == null : this.getOperatorId().equals(other.getOperatorId()))
                && (this.getInnerStatus() == null ? other.getInnerStatus() == null : this.getInnerStatus().equals(other.getInnerStatus()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getRefundOrderId() == null) ? 0 : getRefundOrderId().hashCode());
        result = prime * result + ((getOperateType() == null) ? 0 : getOperateType().hashCode());
        result = prime * result + ((getOperatorId() == null) ? 0 : getOperatorId().hashCode());
        result = prime * result + ((getInnerStatus() == null) ? 0 : getInnerStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        refundOrderId("refund_order_id", "refundOrderId", "VARCHAR", false),
        operateType("operate_type", "operateType", "INTEGER", false),
        operatorId("operator_id", "operatorId", "VARCHAR", false),
        innerStatus("inner_status", "innerStatus", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}