package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description 订单和合作伙伴关系参数类
 */
@Data
public class OrderCooperatorInfoParam {

    /**
     * 原子订单id
     */
    private String atomOrderId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 合作伙伴名称
     */
    private String partnerName;

    /**
     * 合作伙伴用户名称
     */
    private String userName;

    /**
     * 合作伙伴id
     */
    private String cooperatorId;

    /**
     * 原子订单id集合
     */
    private List<String> atomOrderIdList;

}
