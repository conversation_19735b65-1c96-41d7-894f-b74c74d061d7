package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.CarSecurityOrder;
import com.chinamobile.iot.sc.pojo.CarSecurityOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CarSecurityOrderMapper {
    long countByExample(CarSecurityOrderExample example);

    int deleteByExample(CarSecurityOrderExample example);

    int deleteByPrimaryKey(String id);

    int insert(CarSecurityOrder record);

    int insertSelective(CarSecurityOrder record);

    List<CarSecurityOrder> selectByExample(CarSecurityOrderExample example);

    CarSecurityOrder selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CarSecurityOrder record, @Param("example") CarSecurityOrderExample example);

    int updateByExample(@Param("record") CarSecurityOrder record, @Param("example") CarSecurityOrderExample example);

    int updateByPrimaryKeySelective(CarSecurityOrder record);

    int updateByPrimaryKey(CarSecurityOrder record);

    int batchInsert(@Param("list") List<CarSecurityOrder> list);

    int batchInsertSelective(@Param("list") List<CarSecurityOrder> list, @Param("selective") CarSecurityOrder.Column ... selective);
}