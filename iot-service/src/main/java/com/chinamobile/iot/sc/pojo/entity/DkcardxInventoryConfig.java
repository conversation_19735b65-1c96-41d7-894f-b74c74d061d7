package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 代客下单卡+X库存详细配置表（硬件终端详情）
 *
 * <AUTHOR>
public class DkcardxInventoryConfig implements Serializable {
    /**
     * 主键
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String id;

    /**
     * 配置的库存id
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.inventory_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String inventoryId;

    /**
     * 设备型号
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.device_version
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String deviceVersion;

    /**
     * 设备imei号
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.imei
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String imei;

    /**
     * 终端马号
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.msisdn
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String msisdn;

    /**
     * 终端iccid
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.iccid
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String iccid;

    /**
     * 省编码
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.be_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String beId;

    /**
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.province_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String provinceName;

    /**
     * 地市id
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.location
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String location;

    /**
     * 地市名称
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.city_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String cityName;

    /**
     * 销售状态(枚举值):1--未销售 2--销售中 3--已销售 4--销售失败 9--不可销售
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.sale_status
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private String saleStatus;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.update_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private Date updateTime;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..dkcardx_inventory_config.create_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private Date createTime;

    /**
     * Corresponding to the database table supply_chain..dkcardx_inventory_config
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.id
     *
     * @return the value of supply_chain..dkcardx_inventory_config.id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.id
     *
     * @param id the value for supply_chain..dkcardx_inventory_config.id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.inventory_id
     *
     * @return the value of supply_chain..dkcardx_inventory_config.inventory_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getInventoryId() {
        return inventoryId;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withInventoryId(String inventoryId) {
        this.setInventoryId(inventoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.inventory_id
     *
     * @param inventoryId the value for supply_chain..dkcardx_inventory_config.inventory_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setInventoryId(String inventoryId) {
        this.inventoryId = inventoryId == null ? null : inventoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.device_version
     *
     * @return the value of supply_chain..dkcardx_inventory_config.device_version
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getDeviceVersion() {
        return deviceVersion;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withDeviceVersion(String deviceVersion) {
        this.setDeviceVersion(deviceVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.device_version
     *
     * @param deviceVersion the value for supply_chain..dkcardx_inventory_config.device_version
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion == null ? null : deviceVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.imei
     *
     * @return the value of supply_chain..dkcardx_inventory_config.imei
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getImei() {
        return imei;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withImei(String imei) {
        this.setImei(imei);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.imei
     *
     * @param imei the value for supply_chain..dkcardx_inventory_config.imei
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setImei(String imei) {
        this.imei = imei == null ? null : imei.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.msisdn
     *
     * @return the value of supply_chain..dkcardx_inventory_config.msisdn
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getMsisdn() {
        return msisdn;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withMsisdn(String msisdn) {
        this.setMsisdn(msisdn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.msisdn
     *
     * @param msisdn the value for supply_chain..dkcardx_inventory_config.msisdn
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn == null ? null : msisdn.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.iccid
     *
     * @return the value of supply_chain..dkcardx_inventory_config.iccid
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getIccid() {
        return iccid;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withIccid(String iccid) {
        this.setIccid(iccid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.iccid
     *
     * @param iccid the value for supply_chain..dkcardx_inventory_config.iccid
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setIccid(String iccid) {
        this.iccid = iccid == null ? null : iccid.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.be_id
     *
     * @return the value of supply_chain..dkcardx_inventory_config.be_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.be_id
     *
     * @param beId the value for supply_chain..dkcardx_inventory_config.be_id
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.province_name
     *
     * @return the value of supply_chain..dkcardx_inventory_config.province_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.province_name
     *
     * @param provinceName the value for supply_chain..dkcardx_inventory_config.province_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.location
     *
     * @return the value of supply_chain..dkcardx_inventory_config.location
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.location
     *
     * @param location the value for supply_chain..dkcardx_inventory_config.location
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.city_name
     *
     * @return the value of supply_chain..dkcardx_inventory_config.city_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.city_name
     *
     * @param cityName the value for supply_chain..dkcardx_inventory_config.city_name
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.sale_status
     *
     * @return the value of supply_chain..dkcardx_inventory_config.sale_status
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public String getSaleStatus() {
        return saleStatus;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withSaleStatus(String saleStatus) {
        this.setSaleStatus(saleStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.sale_status
     *
     * @param saleStatus the value for supply_chain..dkcardx_inventory_config.sale_status
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setSaleStatus(String saleStatus) {
        this.saleStatus = saleStatus == null ? null : saleStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.update_time
     *
     * @return the value of supply_chain..dkcardx_inventory_config.update_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.update_time
     *
     * @param updateTime the value for supply_chain..dkcardx_inventory_config.update_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..dkcardx_inventory_config.create_time
     *
     * @return the value of supply_chain..dkcardx_inventory_config.create_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public DkcardxInventoryConfig withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..dkcardx_inventory_config.create_time
     *
     * @param createTime the value for supply_chain..dkcardx_inventory_config.create_time
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", inventoryId=").append(inventoryId);
        sb.append(", deviceVersion=").append(deviceVersion);
        sb.append(", imei=").append(imei);
        sb.append(", msisdn=").append(msisdn);
        sb.append(", iccid=").append(iccid);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", location=").append(location);
        sb.append(", cityName=").append(cityName);
        sb.append(", saleStatus=").append(saleStatus);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DkcardxInventoryConfig other = (DkcardxInventoryConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInventoryId() == null ? other.getInventoryId() == null : this.getInventoryId().equals(other.getInventoryId()))
            && (this.getDeviceVersion() == null ? other.getDeviceVersion() == null : this.getDeviceVersion().equals(other.getDeviceVersion()))
            && (this.getImei() == null ? other.getImei() == null : this.getImei().equals(other.getImei()))
            && (this.getMsisdn() == null ? other.getMsisdn() == null : this.getMsisdn().equals(other.getMsisdn()))
            && (this.getIccid() == null ? other.getIccid() == null : this.getIccid().equals(other.getIccid()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getSaleStatus() == null ? other.getSaleStatus() == null : this.getSaleStatus().equals(other.getSaleStatus()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    /**
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInventoryId() == null) ? 0 : getInventoryId().hashCode());
        result = prime * result + ((getDeviceVersion() == null) ? 0 : getDeviceVersion().hashCode());
        result = prime * result + ((getImei() == null) ? 0 : getImei().hashCode());
        result = prime * result + ((getMsisdn() == null) ? 0 : getMsisdn().hashCode());
        result = prime * result + ((getIccid() == null) ? 0 : getIccid().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getSaleStatus() == null) ? 0 : getSaleStatus().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..dkcardx_inventory_config
     *
     * @mbg.generated Sat May 11 09:34:11 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        inventoryId("inventory_id", "inventoryId", "VARCHAR", false),
        deviceVersion("device_version", "deviceVersion", "VARCHAR", false),
        imei("imei", "imei", "VARCHAR", false),
        msisdn("msisdn", "msisdn", "VARCHAR", false),
        iccid("iccid", "iccid", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        saleStatus("sale_status", "saleStatus", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..dkcardx_inventory_config
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Sat May 11 09:34:11 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}