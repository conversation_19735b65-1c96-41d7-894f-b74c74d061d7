package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.CarSecurityOrderMapper;
import com.chinamobile.iot.sc.pojo.CarSecurityOrder;
import com.chinamobile.iot.sc.pojo.CarSecurityOrderExample;
import com.chinamobile.iot.sc.service.CarSecurityOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3
 * @description 行车卫士订单关联service实现类
 */
@Service
public class CarSecurityOrderServiceImpl implements CarSecurityOrderService {

    @Resource
    private CarSecurityOrderMapper carSecurityOrderMapper;

    @Override
    public void addCarSecurityOrder(CarSecurityOrder carSecurityOrder) {
        carSecurityOrderMapper.insert(carSecurityOrder);
    }

    @Override
    public List<CarSecurityOrder> getCarSecurityOrderByExample(CarSecurityOrderExample carSecurityOrderExample) {
        return carSecurityOrderMapper.selectByExample(carSecurityOrderExample);
    }

    @Override
    public void updateCarSecurityOrderByExample(CarSecurityOrder carSecurityOrder,
                                                CarSecurityOrderExample carSecurityOrderExample) {
        carSecurityOrderMapper.updateByExampleSelective(carSecurityOrder,carSecurityOrderExample);
    }
}
