package com.chinamobile.iot.sc.constant.softService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/10
 * @description 软件服务退订状态枚举类
 */
public enum SoftServiceRetailStatusEnum {

    RETAIL_SUCESS(0,"退订成功"),
    RETAIL_FAIL(1,"退订失败"),
    RETAILING(2,"退订中");

    /**
     * 退订状态类型
     */
    private Integer type;

    /**
     * 退订状态描述
     */
    private String desc;

    SoftServiceRetailStatusEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        for (SoftServiceRetailStatusEnum value : SoftServiceRetailStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
