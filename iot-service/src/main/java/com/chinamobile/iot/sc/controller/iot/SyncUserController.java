package com.chinamobile.iot.sc.controller.iot;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.AftermarketOfferingCode;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoHistory;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.IProductNavigationDirectoryService;
import com.chinamobile.iot.sc.service.IProductService;
import com.chinamobile.iot.sc.service.ShopUserOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * @Author: YSC
 * @Date: 2021/10/29 14:14
 * @Description: 商品控制
 */
@RestController
@RequestMapping("/os/userservice")
public class SyncUserController {

    @Resource
    private ShopUserOperationService shopUserOperationService;
    /**
     * 商城客户经理信息实时同步
     * @param baseRequest
     * @return
     */
    @PostMapping("/syncAccountManagerDataRealTime")
    public IOTAnswer<Void> syncAccountManagerDataRealTime(@RequestBody IOTRequest baseRequest){
        return shopUserOperationService.syncAccountManagerDataRealTime(baseRequest);
    }

    /**
     * 商城客户信息（含普通客户及分销员、渠道商角色）实时同步
     * @return
     */
    @PostMapping("/syncCustomerDataRealTime")
    public IOTAnswer<Void> syncCustomerDataRealTime(@RequestBody IOTRequest iotRequest){
        return shopUserOperationService.syncCustomerDataRealTime(iotRequest);
    }

}
