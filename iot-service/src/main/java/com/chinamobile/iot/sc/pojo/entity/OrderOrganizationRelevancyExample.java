package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class OrderOrganizationRelevancyExample {
    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public OrderOrganizationRelevancyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public OrderOrganizationRelevancyExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public OrderOrganizationRelevancyExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        OrderOrganizationRelevancyExample example = new OrderOrganizationRelevancyExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public OrderOrganizationRelevancyExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public OrderOrganizationRelevancyExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdIsNull() {
            addCriterion("organization_relation_id is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdIsNotNull() {
            addCriterion("organization_relation_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdEqualTo(String value) {
            addCriterion("organization_relation_id =", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdNotEqualTo(String value) {
            addCriterion("organization_relation_id <>", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdNotEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdGreaterThan(String value) {
            addCriterion("organization_relation_id >", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdGreaterThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdGreaterThanOrEqualTo(String value) {
            addCriterion("organization_relation_id >=", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdGreaterThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLessThan(String value) {
            addCriterion("organization_relation_id <", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLessThanColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLessThanOrEqualTo(String value) {
            addCriterion("organization_relation_id <=", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLessThanOrEqualToColumn(OrderOrganizationRelevancy.Column column) {
            addCriterion(new StringBuilder("organization_relation_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLike(String value) {
            addCriterion("organization_relation_id like", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdNotLike(String value) {
            addCriterion("organization_relation_id not like", value, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdIn(List<String> values) {
            addCriterion("organization_relation_id in", values, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdNotIn(List<String> values) {
            addCriterion("organization_relation_id not in", values, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdBetween(String value1, String value2) {
            addCriterion("organization_relation_id between", value1, value2, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdNotBetween(String value1, String value2) {
            addCriterion("organization_relation_id not between", value1, value2, "organizationRelationId");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOrganizationRelationIdLikeInsensitive(String value) {
            addCriterion("upper(organization_relation_id) like", value.toUpperCase(), "organizationRelationId");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated do_not_delete_during_merge Fri Jan 03 15:45:38 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..order_organization_relevancy
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        private OrderOrganizationRelevancyExample example;

        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        protected Criteria(OrderOrganizationRelevancyExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        public OrderOrganizationRelevancyExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri Jan 03 15:45:38 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..order_organization_relevancy
     *
     * @mbg.generated Fri Jan 03 15:45:38 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri Jan 03 15:45:38 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.OrderOrganizationRelevancyExample example);
    }
}