package com.chinamobile.iot.sc.response.web;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 * @description 退换售后待办
 */
@Data
public class OrderRocBacklogInfoDTO {

    private String id;

    private String refundOrderId;
    private String refundsType;

    /**
     * 内部退款状态 1 通过 2 不通过 3 取消
     */
    private Integer orderStatus;

    /**
     * 退款、售后状态描述
     */
    private String orderStatusDescribe;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;

    /**
     * 图片数组
     */
    private List<String> pictures;
    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;


    /**
     * 是否是湖南合同履约
     */
    private Boolean isHunanA07;
    

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 图片内部路径
     */
    private String imgUrl;

    /**
     * 是否为含卡终端（卡+X有值，其他无值）
     */
    private Boolean hasCard;

    /**
     * 催单次数
     */
    private Integer reminderCount;

}
