//package com.chinamobile.iot.sc.util;
//
//import com.alibaba.excel.support.ExcelTypeEnum;
//import com.chinamobile.iot.sc.util.excel.Excel2Pdf;
//import com.chinamobile.iot.sc.util.excel.ExcelObject;
//import com.itextpdf.text.RectangleReadOnly;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.codec.binary.Base64;
//import org.apache.poi.hssf.usermodel.HSSFWorkbook;
//import org.apache.poi.poifs.filesystem.POIFSFileSystem;
//import org.apache.poi.xssf.streaming.SXSSFWorkbook;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//
//import java.io.*;
//import java.nio.charset.Charset;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
///**
// * @package: com.chinamobile.iot.sc.util
// * @ClassName: Excel2PDFUtil
// * @description: excel转pdf工具类
// * @author: zyj
// * @create: 2022/2/11 14:04
// * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
// **/
//@Slf4j
//public class Excel2PDFUtil {
//
//    public static void main(String[] args) throws Exception {
//        /*String excelPath = "test.xlsx";
//        String pdfPath = "goods.pdf";
////        excel2pdf(excelPath, pdfPath);
//        byte[] bytes = loadFile(pdfPath);
//        System.out.println(bytes);
//        String pdfNewPath = "D:\\file\\goods_new.pdf";
//        byteToFile(bytes, pdfNewPath);*/
//
//        String excelNewPath = "D:\\file\\goods_new_0222.xls";
//        String jsonByte1 = "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";
//        String jsonByte = "77u/IklvVOW6lOeUqOWVhuWfjiDotK3nianmuIXljZUiUERGUERGUERGUERGUERGDQoNCiLorqLljZXnvJblj7fvvJo3MDEwMDAwMDAwMDE1NjAyNCJQREYi6K6i6LSt5pe26Ze077yaMjAyMC0xMi0yNyAxNjoxMDo0NSJQREZQREYi5a6i5oi35aeT5ZCN77ya5p2O55qEIlBERlBERiLllYblk4HmlbDph4/vvJoxIg0KIuWVhuWTgeWQjeensCjop4TmoLwpIlBERiLllYblk4HnvJbnoIEo6KeE5qC8KSJQREYi5Y6f5a2Q5ZWG5ZOBIlBERiLmlbDph48iUERGIuS7t+agvCJQREYi5bCP6K6hIg0KIjJD5ZCM5q2l5o6l5Y+j6LCD55SoIlBERiI4MDAwMDAwMDQ3IlBERiLnoazku7bljp/lrZDllYblk4EyIlBERiIxKjEiUERGIiJQREYxMA0K";
//        String jsonByte0223 = "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";
//        String pdfPath = "D:\\file\\goods_new_0223.pdf";
//
//        byte[] bytes1 = Base64.decodeBase64(jsonByte0223);
//        Excel2PDFUtil.byteToFile(bytes1, pdfPath);
//
//        //将模版和要写入模版的值传入，转换成workbook
//        /*org.apache.poi.ss.usermodel.Workbook workbook = Excel2PDFUtil.outPutWorkbookByModel(pdfNewPath);
//        List<org.apache.poi.ss.usermodel.Workbook> workbooks = new ArrayList<>();
//        workbooks.add(workbook);
//        //设置导出的页面的大小
//        RectangleReadOnly pageSize = new RectangleReadOnly(1000.0F, 850.0F);
//        String pathOfPdf = "D:\\file\\goods_new0913.pdf";
//        FileOutputStream fos = new FileOutputStream(pathOfPdf);
//
//        Excel2PDFUtil.excelConvertPDF(workbooks, fos, pageSize);*/
//
//        //方法一
//        System.out.println(System.getProperty("file.encoding"));
//        //方法二
//        System.out.println(Charset.defaultCharset());
//    }
//
//    /**
//     * excel转PDF
//     * @param excelPath
//     * @param pdfPath
//     */
//    public static void excel2PDF4POI(String excelPath, String pdfPath){
//        FileOutputStream fos = null;
//        try {
//            //将模版和要写入模版的值传入，转换成workbook
//            org.apache.poi.ss.usermodel.Workbook workbook = Excel2PDFUtil.outPutWorkbookByModel(excelPath);
//            List<org.apache.poi.ss.usermodel.Workbook> workbooks = new ArrayList<>();
//            workbooks.add(workbook);
//            //设置导出的页面的大小
//            RectangleReadOnly pageSize = new RectangleReadOnly(1000.0F, 850.0F);
//            //PDF文件输出流
//            fos = new FileOutputStream(pdfPath);
//            //excel根据页面大小转PDF
//            Excel2PDFUtil.excelConvertPDF(workbooks, fos, pageSize);
//        }catch (Exception e){
//            log.error("excel根据页面大小转PDF失败！", e.getMessage());
//        }finally {
//            if(fos != null){
//                try {
//                    fos.close();
//                } catch (IOException e) {
//                   log.error("文件输出流失败！,{}", e.getMessage());
//                }
//            }
//        }
//    }
//
//    /**
//     * 将文件转二进制字节
//     * @param fileNm
//     * @return
//     */
//    public static byte[] loadFile(String fileNm) {
//        File file = new File(fileNm);
//        FileInputStream fis = null;
//        ByteArrayOutputStream baos = null;
//        byte[] data = null;
//
//        try {
//            fis = new FileInputStream(file);
//            // baos = new ByteArrayOutputStream((int)file.length());
//            baos = new ByteArrayOutputStream(2048);
//            byte[] e = new byte[1024];
//            int len;
//            while ((len = fis.read(e)) != -1) {
//                baos.write(e, 0, len);
//            }
//
//            data = baos.toByteArray();
//        } catch (IOException var15) {
//            var15.printStackTrace();
//        } finally {
//            try {
//                if (fis != null) {
//                    fis.close();
//                    fis = null;
//                }
//                if (null != baos) {
//                    baos.close();
//                    baos = null;
//                }
//            } catch (IOException var14) {
//                var14.printStackTrace();
//            }
//        }
//        return data;
//    }
//
//    /**
//     * @Title: byteToFile
//     * @Description: 把二进制数据转成指定后缀名的文件，例如PDF，PNG等
//     * @param contents 二进制数据
//     * @param filePath 文件存放目录，包括文件名及其后缀，如D:\file\bike.jpg
//     * @Author: Wiener
//     * @Time: 2018-08-26 08:43:36
//     */
//    public static void byteToFile(byte[] contents, String filePath) {
//        BufferedInputStream bis = null;
//        FileOutputStream fos = null;
//        BufferedOutputStream output = null;
//        try {
//            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(contents);
//            bis = new BufferedInputStream(byteInputStream);
//            File file = new File(filePath);
//            // 获取文件的父路径字符串
//            File path = file.getParentFile();
//            if (!path.exists()) {
//                log.info("文件夹不存在，创建。path={}", path);
//                boolean isCreated = path.mkdirs();
//                if (!isCreated) {
//                    log.error("创建文件夹失败，path={}", path);
//                }
//            }
//            fos = new FileOutputStream(file);
//            // 实例化OutputString 对象
//            output = new BufferedOutputStream(fos);
//            byte[] buffer = new byte[1024];
//            int length = bis.read(buffer);
//            while (length != -1) {
//                output.write(buffer, 0, length);
//                length = bis.read(buffer);
//            }
//            output.flush();
//        } catch (Exception e) {
//            log.error("输出文件流时抛异常，filePath={}", filePath, e);
//        } finally {
//            try {
//                bis.close();
//                fos.close();
//                output.close();
//            } catch (IOException e0) {
//                log.error("文件处理失败，filePath={}", filePath, e0);
//            }
//        }
//    }
//
//    /**
//     * excel转为pdf并导出
//     *目前不支持多sheet  如果有多个sheet建议分成多个xls文档，可以导出到一个pdf文件里面
//     * @param
//     * @param out
//     * @throws IOException
//     */
//    public static void excelConvertPDF(List<org.apache.poi.ss.usermodel.Workbook> workbook, OutputStream out, RectangleReadOnly pageSize) throws Exception {
//        List<ExcelObject> objects = new ArrayList<>();
//        for (org.apache.poi.ss.usermodel.Workbook wb : workbook) {
//            objects.add(new ExcelObject(null, wb));
//        }
//        Excel2Pdf pdf = new Excel2Pdf(objects, out,Boolean.FALSE,pageSize);
//        pdf.convert();
//    }
//
//    /**
//     * excel转pdf的中间方法
//     * @param excelPath
//     * @return
//     * @throws Exception
//     */
//    public static org.apache.poi.ss.usermodel.Workbook outPutWorkbookByModel(String excelPath) throws Exception {
//        FileInputStream fis = new FileInputStream(excelPath);
//        org.apache.poi.ss.usermodel.Workbook workbook = createWorkBook(fis, ExcelTypeEnum.XLS);
//        return workbook;
//
//    }
//
//    /**
//     * 读入excel的模版流 返回Workbook对象
//     *
//     * @param templateInputStream
//     * @param excelType
//     * @return
//     * @throws IOException
//     */
//    public static org.apache.poi.ss.usermodel.Workbook createWorkBook(InputStream templateInputStream, ExcelTypeEnum excelType) throws IOException {
//        org.apache.poi.ss.usermodel.Workbook workbook;
//        if (ExcelTypeEnum.XLS.equals(excelType)) {
//            workbook = (templateInputStream == null) ? new HSSFWorkbook() : new HSSFWorkbook(
//                    new POIFSFileSystem(templateInputStream));
//        } else {
//            workbook = (templateInputStream == null) ? new SXSSFWorkbook(500) : new XSSFWorkbook(templateInputStream);
//        }
//        return workbook;
//    }
//}
