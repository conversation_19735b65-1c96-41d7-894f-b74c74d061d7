package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.param.SortNavigationParam;
import com.chinamobile.iot.sc.pojo.param.UpdateNavigationImageParam;
import com.chinamobile.iot.sc.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.iot.sc.request.ProductNavigationInfoMigrateRequest;
import com.chinamobile.iot.sc.request.ProductNavigationInfoRequest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/21 11:16
 * @description TODO
 */
public interface IProductNavigationDirectoryService {

    List<ProductNavigationDirectoryVO> listAll();

    void updateImage(UpdateNavigationImageParam param);

    void sort(List<SortNavigationParam> params);

    IOTAnswer<Void> SyncNavigationInfo(@RequestBody IOTRequest baseRequest);

    void migrateProductNavigation(ProductNavigationInfoMigrateRequest request);
}
