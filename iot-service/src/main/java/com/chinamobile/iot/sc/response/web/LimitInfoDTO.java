package com.chinamobile.iot.sc.response.web;

import lombok.Data;

import java.util.Date;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2025/1/2 10:45
 * @Description:
 */
@Data
public class LimitInfoDTO {

    /**
     * ID
     */

    private String id;


    /**
     * 产品名称
     */

    private String productName;


    /**
     * 服务包编码
     */

    private String serviceCode;


    /**
     * 服务包名称
     */

    private String serviceName;
    /**
     * 额度状态 0短缺，1充足，2失效
     */
    private String status;

    /**
     * 省份编码
     */

    private String companyId;


    /**
     * 商城授权额度总额
     */

    private Double iotLimit;


    /**
     * 预占额度
     */

    private Double reserveQuatity;
//    /**
//     * 剩余额度
//     */
//
//    private long residualQuatity;
    /**
     * 已使用额度
     */
    private Double useInventory;


    /**
     * 当前总额度
     */

    private Double currentInventory;


    /**
     * 创建时间
     */

    private Date createTime;


    /**
     * 更新时间
     */

    private Date updateTime;


    /**
     * 生效时间，格式：YYYYMMDDHH24MISS
     */

    private String efftime;


    /**
     * 截止时间，格式：YYYYMMDDHH24MISS
     */

    private String exptime;


    /**
     * 省名称
     */

    private String companyName;
    /**
     * 账目项id
     */
    private String chargeId;

}
