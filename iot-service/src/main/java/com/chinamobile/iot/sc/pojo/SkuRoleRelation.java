package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商品与合作伙伴角色关联表
 *
 * <AUTHOR>
public class SkuRoleRelation implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private String id;

    /**
     * 合伙人角色id,值对应user_retail表的role_type
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private Integer partnerRoleId;

    /**
     * sku商品id
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private String skuId;

    /**
     * 积分比例
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private Double pointPercent;

    /**
     * 积分上限，单位厘
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private Long pointLimit;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.id
     *
     * @return the value of supply_chain..sku_role_relation.id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.id
     *
     * @param id the value for supply_chain..sku_role_relation.id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.partner_role_id
     *
     * @return the value of supply_chain..sku_role_relation.partner_role_id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public Integer getPartnerRoleId() {
        return partnerRoleId;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withPartnerRoleId(Integer partnerRoleId) {
        this.setPartnerRoleId(partnerRoleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.partner_role_id
     *
     * @param partnerRoleId the value for supply_chain..sku_role_relation.partner_role_id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setPartnerRoleId(Integer partnerRoleId) {
        this.partnerRoleId = partnerRoleId;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.sku_id
     *
     * @return the value of supply_chain..sku_role_relation.sku_id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withSkuId(String skuId) {
        this.setSkuId(skuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.sku_id
     *
     * @param skuId the value for supply_chain..sku_role_relation.sku_id
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.point_percent
     *
     * @return the value of supply_chain..sku_role_relation.point_percent
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public Double getPointPercent() {
        return pointPercent;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withPointPercent(Double pointPercent) {
        this.setPointPercent(pointPercent);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.point_percent
     *
     * @param pointPercent the value for supply_chain..sku_role_relation.point_percent
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setPointPercent(Double pointPercent) {
        this.pointPercent = pointPercent;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.point_limit
     *
     * @return the value of supply_chain..sku_role_relation.point_limit
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public Long getPointLimit() {
        return pointLimit;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withPointLimit(Long pointLimit) {
        this.setPointLimit(pointLimit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.point_limit
     *
     * @param pointLimit the value for supply_chain..sku_role_relation.point_limit
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setPointLimit(Long pointLimit) {
        this.pointLimit = pointLimit;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.create_time
     *
     * @return the value of supply_chain..sku_role_relation.create_time
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.create_time
     *
     * @param createTime the value for supply_chain..sku_role_relation.create_time
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_role_relation.update_time
     *
     * @return the value of supply_chain..sku_role_relation.update_time
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public SkuRoleRelation withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_role_relation.update_time
     *
     * @param updateTime the value for supply_chain..sku_role_relation.update_time
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", partnerRoleId=").append(partnerRoleId);
        sb.append(", skuId=").append(skuId);
        sb.append(", pointPercent=").append(pointPercent);
        sb.append(", pointLimit=").append(pointLimit);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SkuRoleRelation other = (SkuRoleRelation) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPartnerRoleId() == null ? other.getPartnerRoleId() == null : this.getPartnerRoleId().equals(other.getPartnerRoleId()))
            && (this.getSkuId() == null ? other.getSkuId() == null : this.getSkuId().equals(other.getSkuId()))
            && (this.getPointPercent() == null ? other.getPointPercent() == null : this.getPointPercent().equals(other.getPointPercent()))
            && (this.getPointLimit() == null ? other.getPointLimit() == null : this.getPointLimit().equals(other.getPointLimit()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPartnerRoleId() == null) ? 0 : getPartnerRoleId().hashCode());
        result = prime * result + ((getSkuId() == null) ? 0 : getSkuId().hashCode());
        result = prime * result + ((getPointPercent() == null) ? 0 : getPointPercent().hashCode());
        result = prime * result + ((getPointLimit() == null) ? 0 : getPointLimit().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 10:29:03 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        partnerRoleId("partner_role_id", "partnerRoleId", "INTEGER", false),
        skuId("sku_id", "skuId", "VARCHAR", false),
        pointPercent("point_percent", "pointPercent", "DOUBLE", false),
        pointLimit("point_limit", "pointLimit", "BIGINT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Sep 15 10:29:03 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}