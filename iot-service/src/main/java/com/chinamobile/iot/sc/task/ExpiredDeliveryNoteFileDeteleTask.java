package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.dao.MessageMapper;
import com.chinamobile.iot.sc.dao.ext.MessageMapperExt;
import com.chinamobile.iot.sc.pojo.Message;
import com.chinamobile.iot.sc.pojo.MessageExample;
import com.chinamobile.iot.sc.service.impl.OneNetObjectStorageService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2024/9/26 10:35
 * 异步导出的货单文件，有效期3天，需要定期清理
 */
@Component
@Slf4j
public class ExpiredDeliveryNoteFileDeteleTask {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MessageMapper messageMapper;

    @Resource
    private OneNetObjectStorageService storageService;

    @Resource
    private MessageMapperExt messageMapperExt;

    @Autowired
    private ServiceConfig serviceConfig;


    /**
     * 每天1点运行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void expiredDeliveryNoteFileDeteleTask() {
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent("expiredDeliveryNoteFileDeteleTask", "1",30, TimeUnit.SECONDS);
            if(getLock){
                log.info("expiredDeliveryNoteFileDeteleTask删除过期货单导出pdf任务开始。。。");
                //早于此时间的文件都要删除
                Date minCreateTime = DateTimeUtil.addDay(new Date(), 0-serviceConfig.getOrderExportExcelExpireDays());
                List<String> messageTypeList = new ArrayList<>();
                messageTypeList.add("货单导出");
                MessageExample example = new MessageExample().createCriteria().andCreateTimeLessThan(minCreateTime).andFileKeyIsNotNull().andTypeIn(messageTypeList).example();
                List<Message> messages = messageMapper.selectByExample(example);
                List<String> fileKeyList = messages.stream().map(m -> {
                    return m.getFileKey();
                }).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(fileKeyList)){
                    storageService.delete(fileKeyList.toArray(new String[]{}));

                    List<String> messageIdList = messages.stream().map(m -> {
                        return m.getId();
                    }).collect(Collectors.toList());
                    //清除掉消息中的链接和文件key,更新消息内容,大屏的消息自动变为已读
                    messageMapperExt.clearUrlAndFile(messageIdList);
                }
                log.info("expiredDeliveryNoteFileDeteleTask删除过期货单导出pdf任务结束。。。");
            }
        } catch (Exception e) {
            log.error("expiredDeliveryNoteFileDeteleTask删除过期货单导出pdf任务发生异常",e);
        }
    }
}
