package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class ServicePackLimitSync {
    private String id;

    private String transId;

    private String companyId;

    private String productName;

    private String serviceCode;

    private String serviceName;

    private String status;

    private String efftime;

    private String exptime;

    private Double iotLimit;

    private String operType;

    private Date createTime;

    private Date updateTime;

    public String getId() {
        return id;
    }

    public ServicePackLimitSync withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTransId() {
        return transId;
    }

    public ServicePackLimitSync withTransId(String transId) {
        this.setTransId(transId);
        return this;
    }

    public void setTransId(String transId) {
        this.transId = transId == null ? null : transId.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public ServicePackLimitSync withCompanyId(String companyId) {
        this.setCompanyId(companyId);
        return this;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getProductName() {
        return productName;
    }

    public ServicePackLimitSync withProductName(String productName) {
        this.setProductName(productName);
        return this;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public ServicePackLimitSync withServiceCode(String serviceCode) {
        this.setServiceCode(serviceCode);
        return this;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    public String getServiceName() {
        return serviceName;
    }

    public ServicePackLimitSync withServiceName(String serviceName) {
        this.setServiceName(serviceName);
        return this;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName == null ? null : serviceName.trim();
    }

    public String getStatus() {
        return status;
    }

    public ServicePackLimitSync withStatus(String status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getEfftime() {
        return efftime;
    }

    public ServicePackLimitSync withEfftime(String efftime) {
        this.setEfftime(efftime);
        return this;
    }

    public void setEfftime(String efftime) {
        this.efftime = efftime == null ? null : efftime.trim();
    }

    public String getExptime() {
        return exptime;
    }

    public ServicePackLimitSync withExptime(String exptime) {
        this.setExptime(exptime);
        return this;
    }

    public void setExptime(String exptime) {
        this.exptime = exptime == null ? null : exptime.trim();
    }

    public Double getIotLimit() {
        return iotLimit;
    }

    public ServicePackLimitSync withIotLimit(Double iotLimit) {
        this.setIotLimit(iotLimit);
        return this;
    }

    public void setIotLimit(Double iotLimit) {
        this.iotLimit = iotLimit;
    }

    public String getOperType() {
        return operType;
    }

    public ServicePackLimitSync withOperType(String operType) {
        this.setOperType(operType);
        return this;
    }

    public void setOperType(String operType) {
        this.operType = operType == null ? null : operType.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ServicePackLimitSync withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ServicePackLimitSync withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", transId=").append(transId);
        sb.append(", companyId=").append(companyId);
        sb.append(", productName=").append(productName);
        sb.append(", serviceCode=").append(serviceCode);
        sb.append(", serviceName=").append(serviceName);
        sb.append(", status=").append(status);
        sb.append(", efftime=").append(efftime);
        sb.append(", exptime=").append(exptime);
        sb.append(", iotLimit=").append(iotLimit);
        sb.append(", operType=").append(operType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ServicePackLimitSync other = (ServicePackLimitSync) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTransId() == null ? other.getTransId() == null : this.getTransId().equals(other.getTransId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getServiceCode() == null ? other.getServiceCode() == null : this.getServiceCode().equals(other.getServiceCode()))
            && (this.getServiceName() == null ? other.getServiceName() == null : this.getServiceName().equals(other.getServiceName()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getEfftime() == null ? other.getEfftime() == null : this.getEfftime().equals(other.getEfftime()))
            && (this.getExptime() == null ? other.getExptime() == null : this.getExptime().equals(other.getExptime()))
            && (this.getIotLimit() == null ? other.getIotLimit() == null : this.getIotLimit().equals(other.getIotLimit()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTransId() == null) ? 0 : getTransId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getServiceCode() == null) ? 0 : getServiceCode().hashCode());
        result = prime * result + ((getServiceName() == null) ? 0 : getServiceName().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getEfftime() == null) ? 0 : getEfftime().hashCode());
        result = prime * result + ((getExptime() == null) ? 0 : getExptime().hashCode());
        result = prime * result + ((getIotLimit() == null) ? 0 : getIotLimit().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        transId("trans_iD", "transId", "VARCHAR", false),
        companyId("company_iD", "companyId", "VARCHAR", false),
        productName("product_name", "productName", "VARCHAR", false),
        serviceCode("service_code", "serviceCode", "VARCHAR", false),
        serviceName("service_name", "serviceName", "VARCHAR", false),
        status("status", "status", "VARCHAR", false),
        efftime("efftime", "efftime", "VARCHAR", false),
        exptime("exptime", "exptime", "VARCHAR", false),
        iotLimit("iot_limit", "iotLimit", "DOUBLE", false),
        operType("oper_type", "operType", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}