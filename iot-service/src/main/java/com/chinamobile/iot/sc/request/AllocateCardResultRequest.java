package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;


@Data
public class AllocateCardResultRequest {
    /**
     * 请求流水号
     */
    private String orderSeq;

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 0：插拔卡
     * 1：贴片卡
     * 2：M2M芯片非空写卡
     * 3：M2M芯片空写卡
     */
    private String cardType;
    private List<AllocateCardResultRequest.MsisdnInfo> msisdnInfos;

    @Data
    public static class MsisdnInfo{
        /**
         * 正式服务号码
         */
        private String msisdn;

        /**
         * 临时iccid
         */
        private String iccid;

        /**
         * 终端IMEI
         */
        private String imei;
    }

    private List<AllocateCardResultRequest.CardmsisdnInfo> cardmsisdnInfos;

    @Data
    public static class CardmsisdnInfo{
        /**
         * 服务号码
         */
        private String cardMsisdn;

        /**
         * TAC
         */
        private String cardTac;

    }
    /**
     * 终端IMEI
     */
    private List<String> cardImei;

}
