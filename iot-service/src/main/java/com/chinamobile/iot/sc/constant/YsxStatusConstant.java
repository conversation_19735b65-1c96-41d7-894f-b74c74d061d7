package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> daig<PERSON>jun
 * @date : 2023/10/24 17:13
 * @description: 云视讯服务开通状态
 **/
public class YsxStatusConstant {


    /**
     * 云视讯订单状态:
     * 0-未校验，
     * 1-校验成功，
     * 2-开通成功，
     * 3-开通失败，
     * 4-退订成功
     * 5-退订失败
     * */
    public static final Integer YSX_NOT_CHECK = 0;

    public static final Integer YSX_CHECK_SUCCESS = 1;

    public static final Integer YSX_TURN_ON_SUCCESS = 2;

    public static final Integer YSX_TURN_ON_FAILED = 3;

    public static final Integer YSX_CANCEL_SUCCESS = 4;

    public static final Integer YSX_CANCEL_FAILED = 5;



    /**开通*/
    public static final String YSX_OPR_CODE_TURN_ON = "01";
    /**退订*/
    public static final String YSX_OPR_CODE_CANCEL = "02";
}
