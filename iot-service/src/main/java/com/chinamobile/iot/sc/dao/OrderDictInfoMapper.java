package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.OrderDictInfo;
import com.chinamobile.iot.sc.pojo.OrderDictInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderDictInfoMapper {
    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    long countByExample(OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int deleteByExample(OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int insert(OrderDictInfo record);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int insertSelective(OrderDictInfo record);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    List<OrderDictInfo> selectByExampleWithBLOBs(OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    List<OrderDictInfo> selectByExample(OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    OrderDictInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByExampleSelective(@Param("record") OrderDictInfo record, @Param("example") OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByExampleWithBLOBs(@Param("record") OrderDictInfo record, @Param("example") OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByExample(@Param("record") OrderDictInfo record, @Param("example") OrderDictInfoExample example);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByPrimaryKeySelective(OrderDictInfo record);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByPrimaryKeyWithBLOBs(OrderDictInfo record);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int updateByPrimaryKey(OrderDictInfo record);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int batchInsert(@Param("list") List<OrderDictInfo> list);

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    int batchInsertSelective(@Param("list") List<OrderDictInfo> list, @Param("selective") OrderDictInfo.Column ... selective);
}