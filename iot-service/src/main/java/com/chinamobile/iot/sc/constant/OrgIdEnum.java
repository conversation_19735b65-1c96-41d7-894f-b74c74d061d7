package com.chinamobile.iot.sc.constant;

public enum OrgIdEnum {

    A("安阳市", "482303"),
    B("鹤壁市", "482273"),
    C("济源市", "482277"),
    D("焦作市", "482307"),
    E("开封市", "482313"),
    F("洛阳市", "482269"),
    G("南阳市", "482253"),
    H("平顶山市", "482249"),
    I("三门峡市", "482261"),
    J("商丘市", "482295"),
    K("新乡市", "482325"),
    L("信阳市", "482257"),
    M("许昌市", "482281"),
    N("郑州市", "482251"),
    O("周口市", "482265"),
    P("驻马店市", "482319"),
    Q("漯河市", "482291"),
    R("濮阳市", "482285");


    /**
     * 城市名
     */
    private final String cityName;
    /**
     * 渠道ID
     */
    private final String orgId;

    OrgIdEnum(String cityName, String orgId) {
        this.cityName = cityName;
        this.orgId = orgId;
    }

    /**
     * 根据城市名获取对应的渠道ID
     *
     * @param cityName
     * @return
     */
    public static String getOrgId(String cityName) {
        for (OrgIdEnum value : OrgIdEnum.values()) {
            if (value.cityName.equals(cityName)) {
                return value.orgId;
            }
        }
        return "999999";
    }
}
