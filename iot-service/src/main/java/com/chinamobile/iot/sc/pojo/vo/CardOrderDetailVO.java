package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23
 * @description x终端详情的商品订单信息展示类
 */
@Data
public class CardOrderDetailVO {
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组名称
     */
    private String spuName;

    /**
     * 商品组编码
     */
    private String spuCode;

    /**
     * spu版本号
     */
    private String spuOfferingVersion;

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    private String spuOfferingClassName;

    /**
     * 订购金额
     */
    private String totalPrice;

    /**
     * 收货人姓名
     */
    private String receiverName;


    /**
     * 收货人电话
     */
    private String receiverPhone;
    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 原子订单信息
     */
    private List<CardOrderSkuDetailVO> atomOrders;
}
