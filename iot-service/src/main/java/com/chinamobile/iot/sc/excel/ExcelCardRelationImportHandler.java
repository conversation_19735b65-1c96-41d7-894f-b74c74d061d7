package com.chinamobile.iot.sc.excel;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.chinamobile.iot.sc.pojo.dto.ImportCardRelationDTO;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import com.chinamobile.iot.sc.service.CardRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/31
 * @description 导入终端imei相关信息excel处理类
 */
@Component
public class ExcelCardRelationImportHandler  implements IExcelVerifyHandler<ImportCardRelationDTO> {

    @Resource
    private CardRelationService cardRelationService;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(ImportCardRelationDTO importCardRelationDTO) {
        String errorMsg = importCardRelationDTO.getErrorMsg();
        if (StringUtils.isNotEmpty(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, "");
        }

        // 防止空指针
        errorMsg = "";
        String imei = importCardRelationDTO.getImei();
        String tempIccid = importCardRelationDTO.getTempIccid();
        String sn = importCardRelationDTO.getSn();
        Pattern pattern1 = Pattern.compile("[0-9a-zA-Z]{20}");
        Pattern pattern2 = Pattern.compile("[0-9]{15}");
        if (StringUtils.isEmpty(imei)){
            errorMsg = errorMsg.concat("终端IMEI不能为空");
        }else {
            Matcher imeiMatcher = pattern2.matcher(imei);
            if (!imeiMatcher.matches()){
                errorMsg = errorMsg.concat("终端IMEI为15位数字组成");
            }else {
                CardRelationExample example = new CardRelationExample();
                example.createCriteria()
                        .andDeleteTimeIsNull()
                        .andImeiEqualTo(imei);
                List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    errorMsg = errorMsg.concat("终端IMEI编号已经存在");
                }
            }
        }

        if (StringUtils.isEmpty(tempIccid)){
            errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("临时iccid不能为空")
                    :errorMsg.concat(",临时iccid不能为空");
        }else {
            Matcher iccidMatcher = pattern1.matcher(tempIccid);
            if (!iccidMatcher.matches()){
                errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("临时iccid为20位数字或字母组成")
                        :errorMsg.concat(",临时iccid为20位数字或字母组成");
            }else {
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andDeleteTimeIsNull().andTempIccidEqualTo(tempIccid);
                List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("临时iccid编号已经存在")
                            :errorMsg.concat(",临时iccid编号已经存在");
                }
            }
        }

        if (StringUtils.isNotEmpty(sn)){
            Matcher snMatcher = pattern1.matcher(sn);
            if (!snMatcher.matches()){
                errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("SN为20位数字或字母组成")
                        :errorMsg.concat(",SN为20位数字或字母组成");
            }else {
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andDeleteTimeIsNull().andSnEqualTo(sn);
                List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("sn编号已经存在")
                            :errorMsg.concat(",sn编号已经存在");
                }
            }
        }

        if (StringUtils.isNotEmpty(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        } else {
            return new ExcelVerifyHandlerResult(true);
        }
    }
}
