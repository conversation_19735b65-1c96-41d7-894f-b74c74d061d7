package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/21
 * @description 贸易订单详情实体类
 */
@Data
public class TradeOrderDetailDTO {

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String custCode;

    /**
     * 个人客户省份
     */
    @ExcelProperty(value = "个人客户省份")
    private String province;

    /**
     * 订单计收信息
     */
    private String orgName;

    @ExcelProperty(value = "订单收入归属省")
    private String orgProvince;

    /**
     * 下单时间
     */
    @ExcelProperty(value = "下单时间")
    private String createTime;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderId;

    /**
     * 业务编码
     */
    @ExcelProperty(value = "业务编码")
    private String businessCode;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    @ExcelProperty(value = "订单状态")
    private String orderStatusName;

    /**
     * 订单完成时间
     */
    private Date orderFinishTime;

    @ExcelProperty(value = "订单完成时间")
    private String orderFinishTimeStr;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 订单抵扣金额/元
     */
    @ExcelProperty(value = "订单抵扣金额/元")
    private String orderDeductPrice;

    /**
     * 关联领货码
     */
    @ExcelProperty(value = "关联领货码")
    private String couponInfo;

    /**
     * 收货地区
     */
    @ExcelProperty(value = "收货地区")
    private String deliveryArea;

    /**
     * 物流单号
     */
    @ExcelProperty(value = "物流单号")
    private String logisCode;

    /**
     * 设备sn
     */
    @ExcelProperty(value = "设备sn")
    private String sn;

    /**
     * 商品组/销售商品名称
     */
    @ExcelProperty(value = "商品组/销售商品名称")
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    @ExcelProperty(value = "商品组/销售商品编码")
    private String spuOfferingCode;

    /**
     * 商品类型
     */
    @ExcelProperty(value = "商品类型")
    private String spuOfferingClass;

    /**
     * 商品名称(规格)
     */
    @ExcelProperty(value = "商品名称(规格)")
    private String skuOfferingName;

    /**
     * 商品编码（规格）
     */
    @ExcelProperty(value = "商品编码（规格）")
    private String skuOfferingCode;

    /**
     * 订购数量（规格）
     */
    @ExcelProperty(value = "订购数量（规格）")
    private Long skuQuantity;

    /**
     * 原子商品名称
     */
    @ExcelProperty(value = "原子商品名称")
    private String atomOfferingName;

    /**
     * 原子商品编码
     */
    @ExcelProperty(value = "原子商品编码")
    private String atomOfferingCode;

    /**
     * 原子商品类型
     */
    @ExcelProperty(value = "原子商品类型")
    private String atomOfferingClass;

    /**
     * 数量（原子商品）
     */
    @ExcelProperty(value = "数量（原子商品）")
    private Long atomQuantity;

    /**
     * 移动省专公司名称
     */
    @ExcelProperty(value = "移动省专公司名称")
    private String sellerName;

    /**
     * 移动省专合同编号
     */
    @ExcelProperty(value = "移动省专合同编号")
    private String contractNum;

    /**
     * 移动省专合同名称
     */
    @ExcelProperty(value = "移动省专合同名称")
    private String contractName;

    /**
     * 合同相对方名称
     */
    private String buyerName;


    @ExcelProperty(value = "合作伙伴名称")
    private String partnerName;

    /**
     * 结算金额
     */
    private Long baoliSettlePrice;

    @ExcelProperty(value = "结算金额")
    private BigDecimal baoliSettlePriceDec;
}
