package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.OpenAbilityOrganization;
import com.chinamobile.iot.sc.pojo.OpenAbilityOrganizationExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OpenAbilityOrganizationMapper {
    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    long countByExample(OpenAbilityOrganizationExample example);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int deleteByExample(OpenAbilityOrganizationExample example);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int insert(OpenAbilityOrganization record);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int insertSelective(OpenAbilityOrganization record);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    List<OpenAbilityOrganization> selectByExample(OpenAbilityOrganizationExample example);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    OpenAbilityOrganization selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int updateByExampleSelective(@Param("record") OpenAbilityOrganization record, @Param("example") OpenAbilityOrganizationExample example);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int updateByExample(@Param("record") OpenAbilityOrganization record, @Param("example") OpenAbilityOrganizationExample example);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int updateByPrimaryKeySelective(OpenAbilityOrganization record);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int updateByPrimaryKey(OpenAbilityOrganization record);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int batchInsert(@Param("list") List<OpenAbilityOrganization> list);

    /**
     *
     * @mbg.generated Wed Jun 05 16:58:33 CST 2024
     */
    int batchInsertSelective(@Param("list") List<OpenAbilityOrganization> list, @Param("selective") OpenAbilityOrganization.Column ... selective);
}