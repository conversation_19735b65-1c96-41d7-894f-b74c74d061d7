package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuDTO;
import com.chinamobile.iot.sc.pojo.param.ProductFlowSkuParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 * @description 产品流程sku service接口类
 */
public interface ProductFlowInstanceSkuService {

    /**
     * 获取流程实例的sku列表
     *
     * @param productFlowSkuParam
     * @return
     */
    BaseAnswer<List<ProductFlowInstanceSkuDTO>> listProductFlowInstanceSku(ProductFlowSkuParam productFlowSkuParam);
}
