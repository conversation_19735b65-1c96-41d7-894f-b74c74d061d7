package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2022/1/6 16:03
 * @Description:
 */
@Data
public class ConfirmReturnOrderDTO {
    /**
     * 退款订单请求流水号
     */
    private String refundOrderId;
    /**
     * 业务订单流水号
     */
    private String orderId;
    /**
     * 是否收货验货成功	返回0：收货验货成功，1：收货验货失败
     */
    private String receiptResults;
    /**
     * 验货失败原因	当 receiptResults返回1时必填
     */
    private String receiptReason;
}
