package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 能力开放字段规则
 *
 * <AUTHOR>
public class OpenAbilityThemeFieldRule implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String id;

    /**
     * 字段名称
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String name;

    /**
     * 主题ID
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String themeId;

    /**
     * 主题内容Id，标识接口或者订阅内容；主题本身的配置字段为null
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String contentId;

    /**
     * 字段描述
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String description;

    /**
     * 字段类型，string-字符串，number-数值型，bool-布尔型，time-时间型，enum-枚举型
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String type;

    /**
     * 枚举型时的取值列表所转换的json字符串
     *
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private String rangeJson;

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.id
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.id
     *
     * @param id the value for supply_chain..open_ability_theme_field_rule.id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.name
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.name
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.name
     *
     * @param name the value for supply_chain..open_ability_theme_field_rule.name
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.theme_id
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.theme_id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getThemeId() {
        return themeId;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withThemeId(String themeId) {
        this.setThemeId(themeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.theme_id
     *
     * @param themeId the value for supply_chain..open_ability_theme_field_rule.theme_id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setThemeId(String themeId) {
        this.themeId = themeId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.content_id
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.content_id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getContentId() {
        return contentId;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withContentId(String contentId) {
        this.setContentId(contentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.content_id
     *
     * @param contentId the value for supply_chain..open_ability_theme_field_rule.content_id
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.description
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.description
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withDescription(String description) {
        this.setDescription(description);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.description
     *
     * @param description the value for supply_chain..open_ability_theme_field_rule.description
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.type
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.type
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withType(String type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.type
     *
     * @param type the value for supply_chain..open_ability_theme_field_rule.type
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_field_rule.range_json
     *
     * @return the value of supply_chain..open_ability_theme_field_rule.range_json
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public String getRangeJson() {
        return rangeJson;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public OpenAbilityThemeFieldRule withRangeJson(String rangeJson) {
        this.setRangeJson(rangeJson);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_field_rule.range_json
     *
     * @param rangeJson the value for supply_chain..open_ability_theme_field_rule.range_json
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public void setRangeJson(String rangeJson) {
        this.rangeJson = rangeJson;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", themeId=").append(themeId);
        sb.append(", contentId=").append(contentId);
        sb.append(", description=").append(description);
        sb.append(", type=").append(type);
        sb.append(", rangeJson=").append(rangeJson);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OpenAbilityThemeFieldRule other = (OpenAbilityThemeFieldRule) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getThemeId() == null ? other.getThemeId() == null : this.getThemeId().equals(other.getThemeId()))
            && (this.getContentId() == null ? other.getContentId() == null : this.getContentId().equals(other.getContentId()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getRangeJson() == null ? other.getRangeJson() == null : this.getRangeJson().equals(other.getRangeJson()));
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getThemeId() == null) ? 0 : getThemeId().hashCode());
        result = prime * result + ((getContentId() == null) ? 0 : getContentId().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getRangeJson() == null) ? 0 : getRangeJson().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 10:28:17 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        themeId("theme_id", "themeId", "VARCHAR", false),
        contentId("content_id", "contentId", "VARCHAR", false),
        description("description", "description", "VARCHAR", false),
        type("type", "type", "VARCHAR", false),
        rangeJson("range_json", "rangeJson", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jun 04 10:28:17 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}