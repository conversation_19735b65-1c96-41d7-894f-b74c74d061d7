package com.chinamobile.iot.sc.pojo.dto;

import com.chinamobile.iot.sc.pojo.mapper.ProductFlowDO;
import com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailAtomDO;
import com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailSkuDO;
import com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailSpuDO;
import com.chinamobile.iot.sc.pojo.vo.AttachmentListVO;
import lombok.Data;

import java.util.List;

@Data
public class ShelfSpuDetailDTO {
    //spu信息
    private ShelfSpuDetailSpuDO spuItem;
    //sku信息
    private ShelfSpuDetailSkuDO skuItem;
    //原子商品信息
    private ShelfSpuDetailAtomDO atomItem;
    //附件
    /**
     * 附件列表
     */
    private List<AttachmentListVO> fileList;
    //关联的变更/下架流程单
    private List<ProductFlowDO> flowInstanceList;
}
