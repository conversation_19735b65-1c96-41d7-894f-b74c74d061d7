package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2025/5/26 14:23
 * @description:
 **/
@Data
public class CardInfoInventoryErrorParam {

    @NotEmpty(message = "主键id不能为空")
    private String id;

    /**
     * 1；修改库存信息 2：删除 3 修改库存信息关联的号卡信息
     */
    private String errorLabel;

    /**
     * 生成多的码号库存主键id
     */
    private String errorId;

    private Integer  reserveQuatity;

    private Integer currentInventory;

    private Integer totalInventory;
}
