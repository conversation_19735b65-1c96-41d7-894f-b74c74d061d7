package com.chinamobile.iot.sc.excel;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.TerminalTypeEnum;
import com.chinamobile.iot.sc.pojo.dto.ImportNewCardRelationToCardDTO;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.param.ImportCardRelationToCardParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;
import com.chinamobile.iot.sc.request.product.ProvinceCityVO;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardRelationService;
import com.chinamobile.iot.sc.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/29
 * @description 新模板导入终端imei相关信息到指定终端类型excel处理类
 */
@Component
@Slf4j
public class ExcelNewCardRelationImportToCardHandler implements IExcelVerifyHandler<ImportNewCardRelationToCardDTO> {

    @Resource
    private CardRelationService cardRelationService;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Resource
    private CardInfoService cardInfoService;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    private ImportCardRelationToCardParam importCardRelationToCardParam;

    public ImportCardRelationToCardParam getImportCardRelationToCardParam() {
        return importCardRelationToCardParam;
    }

    public void setImportCardRelationToCardParam(ImportCardRelationToCardParam importCardRelationToCardParam) {
        this.importCardRelationToCardParam = importCardRelationToCardParam;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(ImportNewCardRelationToCardDTO importNewCardRelationDTO) {
        String errorMsg = importNewCardRelationDTO.getErrorMsg();
        if (StringUtils.isNotEmpty(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, "");
        }

        Map<Object, Object> provinceNameCodeMap = areaDataConfig.getProvinceNameCodeMap();
        Map<Object, Object> locationNameCodeMap = areaDataConfig.getLocationNameCodeMap();
        // 防止空指针
        errorMsg = "";
        log.info("批量导入卡+X终端到指定终端类型的数据:{}", JSONObject.toJSONString(importNewCardRelationDTO));
        String terminalType = importCardRelationToCardParam.getTerminalType();
        String imei = ExcelUtils.replaceBlank(importNewCardRelationDTO.getImei());
        String tempIccid = ExcelUtils.replaceBlank(importNewCardRelationDTO.getTempIccid());
        String msisdn = ExcelUtils.replaceBlank(importNewCardRelationDTO.getMsisdn());
        String beId = importCardRelationToCardParam.getBeId();
        String cityName = importNewCardRelationDTO.getCityName();
        String deviceVersion = importCardRelationToCardParam.getDeviceVersion();

        CardInfoParam cardInfoParam = new CardInfoParam();
        // 是否终端类型为贴片卡、M2M芯片非空写卡
        boolean isTiePianOrNotNullCard = TerminalTypeEnum.TIE_PIAN_CARD.getType().equals(terminalType)
                /*|| TerminalTypeEnum.M2M_NOT_NULL_CARD.getDesc().equals(terminalType)*/;
        boolean isNoCard = TerminalTypeEnum.NO_CARD.getType().equals(terminalType);
        // 是否同步到OS
        boolean isSynToOs = false;

        Boolean hasTerminalType = TerminalTypeEnum.containType(terminalType);
        if (!hasTerminalType){
            errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("终端类型不存在")
                    :errorMsg.concat(",终端类型不存在");
        }else {
            /**
             * 导入的终端类型为贴片卡、M2M芯片非空写卡时，应校验码号有无同步至OS，
             * 且终端类型应与商城同步至OS的码号的卡号类型一致，且导入省份与地市应
             * 与商城同步至OS的码号的省份、地市一致。若未同步或类型不一致，则导入失败
             */

            Object provinceNameObject = areaDataConfig.getProvinceCodeNameMap().get(beId);
            if (provinceNameObject == null){
                errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("省份信息不存在")
                        :errorMsg.concat(",省份信息不存在");
            }

            if (isTiePianOrNotNullCard) {
                if (StringUtils.isEmpty(msisdn)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("码号不能为空")
                            :errorMsg.concat(",码号不能为空");
                }else {

                    cardInfoParam.setMsisdn(msisdn);
                    List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
                    if (CollectionUtils.isEmpty(cardInfoVOList)){
                        String msg = "码号没有同步至OS";
                        errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat(msg)
                                :errorMsg.concat(",").concat(msg);
                    }else {
                        isSynToOs = true;
                        cardInfoParam.setCardType(terminalType);
                        cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
                        if (CollectionUtils.isEmpty(cardInfoVOList)){
                            String msg = "码号的卡号类型不一致";
                            errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat(msg)
                                    :errorMsg.concat(",").concat(msg);
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(msisdn)){
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andMsisdnEqualTo(msisdn)
                        .andDeleteTimeIsNull();
                List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("码号已经存在")
                            :errorMsg.concat(",码号已经存在");
                }
            }

            if (TerminalTypeEnum.M2M_NULL_CARD.getType().equals(terminalType)){
                if (StringUtils.isEmpty(tempIccid)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("临时iccid不能为空")
                            :errorMsg.concat(",临时iccid不能为空");
                }
            }
            if (StringUtils.isNotEmpty(tempIccid)){
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andTempIccidEqualTo(tempIccid)
                        .andDeleteTimeIsNull();
                List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("临时iccid编号已经存在")
                            :errorMsg.concat(",临时iccid编号已经存在");
                }
            }
        }


        // 如果终端类型为贴片卡、M2M芯片非空写卡并且同步到OS
        if (isTiePianOrNotNullCard && isSynToOs){
            cardInfoParam = new CardInfoParam();
            cardInfoParam.setMsisdn(msisdn);
            cardInfoParam.setBeId(beId);

            List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
            if (CollectionUtils.isEmpty(cardInfoVOList)){
                String msg = "码号的省份不一致";
                errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat(msg)
                        :errorMsg.concat(",").concat(msg);
            }
        }

        if (StringUtils.isEmpty(cityName)){
            if (isNoCard){
                errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("归属地市不能为空")
                        :errorMsg.concat(",归属地市不能为空");
            }

        }else {
            if (!"省级".equals(cityName)){
                Object cityCode = locationNameCodeMap.get(cityName);
                if (cityCode == null){
                    errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("归属地市信息不存在")
                            :errorMsg.concat(",归属地市信息不存在");
                }else {

                    List<ProvinceCityVO.CityMall> cityMallList = provinceCityConfig.getProvinceCodeCityMap().get(beId);
                    if (CollectionUtils.isNotEmpty(cityMallList)){
                        boolean provinceHasCity = false;
                        for (int i= 0;i<cityMallList.size();i++){
                            ProvinceCityVO.CityMall cityMall = cityMallList.get(i);
                            if (cityMall.getMallName().equals(cityName)){
                                provinceHasCity = true;
                            }
                        }
                        if (!provinceHasCity){
                            errorMsg = StringUtils.isEmpty(errorMsg) ? errorMsg.concat("归属地市信息不属于该省份")
                                    :errorMsg.concat(",归属地市信息不属于该省份");
                        }
                    }else{
                        log.info("{}没有地市信息",beId);
                    }
                }
            }
        }


        if (StringUtils.isEmpty(imei)){
            errorMsg = StringUtils.isEmpty(errorMsg) ?errorMsg.concat("终端IMEI/SN不能为空"):errorMsg.concat(",终端IMEI不能为空");
        }else {
            CardRelationExample example = new CardRelationExample();
            example.createCriteria().andImeiEqualTo(imei)
                    .andDeleteTimeIsNull();
            List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(example);
            if (CollectionUtils.isNotEmpty(cardRelationList)){
                errorMsg = StringUtils.isEmpty(errorMsg) ?errorMsg.concat("终端IMEI/SN编号已经存在"):errorMsg.concat(",终端IMEI编号已经存在");
            }
        }

        if (StringUtils.isEmpty(deviceVersion)){
            errorMsg = StringUtils.isEmpty(errorMsg) ?errorMsg.concat("设备型号不能为空"):errorMsg.concat(",设备型号不能为空");
        }


        if (StringUtils.isNotEmpty(errorMsg)) {
            log.info("卡+x批量导入{}解析的数据不通过:{}",JSONObject.toJSONString(importNewCardRelationDTO),errorMsg);
            return new ExcelVerifyHandlerResult(false, errorMsg);
        } else {
            return new ExcelVerifyHandlerResult(true);
        }
    }
}
