package com.chinamobile.iot.sc.request.sso;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:22
 * @description 商城单点登录请求
 */
@Data
public class SsoLoginRequest {

    private Header header;

    private Body body;

    @Data
    public static class Header {

        /**
         * 版本号,初始版本号1.0
         */
        private String version;

        /**
         * 唯一请求流水号
         */
        private String msgid;

        /**
         * 系统时间
         * 请求消息发送的系统时间，精确到毫秒，共17位，格式：20121227180001165
         */
        private String systemtime;

        /**
         * 移动认证分配的应用唯一标识
         */
        private String sourceid;

        /**
         * 渠道来源
         * 1： BOSS
         * 2： web
         * 3： wap
         * 4： PC客户端
         * 5： 手机客户端
         */
        private String apptype;

        /**
         * 用户公网IP
         */
        private String userip;

        /**
         * 签名
         */
        private String mac;

    }

    @Data
    public static class Body {

        /**
         * 加密手机号
         */
        private String msisdn;

        /**
         * 校验凭证码
         */
        private String authenid;

        /**
         * 拓展参数
         */
        private String expandparams;

    }

}
