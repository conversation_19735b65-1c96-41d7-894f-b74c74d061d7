package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27
 * @description 可使用的已存在产品数据实体类
 */
@Data
@ExcelTarget(value = "usedProductExistDataImport")
public class UsedProductExistDataDTO  implements IExcelModel, Serializable {

    /**
     * 商品SPU名称
     */
    @Excel(name = "SPU名称")
    private String spuOfferingName;

    /**
     * 商品SPU编码
     */
    @Excel(name = "SPU编码")
    private String spuCode;


    /**
     * 商品规格名称
     */
    @Excel(name = "SKU名称")
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    @Excel(name = "SKU编码")
    private String skuCode;

    @Excel(name = "产品标准")
    private String productStandard;

    @Excel(name = "产品类别")
    private String productType;

    private String errorMsg;

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

}
