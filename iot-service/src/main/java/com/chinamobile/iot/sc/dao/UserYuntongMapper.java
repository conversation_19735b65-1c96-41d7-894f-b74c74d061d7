package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.UserYuntong;
import com.chinamobile.iot.sc.pojo.entity.UserYuntongExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserYuntongMapper {
    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    long countByExample(UserYuntongExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int deleteByExample(UserYuntongExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int insert(UserYuntong record);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int insertSelective(UserYuntong record);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    List<UserYuntong> selectByExample(UserYuntongExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    UserYuntong selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int updateByExampleSelective(@Param("record") UserYuntong record, @Param("example") UserYuntongExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int updateByExample(@Param("record") UserYuntong record, @Param("example") UserYuntongExample example);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int updateByPrimaryKeySelective(UserYuntong record);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int updateByPrimaryKey(UserYuntong record);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int batchInsert(@Param("list") List<UserYuntong> list);

    /**
     *
     * @mbg.generated Wed Jan 15 17:45:04 CST 2025
     */
    int batchInsertSelective(@Param("list") List<UserYuntong> list, @Param("selective") UserYuntong.Column ... selective);
}