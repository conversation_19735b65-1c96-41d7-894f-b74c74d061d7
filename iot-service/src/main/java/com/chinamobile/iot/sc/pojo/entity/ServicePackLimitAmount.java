package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class ServicePackLimitAmount {
    private String id;

    private String productName;

    private String serviceCode;

    private String serviceName;

    private String companyId;

    private Double iotLimit;

    private Double reserveQuatity;

    private Double currentInventory;

    private Date createTime;

    private Date updateTime;

    private String efftime;

    private String exptime;

    private String companyName;

    private String status;

    private Double useInventory;

    public String getId() {
        return id;
    }

    public ServicePackLimitAmount withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getProductName() {
        return productName;
    }

    public ServicePackLimitAmount withProductName(String productName) {
        this.setProductName(productName);
        return this;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public ServicePackLimitAmount withServiceCode(String serviceCode) {
        this.setServiceCode(serviceCode);
        return this;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    public String getServiceName() {
        return serviceName;
    }

    public ServicePackLimitAmount withServiceName(String serviceName) {
        this.setServiceName(serviceName);
        return this;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName == null ? null : serviceName.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public ServicePackLimitAmount withCompanyId(String companyId) {
        this.setCompanyId(companyId);
        return this;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public Double getIotLimit() {
        return iotLimit;
    }

    public ServicePackLimitAmount withIotLimit(Double iotLimit) {
        this.setIotLimit(iotLimit);
        return this;
    }

    public void setIotLimit(Double iotLimit) {
        this.iotLimit = iotLimit;
    }

    public Double getReserveQuatity() {
        return reserveQuatity;
    }

    public ServicePackLimitAmount withReserveQuatity(Double reserveQuatity) {
        this.setReserveQuatity(reserveQuatity);
        return this;
    }

    public void setReserveQuatity(Double reserveQuatity) {
        this.reserveQuatity = reserveQuatity;
    }

    public Double getCurrentInventory() {
        return currentInventory;
    }

    public ServicePackLimitAmount withCurrentInventory(Double currentInventory) {
        this.setCurrentInventory(currentInventory);
        return this;
    }

    public void setCurrentInventory(Double currentInventory) {
        this.currentInventory = currentInventory;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ServicePackLimitAmount withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ServicePackLimitAmount withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEfftime() {
        return efftime;
    }

    public ServicePackLimitAmount withEfftime(String efftime) {
        this.setEfftime(efftime);
        return this;
    }

    public void setEfftime(String efftime) {
        this.efftime = efftime == null ? null : efftime.trim();
    }

    public String getExptime() {
        return exptime;
    }

    public ServicePackLimitAmount withExptime(String exptime) {
        this.setExptime(exptime);
        return this;
    }

    public void setExptime(String exptime) {
        this.exptime = exptime == null ? null : exptime.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public ServicePackLimitAmount withCompanyName(String companyName) {
        this.setCompanyName(companyName);
        return this;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getStatus() {
        return status;
    }

    public ServicePackLimitAmount withStatus(String status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Double getUseInventory() {
        return useInventory;
    }

    public ServicePackLimitAmount withUseInventory(Double useInventory) {
        this.setUseInventory(useInventory);
        return this;
    }

    public void setUseInventory(Double useInventory) {
        this.useInventory = useInventory;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", productName=").append(productName);
        sb.append(", serviceCode=").append(serviceCode);
        sb.append(", serviceName=").append(serviceName);
        sb.append(", companyId=").append(companyId);
        sb.append(", iotLimit=").append(iotLimit);
        sb.append(", reserveQuatity=").append(reserveQuatity);
        sb.append(", currentInventory=").append(currentInventory);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", efftime=").append(efftime);
        sb.append(", exptime=").append(exptime);
        sb.append(", companyName=").append(companyName);
        sb.append(", status=").append(status);
        sb.append(", useInventory=").append(useInventory);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ServicePackLimitAmount other = (ServicePackLimitAmount) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getServiceCode() == null ? other.getServiceCode() == null : this.getServiceCode().equals(other.getServiceCode()))
            && (this.getServiceName() == null ? other.getServiceName() == null : this.getServiceName().equals(other.getServiceName()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getIotLimit() == null ? other.getIotLimit() == null : this.getIotLimit().equals(other.getIotLimit()))
            && (this.getReserveQuatity() == null ? other.getReserveQuatity() == null : this.getReserveQuatity().equals(other.getReserveQuatity()))
            && (this.getCurrentInventory() == null ? other.getCurrentInventory() == null : this.getCurrentInventory().equals(other.getCurrentInventory()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getEfftime() == null ? other.getEfftime() == null : this.getEfftime().equals(other.getEfftime()))
            && (this.getExptime() == null ? other.getExptime() == null : this.getExptime().equals(other.getExptime()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getUseInventory() == null ? other.getUseInventory() == null : this.getUseInventory().equals(other.getUseInventory()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getServiceCode() == null) ? 0 : getServiceCode().hashCode());
        result = prime * result + ((getServiceName() == null) ? 0 : getServiceName().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getIotLimit() == null) ? 0 : getIotLimit().hashCode());
        result = prime * result + ((getReserveQuatity() == null) ? 0 : getReserveQuatity().hashCode());
        result = prime * result + ((getCurrentInventory() == null) ? 0 : getCurrentInventory().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getEfftime() == null) ? 0 : getEfftime().hashCode());
        result = prime * result + ((getExptime() == null) ? 0 : getExptime().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getUseInventory() == null) ? 0 : getUseInventory().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        productName("product_name", "productName", "VARCHAR", false),
        serviceCode("service_code", "serviceCode", "VARCHAR", false),
        serviceName("service_name", "serviceName", "VARCHAR", false),
        companyId("company_id", "companyId", "VARCHAR", false),
        iotLimit("iot_limit", "iotLimit", "DOUBLE", false),
        reserveQuatity("reserve_quatity", "reserveQuatity", "DOUBLE", false),
        currentInventory("current_inventory", "currentInventory", "DOUBLE", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        efftime("efftime", "efftime", "VARCHAR", false),
        exptime("exptime", "exptime", "VARCHAR", false),
        companyName("company_name", "companyName", "VARCHAR", false),
        status("status", "status", "VARCHAR", false),
        useInventory("use_inventory", "useInventory", "DOUBLE", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}