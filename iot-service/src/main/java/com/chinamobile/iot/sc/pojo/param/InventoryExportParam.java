package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/27
 * @description 库存导出查询参数
 */
@Data
public class InventoryExportParam {

    /**
     * 商品创建开始时间 yyyy-MM-dd
     */
    private String startTime;

    /**
     * 商品创建结束时间 yyyy-MM-dd
     */
    private String endTime;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private Integer spuOfferingStatus;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     * SKU发布省
     */
    private String skuReleaseProvince;

    /**
     * SKU发布地市
     */
    private String skuReleaseCity;

    /**
     * 合作伙伴名称
     */
    private String partnerName;

    /**
     * 库存状态 0-短缺 1-充足
     */
    private Integer inventoryStatus;

    /**
     * 导出短信验证码
     */
    private Integer exportMask;

    /**
     * 导出验证码的电话
     */
    private String exportPhone;

    private List<String> userIdList;

    /**
     * 省份编码
     */
    private String beId;

    /**
     * 地市编码
     */
    private String location;
}
