package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.pojo.vo.ProOnlineDownloadVO;
import com.chinamobile.iot.sc.pojo.vo.ProOnlineUploadVO;
import org.springframework.web.multipart.MultipartFile;
import com.chinamobile.iot.sc.pojo.vo.OnlineRequestVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架service接口类
 */
public interface NewProductRequestOnlineOfflineService {

    /**
     * 新增上下架信息
     *
     * @param onlineRequestParam
     * @param loginIfo4Redis
     */
    void saveOnlineRequestInfo(OnlineRequestParam onlineRequestParam,
                               LoginIfo4Redis loginIfo4Redis);

    /**
     * 下架申请信息
     * @param offlineRequestParam
     * @param loginIfo4Redis
     */
    void saveOfflineRequestInfo(NewProductOfflineRequestParam offlineRequestParam,
                                LoginIfo4Redis loginIfo4Redis);

    /**
     *  复制未通过的上架请求重新上架
     * @param copyOnlineParam
     * @param loginIfo4Redis
     */
    void copyNotPassOnlineToReOnline(NewProductCopyOnlineParam copyOnlineParam,
                                     LoginIfo4Redis loginIfo4Redis);

    /**
     * 获取序列号
     *
     * @param redisKey
     * @return
     */
    String getNewOddNumber(String redisKey);

    /**
     * 分页查询非合作伙伴上下架列表页面
     *
     * @param onlineOfflineParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<NewProductOnlineOfflineVO> pageNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                    LoginIfo4Redis loginIfo4Redis);

    /**
     * 获取上下架详情
     * @param detailParam
     * @param loginIfo4Redis
     * @return
     */
    OnlineRequestVO getOnlineRequestDetail(NewProductOnlineOfflineDetailParam detailParam,
                                           LoginIfo4Redis loginIfo4Redis);

    /**
     * 更新流程状态
     * @param onlineOfflineParam
     * @param loginIfo4Redis
     */
    void judgeOnlineOfflineFlow(NewProductJudgeOnlineOfflineParam onlineOfflineParam,
                                LoginIfo4Redis loginIfo4Redis);

    /**
     * 导出商品上架信息
     * @param detailParam
     * @param loginIfo4Redis
     * @param response
     */
    void exportNewProductOnlineOfflineInfo(NewProductOnlineOfflineDetailParam detailParam,
                                           LoginIfo4Redis loginIfo4Redis,
                                           HttpServletResponse response);


    BaseAnswer<Void> downLoadSingleFile();

    BaseAnswer<Void> downLoadFilePack();

    BaseAnswer<Void> deleteFileByKey(NewProductOnlineOfflineDeleteParam param);

    BaseAnswer<ProOnlineUploadVO> uploadFile(MultipartFile upfile, String fileType);

    BaseAnswer<String> uploadBackendFile(MultipartFile upfile, String dir);

    BaseAnswer<ProOnlineDownloadVO> downLoadFile(NewProductOnlineOfflineDownloadParam param);

    BaseAnswer<Void> deleteDir(String path);

}
