package com.chinamobile.iot.sc.request;

import com.chinamobile.iot.sc.request.order2c.AfterMarketOfferingInfoRequestDTO;
import com.chinamobile.iot.sc.request.order2c.AppointmentInfoRequestDTO;
import lombok.Data;

import java.util.List;

/**
 * @AUTHOR: HWF
 * @DATE: 2023/6/14
 */
@Data
public class AfterMarketOrder2CInfoRequest {
    /**售后订单号*/
    private String serviceOrderId;

    /**关联商品订单号*/
    private String offeringsOrderId;

    /**售后服务订单状态:
     * 1:待预约（付款完成）
     * 2:派单中（预约完成）
     * 3:订单计收（订单同步至CMIoT成功后，同步本状态）
     * 4：退款完成
     * */
    private Integer status;

    /**
     * 订单总金额
     */
    private String totalPrice;

    /**
     * 预约信息
     * status=2:派单中必填
     */
    private AppointmentInfoRequestDTO appointmentInfo;


    /**
     * 商品信息
     * status=1:待预约（付款完成）时必传；
     */
    private List<AfterMarketOfferingInfoRequestDTO> afterMarketOfferingInfo;
}
