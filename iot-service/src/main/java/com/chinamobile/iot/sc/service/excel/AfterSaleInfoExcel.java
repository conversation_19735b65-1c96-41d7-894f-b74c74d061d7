package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 上架商品售后信息
 * <AUTHOR>
 * @date 2022/9/22 10:12
 */
@Data
public class AfterSaleInfoExcel {

    @ExcelProperty(value = "商品名称SPU", index = 0)
    private String spuOfferingName;

    @ExcelProperty(value = "商品规格SKU", index = 1)
    private String skuOfferingName;

    @ExcelProperty(value = "商城订单处理人（主）", index = 2)
    private String storeOrderHandler;

    @ExcelProperty(value = "商城订单处理人（次）", index = 3)
    private String storeOrderHandlerSub;

    @ExcelProperty(value = "售前市场经理", index = 4)
    private String preSaleManager;

    @ExcelProperty(value = "商品发货接口人", index = 5)
    private String productShipUser;

    @ExcelProperty(value = "商品安装接口人", index = 6)
    private String productInstaller;

    @ExcelProperty(value = "物联卡套餐接口人", index = 7)
    private String thingsCardUser;

    @ExcelProperty(value = "商品软件权限开通接口人", index = 8)
    private String softwareAuthorityUser;

    @ExcelProperty(value = "商品售后接口人", index = 9)
    private String afterSaleUser;

    @ExcelProperty(value = "质保/售后细则", index = 10)
    private String afterSaleDetail;

    @ExcelProperty(value = "商品维修联系信息与地址", index = 11)
    private String productMaintainInfo;

    @ExcelProperty(value = "商品退货联系信息与地址", index = 12)
    private String productSaleReturnInfo;

}
