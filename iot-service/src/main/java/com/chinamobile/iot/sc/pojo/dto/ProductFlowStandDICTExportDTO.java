package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/12 11:24
 * 导出流程主商品类
 */
@Data
public class ProductFlowStandDICTExportDTO {
    /**
     * 商品SPU信息
     */


    /**
     * 商品类目
     */
    private String shelfCatagoryId;
    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品上架产品类目")
    private String shelfCatagoryName;

    /**
     * 一级导航目录
     */
//    @Excel(name = "一级导航目录")
    private String firstDirectoryId;
    /**
     * 一级导航目录名称
     */
    @Excel(name = "一级导航目录")
    private String firstDirectoryName;
    /**
     * 二级导航目录
     */
//    @Excel(name = "二级导航目录")
    private String secondDirectoryId;
    /**
     * 二级导航目录名称
     */
    @Excel(name = "二级导航目录")
    private String secondDirectoryName;
    /**
     * 三级导航目录
     */
//    @Excel(name = "三级导航目录")
    private String thirdDirectoryId;
    /**
     * 三级导航目录名称
     */
    @Excel(name = "三级导航目录")
    private String thirdDirectoryName;
//    /**
//     * 销售对象
//     */
//    @Excel(name = "销售对象")
//    private String saleObject;
    /**
     * 商品名称（SPU）
     */
    @Excel(name = "商品名称（SPU")
    private String spuName;
    /**
     * 产品经理
     */
    @Excel(name = "产品经理")
    private String manager;
    /**
     * 商品简介
     */
    @Excel(name = "商品简介")
    private String productDesc;
    /**
     *典型应用领域
     */
    @Excel(name = "典型应用领域")
    private String applicationArea;
    /**
     *是否隐秘上架
     */
    @Excel(name = "是否隐秘上架")
    private String isHiddenShelf;
    /**
     * 服务商
     */
    @Excel(name = "服务商")
    private String spuServiceProvider;
    /**
     * 销售标签
     */
    @Excel(name = "销售标签")
    private String saleTag;
    /**
     * 映射检索词
     */
    @Excel(name = "映射检索词")
    private String searchWord;
    /**
     * 备注
     */
    @Excel(name = "备注")
    private String spuRemark;



    /**
     * 商品规格SKU信息
     */


    /**
     * 规格名称（sku）
     */
    @Excel(name = "规格名称（sku）")
    private String skuName;
    /**
     * 规格简称（sku）
     */
    @Excel(name = "规格简称（sku）")
    private String skuShortName;
    /**
     * 实际销售产品名称
     */
    @Excel(name = "实际销售产品名称")
    private String keyCompomentName;
    @ExcelProperty("实际销售产品及服务内容")
    private String keyComponentServiceInfo;
    /**
     * 销售价格
     */
    @Excel(name = "销售价格")
    private Long salePrice;
    private Long saleMinPrice;
    @ExcelProperty("上限价格")
    private Long saleMaxPrice;
    /**
     * 价格区间外销售
     */
    @Excel(name = "价格区间外销售")
    private String saleOutOfPriceRange;
    /**
     * 发布订购范围
     */
    @Excel(name = "发布订购范围")
    private String saleProvinceCity;
    /**
     * 配送范围
     */
    @Excel(name = "配送范围")
    private String deliveryRange;
    /**
     * 游客/合作伙伴可见
     */
    @Excel(name = "游客/合作伙伴可见")
    private String touristPartnerVisible;
    /**
     * 标准服务产品名称
     */
    @Excel(name = "标准服务产品名称")
    private String standardProductName;
    /**
     * 产品属性
     */
    @Excel(name = "产品属性")
    private String standardProductAttribute;
    /**
     * 服务供应商
     */
    @Excel(name = "服务供应商")
    private String skuServiceProvider;
    /**
     * 产品管理部门
     */
    @Excel(name = "产品管理部门")
    private String manageDepartment;
    /**
     * 标准服务产品经理
     */
    @Excel(name = "标准服务产品经理")
    private String standardProductManager;
    /**
     * 商城订单管理账号(接单)
     */
    @Excel(name = "商城订单管理账号(接单)")
    private String receiveOrderAccount;
    /**
     * 商城订单管理账号(交付)
     */
    @Excel(name = "商城订单管理账号(交付)")
    private String deliverAccount;
    /**
     * 商城订单管理账号(售后)
     */
    @Excel(name = "商城订单管理账号(售后)")
    private String aftermarketAccount;
    /**
     * 发货接口人
     */
    @Excel(name = "发货接口人")
    private String sendContactPerson;
    @Excel(name = "备注")
    private String skuRemark;




    /**
     * 硬件原子商品信息,软件功能费原子商品信息
     */

   /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomName;
    /**
     * （省-专）结算单价
     */
    @Excel(name = "（省-专）结算单价")
    private Long settlePrice;
    /**
     * （专-合）结算单价
     */
    @Excel(name = "（专-合）结算单价")
    private Long zhuanheSettlePrice;
    /**
     * CMIOT费用项
     */
    private String cmiotCostProjectId;
    /**
     * CMIOT费用项名称
     */
    @Excel(name = "CMIOT费用项")
    private String cmiotCostProjectName;
    /**
     * 计量单位
     */
    @Excel(name = "计量单位")
    private String unit;
    /**
     * 不需结算（对账单）
     */
    @Excel(name = "不需结算（对账单）")
    private String noSettlement;
    /**
     *  结算明细服务名称（产品）
     */
    @Excel(name = " 结算明细服务名称（产品）")
    private String settlementDetailName;
    @ExcelProperty("服务包名称")
    private String servicePackageName;
    /**
     * 服务内容
     */
    @Excel(name = "服务内容")
    private String serviceContent;
    @Excel(name = "订购数量最小值")
    private Integer minPurchaseNum;
    @Excel(name = "库存数")
    private Integer inventory;
    @ExcelProperty("服务产品合同信息（销售侧）")
    private String serviceContract;
    @Excel(name = "物联网公司采购合同信息")
    private String iotPurchaseContract;
    @Excel(name = "物联网公司K3系统物料编码")
    private String materialNum;
    /**
     * 备注
     */
    @Excel(name = "备注")
    private String atomRemark;


    /**
     *  商城上架信息
     */
    @Excel(name = "商城链接")
    private String url;
    @Excel(name = "商品编码（SPU")
    private String spuCode;
    @Excel(name = "商品编码（SKU)")
    private String skuCode;
    @Excel(name = "备注")
    private String configRemark;

}
