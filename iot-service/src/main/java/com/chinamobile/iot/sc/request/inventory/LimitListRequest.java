package com.chinamobile.iot.sc.request.inventory;

import lombok.Data;

import java.util.Date;

@Data
public class LimitListRequest {
    /**
     * 产品名称
     */

    private String productName;


    /**
     * 服务包编码
     */

    private String serviceCode;


    /**
     * 服务包名称
     */

    private String serviceName;


    /**
     * 省份编码
     */

    private String companyId;


    /**
     * 商城授权额度总额
     */

    private Long iotLimit;


    /**
     * 预占额度
     */

    private Long reserveQuatity;


    /**
     * 当前总额度
     */

    private Long currentInventory;
    /**
     * 额度状态 0短缺，1充足，2失效
     */
    private String status;

    /**
     * 创建时间
     */

    private Date createTime;


    /**
     * 更新时间
     */

    private Date updateTime;


    /**
     * 生效时间，格式：YYYYMMDDHH24MISS
     */

    private String efftime;


    /**
     * 截止时间，格式：YYYYMMDDHH24MISS
     */

    private String exptime;


    /**
     * 省名称
     */

    private String companyName;

    /**
     * 账目项id
     */

    private String chargeId;
    /**
     * 导出短信验证码
     */
    private Integer exportMask;

    /**
     * 导出验证码的电话
     */
    private String exportPhone;
    private Integer page;
    private Integer num;

}
