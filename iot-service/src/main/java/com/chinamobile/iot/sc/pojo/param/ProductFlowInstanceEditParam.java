package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.pojo.vo.ProductFlowInstanceDetailVO;
import lombok.Data;

import java.util.List;

/**
 * created by liuxiang on 2024/3/7 16:19
 */
@Data
public class ProductFlowInstanceEditParam {

    private String productFlowInstanceId;

    /**
     * 下架理由
     */
    private String offShelfReason;

    /**
     * SPU信息
     */
    private ProductFlowInstanceDetailVO.SpuInfo spuInfo;

    /**
     * sku信息
     */
    private ProductFlowInstanceDetailVO.SkuInfo skuInfo;

    /**
     * 原子信息
     */
    private List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList;

    /**
     * 配置信息
     */
    private ProductFlowInstanceDetailVO.ConfigInfo configInfo;

}
