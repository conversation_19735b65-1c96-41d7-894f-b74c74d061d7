package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderDictInfoExample {
    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public OrderDictInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public OrderDictInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public OrderDictInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        OrderDictInfoExample example = new OrderDictInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public OrderDictInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public OrderDictInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderIsNull() {
            addCriterion("associated_order is null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderIsNotNull() {
            addCriterion("associated_order is not null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderEqualTo(String value) {
            addCriterion("associated_order =", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderNotEqualTo(String value) {
            addCriterion("associated_order <>", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderGreaterThan(String value) {
            addCriterion("associated_order >", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderGreaterThanOrEqualTo(String value) {
            addCriterion("associated_order >=", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLessThan(String value) {
            addCriterion("associated_order <", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLessThanOrEqualTo(String value) {
            addCriterion("associated_order <=", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("associated_order <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLike(String value) {
            addCriterion("associated_order like", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderNotLike(String value) {
            addCriterion("associated_order not like", value, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderIn(List<String> values) {
            addCriterion("associated_order in", values, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderNotIn(List<String> values) {
            addCriterion("associated_order not in", values, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderBetween(String value1, String value2) {
            addCriterion("associated_order between", value1, value2, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderNotBetween(String value1, String value2) {
            addCriterion("associated_order not between", value1, value2, "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusIsNull() {
            addCriterion("business_order_status is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusIsNotNull() {
            addCriterion("business_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusEqualTo(Integer value) {
            addCriterion("business_order_status =", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusNotEqualTo(Integer value) {
            addCriterion("business_order_status <>", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusGreaterThan(Integer value) {
            addCriterion("business_order_status >", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_order_status >=", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusLessThan(Integer value) {
            addCriterion("business_order_status <", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("business_order_status <=", value, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("business_order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusIn(List<Integer> values) {
            addCriterion("business_order_status in", values, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusNotIn(List<Integer> values) {
            addCriterion("business_order_status not in", values, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("business_order_status between", value1, value2, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("business_order_status not between", value1, value2, "businessOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusIsNull() {
            addCriterion("return_order_status is null");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusIsNotNull() {
            addCriterion("return_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusEqualTo(Integer value) {
            addCriterion("return_order_status =", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusNotEqualTo(Integer value) {
            addCriterion("return_order_status <>", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusGreaterThan(Integer value) {
            addCriterion("return_order_status >", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_order_status >=", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusLessThan(Integer value) {
            addCriterion("return_order_status <", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("return_order_status <=", value, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("return_order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusIn(List<Integer> values) {
            addCriterion("return_order_status in", values, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusNotIn(List<Integer> values) {
            addCriterion("return_order_status not in", values, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("return_order_status between", value1, value2, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andReturnOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("return_order_status not between", value1, value2, "returnOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusIsNull() {
            addCriterion("valet_order_status is null");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusIsNotNull() {
            addCriterion("valet_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusEqualTo(String value) {
            addCriterion("valet_order_status =", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusNotEqualTo(String value) {
            addCriterion("valet_order_status <>", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusGreaterThan(String value) {
            addCriterion("valet_order_status >", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusGreaterThanOrEqualTo(String value) {
            addCriterion("valet_order_status >=", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLessThan(String value) {
            addCriterion("valet_order_status <", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLessThanOrEqualTo(String value) {
            addCriterion("valet_order_status <=", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLike(String value) {
            addCriterion("valet_order_status like", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusNotLike(String value) {
            addCriterion("valet_order_status not like", value, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusIn(List<String> values) {
            addCriterion("valet_order_status in", values, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusNotIn(List<String> values) {
            addCriterion("valet_order_status not in", values, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusBetween(String value1, String value2) {
            addCriterion("valet_order_status between", value1, value2, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusNotBetween(String value1, String value2) {
            addCriterion("valet_order_status not between", value1, value2, "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIsNull() {
            addCriterion("order_status_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIsNotNull() {
            addCriterion("order_status_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeEqualTo(Date value) {
            addCriterion("order_status_time =", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotEqualTo(Date value) {
            addCriterion("order_status_time <>", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThan(Date value) {
            addCriterion("order_status_time >", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_status_time >=", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThan(Date value) {
            addCriterion("order_status_time <", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_status_time <=", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIn(List<Date> values) {
            addCriterion("order_status_time in", values, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotIn(List<Date> values) {
            addCriterion("order_status_time not in", values, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeBetween(Date value1, Date value2) {
            addCriterion("order_status_time between", value1, value2, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_status_time not between", value1, value2, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonIsNull() {
            addCriterion("valet_order_return_reason is null");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonIsNotNull() {
            addCriterion("valet_order_return_reason is not null");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonEqualTo(String value) {
            addCriterion("valet_order_return_reason =", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonNotEqualTo(String value) {
            addCriterion("valet_order_return_reason <>", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonGreaterThan(String value) {
            addCriterion("valet_order_return_reason >", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonGreaterThanOrEqualTo(String value) {
            addCriterion("valet_order_return_reason >=", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLessThan(String value) {
            addCriterion("valet_order_return_reason <", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLessThanOrEqualTo(String value) {
            addCriterion("valet_order_return_reason <=", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_return_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLike(String value) {
            addCriterion("valet_order_return_reason like", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonNotLike(String value) {
            addCriterion("valet_order_return_reason not like", value, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonIn(List<String> values) {
            addCriterion("valet_order_return_reason in", values, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonNotIn(List<String> values) {
            addCriterion("valet_order_return_reason not in", values, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonBetween(String value1, String value2) {
            addCriterion("valet_order_return_reason between", value1, value2, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonNotBetween(String value1, String value2) {
            addCriterion("valet_order_return_reason not between", value1, value2, "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andRefundTypeIsNull() {
            addCriterion("refund_type is null");
            return (Criteria) this;
        }

        public Criteria andRefundTypeIsNotNull() {
            addCriterion("refund_type is not null");
            return (Criteria) this;
        }

        public Criteria andRefundTypeEqualTo(Integer value) {
            addCriterion("refund_type =", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeNotEqualTo(Integer value) {
            addCriterion("refund_type <>", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeGreaterThan(Integer value) {
            addCriterion("refund_type >", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("refund_type >=", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeLessThan(Integer value) {
            addCriterion("refund_type <", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeLessThanOrEqualTo(Integer value) {
            addCriterion("refund_type <=", value, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTypeIn(List<Integer> values) {
            addCriterion("refund_type in", values, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeNotIn(List<Integer> values) {
            addCriterion("refund_type not in", values, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeBetween(Integer value1, Integer value2) {
            addCriterion("refund_type between", value1, value2, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("refund_type not between", value1, value2, "refundType");
            return (Criteria) this;
        }

        public Criteria andRefundReasonIsNull() {
            addCriterion("refund_reason is null");
            return (Criteria) this;
        }

        public Criteria andRefundReasonIsNotNull() {
            addCriterion("refund_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRefundReasonEqualTo(Integer value) {
            addCriterion("refund_reason =", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonNotEqualTo(Integer value) {
            addCriterion("refund_reason <>", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonGreaterThan(Integer value) {
            addCriterion("refund_reason >", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonGreaterThanOrEqualTo(Integer value) {
            addCriterion("refund_reason >=", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonLessThan(Integer value) {
            addCriterion("refund_reason <", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonLessThanOrEqualTo(Integer value) {
            addCriterion("refund_reason <=", value, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refund_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundReasonIn(List<Integer> values) {
            addCriterion("refund_reason in", values, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonNotIn(List<Integer> values) {
            addCriterion("refund_reason not in", values, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonBetween(Integer value1, Integer value2) {
            addCriterion("refund_reason between", value1, value2, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefundReasonNotBetween(Integer value1, Integer value2) {
            addCriterion("refund_reason not between", value1, value2, "refundReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonIsNull() {
            addCriterion("refuse_reason is null");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonIsNotNull() {
            addCriterion("refuse_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonEqualTo(Integer value) {
            addCriterion("refuse_reason =", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonNotEqualTo(Integer value) {
            addCriterion("refuse_reason <>", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonGreaterThan(Integer value) {
            addCriterion("refuse_reason >", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonGreaterThanOrEqualTo(Integer value) {
            addCriterion("refuse_reason >=", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonLessThan(Integer value) {
            addCriterion("refuse_reason <", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonLessThanOrEqualTo(Integer value) {
            addCriterion("refuse_reason <=", value, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("refuse_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefuseReasonIn(List<Integer> values) {
            addCriterion("refuse_reason in", values, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonNotIn(List<Integer> values) {
            addCriterion("refuse_reason not in", values, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonBetween(Integer value1, Integer value2) {
            addCriterion("refuse_reason between", value1, value2, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andRefuseReasonNotBetween(Integer value1, Integer value2) {
            addCriterion("refuse_reason not between", value1, value2, "refuseReason");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoIsNull() {
            addCriterion("orders_logis_info is null");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoIsNotNull() {
            addCriterion("orders_logis_info is not null");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoEqualTo(String value) {
            addCriterion("orders_logis_info =", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoNotEqualTo(String value) {
            addCriterion("orders_logis_info <>", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoGreaterThan(String value) {
            addCriterion("orders_logis_info >", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoGreaterThanOrEqualTo(String value) {
            addCriterion("orders_logis_info >=", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLessThan(String value) {
            addCriterion("orders_logis_info <", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLessThanOrEqualTo(String value) {
            addCriterion("orders_logis_info <=", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("orders_logis_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLike(String value) {
            addCriterion("orders_logis_info like", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoNotLike(String value) {
            addCriterion("orders_logis_info not like", value, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoIn(List<String> values) {
            addCriterion("orders_logis_info in", values, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoNotIn(List<String> values) {
            addCriterion("orders_logis_info not in", values, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoBetween(String value1, String value2) {
            addCriterion("orders_logis_info between", value1, value2, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoNotBetween(String value1, String value2) {
            addCriterion("orders_logis_info not between", value1, value2, "ordersLogisInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(OrderDictInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLikeInsensitive(String value) {
            addCriterion("upper(order_type) like", value.toUpperCase(), "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andAssociatedOrderLikeInsensitive(String value) {
            addCriterion("upper(associated_order) like", value.toUpperCase(), "associatedOrder");
            return (Criteria) this;
        }

        public Criteria andValetOrderStatusLikeInsensitive(String value) {
            addCriterion("upper(valet_order_status) like", value.toUpperCase(), "valetOrderStatus");
            return (Criteria) this;
        }

        public Criteria andValetOrderReturnReasonLikeInsensitive(String value) {
            addCriterion("upper(valet_order_return_reason) like", value.toUpperCase(), "valetOrderReturnReason");
            return (Criteria) this;
        }

        public Criteria andOrdersLogisInfoLikeInsensitive(String value) {
            addCriterion("upper(orders_logis_info) like", value.toUpperCase(), "ordersLogisInfo");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu May 04 14:23:18 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        private OrderDictInfoExample example;

        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        protected Criteria(OrderDictInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        public OrderDictInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu May 04 14:23:18 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu May 04 14:23:18 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu May 04 14:23:18 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.OrderDictInfoExample example);
    }
}