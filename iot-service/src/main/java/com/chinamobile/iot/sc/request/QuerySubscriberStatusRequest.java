package com.chinamobile.iot.sc.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29
 * @description 卡状态（生命周期）实体类
 */
@Data
public class QuerySubscriberStatusRequest {

    /**
     * 省编码
     */
    private String beid;

    private String iccid;

    private String imsi;

    private String msisdn;

    /**
     * 请求鉴权类型，用于校验请求方数据权限，枚举为：
     * 01 msisdn 服务号码
     * 02 iccid  iccid
     * 03 imsi  imsi
     * 04 groupCode 群用户编码
     * 05 custCode 客户编码（含个人客户）
     * 06 acctCode 账户编码。传参为iccid或imsi或msisdn时，填与之对应的枚举
     */
    private String authType;

}
