package com.chinamobile.iot.sc.controller.iot;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.AftermarketOfferingCode;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.param.UpdateAtomInventoryParam;
import com.chinamobile.iot.sc.service.IProductNavigationDirectoryService;
import com.chinamobile.iot.sc.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Author: YSC
 * @Date: 2021/10/29 14:14
 * @Description: 商品控制
 */
@RestController
@RequestMapping("/os/productservice")
public class ProductController {
    private IProductService iProductService;

    @Resource
    private IProductNavigationDirectoryService productNavigationDirectoryService;
    /**
     * 商品同步接口
     * @param baseRequest
     * @return
     */
    @PostMapping("/SyncOfferingInfo")
    public IOTAnswer<Void> SyncOfferingInfo(@RequestBody IOTRequest baseRequest){
        return iProductService.syncOfferingInfo(baseRequest);
    }

    /**
     * 规格商品信息同步
     * @return
     */
    @PostMapping("/SyncSkuOfferingInfos")
    public IOTAnswer<Void> SyncSkuOfferingInfos(@RequestBody IOTRequest iotRequest){
        return iProductService.syncSkuOfferingInfos(iotRequest);
    }

    /**
     * IoT省内融合包信息同步
     * @return
     */
    @PostMapping("/SyncBenefitOfferingsInfo")
    public IOTAnswer<Void> syncBenefitOfferingsInfo(@RequestBody IOTRequest iotRequest){
        return iProductService.syncBenefitOfferingsInfo(iotRequest);
    }

    /**
     * 商品状态数据割接(spu, sku, atom)
     * @param excel
     */
    @PostMapping("/spuStatusCutOver")
    public BaseAnswer spuStatusCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.spuStatusCutOver(excel);
    }

    /**
     * IoT售后服务包信息同步 商城 -> OS
     * @param request
     * @return
     */
    @PostMapping("/SyncAftermarketOfferingInfos")
    public IOTAnswer<Void> syncAftermarketOfferingInfos(@RequestBody IOTRequest request) {
        return iProductService.syncAftermarketOfferingInfos(request);
    }

    @Autowired
    public void setIProductService(IProductService iProductService){
        this.iProductService=iProductService;
    }

    /**
     * sku商品发布范围割接
     * @param excel
     */
    @PostMapping("/skuReleaseTargetCutOver")
    public BaseAnswer skuReleaseTargetCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.skuReleaseTargetCutOver(excel);
    }

    /**
     * 商品原子信息结算单价+结算明细服务名称割接模板割接
     * @param excel
     * @return
     */
    @PostMapping("/atomSettlePriceCutOver")
    public BaseAnswer atomSettlePriceCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.atomSettlePriceCutOver(excel);
    }

    /**
     * 账目项ID割接
     * @param excel
     * @return
     */
    @PostMapping("/atomChargeCodeCutOver")
    public BaseAnswer atomChargeCodeCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.atomChargeCodeCutOver(excel);
    }


    /**
     * 原子商品信息同步
     * @param iotRequest
     * @return
     */
    @PostMapping("/SyncAtomOfferingInfos")
    public IOTAnswer<Void> SyncAtomOfferingInfos(@RequestBody IOTRequest iotRequest){
        return iProductService.syncAtomOfferingInfos(iotRequest);
    }

    /**
     * 商品版本号数据割接(spu, sku, atom)
     * @param excel
     */
    @PostMapping("/productVersionCutOver")
    public BaseAnswer productVersionCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.productVersionCutOver(excel);
    }

    /**
     * 售后服务商品版本号数据割接
     * @param excel
     */
    @PostMapping("/aftermarketVersionCutOver")
    public BaseAnswer aftermarketVersionCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.aftermarketVersionCutOver(excel);
    }

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    @PostMapping("/productVersionCutOverFix")
    public BaseAnswer productVersionCutOverFix(@RequestPart("file") MultipartFile excel) {
        return iProductService.productVersionCutOverFix(excel);
    }

    /**
     * 售后服务商品版本号数据割接修复
     * @param excel
     */
    @PostMapping("/aftermarketVersionCutOverFix")
    public BaseAnswer aftermarketVersionCutOverFix(@RequestPart("file") MultipartFile excel) {
        return iProductService.aftermarketVersionCutOverFix(excel);
    }

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    @PostMapping("/productVersionCutOverFixSku")
    public BaseAnswer productVersionCutOverFixSku(@RequestPart("file") MultipartFile excel) {
        return iProductService.productVersionCutOverFixSku(excel);
    }

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    @PostMapping("/productVersionCutOverFixAtom")
    public BaseAnswer productVersionCutOverFixAtom(@RequestPart("file") MultipartFile excel) {
        return iProductService.productVersionCutOverFixAtom(excel);
    }

    /**
     * 商品版本号修复-针对只同步了spu的信息(spu)
     * @param excel
     */
    @PostMapping("/productVersionSpuCutOver")
    public BaseAnswer productVersionSpuCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.productVersionSpuCutOver(excel);
    }

    /**
     * 售后服务商品版本号修复
     * @param request
     */
    @PostMapping("/aftermarketVersionFix")
    public BaseAnswer aftermarketVersionFix(@RequestBody AftermarketOfferingCode request) {
        return iProductService.aftermarketVersionFix(request);
    }

    /**
     * 商品导航目录信息同步
     * @param baseRequest
     * @return
     */
    @PostMapping("/SyncOfferingNavigationInfo")
    public IOTAnswer<Void> SyncOfferingNavigationInfo(@RequestBody IOTRequest baseRequest){
        return iProductService.SyncOfferingNavigationInfo(baseRequest);
    }

    /**
     * 导航目录信息同步
     * @param baseRequest
     * @return
     */
    @PostMapping("/SyncNavigationInfo")
    public IOTAnswer<Void> SyncNavigationInfo(@RequestBody IOTRequest baseRequest){
        return productNavigationDirectoryService.SyncNavigationInfo(baseRequest);
    }

    /**
     * 商品导航目录割接
     * @param excel
     */
    @PostMapping("/SyncOfferingNavigationInfoCutOver")
    public BaseAnswer SyncOfferingNavigationInfoCutOver(@RequestPart("file") MultipartFile excel) {
        return iProductService.SyncOfferingNavigationInfoCutOver(excel);
    }

    /**
     * IoT省内融合包信息同步割接
     * @param excel
     * @return
     */
    @PostMapping("/SyncBenefitOfferingsInfoCutOver")
    public BaseAnswer syncBenefitOfferingsInfoCutOver(@RequestPart("file") MultipartFile excel) throws Exception {
        return iProductService.syncBenefitOfferingsInfoCutOver(excel);
    }

    /**
     * 商品开卡模板编码数据割接
     * @param excel
     * @return
     * @throws Exception
     */
    @PostMapping("/syncSkuTemplateIdCutOver")
    public BaseAnswer syncSkuTemplateIdCutOver(@RequestPart("file") MultipartFile excel) throws Exception {
        return iProductService.syncSkuTemplateIdCutOver(excel);
    }

    /**
     * sku版本号修复
     * @param request
     */
    @PostMapping("/skuOfferingInfoVersionFix")
    public BaseAnswer skuOfferingInfoVersionFix(@RequestBody SkuOfferingInfoHistory request) {
        return iProductService.skuOfferingInfoVersionFix(request);
    }

    /**
     * atom版本号修复
     * @param request
     */
    @PostMapping("/atomOfferingInfoVersionFix")
    public BaseAnswer atomOfferingInfoVersionFix(@RequestBody AtomOfferingInfoHistory request) {
        return iProductService.atomOfferingInfoVersionFix(request);
    }

    @PostMapping("/import")
    public BaseAnswer importProductDir(@RequestPart("file") MultipartFile excel) {
        iProductService.importProductDir(excel);
        return BaseAnswer.success("成功");
    }

    /**
     * SPU割接
     * @param excel
     */
    @PostMapping("/spuCutOver")
    public BaseAnswer spuCutOver(@RequestPart("file") MultipartFile excel) throws Exception{
        return iProductService.spuCutOver(excel);
    }

    /**
     * 设置修改服务类原子预占信息
     * @param param
     */
    @PostMapping("/updateAtomInventory")
    public BaseAnswer<Void> updateAtomInventoryMessage(@RequestBody @Valid UpdateAtomInventoryParam param){
         iProductService.updateAtomInventory(param);
         return new BaseAnswer<>();
    }


    /**
     * SPU割接
     * @param excel
     */
    @PostMapping("/skuCutOver")
    public BaseAnswer skuCutOver(@RequestPart("file") MultipartFile excel) throws Exception {
        return iProductService.skuCutOver(excel);
    }
}
