package com.chinamobile.iot.sc.constant;

import java.util.Objects;

public enum ManagerStatusEnum {

    NORMAL("1", "正常", "待激活"),
    HANG_UP("2", "挂起", "激活"),
    FORBIDDEN("3", "禁用", "失效");

    public String code;
    public String name; //客户经理
    //    0：普通用户
//1：一级分销员
//2：二级分销员
//3：渠道商
    public String nameOther;


    ManagerStatusEnum(String code, String name,String nameOther) {
        this.code = code;
        this.name = name;
        this.nameOther = nameOther;
    }

    public static Boolean contains(String code) {
        if (code == null) {
            return false;
        }
        ManagerStatusEnum[] values = ManagerStatusEnum.values();
        for (ManagerStatusEnum value : values) {
            if (Objects.equals(value.code, code)) {
                return true;
            }
        }
        return false;
    }

    public static ManagerStatusEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        ManagerStatusEnum[] values = ManagerStatusEnum.values();
        for (ManagerStatusEnum value : values) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

    public static String getName(String status) {

        if (status == null) {
            return null;
        }
        ManagerStatusEnum[] values = ManagerStatusEnum.values();
        for (ManagerStatusEnum value : values) {
            if (Objects.equals(value.code, status)) {
                return value.name;
            }
        }
        return null;

    }

    public static String getNameOther(String status) {

        if (status == null) {
            return null;
        }
        ManagerStatusEnum[] values = ManagerStatusEnum.values();
        for (ManagerStatusEnum value : values) {
            if (Objects.equals(value.code, status)) {
                return value.nameOther;
            }
        }
        return null;

    }
}
