package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/7/6 11:02
 */
@Data
public class OrderFinancingStatusParam extends BaseFinancingParam {

    /**
     * 融资订单编号列表
     */
    @NotEmpty(message = "订单列表不能为空")
    @Valid
    private List<OrderFinancingItem> orderList;

    /**
     * 审核状态
     * 待确认方案：toBeConfirmed
     * 供应商退回：back
     * 供应商拒绝：reject
     * 财司审核中:cmccChecking
     * 财司审核拒绝：cmccReject
     * 银行拒绝：bankReject
     * 银行审批中：bankChecking
     * 已放款:  pass
     */
    @NotEmpty(message = "审核状态不能为空")
    private String status;
    /**
     * 审核意见
     */
    private String advice;

    @Data
    public static class OrderFinancingItem{

        /**
         * 贸易订单编号
         */
        @NotEmpty(message = "订单编号不能为空")
        private String orderCode;

        /**
         * 融资方案编号
         */
        @NotEmpty(message = "融资方案编号不能为空")
        private String financeCode;
        /**
         * 流程编号
         */
        @NotEmpty(message = "流程编号不能为空")
        private String appNo;
        /**
         * 客户名称
         */
        @NotEmpty(message = "客户名称不能为空")
        private String custName;
        /**
         * 省专公司名称
         */
        @NotEmpty(message = "省专公司名称不能为空")
        private String provinceName;
        /**
         * 放款日期 yyyy-MM-dd
         */
        private String payDate;
        /**
         * 放款金额(元)
         */
        private String payAmount;
        /**
         * 放款银行
         */
        private String payBank;
        /**
         * 融资到期日yyyy-MM-dd
         */
        private String dueDate;
    }

}
