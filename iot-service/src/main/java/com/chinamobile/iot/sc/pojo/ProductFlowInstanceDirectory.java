package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 流程实例和导航目录的对应关系表
 *
 * <AUTHOR>
public class ProductFlowInstanceDirectory implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String id;

    /**
     * 流程实例id
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.flow_instance_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String flowInstanceId;

    /**
     * 一级导航目录id
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.first_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String firstDirectoryId;

    /**
     * 一级导航目录名称
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.first_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String firstDirectoryName;

    /**
     * 二级导航目录id
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.second_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String secondDirectoryId;

    /**
     * 二级导航目录名称
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.second_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String secondDirectoryName;

    /**
     * 三级导航目录id
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.third_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String thirdDirectoryId;

    /**
     * 三级导航目录名称
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.third_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private String thirdDirectoryName;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.create_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..product_flow_instance_directory.update_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.id
     *
     * @return the value of supply_chain..product_flow_instance_directory.id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.id
     *
     * @param id the value for supply_chain..product_flow_instance_directory.id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.flow_instance_id
     *
     * @return the value of supply_chain..product_flow_instance_directory.flow_instance_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getFlowInstanceId() {
        return flowInstanceId;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withFlowInstanceId(String flowInstanceId) {
        this.setFlowInstanceId(flowInstanceId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.flow_instance_id
     *
     * @param flowInstanceId the value for supply_chain..product_flow_instance_directory.flow_instance_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId == null ? null : flowInstanceId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.first_directory_id
     *
     * @return the value of supply_chain..product_flow_instance_directory.first_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getFirstDirectoryId() {
        return firstDirectoryId;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withFirstDirectoryId(String firstDirectoryId) {
        this.setFirstDirectoryId(firstDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.first_directory_id
     *
     * @param firstDirectoryId the value for supply_chain..product_flow_instance_directory.first_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setFirstDirectoryId(String firstDirectoryId) {
        this.firstDirectoryId = firstDirectoryId == null ? null : firstDirectoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.first_directory_name
     *
     * @return the value of supply_chain..product_flow_instance_directory.first_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getFirstDirectoryName() {
        return firstDirectoryName;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withFirstDirectoryName(String firstDirectoryName) {
        this.setFirstDirectoryName(firstDirectoryName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.first_directory_name
     *
     * @param firstDirectoryName the value for supply_chain..product_flow_instance_directory.first_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setFirstDirectoryName(String firstDirectoryName) {
        this.firstDirectoryName = firstDirectoryName == null ? null : firstDirectoryName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.second_directory_id
     *
     * @return the value of supply_chain..product_flow_instance_directory.second_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getSecondDirectoryId() {
        return secondDirectoryId;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withSecondDirectoryId(String secondDirectoryId) {
        this.setSecondDirectoryId(secondDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.second_directory_id
     *
     * @param secondDirectoryId the value for supply_chain..product_flow_instance_directory.second_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setSecondDirectoryId(String secondDirectoryId) {
        this.secondDirectoryId = secondDirectoryId == null ? null : secondDirectoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.second_directory_name
     *
     * @return the value of supply_chain..product_flow_instance_directory.second_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getSecondDirectoryName() {
        return secondDirectoryName;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withSecondDirectoryName(String secondDirectoryName) {
        this.setSecondDirectoryName(secondDirectoryName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.second_directory_name
     *
     * @param secondDirectoryName the value for supply_chain..product_flow_instance_directory.second_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setSecondDirectoryName(String secondDirectoryName) {
        this.secondDirectoryName = secondDirectoryName == null ? null : secondDirectoryName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.third_directory_id
     *
     * @return the value of supply_chain..product_flow_instance_directory.third_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getThirdDirectoryId() {
        return thirdDirectoryId;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withThirdDirectoryId(String thirdDirectoryId) {
        this.setThirdDirectoryId(thirdDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.third_directory_id
     *
     * @param thirdDirectoryId the value for supply_chain..product_flow_instance_directory.third_directory_id
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setThirdDirectoryId(String thirdDirectoryId) {
        this.thirdDirectoryId = thirdDirectoryId == null ? null : thirdDirectoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.third_directory_name
     *
     * @return the value of supply_chain..product_flow_instance_directory.third_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getThirdDirectoryName() {
        return thirdDirectoryName;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withThirdDirectoryName(String thirdDirectoryName) {
        this.setThirdDirectoryName(thirdDirectoryName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.third_directory_name
     *
     * @param thirdDirectoryName the value for supply_chain..product_flow_instance_directory.third_directory_name
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setThirdDirectoryName(String thirdDirectoryName) {
        this.thirdDirectoryName = thirdDirectoryName == null ? null : thirdDirectoryName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.create_time
     *
     * @return the value of supply_chain..product_flow_instance_directory.create_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.create_time
     *
     * @param createTime the value for supply_chain..product_flow_instance_directory.create_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_directory.update_time
     *
     * @return the value of supply_chain..product_flow_instance_directory.update_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_directory.update_time
     *
     * @param updateTime the value for supply_chain..product_flow_instance_directory.update_time
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowInstanceId=").append(flowInstanceId);
        sb.append(", firstDirectoryId=").append(firstDirectoryId);
        sb.append(", firstDirectoryName=").append(firstDirectoryName);
        sb.append(", secondDirectoryId=").append(secondDirectoryId);
        sb.append(", secondDirectoryName=").append(secondDirectoryName);
        sb.append(", thirdDirectoryId=").append(thirdDirectoryId);
        sb.append(", thirdDirectoryName=").append(thirdDirectoryName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductFlowInstanceDirectory other = (ProductFlowInstanceDirectory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowInstanceId() == null ? other.getFlowInstanceId() == null : this.getFlowInstanceId().equals(other.getFlowInstanceId()))
            && (this.getFirstDirectoryId() == null ? other.getFirstDirectoryId() == null : this.getFirstDirectoryId().equals(other.getFirstDirectoryId()))
            && (this.getFirstDirectoryName() == null ? other.getFirstDirectoryName() == null : this.getFirstDirectoryName().equals(other.getFirstDirectoryName()))
            && (this.getSecondDirectoryId() == null ? other.getSecondDirectoryId() == null : this.getSecondDirectoryId().equals(other.getSecondDirectoryId()))
            && (this.getSecondDirectoryName() == null ? other.getSecondDirectoryName() == null : this.getSecondDirectoryName().equals(other.getSecondDirectoryName()))
            && (this.getThirdDirectoryId() == null ? other.getThirdDirectoryId() == null : this.getThirdDirectoryId().equals(other.getThirdDirectoryId()))
            && (this.getThirdDirectoryName() == null ? other.getThirdDirectoryName() == null : this.getThirdDirectoryName().equals(other.getThirdDirectoryName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowInstanceId() == null) ? 0 : getFlowInstanceId().hashCode());
        result = prime * result + ((getFirstDirectoryId() == null) ? 0 : getFirstDirectoryId().hashCode());
        result = prime * result + ((getFirstDirectoryName() == null) ? 0 : getFirstDirectoryName().hashCode());
        result = prime * result + ((getSecondDirectoryId() == null) ? 0 : getSecondDirectoryId().hashCode());
        result = prime * result + ((getSecondDirectoryName() == null) ? 0 : getSecondDirectoryName().hashCode());
        result = prime * result + ((getThirdDirectoryId() == null) ? 0 : getThirdDirectoryId().hashCode());
        result = prime * result + ((getThirdDirectoryName() == null) ? 0 : getThirdDirectoryName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        flowInstanceId("flow_instance_id", "flowInstanceId", "VARCHAR", false),
        firstDirectoryId("first_directory_id", "firstDirectoryId", "VARCHAR", false),
        firstDirectoryName("first_directory_name", "firstDirectoryName", "VARCHAR", false),
        secondDirectoryId("second_directory_id", "secondDirectoryId", "VARCHAR", false),
        secondDirectoryName("second_directory_name", "secondDirectoryName", "VARCHAR", false),
        thirdDirectoryId("third_directory_id", "thirdDirectoryId", "VARCHAR", false),
        thirdDirectoryName("third_directory_name", "thirdDirectoryName", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}