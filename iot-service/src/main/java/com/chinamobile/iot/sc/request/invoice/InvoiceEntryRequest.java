package com.chinamobile.iot.sc.request.invoice;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: InvoiceEntryDTO
 * @description: 发票录入信息请求
 * @author: zyj
 * @create: 2021/12/6 14:45
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InvoiceEntryRequest {
    //发票号码
    @NotBlank(message = "发票号码必填！")
    private String voucherNum;
    //发票代码
    @NotBlank(message = "发票代码必填！")
    private String voucherID;
    //发票金额，单位厘
    @NotBlank(message = "发票金额必填！")
    private Long voucherSum;
    //发票开具时间
    @NotBlank(message = "发票开具时间必填！")
    private String billingDate;
    //原子商品订单信息
    private String atomOrderId;
    //录入顺序：从0开始
//    private Integer sort;
}
