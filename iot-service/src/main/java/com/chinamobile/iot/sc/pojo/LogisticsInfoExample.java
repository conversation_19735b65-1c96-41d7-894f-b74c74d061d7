package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LogisticsInfoExample {
    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public LogisticsInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public LogisticsInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public LogisticsInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        LogisticsInfoExample example = new LogisticsInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public LogisticsInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public LogisticsInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIsNull() {
            addCriterion("order_atom_info_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIsNotNull() {
            addCriterion("order_atom_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdEqualTo(String value) {
            addCriterion("order_atom_info_id =", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotEqualTo(String value) {
            addCriterion("order_atom_info_id <>", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThan(String value) {
            addCriterion("order_atom_info_id >", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_atom_info_id >=", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThan(String value) {
            addCriterion("order_atom_info_id <", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanOrEqualTo(String value) {
            addCriterion("order_atom_info_id <=", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("order_atom_info_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLike(String value) {
            addCriterion("order_atom_info_id like", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotLike(String value) {
            addCriterion("order_atom_info_id not like", value, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdIn(List<String> values) {
            addCriterion("order_atom_info_id in", values, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotIn(List<String> values) {
            addCriterion("order_atom_info_id not in", values, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdBetween(String value1, String value2) {
            addCriterion("order_atom_info_id between", value1, value2, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdNotBetween(String value1, String value2) {
            addCriterion("order_atom_info_id not between", value1, value2, "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdIsNull() {
            addCriterion("refund_order_id is null");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdIsNotNull() {
            addCriterion("refund_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdEqualTo(String value) {
            addCriterion("refund_order_id =", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdNotEqualTo(String value) {
            addCriterion("refund_order_id <>", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdGreaterThan(String value) {
            addCriterion("refund_order_id >", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("refund_order_id >=", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLessThan(String value) {
            addCriterion("refund_order_id <", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLessThanOrEqualTo(String value) {
            addCriterion("refund_order_id <=", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("refund_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLike(String value) {
            addCriterion("refund_order_id like", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdNotLike(String value) {
            addCriterion("refund_order_id not like", value, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdIn(List<String> values) {
            addCriterion("refund_order_id in", values, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdNotIn(List<String> values) {
            addCriterion("refund_order_id not in", values, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdBetween(String value1, String value2) {
            addCriterion("refund_order_id between", value1, value2, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdNotBetween(String value1, String value2) {
            addCriterion("refund_order_id not between", value1, value2, "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeIsNull() {
            addCriterion("logistics_type is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeIsNotNull() {
            addCriterion("logistics_type is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeEqualTo(Integer value) {
            addCriterion("logistics_type =", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeNotEqualTo(Integer value) {
            addCriterion("logistics_type <>", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeGreaterThan(Integer value) {
            addCriterion("logistics_type >", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("logistics_type >=", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeLessThan(Integer value) {
            addCriterion("logistics_type <", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeLessThanOrEqualTo(Integer value) {
            addCriterion("logistics_type <=", value, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeIn(List<Integer> values) {
            addCriterion("logistics_type in", values, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeNotIn(List<Integer> values) {
            addCriterion("logistics_type not in", values, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeBetween(Integer value1, Integer value2) {
            addCriterion("logistics_type between", value1, value2, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisticsTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("logistics_type not between", value1, value2, "logisticsType");
            return (Criteria) this;
        }

        public Criteria andLogisCodeIsNull() {
            addCriterion("logis_code is null");
            return (Criteria) this;
        }

        public Criteria andLogisCodeIsNotNull() {
            addCriterion("logis_code is not null");
            return (Criteria) this;
        }

        public Criteria andLogisCodeEqualTo(String value) {
            addCriterion("logis_code =", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeNotEqualTo(String value) {
            addCriterion("logis_code <>", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeGreaterThan(String value) {
            addCriterion("logis_code >", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeGreaterThanOrEqualTo(String value) {
            addCriterion("logis_code >=", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeLessThan(String value) {
            addCriterion("logis_code <", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeLessThanOrEqualTo(String value) {
            addCriterion("logis_code <=", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logis_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisCodeLike(String value) {
            addCriterion("logis_code like", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeNotLike(String value) {
            addCriterion("logis_code not like", value, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeIn(List<String> values) {
            addCriterion("logis_code in", values, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeNotIn(List<String> values) {
            addCriterion("logis_code not in", values, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeBetween(String value1, String value2) {
            addCriterion("logis_code between", value1, value2, "logisCode");
            return (Criteria) this;
        }

        public Criteria andLogisCodeNotBetween(String value1, String value2) {
            addCriterion("logis_code not between", value1, value2, "logisCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("supplier_name is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("supplier_name is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("supplier_name =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("supplier_name <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("supplier_name >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_name >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("supplier_name <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("supplier_name <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("supplier_name like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("supplier_name not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("supplier_name in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("supplier_name not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("supplier_name between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("supplier_name not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameIsNull() {
            addCriterion("sign_receipt_name is null");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameIsNotNull() {
            addCriterion("sign_receipt_name is not null");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameEqualTo(String value) {
            addCriterion("sign_receipt_name =", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameNotEqualTo(String value) {
            addCriterion("sign_receipt_name <>", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameGreaterThan(String value) {
            addCriterion("sign_receipt_name >", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameGreaterThanOrEqualTo(String value) {
            addCriterion("sign_receipt_name >=", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLessThan(String value) {
            addCriterion("sign_receipt_name <", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLessThanOrEqualTo(String value) {
            addCriterion("sign_receipt_name <=", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("sign_receipt_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLike(String value) {
            addCriterion("sign_receipt_name like", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameNotLike(String value) {
            addCriterion("sign_receipt_name not like", value, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameIn(List<String> values) {
            addCriterion("sign_receipt_name in", values, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameNotIn(List<String> values) {
            addCriterion("sign_receipt_name not in", values, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameBetween(String value1, String value2) {
            addCriterion("sign_receipt_name between", value1, value2, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameNotBetween(String value1, String value2) {
            addCriterion("sign_receipt_name not between", value1, value2, "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("description <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateIsNull() {
            addCriterion("logistics_state is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateIsNotNull() {
            addCriterion("logistics_state is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateEqualTo(Integer value) {
            addCriterion("logistics_state =", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateNotEqualTo(Integer value) {
            addCriterion("logistics_state <>", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateNotEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateGreaterThan(Integer value) {
            addCriterion("logistics_state >", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateGreaterThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("logistics_state >=", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateGreaterThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateLessThan(Integer value) {
            addCriterion("logistics_state <", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateLessThanColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateLessThanOrEqualTo(Integer value) {
            addCriterion("logistics_state <=", value, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateLessThanOrEqualToColumn(LogisticsInfo.Column column) {
            addCriterion(new StringBuilder("logistics_state <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLogisticsStateIn(List<Integer> values) {
            addCriterion("logistics_state in", values, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateNotIn(List<Integer> values) {
            addCriterion("logistics_state not in", values, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateBetween(Integer value1, Integer value2) {
            addCriterion("logistics_state between", value1, value2, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andLogisticsStateNotBetween(Integer value1, Integer value2) {
            addCriterion("logistics_state not between", value1, value2, "logisticsState");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderAtomInfoIdLikeInsensitive(String value) {
            addCriterion("upper(order_atom_info_id) like", value.toUpperCase(), "orderAtomInfoId");
            return (Criteria) this;
        }

        public Criteria andRefundOrderIdLikeInsensitive(String value) {
            addCriterion("upper(refund_order_id) like", value.toUpperCase(), "refundOrderId");
            return (Criteria) this;
        }

        public Criteria andLogisCodeLikeInsensitive(String value) {
            addCriterion("upper(logis_code) like", value.toUpperCase(), "logisCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLikeInsensitive(String value) {
            addCriterion("upper(supplier_name) like", value.toUpperCase(), "supplierName");
            return (Criteria) this;
        }

        public Criteria andSignReceiptNameLikeInsensitive(String value) {
            addCriterion("upper(sign_receipt_name) like", value.toUpperCase(), "signReceiptName");
            return (Criteria) this;
        }

        public Criteria andDescriptionLikeInsensitive(String value) {
            addCriterion("upper(description) like", value.toUpperCase(), "description");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Sep 27 17:12:00 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        private LogisticsInfoExample example;

        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        protected Criteria(LogisticsInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        public LogisticsInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Sep 27 17:12:00 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Sep 27 17:12:00 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Sep 27 17:12:00 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.LogisticsInfoExample example);
    }
}