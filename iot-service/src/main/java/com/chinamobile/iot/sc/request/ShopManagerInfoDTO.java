package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**

 * 商城客户经理信息同步传递
 */
@Data
public class ShopManagerInfoDTO implements Serializable {
    /**
     * 操作类型
     * 1、	新增
     * 2、	修改
     * */
    private String oprType;
    /**
     * 客户经理编码
     */
    private String createOperCode;

    /**
     * 客户经理手机号
     *
     */
    private String createOperPhone;

    /**
     * 用户标识
     *
     */
    private String userID;

    /**
     * 客户经理姓名
     *
     */
    private String customerManagerName;

    /**
     * 客户经理工号
     */
    private String employeeNum;

    /**
     * operType=1时，传从账号生效时间
     * operType=2时，传从账号变更时间
     * 格式：yyyyMMddhhmmss
     * */
    private String custStatusTime;


    /**
     * 客户经理省份
     *
     */
    private String beId;

    /**
     * 客户经理地市
     *
     */
    private String location;


    /**
     * 客户经理区县
     *
     */
    private String regionID;


    /**
     * 客户经理状态1：正常  2：挂起（账号冻结，可解冻转变为正常）3：禁用（账号注销，不能转变为正常，只能重新注册）
     *
     */
    private String mrgStatus;

}