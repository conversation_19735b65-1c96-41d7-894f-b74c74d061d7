package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.dto.OnlineSettlementInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.OsOrderToOnlineOrderDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15
 * @description 线上结算管理商城订单mapper扩展类
 */
public interface OnlineSettlementOsOrderMapperExt {

    /**
     * 获取OS订单到线上管理订单
     * @param orderId
     * @return
     */
    List<OsOrderToOnlineOrderDTO> listOsOrderToOnlineOrder(@Param(value = "orderId") String orderId);

    /**
     * 获取采购订单编码
     * @param orderId
     * @return
     */
    OnlineSettlementInfoDTO getOnlineSettlementInfo(@Param(value = "orderId") String orderId);
}
