package com.chinamobile.iot.sc.response.web.invoice;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 17:48
 */
@Data
@Accessors(chain = true)
public class Data4UnInvoice {

    /**
     * 开票订单号
     */
    @Excel(name = "开票订单号")
    private String id;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 申请时间
     */
    @Excel(name = "申请时间")
    private Date createTime;

    /**
     * 发票标记
     */
    @Excel(name = "发票标记")
    private String frank;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    private Long orderPrice;

    /**
     * 合作伙伴名称
     */
    @Excel(name = "合作伙伴名称")
    private String partnerName;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String cooperatorName;

    /**
     * 开票状态
     */
    @Excel(name = "开票状态")
    private String status;

    /**
     * 发票抬头
     */
    @Excel(name = "发票抬头")
    private String pName;


}
