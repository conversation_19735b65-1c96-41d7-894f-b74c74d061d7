package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 上架商品套餐信息
 * <AUTHOR>
 * @date 2022/9/22 10:12
 */
@Data
public class ComboInfoExcel {

    @ExcelProperty(value = "商品名称SPU", index = 0)
    private String spuOfferingName;

    @ExcelProperty(value = "商品规格SKU", index = 1)
    private String skuOfferingName;

    @ExcelProperty(value = "合作伙伴名称", index = 2)
    private String cooperatorName;

    @ExcelProperty(value = "典型应用领域", index = 3)
    private String applicationDomain;

    @ExcelProperty(value = "商品简介", index = 4)
    private String productIntroduction;

    @ExcelProperty(value = "产品可销售区域", index = 5)
    private String productSaleArea;

    @ExcelProperty(value = "产品经理联系方式", index = 6)
    private String productManagerContact;

    @ExcelProperty(value = "商品规格供货价（元）", index = 7)
    private String supplyPrice;

    @ExcelProperty(value = "物联卡套餐说明", index = 8)
    private String thingsCardCombo;

    @ExcelProperty(value = "硬件商品发货清单", index = 9)
    private String hardwareShippingList;

    @ExcelProperty(value = "商品参数信息", index = 10)
    private String productParam;

    @ExcelProperty(value = "商品发货地址", index = 11)
    private String productShipAddress;

    @ExcelProperty(value = "硬件商品发货默认快递", index = 12)
    private String hardwareExpressageName;

    @ExcelProperty(value = "商品发货时间信息", index = 13)
    private String productShipTime;

    @ExcelProperty(value = "商品使用条件", index = 14)
    private String productWorkingCondition;

    @ExcelProperty(value = "软件平台名称及介绍", index = 15)
    private String softwareInfo;

    @ExcelProperty(value = "软件平台下载方式及地址", index = 16)
    private String softwareGetWay;

    @ExcelProperty(value = "APP、小程序名称及介绍", index = 17)
    private String appMiniProgramInfo;

    @ExcelProperty(value = "APP、小程序下载方式及地址", index = 18)
    private String appMiniProgramGetWay;

    @ExcelProperty(value = "安装服务说明", index = 19)
    private String installationServices;

}
