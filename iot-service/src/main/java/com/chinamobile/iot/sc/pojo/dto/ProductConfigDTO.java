package com.chinamobile.iot.sc.pojo.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class ProductConfigDTO implements Serializable {
    /**
     * 规格商品ID
     */
    private String id;


    // sku/spu分割线

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private String  spuOfferingStatus;
    /**
     * 销售对象
     */
    private String saleObject;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     * 规格目录价
     */
    private Long  price;

    /**
     * 合作伙伴id
     */
    private String cooperatorId;

    /**
     * spu图片
     */
    private String spuImgUrl;

    /**
     * 接单方式  1--OS接单   2--省内接单
     */
    private Integer ordertakeType;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;


    /**
     * X产品类型,1:5G CPE,2:5G 快线,3:千里眼,4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,7:OnePark独立服务,8:标准产品（OnePark）
     */
    private String productType;

    /**
     * 是否需要合作伙伴接单,1：是,2：否
     */
    private String receiveOrder;

    /**
     * 卡服务商EC编码
     */
    private String custCode;

    /**
     * 卡服务商名称
     */
    private String custName;

    /**
     * 卡片类型,0：插拔卡,1：贴片卡,2：M2M芯片非空写卡,3：M2M芯片空写卡
     */
    private String cardType;

    /**
     * 主商品,01：物联卡个人、03：窄带网个人、04：和对讲个人、 16：行车卫士个人
     */
    private String mainOfferingCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 项目信息
     */
    private String project;

    /**
     * CMIOT关联商品
     */
    private String associatedOffer;

    /**
     * 资费有效期,需带单位，例如：3月、180天
     */
    private String validityPeriod;

    /**
     * 一级导航目录名称
     */
    private String firstNavigationName;

    /**
     * 二级导航目录名称
     */
    private String secondNavigationName;

    /**
     * 三级导航目录名称
     */
    private String thirdNavigationName;

    /**
     * 是否隐秘上架,0：是 , 1：否
     */
    private String secretlyListed;

    /**
     * 商品简介
     */
    private String productDescription;

    /**
     * 主销售标签
     */
    private String mainSaleLabel;

    /**
     * 副销售标签
     */
    private String subSaleLabel;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 接单合作伙伴名称
     */
    private String receiveOrderName;

    /**
     * 接单合作伙伴电话
     */
    private String receiveOrderPhone;

    /**
     * 交付合作伙伴名称
     */
    private String deliverName;

    /**
     * 交付合作伙伴电话
     */
    private String deliverPhone;

    /**
     * 售后合作伙伴名称
     */
    private String aftermarketName;

    /**
     * 售后合作伙伴电话
     */
    private String aftermarketPhone;

    /**
     * 商品关键字
     */
    private String productKeywords;



}
