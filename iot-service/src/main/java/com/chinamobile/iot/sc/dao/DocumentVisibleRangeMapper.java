package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.DocumentVisibleRange;
import com.chinamobile.iot.sc.pojo.DocumentVisibleRangeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DocumentVisibleRangeMapper {
    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    long countByExample(DocumentVisibleRangeExample example);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int deleteByExample(DocumentVisibleRangeExample example);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int insert(DocumentVisibleRange record);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int insertSelective(DocumentVisibleRange record);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    List<DocumentVisibleRange> selectByExample(DocumentVisibleRangeExample example);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    DocumentVisibleRange selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int updateByExampleSelective(@Param("record") DocumentVisibleRange record, @Param("example") DocumentVisibleRangeExample example);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int updateByExample(@Param("record") DocumentVisibleRange record, @Param("example") DocumentVisibleRangeExample example);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int updateByPrimaryKeySelective(DocumentVisibleRange record);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int updateByPrimaryKey(DocumentVisibleRange record);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int batchInsert(@Param("list") List<DocumentVisibleRange> list);

    /**
     *
     * @mbg.generated Wed Jul 23 15:25:59 CST 2025
     */
    int batchInsertSelective(@Param("list") List<DocumentVisibleRange> list, @Param("selective") DocumentVisibleRange.Column ... selective);
}