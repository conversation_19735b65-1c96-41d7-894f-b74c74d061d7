package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:12
 */
@Data
public class NewProductOnlineOfflineInfoExport {

    @ExcelProperty(value="商城展示一级类目",index=0)
    private Integer storeFirstCatalog;

    @ExcelProperty(value="商城展示二级类目",index=1)
    private String storeSecondCatalog;

    @ExcelProperty(value="原子上架类目",index=2)
    private String spuOfferingClass;

//    @ExcelProperty(value="服务商",index=3)
//    private String serviceProvider;

    @ExcelProperty(value="商品规格销售价",index=3)
    private String salePrice;

    @ExcelProperty(value="硬件原子名称",index=4)
    private String atomOfferingName;

    @ExcelProperty(value="硬件原子结算价",index=5)
    private String atomHardwareSettlePrice;

    @ExcelProperty(value="硬件原子销售价",index=6)
    private String atomHardwareSalePrice;

    @ExcelProperty(value="软件原子销售价",index=7)
    private String atomSoftwareSalePrice;

    @ExcelProperty(value="省公司计收额",index=8)
    private String provinceAccrual;

    @ExcelProperty(value="归属部门",index=9)
    private String departmentName;

    @ExcelProperty(value="产品属性",index=10)
    private String productProperty;
}
