package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

import java.util.List;

@Data
public class GioSkuDTO {

    private String item_id;
    private GioSkuAttr attrs;
    @Data
    public static class GioSkuAttr{
        private String sku_name;
        private String sku_price;
        private String sku_status;
        private String sku_version;
        private String sku_province;
        private String spu_name;
        private String spu_code;
        private String spu_version;
        private String spu_type;
        private String spu_create;
        private String spu_sales_type;
        private String supplier_code;
        private String supplier_name;
        private String supplier_contact;
        private String contact_phone;
        private String commission_rate;
        private String is_delete;

        // 新增字段：导航目录
        private String firstLevelNavCatalog;
        private String secondLevelNavCatalog;
        private String thirdLevelNavCatalog;

        // 新增字段：商品关键字
        private String productKeyword;

        // 新增字段：销售标签
        private String mainSaleTag;
        private String subSaleTag;
    }


}
