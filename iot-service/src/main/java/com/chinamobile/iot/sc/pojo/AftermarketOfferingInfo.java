package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * IoT售后服务包信息
 *
 * <AUTHOR>
public class AftermarketOfferingInfo implements Serializable {
    /**
     * IoT售后服务包编码
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String afterMarketCode;

    /**
     * IoT售后服务包状态 A：已生效 M：已失效
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String operType;

    /**
     * IoT售后服务包内部名称
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String afterMarketInternalName;

    /**
     * IoT售后服务包对外名称
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String afterMarketExternalName;

    /**
     * 销售单价
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String sellPrice;

    /**
     * 结算单价
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String settlePrice;

    /**
     * 计量单位
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String unit;

    /**
     * 是否必选 枚举值 0:非必选 1:必选
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String mandatory;

    /**
     * 售后服务类型 枚举值： 1：OneNET/OnePark属地化服务 2：铁通增值服务 3：铁通增值服务（卡+X专用）
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private String aftermarketType;

    /**
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.after_market_code
     *
     * @return the value of supply_chain..aftermarket_offering_info.after_market_code
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getAfterMarketCode() {
        return afterMarketCode;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withAfterMarketCode(String afterMarketCode) {
        this.setAfterMarketCode(afterMarketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.after_market_code
     *
     * @param afterMarketCode the value for supply_chain..aftermarket_offering_info.after_market_code
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setAfterMarketCode(String afterMarketCode) {
        this.afterMarketCode = afterMarketCode;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.oper_type
     *
     * @return the value of supply_chain..aftermarket_offering_info.oper_type
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getOperType() {
        return operType;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withOperType(String operType) {
        this.setOperType(operType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.oper_type
     *
     * @param operType the value for supply_chain..aftermarket_offering_info.oper_type
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setOperType(String operType) {
        this.operType = operType;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.after_market_internal_name
     *
     * @return the value of supply_chain..aftermarket_offering_info.after_market_internal_name
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getAfterMarketInternalName() {
        return afterMarketInternalName;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withAfterMarketInternalName(String afterMarketInternalName) {
        this.setAfterMarketInternalName(afterMarketInternalName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.after_market_internal_name
     *
     * @param afterMarketInternalName the value for supply_chain..aftermarket_offering_info.after_market_internal_name
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setAfterMarketInternalName(String afterMarketInternalName) {
        this.afterMarketInternalName = afterMarketInternalName;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.after_market_external_name
     *
     * @return the value of supply_chain..aftermarket_offering_info.after_market_external_name
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getAfterMarketExternalName() {
        return afterMarketExternalName;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withAfterMarketExternalName(String afterMarketExternalName) {
        this.setAfterMarketExternalName(afterMarketExternalName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.after_market_external_name
     *
     * @param afterMarketExternalName the value for supply_chain..aftermarket_offering_info.after_market_external_name
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setAfterMarketExternalName(String afterMarketExternalName) {
        this.afterMarketExternalName = afterMarketExternalName;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.sell_price
     *
     * @return the value of supply_chain..aftermarket_offering_info.sell_price
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getSellPrice() {
        return sellPrice;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withSellPrice(String sellPrice) {
        this.setSellPrice(sellPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.sell_price
     *
     * @param sellPrice the value for supply_chain..aftermarket_offering_info.sell_price
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setSellPrice(String sellPrice) {
        this.sellPrice = sellPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.settle_price
     *
     * @return the value of supply_chain..aftermarket_offering_info.settle_price
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getSettlePrice() {
        return settlePrice;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withSettlePrice(String settlePrice) {
        this.setSettlePrice(settlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.settle_price
     *
     * @param settlePrice the value for supply_chain..aftermarket_offering_info.settle_price
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setSettlePrice(String settlePrice) {
        this.settlePrice = settlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.unit
     *
     * @return the value of supply_chain..aftermarket_offering_info.unit
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.unit
     *
     * @param unit the value for supply_chain..aftermarket_offering_info.unit
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.mandatory
     *
     * @return the value of supply_chain..aftermarket_offering_info.mandatory
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getMandatory() {
        return mandatory;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withMandatory(String mandatory) {
        this.setMandatory(mandatory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.mandatory
     *
     * @param mandatory the value for supply_chain..aftermarket_offering_info.mandatory
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setMandatory(String mandatory) {
        this.mandatory = mandatory;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.aftermarket_type
     *
     * @return the value of supply_chain..aftermarket_offering_info.aftermarket_type
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public String getAftermarketType() {
        return aftermarketType;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withAftermarketType(String aftermarketType) {
        this.setAftermarketType(aftermarketType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.aftermarket_type
     *
     * @param aftermarketType the value for supply_chain..aftermarket_offering_info.aftermarket_type
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setAftermarketType(String aftermarketType) {
        this.aftermarketType = aftermarketType;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.create_time
     *
     * @return the value of supply_chain..aftermarket_offering_info.create_time
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.create_time
     *
     * @param createTime the value for supply_chain..aftermarket_offering_info.create_time
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_offering_info.update_time
     *
     * @return the value of supply_chain..aftermarket_offering_info.update_time
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public AftermarketOfferingInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_offering_info.update_time
     *
     * @param updateTime the value for supply_chain..aftermarket_offering_info.update_time
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", afterMarketCode=").append(afterMarketCode);
        sb.append(", operType=").append(operType);
        sb.append(", afterMarketInternalName=").append(afterMarketInternalName);
        sb.append(", afterMarketExternalName=").append(afterMarketExternalName);
        sb.append(", sellPrice=").append(sellPrice);
        sb.append(", settlePrice=").append(settlePrice);
        sb.append(", unit=").append(unit);
        sb.append(", mandatory=").append(mandatory);
        sb.append(", aftermarketType=").append(aftermarketType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AftermarketOfferingInfo other = (AftermarketOfferingInfo) that;
        return (this.getAfterMarketCode() == null ? other.getAfterMarketCode() == null : this.getAfterMarketCode().equals(other.getAfterMarketCode()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getAfterMarketInternalName() == null ? other.getAfterMarketInternalName() == null : this.getAfterMarketInternalName().equals(other.getAfterMarketInternalName()))
            && (this.getAfterMarketExternalName() == null ? other.getAfterMarketExternalName() == null : this.getAfterMarketExternalName().equals(other.getAfterMarketExternalName()))
            && (this.getSellPrice() == null ? other.getSellPrice() == null : this.getSellPrice().equals(other.getSellPrice()))
            && (this.getSettlePrice() == null ? other.getSettlePrice() == null : this.getSettlePrice().equals(other.getSettlePrice()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getMandatory() == null ? other.getMandatory() == null : this.getMandatory().equals(other.getMandatory()))
            && (this.getAftermarketType() == null ? other.getAftermarketType() == null : this.getAftermarketType().equals(other.getAftermarketType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAfterMarketCode() == null) ? 0 : getAfterMarketCode().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getAfterMarketInternalName() == null) ? 0 : getAfterMarketInternalName().hashCode());
        result = prime * result + ((getAfterMarketExternalName() == null) ? 0 : getAfterMarketExternalName().hashCode());
        result = prime * result + ((getSellPrice() == null) ? 0 : getSellPrice().hashCode());
        result = prime * result + ((getSettlePrice() == null) ? 0 : getSettlePrice().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getMandatory() == null) ? 0 : getMandatory().hashCode());
        result = prime * result + ((getAftermarketType() == null) ? 0 : getAftermarketType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Feb 21 15:06:20 CST 2024
     */
    public enum Column {
        afterMarketCode("after_market_code", "afterMarketCode", "VARCHAR", false),
        operType("oper_type", "operType", "VARCHAR", false),
        afterMarketInternalName("after_market_internal_name", "afterMarketInternalName", "VARCHAR", false),
        afterMarketExternalName("after_market_external_name", "afterMarketExternalName", "VARCHAR", false),
        sellPrice("sell_price", "sellPrice", "VARCHAR", false),
        settlePrice("settle_price", "settlePrice", "VARCHAR", false),
        unit("unit", "unit", "VARCHAR", false),
        mandatory("mandatory", "mandatory", "VARCHAR", false),
        aftermarketType("aftermarket_type", "aftermarketType", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Feb 21 15:06:20 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}