package com.chinamobile.iot.sc.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 卡相关信息展示类
 */
@Data
public class CardRelationVO {

    /**
     * 终端IMEI
     */
    @Excel(name = "终端IMEI")
    private String imei;

    /**
     * 临时iccid
     */
    @Excel(name = "临时iccid")
    private String tempIccid;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    private String clientName;

    /**
     * 产品编码
     */
    @Excel(name = "产品编码")
    private String productNum;

    @Excel(name = "SN")
    private String sn;

    /**
     * 导入时间
     */
    private Date createTime;

    @Excel(name = "导入时间")
    private String createTimeStr;
}
