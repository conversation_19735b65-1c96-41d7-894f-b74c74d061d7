package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.Order2cAtomHistoryMapper;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.dao.ext.OrderDictInfoMapperExt;
import com.chinamobile.iot.sc.pojo.Order2cAtomHistory;
import com.chinamobile.iot.sc.pojo.Order2cAtomHistoryExample;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.mapper.ValetOrderStatusDO;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * created by liuxiang on 2023/3/23 16:57
 * 根据订单状态日文件中订单状态的变化（商城接口文档3.1.9），同步更新订单表数据
 */
@Component
@Slf4j
public class AfterPayOrderStatusTask {

    private String lockKey = "AfterPayOrderStatusTaskTask";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OrderDictInfoMapperExt orderDictInfoMapperExt;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    @Value("${tocustomer.orderType}")
    private List toCustomerOrderType;

    //每天10点执行（目的是和状态文件的任务错开）
    @Scheduled(cron = "0 0 10 * * ?")
    public void work(){
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1",10, TimeUnit.MINUTES);
            if(getLock){
                log.info("【开始】AfterPayOrderStatusTask通过状态文件更新代客下单状态");
                Date now = new Date();
                Date todayBegin = DateTimeUtil.getDayBeginDate(now);
                Date todayEnd = DateTimeUtil.getDayEndDate(now);
                Date yesterdayBegin = DateTimeUtil.addDay(todayBegin, -1);
                Date yesterdayEnd = DateTimeUtil.addDay(todayEnd, -1);
                //查出订单状态日文件新增的昨日有状态变化的代客下单订单及其最新状态
                List<ValetOrderStatusDO> valetOrderStatusDOList = orderDictInfoMapperExt.findOrderStatusByTime(yesterdayBegin,yesterdayEnd);
                log.info("AfterPayOrderStatusTask昨日代客下单更新订单数量:{}",valetOrderStatusDOList.size());
                if(valetOrderStatusDOList.isEmpty()){
                    log.info("【无数据】AfterPayOrderStatusTask通过状态文件更新代客下单状态");
                    return;
                }
                valetOrderStatusDOList.forEach(o -> {
                    /**
                         * B0：草稿单
                         * B1：待合作伙伴接单（即实时同步接口的预受理单创建）
                         * B2：订单待审批
                         * B3：待合作伙伴交付（即实时同步接口的订单创建）
                         * B6：待客户经理确认交付
                         * B7：客户经理已交付
                         * B8：待出账（即实时同步接口的订单验收状态）
                         * B4：交易完成（即实时同步接口的订单计收状态）
                         * B5：交易关闭 (即实时同步接口的订单退款完成）
                         * B9：草稿单删除
                         * B11：制卡中（卡+X订单才有此类型）
                     */
                    String orderId = o.getOrderId();
                    try {
                        //格式: B7_2022-11-14 01:42:17,B6_2022-11-14 02:42:17,B5_2022-11-13 03:42:17,B8_2022-11-15 04:42:17
                        String orderStatusAndTimes = o.getOrderStatusAndTime();
                        String[] split = orderStatusAndTimes.split(",");
                        List<String> list = new ArrayList<>(Arrays.asList(split));
                        //根据时间倒序排列，使得第一条数据是订单的最新状态
                        Collections.sort(list,(o1,o2) -> {
                            String timeStr1 = o1.substring(o1.indexOf("_") + 1);
                            String timeStr2 = o2.substring(o2.indexOf("_") + 1);
                            try {
                                Date date1 = DateTimeUtil.getFormatDate(timeStr1, DateTimeUtil.DEFAULT_DATE_DEFAULT);
                                Date date2 = DateTimeUtil.getFormatDate(timeStr2, DateTimeUtil.DEFAULT_DATE_DEFAULT);
                                return date1.after(date2) ? -1 : 1;
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            return 0;
                        });
                        List<String> orderStatusList = new ArrayList<>();
                        boolean addB7 = true;
                        Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
                        order2cAtomInfoExample.createCriteria().andOrderIdEqualTo(orderId).andAtomOfferingClassNotEqualTo("S");
                        List<Order2cAtomInfo> order2cAtomInfos = order2cAtomInfoMapper.selectByExample(order2cAtomInfoExample);

                        for(int i =0;i<list.size();i++){
                            String statusAndTime = list.get(i);
                            String[] s = statusAndTime.split("_");
                            String orderStatus = s[0];
                            String timeStr = s[1];
                            if(orderStatusList.contains(orderStatus)){
                                //同一天，某个订单有很多重复的状态，过滤掉
                                continue;
                            }
                            try {
                                Date orderStatusTime = DateTimeUtil.getFormatDate(timeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT);
                                //将状态文件的状态，转换为原子订单表的状态
                                Integer status = getStatus(orderStatus);
                                if(status != null){
                                    if(i == 0){
                                        //更新最新的状态和对应时间
                                        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
                                        if(order2cInfo == null){
                                            log.error("订单id:{}不存在",orderId);
                                        }else {
                                            //数据库中，订单状态的最新时间（通过原子订单表获取，因为有些状态更新不是通过商城接口同步的，比如发货等）
                                            Date currentStatusTime = order2cAtomInfos.get(0).getUpdateTime();
                                            if(orderStatusTime.after(currentStatusTime)){
                                                //状态文件中的时间晚于订单中的最新状态时间，更新数据
                                                order2cInfo.setOrderId(orderId);
                                                order2cInfo.setOrderStatusTime(orderStatusTime);
                                                //没有走IOT订单同步接口，所以不更新oder_2c_info表status,仅更新order_status和原子订单表对应
                                                order2cInfo.setOrderStatus(status);
                                                order2cInfo.setUpdateTime(orderStatusTime);
                                                if(status.intValue() == OrderStatusInnerEnum.VALET_ORDER_COMPLETE.getStatus().intValue()){
                                                    order2cInfo.setValetOrderCompleteTime(DateTimeUtil.getDbTimeStr(orderStatusTime));
                                                }
                                                order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);

                                                Order2cAtomInfoExample example = new Order2cAtomInfoExample().createCriteria().andOrderIdEqualTo(orderId).example();
                                                Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                                                order2cAtomInfo.setOrderStatus(status);
                                                order2cAtomInfo.setUpdateTime(orderStatusTime);
                                                if(status.intValue() == OrderStatusInnerEnum.VALET_ORDER_COMPLETE.getStatus().intValue()){
                                                    order2cAtomInfo.setValetOrderCompleteTime(DateTimeUtil.getDbTimeStr(orderStatusTime));
                                                }
                                                order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo,example);
                                            }
                                            String orderType = order2cInfo.getOrderType();
                                            if((/*"00".equals(orderType) || "02".equals(orderType)*/toCustomerOrderType.contains(orderType)) && "B6".equals(orderStatus)){
                                                //最新的状态是 待收货，所以不需要再添加 已收货
                                                addB7 = false;
                                            }
                                        }
                                    }


                                    //找出 待收货(待客户经理确认交付)，已收货(客户经理已交付) 的订单操作记录，并存入历史表
                                    if(("B6".equals(orderStatus))){
                                        //从“客户经理已交付”回退至“待客户经理确认交付”。删掉之前保存的对应历史记录（回退到草稿单不涉及历史）
                                        List<Integer> statusList = new ArrayList<>();
                                        statusList.add(OrderStatusInnerEnum.WAIT_RECEIVE.getStatus());
                                        statusList.add(OrderStatusInnerEnum.COMPLETE.getStatus());
                                        Order2cAtomHistoryExample example = new Order2cAtomHistoryExample().createCriteria().andOrderIdEqualTo(orderId).andInnerStatusIn(statusList).andCreateTimeLessThan(orderStatusTime).example();
                                        order2cAtomHistoryMapper.deleteByExample(example);

                                        order2cAtomInfos.forEach(atomOrder -> {
                                            Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
                                            order2cAtomHistory.setOrderId(orderId);
                                            order2cAtomHistory.setAtomOrderId(atomOrder.getId());
                                            order2cAtomHistory.setOperateType(1);
                                            order2cAtomHistory.setInnerStatus(status);
                                            order2cAtomHistory.setCreateTime(orderStatusTime);
                                            order2cAtomHistory.setUpdateTime(orderStatusTime);
                                            order2cAtomHistoryMapper.insertSelective(order2cAtomHistory);
                                        });
                                    }

                                    if(("B7".equals(orderStatus)) && addB7 ){
                                        order2cAtomInfos.forEach(atomOrder -> {
                                            Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
                                            order2cAtomHistory.setOrderId(orderId);
                                            order2cAtomHistory.setAtomOrderId(atomOrder.getId());
                                            order2cAtomHistory.setOperateType(1);
                                            order2cAtomHistory.setInnerStatus(status);
                                            order2cAtomHistory.setCreateTime(orderStatusTime);
                                            order2cAtomHistory.setUpdateTime(orderStatusTime);
                                            order2cAtomHistoryMapper.insertSelective(order2cAtomHistory);

                                        });
                                    }
                                    orderStatusList.add(orderStatus);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    } catch (Exception e) {
                        log.error("订单:{}状态文件同步出错",orderId,e);
                    }
                });
                log.info("【结束】AfterPayOrderStatusTask通过状态文件更新代客下单状态");
            }
        } catch (Exception e) {
            log.error("AfterPayOrderStatusTask定时任务更新代客下单订单状态发生异常",e);
        }
    }

    /**
     * 由于3.1.7接口和3.1.9接口的同一订单，相同状态的orderStatusTime有些偏差，避免状态错误回退，所以需要将所有的状态都更新.
     * @param orderStatus
     * @return
     */
    public Integer getStatus(String orderStatus){
        Integer status = null;
        if("B0".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_DRAFT.getStatus();
        }
        if("B1".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_ORDER_TAKING.getStatus();
        }
        if("B2".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_ORDER_APPROVE.getStatus();
        }
        if("B3".equals(orderStatus)){
            status = OrderStatusInnerEnum.WAIT_SEND.getStatus();
        }
        if("B6".equals(orderStatus)){
            status = OrderStatusInnerEnum.WAIT_RECEIVE.getStatus();
        }
        if("B7".equals(orderStatus)){
            status = OrderStatusInnerEnum.COMPLETE.getStatus();
        }
        if("B8".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_ORDER_COMPLETE.getStatus();
        }
        if("B4".equals(orderStatus)){
            status = OrderStatusInnerEnum.ORDER_SUCCESS.getStatus();
        }
        if("B5".equals(orderStatus)){
            status = OrderStatusInnerEnum.ORDER_FAIL.getStatus();
        }
        if("B9".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_DRAFT_DELETE.getStatus();
        }
        //经过静姐确定，B11不再更新订单状态，避免无法交付的问题
        /*if("B11".equals(orderStatus)){
            status = OrderStatusInnerEnum.VALET_CARD_MAKING.getStatus();
        }*/
        return status;
    }

}
