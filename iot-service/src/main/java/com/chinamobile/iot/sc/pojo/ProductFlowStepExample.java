package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductFlowStepExample {
    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    protected String orderByClause;

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    protected boolean distinct;

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public ProductFlowStepExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public ProductFlowStepExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public ProductFlowStepExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public static Criteria newAndCreateCriteria() {
        ProductFlowStepExample example = new ProductFlowStepExample();
        return example.createCriteria();
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public ProductFlowStepExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public ProductFlowStepExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("flow_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andStepIndexIsNull() {
            addCriterion("step_index is null");
            return (Criteria) this;
        }

        public Criteria andStepIndexIsNotNull() {
            addCriterion("step_index is not null");
            return (Criteria) this;
        }

        public Criteria andStepIndexEqualTo(Integer value) {
            addCriterion("step_index =", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexNotEqualTo(Integer value) {
            addCriterion("step_index <>", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexGreaterThan(Integer value) {
            addCriterion("step_index >", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("step_index >=", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexLessThan(Integer value) {
            addCriterion("step_index <", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexLessThanOrEqualTo(Integer value) {
            addCriterion("step_index <=", value, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_index <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepIndexIn(List<Integer> values) {
            addCriterion("step_index in", values, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexNotIn(List<Integer> values) {
            addCriterion("step_index not in", values, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexBetween(Integer value1, Integer value2) {
            addCriterion("step_index between", value1, value2, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("step_index not between", value1, value2, "stepIndex");
            return (Criteria) this;
        }

        public Criteria andStepNameIsNull() {
            addCriterion("step_name is null");
            return (Criteria) this;
        }

        public Criteria andStepNameIsNotNull() {
            addCriterion("step_name is not null");
            return (Criteria) this;
        }

        public Criteria andStepNameEqualTo(String value) {
            addCriterion("step_name =", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameNotEqualTo(String value) {
            addCriterion("step_name <>", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThan(String value) {
            addCriterion("step_name >", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThanOrEqualTo(String value) {
            addCriterion("step_name >=", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameLessThan(String value) {
            addCriterion("step_name <", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameLessThanOrEqualTo(String value) {
            addCriterion("step_name <=", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("step_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStepNameLike(String value) {
            addCriterion("step_name like", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotLike(String value) {
            addCriterion("step_name not like", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameIn(List<String> values) {
            addCriterion("step_name in", values, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotIn(List<String> values) {
            addCriterion("step_name not in", values, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameBetween(String value1, String value2) {
            addCriterion("step_name between", value1, value2, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotBetween(String value1, String value2) {
            addCriterion("step_name not between", value1, value2, "stepName");
            return (Criteria) this;
        }

        public Criteria andTipIsNull() {
            addCriterion("tip is null");
            return (Criteria) this;
        }

        public Criteria andTipIsNotNull() {
            addCriterion("tip is not null");
            return (Criteria) this;
        }

        public Criteria andTipEqualTo(String value) {
            addCriterion("tip =", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipNotEqualTo(String value) {
            addCriterion("tip <>", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipGreaterThan(String value) {
            addCriterion("tip >", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipGreaterThanOrEqualTo(String value) {
            addCriterion("tip >=", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipLessThan(String value) {
            addCriterion("tip <", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipLessThanOrEqualTo(String value) {
            addCriterion("tip <=", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("tip <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTipLike(String value) {
            addCriterion("tip like", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipNotLike(String value) {
            addCriterion("tip not like", value, "tip");
            return (Criteria) this;
        }

        public Criteria andTipIn(List<String> values) {
            addCriterion("tip in", values, "tip");
            return (Criteria) this;
        }

        public Criteria andTipNotIn(List<String> values) {
            addCriterion("tip not in", values, "tip");
            return (Criteria) this;
        }

        public Criteria andTipBetween(String value1, String value2) {
            addCriterion("tip between", value1, value2, "tip");
            return (Criteria) this;
        }

        public Criteria andTipNotBetween(String value1, String value2) {
            addCriterion("tip not between", value1, value2, "tip");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdIsNull() {
            addCriterion("assignee_role_id is null");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdIsNotNull() {
            addCriterion("assignee_role_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdEqualTo(String value) {
            addCriterion("assignee_role_id =", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdNotEqualTo(String value) {
            addCriterion("assignee_role_id <>", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdGreaterThan(String value) {
            addCriterion("assignee_role_id >", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdGreaterThanOrEqualTo(String value) {
            addCriterion("assignee_role_id >=", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLessThan(String value) {
            addCriterion("assignee_role_id <", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLessThanOrEqualTo(String value) {
            addCriterion("assignee_role_id <=", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("assignee_role_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLike(String value) {
            addCriterion("assignee_role_id like", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdNotLike(String value) {
            addCriterion("assignee_role_id not like", value, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdIn(List<String> values) {
            addCriterion("assignee_role_id in", values, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdNotIn(List<String> values) {
            addCriterion("assignee_role_id not in", values, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdBetween(String value1, String value2) {
            addCriterion("assignee_role_id between", value1, value2, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdNotBetween(String value1, String value2) {
            addCriterion("assignee_role_id not between", value1, value2, "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdIsNull() {
            addCriterion("reject_next_step_id is null");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdIsNotNull() {
            addCriterion("reject_next_step_id is not null");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdEqualTo(String value) {
            addCriterion("reject_next_step_id =", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdNotEqualTo(String value) {
            addCriterion("reject_next_step_id <>", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdGreaterThan(String value) {
            addCriterion("reject_next_step_id >", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdGreaterThanOrEqualTo(String value) {
            addCriterion("reject_next_step_id >=", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLessThan(String value) {
            addCriterion("reject_next_step_id <", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLessThanOrEqualTo(String value) {
            addCriterion("reject_next_step_id <=", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("reject_next_step_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLike(String value) {
            addCriterion("reject_next_step_id like", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdNotLike(String value) {
            addCriterion("reject_next_step_id not like", value, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdIn(List<String> values) {
            addCriterion("reject_next_step_id in", values, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdNotIn(List<String> values) {
            addCriterion("reject_next_step_id not in", values, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdBetween(String value1, String value2) {
            addCriterion("reject_next_step_id between", value1, value2, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdNotBetween(String value1, String value2) {
            addCriterion("reject_next_step_id not between", value1, value2, "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectIsNull() {
            addCriterion("allow_redirect is null");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectIsNotNull() {
            addCriterion("allow_redirect is not null");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectEqualTo(Boolean value) {
            addCriterion("allow_redirect =", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectNotEqualTo(Boolean value) {
            addCriterion("allow_redirect <>", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectGreaterThan(Boolean value) {
            addCriterion("allow_redirect >", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_redirect >=", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectLessThan(Boolean value) {
            addCriterion("allow_redirect <", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_redirect <=", value, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_redirect <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowRedirectIn(List<Boolean> values) {
            addCriterion("allow_redirect in", values, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectNotIn(List<Boolean> values) {
            addCriterion("allow_redirect not in", values, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_redirect between", value1, value2, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andAllowRedirectNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_redirect not between", value1, value2, "allowRedirect");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdIsNull() {
            addCriterion("redirect_role_id is null");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdIsNotNull() {
            addCriterion("redirect_role_id is not null");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdEqualTo(String value) {
            addCriterion("redirect_role_id =", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdNotEqualTo(String value) {
            addCriterion("redirect_role_id <>", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdGreaterThan(String value) {
            addCriterion("redirect_role_id >", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdGreaterThanOrEqualTo(String value) {
            addCriterion("redirect_role_id >=", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLessThan(String value) {
            addCriterion("redirect_role_id <", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLessThanOrEqualTo(String value) {
            addCriterion("redirect_role_id <=", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("redirect_role_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLike(String value) {
            addCriterion("redirect_role_id like", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdNotLike(String value) {
            addCriterion("redirect_role_id not like", value, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdIn(List<String> values) {
            addCriterion("redirect_role_id in", values, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdNotIn(List<String> values) {
            addCriterion("redirect_role_id not in", values, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdBetween(String value1, String value2) {
            addCriterion("redirect_role_id between", value1, value2, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdNotBetween(String value1, String value2) {
            addCriterion("redirect_role_id not between", value1, value2, "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andAllowKnownIsNull() {
            addCriterion("allow_known is null");
            return (Criteria) this;
        }

        public Criteria andAllowKnownIsNotNull() {
            addCriterion("allow_known is not null");
            return (Criteria) this;
        }

        public Criteria andAllowKnownEqualTo(Boolean value) {
            addCriterion("allow_known =", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownNotEqualTo(Boolean value) {
            addCriterion("allow_known <>", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownGreaterThan(Boolean value) {
            addCriterion("allow_known >", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_known >=", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownLessThan(Boolean value) {
            addCriterion("allow_known <", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_known <=", value, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_known <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowKnownIn(List<Boolean> values) {
            addCriterion("allow_known in", values, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownNotIn(List<Boolean> values) {
            addCriterion("allow_known not in", values, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_known between", value1, value2, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andAllowKnownNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_known not between", value1, value2, "allowKnown");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdIsNull() {
            addCriterion("known_role_id is null");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdIsNotNull() {
            addCriterion("known_role_id is not null");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdEqualTo(String value) {
            addCriterion("known_role_id =", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdNotEqualTo(String value) {
            addCriterion("known_role_id <>", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdGreaterThan(String value) {
            addCriterion("known_role_id >", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdGreaterThanOrEqualTo(String value) {
            addCriterion("known_role_id >=", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLessThan(String value) {
            addCriterion("known_role_id <", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLessThanOrEqualTo(String value) {
            addCriterion("known_role_id <=", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("known_role_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLike(String value) {
            addCriterion("known_role_id like", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdNotLike(String value) {
            addCriterion("known_role_id not like", value, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdIn(List<String> values) {
            addCriterion("known_role_id in", values, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdNotIn(List<String> values) {
            addCriterion("known_role_id not in", values, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdBetween(String value1, String value2) {
            addCriterion("known_role_id between", value1, value2, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdNotBetween(String value1, String value2) {
            addCriterion("known_role_id not between", value1, value2, "knownRoleId");
            return (Criteria) this;
        }

        public Criteria andAllowLimitIsNull() {
            addCriterion("allow_limit is null");
            return (Criteria) this;
        }

        public Criteria andAllowLimitIsNotNull() {
            addCriterion("allow_limit is not null");
            return (Criteria) this;
        }

        public Criteria andAllowLimitEqualTo(Boolean value) {
            addCriterion("allow_limit =", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitNotEqualTo(Boolean value) {
            addCriterion("allow_limit <>", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitGreaterThan(Boolean value) {
            addCriterion("allow_limit >", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_limit >=", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitLessThan(Boolean value) {
            addCriterion("allow_limit <", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_limit <=", value, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("allow_limit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowLimitIn(List<Boolean> values) {
            addCriterion("allow_limit in", values, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitNotIn(List<Boolean> values) {
            addCriterion("allow_limit not in", values, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_limit between", value1, value2, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andAllowLimitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_limit not between", value1, value2, "allowLimit");
            return (Criteria) this;
        }

        public Criteria andLimitIdIsNull() {
            addCriterion("limit_id is null");
            return (Criteria) this;
        }

        public Criteria andLimitIdIsNotNull() {
            addCriterion("limit_id is not null");
            return (Criteria) this;
        }

        public Criteria andLimitIdEqualTo(Integer value) {
            addCriterion("limit_id =", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdNotEqualTo(Integer value) {
            addCriterion("limit_id <>", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdGreaterThan(Integer value) {
            addCriterion("limit_id >", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("limit_id >=", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdLessThan(Integer value) {
            addCriterion("limit_id <", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdLessThanOrEqualTo(Integer value) {
            addCriterion("limit_id <=", value, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("limit_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLimitIdIn(List<Integer> values) {
            addCriterion("limit_id in", values, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdNotIn(List<Integer> values) {
            addCriterion("limit_id not in", values, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdBetween(Integer value1, Integer value2) {
            addCriterion("limit_id between", value1, value2, "limitId");
            return (Criteria) this;
        }

        public Criteria andLimitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("limit_id not between", value1, value2, "limitId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProductFlowStep.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andFlowIdLikeInsensitive(String value) {
            addCriterion("upper(flow_id) like", value.toUpperCase(), "flowId");
            return (Criteria) this;
        }

        public Criteria andStepNameLikeInsensitive(String value) {
            addCriterion("upper(step_name) like", value.toUpperCase(), "stepName");
            return (Criteria) this;
        }

        public Criteria andTipLikeInsensitive(String value) {
            addCriterion("upper(tip) like", value.toUpperCase(), "tip");
            return (Criteria) this;
        }

        public Criteria andAssigneeRoleIdLikeInsensitive(String value) {
            addCriterion("upper(assignee_role_id) like", value.toUpperCase(), "assigneeRoleId");
            return (Criteria) this;
        }

        public Criteria andRejectNextStepIdLikeInsensitive(String value) {
            addCriterion("upper(reject_next_step_id) like", value.toUpperCase(), "rejectNextStepId");
            return (Criteria) this;
        }

        public Criteria andRedirectRoleIdLikeInsensitive(String value) {
            addCriterion("upper(redirect_role_id) like", value.toUpperCase(), "redirectRoleId");
            return (Criteria) this;
        }

        public Criteria andKnownRoleIdLikeInsensitive(String value) {
            addCriterion("upper(known_role_id) like", value.toUpperCase(), "knownRoleId");
            return (Criteria) this;
        }
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated do_not_delete_during_merge Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated do_not_delete_during_merge Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        private ProductFlowStepExample example;

        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        protected Criteria(ProductFlowStepExample example) {
            super();
            this.example = example;
        }

        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        public ProductFlowStepExample example() {
            return this.example;
        }

        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
<<<<<<< HEAD
             * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
             * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
     * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
<<<<<<< HEAD
         * @mbg.generated Fri Mar 22 16:35:05 CST 2024
=======
         * @mbg.generated Thu Mar 21 15:45:46 CST 2024
>>>>>>> feature-product-flow
         */
        void example(com.chinamobile.iot.sc.pojo.ProductFlowStepExample example);
    }
}