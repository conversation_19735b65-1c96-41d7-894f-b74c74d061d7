package com.chinamobile.iot.sc.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;

/**
 * 配置卡+X
 */
@Data
public class CardXProductConfigDTO {

    @Excel(name = "销售组/销售商品编码",needMerge = true,width = 20)
    private String spuOfferingCode;

    @Excel(name = "商品规格编码",needMerge = true,width = 20)
    private String skuOfferingCode;

    @Excel(name = "合作伙伴主账号",needMerge = true,width = 20)
    private String partnerMaster;

    @Excel(name = "合作伙伴从账号",needMerge = true,width = 20)
    private String partnerSlave;

    @Excel(name = "OS接单方式",needMerge = true,width = 20)
    private String orderTakeType;

    @ExcelCollection(name = "原子商品编码")
    private List<AtomCodeItem> atomCodeItemList;

    @ExcelCollection(name = "标准服务编码")
    private List<StdServiceCodeItem> stdServiceCodeItemList;

    @ExcelCollection(name = "物料信息")
    private List<MaterialConfigDTO> materials;

    /**失败原因*/
    @Excel(name = "失败原因",needMerge = true,width = 30)
    private String failedMsg;

    @Data
    public static class AtomCodeItem{
        @Excel(name = "原子商品编码",width = 20,mergeVertical = true)
        private String atomOfferingCode;
    }

    @Data
    public static class StdServiceCodeItem{
        private String atomOfferingCode;

        @Excel(name = "标准服务编码",width = 20,mergeVertical = true)
        private String standardServiceCode;
    }


}
