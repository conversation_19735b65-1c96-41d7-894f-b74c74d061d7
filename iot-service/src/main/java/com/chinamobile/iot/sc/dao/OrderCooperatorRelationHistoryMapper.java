package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistory;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderCooperatorRelationHistoryMapper {
    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    long countByExample(OrderCooperatorRelationHistoryExample example);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int deleteByExample(OrderCooperatorRelationHistoryExample example);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int insert(OrderCooperatorRelationHistory record);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int insertSelective(OrderCooperatorRelationHistory record);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    List<OrderCooperatorRelationHistory> selectByExample(OrderCooperatorRelationHistoryExample example);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    OrderCooperatorRelationHistory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int updateByExampleSelective(@Param("record") OrderCooperatorRelationHistory record, @Param("example") OrderCooperatorRelationHistoryExample example);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int updateByExample(@Param("record") OrderCooperatorRelationHistory record, @Param("example") OrderCooperatorRelationHistoryExample example);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int updateByPrimaryKeySelective(OrderCooperatorRelationHistory record);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int updateByPrimaryKey(OrderCooperatorRelationHistory record);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int batchInsert(@Param("list") List<OrderCooperatorRelationHistory> list);

    /**
     *
     * @mbg.generated Fri May 30 11:19:10 CST 2025
     */
    int batchInsertSelective(@Param("list") List<OrderCooperatorRelationHistory> list, @Param("selective") OrderCooperatorRelationHistory.Column ... selective);
}