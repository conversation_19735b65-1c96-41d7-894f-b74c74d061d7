package com.chinamobile.iot.sc.pojo.param;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/29 15:53、
 * 标准类 省框/省内产品上架excel参数
 */
@Data
public class StandardProductSheflProvinceParam {

    @ExcelProperty("商品上架类目")
    private String shelfCatagoryName;

    @ExcelProperty("一级导航目录")
    private String firstDirectoryName;

    @ExcelProperty("二级导航目录")
    private String secondDirectoryName;

    @ExcelProperty("三级导航目录")
    private String thirdDirectoryName;

    @ExcelProperty("商品名称（SPU）")
    private String spuName;

    @ExcelProperty("产品经理")
    private String manager;

    @ExcelProperty("商品简介")
    private String productDesc;

    @ExcelProperty("典型应用领域")
    private String applicationArea;

    @ExcelProperty("是否隐秘上架")
    private String isHiddenShelf;

    @ExcelProperty("服务商")
    private String spuServiceProvider;

    @ExcelProperty("销售标签")
    private String saleTag;

    @ExcelProperty("映射检索词")
    private String searchWord;

    @ExcelProperty("商品规格名称")
    private String skuName;

    @ExcelProperty("商品规格简称")
    private String skuShortName;

    @ExcelProperty("核心部件名称")
    private String keyCompomentName;

    @ExcelProperty("核心部件及服务内容")
    private String keyComponentServiceInfo;

    @ExcelProperty("发布订购范围")
    private String saleProvinceCity;

    @ExcelProperty("配送范围")
    private String deliveryRange;

    @ExcelProperty("游客/合作伙伴可见")
    private String touristPartnerVisible;

    @ExcelProperty("标准服务产品名称")
    private String standardProductName;

    @ExcelProperty("产品属性")
    private String standardProductAttribute;

    @ExcelProperty("服务供应商")
    private String skuServiceProvider;

    @ExcelProperty("产品管理部门")
    private String manageDepartment;

    @ExcelProperty("标准服务产品经理")
    private String standardProductManager;

    @ExcelProperty("接单账号")
    private String receiveOrderAccount;

    @ExcelProperty("交付账号")
    private String deliverAccount;

    @ExcelProperty("售后账号")
    private String aftermarketAccount;

    @ExcelProperty("原子商品名称")
    private String atomName;

    @ExcelProperty("结算单价")
    private String settlePriceYuan;

    @ExcelProperty("结算单价核对")
    private String settlePriceCheckYuan;

    @ExcelProperty("计量单位")
    private String unit;

    @ExcelProperty("服务内容")
    private String serviceContent;

    @ExcelProperty("订购数量最小值")
    private String minPurchaseNumStr;

    @ExcelProperty("库存数")
    private String inventoryStr;

    @ExcelProperty("省公司采购合同信息")
    private String provincePurchaseContract;

    @ExcelProperty("物联网公司采购合同信息（物联网必填，非物联网非必填）")
    private String iotPurchaseContract;

    @ExcelProperty("物联网公司K3系统物料编码（物联网必填，非物联网非必填）")
    private String materialNum;

}
