package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CardRelationMapper {
    long countByExample(CardRelationExample example);

    int deleteByExample(CardRelationExample example);

    int deleteByPrimaryKey(String id);

    int insert(CardRelation record);

    int insertSelective(CardRelation record);

    List<CardRelation> selectByExample(CardRelationExample example);

    CardRelation selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CardRelation record, @Param("example") CardRelationExample example);

    int updateByExample(@Param("record") CardRelation record, @Param("example") CardRelationExample example);

    int updateByPrimaryKeySelective(CardRelation record);

    int updateByPrimaryKey(CardRelation record);

    int batchInsert(@Param("list") List<CardRelation> list);

    int batchInsertSelective(@Param("list") List<CardRelation> list, @Param("selective") CardRelation.Column ... selective);
}