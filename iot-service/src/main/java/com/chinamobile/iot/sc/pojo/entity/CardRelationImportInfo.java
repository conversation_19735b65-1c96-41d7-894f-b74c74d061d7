package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class CardRelationImportInfo implements Serializable {
    private String id;

    private String importNum;

    private String deviceVersion;

    private Integer importCount;

    private String beId;

    private String createdUser;

    private String createdUserName;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public CardRelationImportInfo withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getImportNum() {
        return importNum;
    }

    public CardRelationImportInfo withImportNum(String importNum) {
        this.setImportNum(importNum);
        return this;
    }

    public void setImportNum(String importNum) {
        this.importNum = importNum == null ? null : importNum.trim();
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public CardRelationImportInfo withDeviceVersion(String deviceVersion) {
        this.setDeviceVersion(deviceVersion);
        return this;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion == null ? null : deviceVersion.trim();
    }

    public Integer getImportCount() {
        return importCount;
    }

    public CardRelationImportInfo withImportCount(Integer importCount) {
        this.setImportCount(importCount);
        return this;
    }

    public void setImportCount(Integer importCount) {
        this.importCount = importCount;
    }

    public String getBeId() {
        return beId;
    }

    public CardRelationImportInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public CardRelationImportInfo withCreatedUser(String createdUser) {
        this.setCreatedUser(createdUser);
        return this;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser == null ? null : createdUser.trim();
    }

    public String getCreatedUserName() {
        return createdUserName;
    }

    public CardRelationImportInfo withCreatedUserName(String createdUserName) {
        this.setCreatedUserName(createdUserName);
        return this;
    }

    public void setCreatedUserName(String createdUserName) {
        this.createdUserName = createdUserName == null ? null : createdUserName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CardRelationImportInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", importNum=").append(importNum);
        sb.append(", deviceVersion=").append(deviceVersion);
        sb.append(", importCount=").append(importCount);
        sb.append(", beId=").append(beId);
        sb.append(", createdUser=").append(createdUser);
        sb.append(", createdUserName=").append(createdUserName);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardRelationImportInfo other = (CardRelationImportInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getImportNum() == null ? other.getImportNum() == null : this.getImportNum().equals(other.getImportNum()))
            && (this.getDeviceVersion() == null ? other.getDeviceVersion() == null : this.getDeviceVersion().equals(other.getDeviceVersion()))
            && (this.getImportCount() == null ? other.getImportCount() == null : this.getImportCount().equals(other.getImportCount()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getCreatedUser() == null ? other.getCreatedUser() == null : this.getCreatedUser().equals(other.getCreatedUser()))
            && (this.getCreatedUserName() == null ? other.getCreatedUserName() == null : this.getCreatedUserName().equals(other.getCreatedUserName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getImportNum() == null) ? 0 : getImportNum().hashCode());
        result = prime * result + ((getDeviceVersion() == null) ? 0 : getDeviceVersion().hashCode());
        result = prime * result + ((getImportCount() == null) ? 0 : getImportCount().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getCreatedUser() == null) ? 0 : getCreatedUser().hashCode());
        result = prime * result + ((getCreatedUserName() == null) ? 0 : getCreatedUserName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        importNum("import_num", "importNum", "VARCHAR", false),
        deviceVersion("device_version", "deviceVersion", "VARCHAR", false),
        importCount("import_count", "importCount", "INTEGER", false),
        beId("be_id", "beId", "VARCHAR", false),
        createdUser("created_user", "createdUser", "VARCHAR", false),
        createdUserName("created_user_name", "createdUserName", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}