package com.chinamobile.iot.sc.request.aftermarket;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 09:33
 * @description IoT售后服务包信息
 */
@Data
public class AftermarketOfferingInfoDTO {

    /**
     * IoT售后服务包状态
     * A：已生效
     * M：已失效
     */
    private String operType;

    /**
     * IoT售后服务包名称
     */
    private String afterMarketInternalName;

    /**
     * IoT售后服务包对外名称
     */
    private String afterMarketExternalName;

    /**
     * IoT售后服务包编码
     */
    private String afterMarketCode;

    /**
     * 销售单价
     */
    private String sellPrice;

    /**
     * 结算单价
     */
    private String settlePrice;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 是否必选
     * 枚举值
     * 0:非必选
     * 1:必选
     */
    private String mandatory;

    /**
     * 售后服务类型
     * 枚举值：
     1：属地化服务
     2：铁通增值服务
     3：铁通增值服务（卡+X专用）
     */
    private String aftermarketType;

    /**
     * 关联商品编码
     */
    private List<OfferingInfoDTO> offeringInfo;

    /**
     * 售后商品版本号
     */
    private String afterMarketVersion;
}
