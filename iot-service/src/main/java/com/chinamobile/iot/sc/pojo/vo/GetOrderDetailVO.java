package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.request.LogisticsInfoRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/8
 * @description 接单详情展示类
 */
@Data
public class GetOrderDetailVO {

    /**
     * 原子订单ID
     */
    private String id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 接单状态 1--接单 2--拒单
     */
    private Integer allowOrderStatus;

    /**
     * 拒绝接单的原因
     */
    private String allowOrderFailureReason;

    /**
     * imei和临时iccid连接集合
     */
    private List<String> imeiTempIccidList;

    /**
     * 物流信息集合
     */
    private List<LogisticsInfoRequest.LogisticsMsg> logisticsMsgs;
}
