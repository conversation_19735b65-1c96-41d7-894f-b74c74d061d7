package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ContractServicePackDTO implements Serializable {
    /**
     * 主键
     */
    private String serviceId;

    /**
     * 服务包名称
     */
    private String serviceName;


    /**
     * 服务包额度已经使用
     */
    private Long serviceQuotaUsed;

    /**
     * 服务包额度余额
     */
    private Long serviceQuotaRemain;

    /**
     * 服务包预占余额
     */
    private Long serviceQuotaReverse;

    /**
     * 合同编码
     */
    private String contractNum;

    /**
     * 所包含的物料列表
     */
    private List<ContractMaterialDTO> materials;
}