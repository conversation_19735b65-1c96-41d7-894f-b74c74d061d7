
package com.chinamobile.iot.sc.service.soa.scmOrderQuery;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.cmcc.soa.osb_sscm_zx_hq_pageinquiryorderinfosrv package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _InputParameters_QNAME = new QName("http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv", "InputParameters");
    private final static QName _OutputParameters_QNAME = new QName("http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv", "OutputParameters");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cmcc.soa.osb_sscm_zx_hq_pageinquiryorderinfosrv
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link MSGHEADER }
     * 
     */
    public MSGHEADER createMSGHEADER() {
        return new MSGHEADER();
    }

    /**
     * Create an instance of {@link InputParameters }
     * 
     */
    public InputParameters createInputParameters() {
        return new InputParameters();
    }

    /**
     * Create an instance of {@link OutputParameters }
     * 
     */
    public OutputParameters createOutputParameters() {
        return new OutputParameters();
    }

    /**
     * Create an instance of {@link INSIDEAPPLYATTACHITEM }
     * 
     */
    public INSIDEAPPLYATTACHITEM createINSIDEAPPLYATTACHITEM() {
        return new INSIDEAPPLYATTACHITEM();
    }

    /**
     * Create an instance of {@link POLINEITEM }
     * 
     */
    public POLINEITEM createPOLINEITEM() {
        return new POLINEITEM();
    }

    /**
     * Create an instance of {@link OUTPUTCOLLECTIONITEM }
     * 
     */
    public OUTPUTCOLLECTIONITEM createOUTPUTCOLLECTIONITEM() {
        return new OUTPUTCOLLECTIONITEM();
    }

    /**
     * Create an instance of {@link OUTPUTCOLLECTION }
     * 
     */
    public OUTPUTCOLLECTION createOUTPUTCOLLECTION() {
        return new OUTPUTCOLLECTION();
    }

    /**
     * Create an instance of {@link POLINE }
     * 
     */
    public POLINE createPOLINE() {
        return new POLINE();
    }

    /**
     * Create an instance of {@link INSIDEAPPLYATTACH }
     * 
     */
    public INSIDEAPPLYATTACH createINSIDEAPPLYATTACH() {
        return new INSIDEAPPLYATTACH();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InputParameters }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv", name = "InputParameters")
    public JAXBElement<InputParameters> createInputParameters(InputParameters value) {
        return new JAXBElement<InputParameters>(_InputParameters_QNAME, InputParameters.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link OutputParameters }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv", name = "OutputParameters")
    public JAXBElement<OutputParameters> createOutputParameters(OutputParameters value) {
        return new JAXBElement<OutputParameters>(_OutputParameters_QNAME, OutputParameters.class, null, value);
    }

}
