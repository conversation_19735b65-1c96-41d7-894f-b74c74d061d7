package com.chinamobile.iot.sc.pojo.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 汇总结算单行信息
 **/
@Data
public class SupplyChainIotLineInfoQuery {



    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料描述
     */
    private String materialName;

    /**
     * 数量
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal quantity;

    /**
     * 不含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 税码
     */
    private String taxCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;


    /**
     * 行不含税金额(元)
     */
    private BigDecimal lineAmt;

    /**
     * 行税额(元)
     */
    private BigDecimal lineTax;

    /**
     * 行含税金额(元)
     */
    private BigDecimal lineAmtTax;


    /**
     * 发运组织代码
     */
    private String organizationCode;

    /**
     * 分配类型
     */
    private String itemType;


    /**
     * 接收人员工编号
     */
    private String rcvUserNum;


    /**
     * 接收人姓名
     */
    private String rcvUser;
    /**
     * 接收人姓名
     */
    private String rcvContactPhone;
    /**
     * 接收人姓名
     */
    private String rcvSiteAddress;

    /**
     * 扩展物料编码
     */
    private String attr2;

    /**
     * 扩展物料描述
     */
    private String attr3;


}
