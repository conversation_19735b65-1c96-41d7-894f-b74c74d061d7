package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.ContractTypeEnum;
import com.chinamobile.iot.sc.constant.K3SyncStatusConstant;
import com.chinamobile.iot.sc.constant.ProductTypeEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.exception.StatusContant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.*;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.query.*;
import com.chinamobile.iot.sc.pojo.vo.K3OrgOrderVO;
import com.chinamobile.iot.sc.pojo.vo.K3SyncStatusVO;
import com.chinamobile.iot.sc.pojo.vo.PageVO;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.chinamobile.iot.sc.util.excel.ExcelFillCellRowMergeStrategy;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chinamobile.iot.sc.common.BaseConstant.ADMIN_ROLE;
import static com.chinamobile.iot.sc.common.BaseConstant.OPERATOR_ROLE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/17
 * @description k3月数据service实现类
 */
@Service
@Slf4j
public class K3ServiceImpl implements K3Service {

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Resource
    private UserK3Mapper userK3Mapper;

    @Resource
    private K3OrdersMapper k3OrdersMapper;

    @Resource
    private K3OrdersMapperExt k3OrdersMapperExt;

    @Resource
    private K3syncStatisProvinceMapper k3syncStatisProvinceMapper;

    @Resource
    private K3syncStatisCityMapper k3syncStatisCityMapper;

    @Resource
    private K3syncStatisContractMapper k3syncStatisContractMapper;

    @Resource
    private K3syncStatisCityService k3syncStatisCityService;

    @Resource
    private K3syncStatisProvinceService k3syncStatisProvinceService;

    @Resource
    private K3syncStatisContractService k3syncStatisContractService;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private ContractMaterialMapper contractMaterialMapper;

    @Resource
    private K3ProductMaterialMapper k3ProductMaterialMapper;

    @Resource
    private ContractCityInfoMapperExt contractCityInfoMapperExt;

    @Resource
    private K3syncStatisProcityMapper k3syncStatisProcityMapper;

    @Resource
    private K3syncStatisProcityService k3syncStatisProcityService;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private ProvinceCityConfig pcConfig;

    @Resource
    private K3syncStatisDepartmentService k3syncStatisDepartmentService;

    @Override
    public BaseAnswer<PageVO<K3OrdersDO>> listK3Orders(K3DataParam k3DataParam) {
        log.info("begin query k3 orders");
        PageVO<K3OrdersDO> pageVO = new PageVO<>();
        K3DataQuery k3DataQuery = new K3DataQuery();
        BeanUtils.copyProperties(k3DataParam, k3DataQuery);
        Integer pageIndex = k3DataParam.getPageIndex();
        Integer pageCount = k3DataParam.getPageCount();
        if (pageIndex == null || pageIndex == 0) {
            pageIndex = 1;
        }
        if (pageCount == null || pageCount == 0) {
            pageCount = 5;
        }

        Page<K3OrdersDO> page = new Page<>(pageIndex, pageCount);
        Page<K3OrdersDO> k3OrdersDOPage = k3OrdersMapperExt.listK3Orders(page, k3DataQuery);

        pageVO.setCurrentPage(k3OrdersDOPage.getCurrent());
        pageVO.setPageCount(k3OrdersDOPage.getSize());
        pageVO.setTotalCount(k3OrdersDOPage.getTotal());
        pageVO.setList(k3OrdersDOPage.getRecords());
        log.info("query result:"+ JSON.toJSONString(k3OrdersDOPage.getRecords()));
        return new BaseAnswer<PageVO<K3OrdersDO>>().setData(pageVO);
    }

    @Override
    public BaseAnswer<PageData<K3syncStatisDO>> listK3syncStatis(K3syncStatisParam k3SyncStatisParam,LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        PageData<K3syncStatisDO> pageData = new PageData<>();
        K3syncStatisQuery k3syncStatisQuery = new K3syncStatisQuery();
        BeanUtils.copyProperties(k3SyncStatisParam, k3syncStatisQuery);

        Integer pageNum = k3SyncStatisParam.getPageNum();
        Integer pageSize = k3SyncStatisParam.getPageSize();
        Page<K3syncStatisDO> page = new Page<>(pageNum, pageSize);
        int statisType = k3SyncStatisParam.getStatisType();
        Page<K3syncStatisDO> k3syncStatisDOPage = null;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3syncStatisDOPage = k3syncStatisCityService.listK3syncStatisCity(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3syncStatisDOPage = k3syncStatisProvinceService.listK3syncStatisProvince(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3syncStatisDOPage = k3syncStatisContractService.listK3syncStatisContract(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3syncStatisDOPage = k3syncStatisProcityService.listK3syncStatisProcity(page, k3syncStatisQuery);
        } else {
            log.info("无效的月数据统计方式");
        }

        if (Optional.ofNullable(k3syncStatisDOPage).isPresent()) {
            BigDecimal oneThousand = new BigDecimal(1000);
            List<K3syncStatisDO> k3syncStatisDOList = k3syncStatisDOPage.getRecords();
            k3syncStatisDOList.stream().forEach(k3syncStatisDO -> {k3syncStatisDO.setTotalPrice(
                k3syncStatisDO.getTotalPrice().divide(oneThousand,2,RoundingMode.HALF_UP));
                k3syncStatisDO.setCreateTimeStr(DateUtils.dateToStr(k3syncStatisDO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
                Date commitSucTime = k3syncStatisDO.getCommitSucTime();
                if (Optional.ofNullable(commitSucTime).isPresent()){
                    k3syncStatisDO.setCommitSucTimeStr(DateUtils.dateToStr(commitSucTime,DateUtils.DEFAULT_DATETIME_FORMAT));
                }
            });
            pageData.setCount(k3syncStatisDOPage.getTotal());
            pageData.setData(k3syncStatisDOList);
        } else {
            pageData.setCount(0);
            pageData.setData(null);
        }
        pageData.setPage(pageNum);

        return new BaseAnswer<PageData<K3syncStatisDO>>().setData(pageData);
    }

    @Override
    public BaseAnswer<PageData<K3syncStatisProDO>> listK3syncStatisPro(K3syncStatisParam k3SyncStatisParam, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        PageData<K3syncStatisProDO> pageData = new PageData<>();
        K3syncStatisQuery k3syncStatisQuery = new K3syncStatisQuery();
        BeanUtils.copyProperties(k3SyncStatisParam, k3syncStatisQuery);

        Integer pageNum = k3SyncStatisParam.getPageNum();
        Integer pageSize = k3SyncStatisParam.getPageSize();
        Page<K3syncStatisProDO> page = new Page<>(pageNum, pageSize);
        int statisType = k3SyncStatisParam.getStatisType();
        Page<K3syncStatisProDO> k3syncStatisProDOPage = null;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3syncStatisProDOPage = k3syncStatisCityService.listProK3syncStatisCity(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3syncStatisProDOPage = k3syncStatisProvinceService.listProK3syncStatisProvince(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3syncStatisProDOPage = k3syncStatisContractService.listProK3syncStatisContract(page, k3syncStatisQuery);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3syncStatisProDOPage = k3syncStatisProcityService.listProK3syncStatisProcity(page, k3syncStatisQuery);
        } else {
            log.info("无效的月数据统计方式");
        }

        if (Optional.ofNullable(k3syncStatisProDOPage).isPresent()) {
            List<K3syncStatisProDO> k3syncStatisDOList = k3syncStatisProDOPage.getRecords();
            BigDecimal oneThousand = new BigDecimal(1000);
            k3syncStatisDOList.stream().forEach(k3syncStatisDO -> {
                k3syncStatisDO.setTotalPrice(k3syncStatisDO.getTotalPrice().divide(oneThousand,2,RoundingMode.HALF_UP));
                k3syncStatisDO.setCreateTimeStr(DateUtils.dateToStr(k3syncStatisDO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
            });
            pageData.setCount(k3syncStatisProDOPage.getTotal());
            pageData.setData(k3syncStatisDOList);
        } else {
            pageData.setCount(0);
            pageData.setData(null);
        }
        pageData.setPage(pageNum);

        return new BaseAnswer<PageData<K3syncStatisProDO>>().setData(pageData);
    }

    @Override
    public List<K3SynDO> listSynData(K3SynParam k3SynParam) {
        int statisType = k3SynParam.getStatisType();
        K3SynQuery k3SynQuery = new K3SynQuery();
        BeanUtils.copyProperties(k3SynParam, k3SynQuery);
        List<K3SynDO> k3SynDOList = null;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3SynDOList = k3syncStatisCityService.listSynCity(k3SynQuery);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3SynDOList = k3syncStatisProvinceService.listSynProvince(k3SynQuery);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3SynDOList = k3syncStatisContractService.listSynContract(k3SynQuery);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3SynDOList = k3syncStatisProcityService.listSynProcity(k3SynQuery);
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_STATIS_TYPE);
        }
        return k3SynDOList;
    }

    @Override
    public List<K3SynOrderDO> listSynOrder(K3SynOrderQuery k3SynOrderQuery) {
        int statisType = k3SynOrderQuery.getStatisType();
        List<K3SynOrderDO> k3SynOrderDOList;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3SynOrderDOList = k3syncStatisCityService.listSynOrderCity(k3SynOrderQuery);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3SynOrderDOList = k3syncStatisProvinceService.listSynOrderProvince(k3SynOrderQuery);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3SynOrderDOList = k3syncStatisContractService.listSynOrderContract(k3SynOrderQuery);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3SynOrderDOList = k3syncStatisProcityService.listSynOrderProcity(k3SynOrderQuery);
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_STATIS_TYPE);
        }
        return k3SynOrderDOList;
    }

    @Override
    public List<K3SynCommitDO> listSynCommitData(K3SynParam k3SynParam) {
        int statisType = k3SynParam.getStatisType();
        K3SynQuery k3SynQuery = new K3SynQuery();
        BeanUtils.copyProperties(k3SynParam, k3SynQuery);
        List<K3SynCommitDO> k3SynCommitDOList = null;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3SynCommitDOList = k3syncStatisCityService.listSynCommitCity(k3SynQuery);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3SynCommitDOList = k3syncStatisProvinceService.listSynCommitProvince(k3SynQuery);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3SynCommitDOList = k3syncStatisContractService.listSynCommitContract(k3SynQuery);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3SynCommitDOList = k3syncStatisProcityService.listSynCommitProcity(k3SynQuery);
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_STATIS_TYPE);
        }
        return k3SynCommitDOList;
    }

    @Override
    public void exportOrder(K3DataParam k3DataParam) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        K3DataQuery k3DataQuery = new K3DataQuery();
        BeanUtils.copyProperties(k3DataParam, k3DataQuery);
        List<K3OrdersDO> k3OrdersDOList = k3OrdersMapperExt.listK3Orders(k3DataQuery);
        BigDecimal exactPoint = new BigDecimal(1000);
        k3OrdersDOList.stream().forEach(k3OrdersDO -> {
            String totalPrice = k3OrdersDO.getTotalPrice();
            if (StringUtils.isNotEmpty(totalPrice)) {
                k3OrdersDO.setTotalPrice(new BigDecimal(totalPrice).divide(exactPoint, 2, RoundingMode.HALF_UP).toString());
            }


            String unitPrice = k3OrdersDO.getUnitPrice();
            if (StringUtils.isNotEmpty(unitPrice)) {
                k3OrdersDO.setUnitPrice(new BigDecimal(unitPrice).divide(exactPoint, 2, RoundingMode.HALF_UP).toString());
            }

            String deductPrice = k3OrdersDO.getDeductPrice();
            if (StringUtils.isNotEmpty(deductPrice)) {
                k3OrdersDO.setDeductPrice(new BigDecimal(deductPrice).divide(exactPoint, 2, RoundingMode.HALF_UP).toString());
            }


        });
        //导出excel
        ServletOutputStream outputStream = null;
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("导出月数据订单列表", "导出月数据订单列表", ExcelType.XSSF), K3OrdersDO.class, k3OrdersDOList);
            outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            workbook.write(outputStream);

        } catch (Exception e) {
            log.error("导出月数据订单列表excel出错", e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("导出月数据订单列表excel关闭流出错", e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<List<String>> genbill(String saleDate) {
        if (StringUtils.isEmpty(saleDate)) {
            throw new BusinessException(StatusContant.K3_BILL_TIME_EMPTY);
        }
        BaseAnswer<List<String>> answer = new BaseAnswer<List<String>>();

        try {

            Date monthBegin = DateTimeUtil.getMonthBegin(saleDate);
            Date monthEnd = DateTimeUtil.getMonthEnd(saleDate);

            //检查是否有未绑定商品的订单，有的话，生成失败
            List<UnBindOrderDTO> unBindOrderDTOList = k3OrdersMapperExt.unBindOrders(monthBegin, monthEnd);
            if (!CollectionUtils.isEmpty(unBindOrderDTOList)) {
                log.info("有未绑定的订单 has unbind bill");
//                throw new BusinessException(StatusContant.K3_GENBILL_EMPTY);
                List<String> unbindList = new ArrayList<>();
                for (UnBindOrderDTO item : unBindOrderDTOList) {
                    String s = "订单号：" + item.getOrderId() + "; spu编码：" + item.getSpuCode() + "; sku编码：" + item.getSkuCode()
                            + "; 原子商品编号：" + item.getAtomCode() + "; 商品类型：" + item.getAtomClass()
                            + "; 订单收入归属省市：" + item.getProvinceName() + " " + (item.getCityName() == null ? "" : item.getCityName());

                    unbindList.add(s);
                }
                answer.setStatus(StatusContant.K3_GENBILL_EMPTY);
                answer.setMessage("有" + unbindList.size() + "条订单无法生成月数据,请检查合同与物料的配置");
                answer.setData(unbindList);
                return answer;
            }


            List<K3Orders> billOrders = k3OrdersMapperExt.genOrderBills(monthBegin, monthEnd);

            if (billOrders.isEmpty()) {
                log.error("genbill empty 生成订单 为空");
                throw new BusinessException(StatusContant.K3_BILL_ORDERUNBIND);
            }

            List<K3Orders> dbBillOrders = new ArrayList<>();
            List<String> conflictList = new ArrayList<>();
            Map<String, String> conflictMap = new HashMap<String, String>();
            for (K3Orders order : billOrders) {
                order.setId(BaseServiceUtils.getId());

                //通过省市代码校验订单合同归属地
                String orderProvinceCode = order.getOrderProvinceCode();
                String orderCityCode = order.getOrderCityCode();
                String buyerProvinceMallCode = order.getBuyerProvinceMallCode();
                String buyerCityMallCode = order.getBuyerCityMallCode();
                String buyerCity = order.getBuyerCity();
                String buyerProvince = order.getBuyerProvince();
                log.info("遍历k3Order orderId = {}, orderProvince = {}, orderCity = {}, buyerProvince = {}, buyerCity = {}, buyerProvinceName = {}, buyerCityName = {}", order.getOrderId(), orderProvinceCode, orderCityCode,
                        buyerProvinceMallCode, buyerCityMallCode, buyerProvince, buyerCity);
                int statisType = order.getContractStatisEnum();
                if (ContractTypeEnum.City.getCode() == statisType) {
                    //按地市
                    log.info("按地市 orderByCity");
                    if (!buyerProvinceMallCode.equals(orderProvinceCode) || !buyerCityMallCode.equals(orderCityCode)) {
                        String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 收入市编码为： " + order.getOrderCityCode()
                                + ", 与关联合同 " + order.getContractNum() + " 省份编码: " + buyerProvinceMallCode + "省份名称：" + buyerProvince + ", 地市编码: " + buyerCityMallCode + ", 地市名称 " + buyerCity + " 不一致";
                        log.info("按地市统计冲突：{}", errorInfo);
                        conflictMap.put(order.getOrderId(), errorInfo);
                        continue;
                    } else {
                        order.setOrderCity(buyerCity);
                        order.setOrderProvince(buyerProvince);
                        //解密收货人电话
                        order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                        //合同类型转换
                        String contractType = order.getContractType();
                        if ("FRAMEWORK".equals(contractType)) {
                            order.setContractType("1");
                        } else if ("SINGLE_CONTRACT".equals(contractType)) {
                            order.setContractType("2");
                        } else {
                            log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                            order.setContractType("1");
                        }
                        //币种转换
                        order.setContractMoneyUnit("PRE001");
                        order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                        //销售单位写死100
                        order.setContractSellUnit("100");
                        dbBillOrders.add(order);
                    }
                } else if (ContractTypeEnum.Province.getCode() == statisType) {
                    //按省份
                    log.info("按省份 orderByProvince");
                    if (!buyerProvinceMallCode.equals(orderProvinceCode)) {
                        String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 与关联合同 " + order.getContractNum() + " 省份编码: "
                                + buyerProvinceMallCode + "; 省名称: " + buyerProvince + " 不一致";
                        log.info("按省份统计冲突：{}", errorInfo);
                        conflictMap.put(order.getOrderId(), errorInfo);
                        continue;
                    } else {
                        if (order.getContractSettleMode() == null) {
                            log.error("按省统计的合同没有选择结算方式，contract statistics by province not have settlemode 合同编号 : " + order.getContractNum());
                            //如果数据库没有，默认这个订单是按省结算的
                            order.setContractSettleMode(1);
                        }
                        if (order.getContractSettleMode() == 0) {
                            //按地市结算，查询出订单的地市，作为合同的地市
                            String orderCity = contractCityInfoMapperExt.getCityNameByCode(orderCityCode);
                            order.setOrderCity(orderCity);
                        } else if (order.getContractSettleMode() == 1) {
                            //按省份结算
                            order.setOrderCity(buyerCity);
                        }
//                        order.setOrderCity(buyerCity);
                        order.setOrderProvince(buyerProvince);
                        //解密收货人电话
                        order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                        //合同类型转换
                        String contractType = order.getContractType();
                        if ("FRAMEWORK".equals(contractType)) {
                            order.setContractType("1");
                        } else if ("SINGLE_CONTRACT".equals(contractType)) {
                            order.setContractType("2");
                        } else {
                            log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                            order.setContractType("1");
                        }
                        //币种转换
                        order.setContractMoneyUnit("PRE001");
                        order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                        //销售单位写死100
                        order.setContractSellUnit("100");
                        dbBillOrders.add(order);
                    }
                } else if (ContractTypeEnum.Contract.getCode() == statisType) {
                    //按合同
                    log.info("按合同 orderByContract");
                    if (!buyerProvinceMallCode.equals(orderProvinceCode)) {
                        String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 与关联合同 " + order.getContractNum() + " 省份编码: "
                                + buyerProvinceMallCode + "; 省名称: " + buyerProvince + " 不一致";
                        log.info("按合同统计冲突：{}", errorInfo);
                        conflictMap.put(order.getOrderId(), errorInfo);
                        continue;
                    } else {
                        order.setOrderCity(buyerCity);
                        order.setOrderProvince(buyerProvince);
                        //解密收货人电话
                        order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                        //合同类型转换
                        String contractType = order.getContractType();
                        if ("FRAMEWORK".equals(contractType)) {
                            order.setContractType("1");
                        } else if ("SINGLE_CONTRACT".equals(contractType)) {
                            order.setContractType("2");
                        } else {
                            log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                            order.setContractType("1");
                        }
                        //币种转换
                        order.setContractMoneyUnit("PRE001");
                        order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                        //销售单位写死100
                        order.setContractSellUnit("100");
                        dbBillOrders.add(order);
                    }
                }

            }
            //TEST LOG
            for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
                log.info("before remove Key = {} ; value = {}", entry.getKey(), entry.getValue());
            }
            //END TEST LOG

            //所有循环完了，遍历成功的订单
            for (K3Orders item : dbBillOrders) {
                log.info("成功订单 orderId = {}, orderProvince = {}, contractNum = {}, buyerProvince = {}", item.getOrderId(), item.getOrderProvince(), item.getContractNum(), item.getBuyerProvince());
                conflictMap.remove(item.getOrderId());
            }
            //TEST LOG
            for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
                log.info("after remove Key = {} ; value = {}", entry.getKey(), entry.getValue());
            }
            //END TEST LOG

            if (!conflictMap.isEmpty()) {
                for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
                    conflictList.add(entry.getValue());
                }
                answer.setStatus(StatusContant.K3_BILL_ORDERCONTRACT_CONFLICT);
                answer.setData(conflictList);
                return answer;
            }


            Date currentDate = new Date();
            Date dmBegin = DateTimeUtil.getMonthBegin(currentDate);
            Date dmEnd = DateTimeUtil.getMonthEnd(currentDate);
            if (!dbBillOrders.isEmpty()) {
                //删除已有订单，然后插入
                int deleteOrderCount = k3OrdersMapper.deleteByExample(new K3OrdersExample().createCriteria().andCreateTimeBetween(dmBegin, dmEnd).example());
                int deleteCityCount = k3syncStatisCityMapper.deleteByExample(new K3syncStatisCityExample().createCriteria().andCreateTimeBetween(dmBegin, dmEnd).example());
                int deleteProvinceCount = k3syncStatisProvinceMapper.deleteByExample(new K3syncStatisProvinceExample().createCriteria().andCreateTimeBetween(dmBegin, dmEnd).example());
                int deleteContractCount = k3syncStatisContractMapper.deleteByExample(new K3syncStatisContractExample().createCriteria().andCreateTimeBetween(dmBegin, dmEnd).example());
                int deleteProvinceCityCount = k3syncStatisProcityMapper.deleteByExample(new K3syncStatisProcityExample().createCriteria().andCreateTimeBetween(dmBegin, dmEnd).example());
                log.info("genbill Deleted k3orders count = {}, cityCount = {}, provinceCount = {}, contractCount = {}, provinceCityCount = {}", deleteOrderCount, deleteCityCount, deleteProvinceCount, deleteContractCount, deleteProvinceCityCount);
                k3OrdersMapper.batchInsert(dbBillOrders);
            } else {
                log.warn("genbill, orderList tobe inserted to DB is Null");
            }

            List<UserK3> userList = userK3Mapper.selectByExample(new UserK3Example());
            if (userList == null || userList.size() == 0) {
                log.info("生成同步订单，获取销售员信息失败，sellerUserList is null");
                throw new BusinessException(StatusContant.K3_BILL_USERINFO_EMPTY);
            }
            UserK3 user = userList.get(0);

            genProviceBill(user, dmBegin, dmEnd);
            genCityBill(user, dmBegin, dmEnd);
            genContractBill(user, dmBegin, dmEnd);
            genProvinceCityBill(user, dmBegin, dmEnd);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }
        return answer;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<List<String>> genOrderbillByOrder(K3ListIdParam orderIds) {
        if (CollectionUtils.isEmpty(orderIds.getOrderIds())) {
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "请先选择订单！");
        }
        BaseAnswer<List<String>> answer = new BaseAnswer<List<String>>();
        //判断选择的订单中是否有未绑定物料的订单
        List<UnBindOrderDTO> unBindOrderDTOList = k3OrdersMapperExt.unBindOrdersById(orderIds.getOrderIds());
        log.info("unBindOrderDTOList = {}", unBindOrderDTOList);
        if (CollectionUtils.isNotEmpty(unBindOrderDTOList)) {
            log.info("选择订单中有原子订单商品未绑定物料 has unbindmaterial orders");
//                throw new BusinessException(StatusContant.K3_GENBILL_EMPTY);
            List<String> unbindList = new ArrayList<>();
            for (UnBindOrderDTO item : unBindOrderDTOList) {
                String s = "订单号：" + item.getOrderId() + "; spu编码：" + item.getSpuCode() + "; sku编码：" + item.getSkuCode()
                        + "; 原子商品编号：" + item.getAtomCode() + "; 商品类型：" + item.getAtomClass()
                        + "; 订单收入归属省市：" + item.getProvinceName() + " " + (item.getCityName() == null ? "" : item.getCityName());

                unbindList.add(s);
            }
            answer.setStatus(StatusContant.K3_GENBILL_EMPTY);
            answer.setMessage("有" + unbindList.size() + "条订单无法生成月数据,请检查订单中原子商品与物料的配置");
            answer.setData(unbindList);
            return answer;
        }

        //判断订单中的物料是否属于同一个合同
        List<MultiAtomContractDTO> multiAtomContractList = k3OrdersMapperExt.checkMaterialContract(orderIds.getOrderIds());
        if (!CollectionUtils.isEmpty(multiAtomContractList)) {
            log.info("选择生成K3数据的订单中，有订单商品绑定的物料属于不同物料 multi atom contract warn！");
            List<String> multiList = new ArrayList<>();
            for (MultiAtomContractDTO item : multiAtomContractList) {
                String s = "订单号： " + item.getOrderId() + "的订单中原子商品配置的物料属于不同合同";
                multiList.add(s);
            }
            answer.setStatus(StatusContant.K3_GENBILL_EMPTY);
            answer.setMessage("有" + multiList.size() + "条订单无法生成月数据,订单物料配置不属于统一合同，请检查");
            answer.setData(multiList);
            return answer;
        }


        List<K3Orders> billOrders = k3OrdersMapperExt.genOrderBillsById(orderIds.getOrderIds());

        if (billOrders.isEmpty()) {
            log.error("genbill empty 生成订单 为空");
            throw new BusinessException(StatusContant.K3_BILL_ORDERUNBIND);
        }
        //根据物料合同地址和订单地址来筛选订单，订单地址，跟原子订单配置的物料合同地址不一致的进行提示
        //目前应该不存在一个商品绑定的物料属于多个省的情况（同省市同种类型的合同只存在一个）
        //业务层面来讲，一个原子订单下的物料一般在一个合同下的。但OS生成K3时，不限制这种条件哈，按照物料实际配置的合同 来生成不同tab数据及订单明细
        //现在问题是原子商品对应多物料的情况下，如果多个物料配的合同地址不相同，只要有不同的，都不生成，全部物料地址跟订单相同，才生成数据
        //以前因为只有一个物料，照理说跟订单地址一样就一样，不一样就不一样，为啥会用连个列表来筛选，sql链接多出来了数据？？？
        //以前 虽然是一个商品只配一个物料，但是因为是生成一个月的数据，可能同个原子商品属于不通省市的订单，这样order生成出来会有多条，也不对，因为后面处理map是针对同一orderId的
        //目前的情况看，只有一个订单对应对个原子商品的时候，由于配的合同不一样，地址才有可能冲突，就按这样想，先不管了

        //因为map用orderId做键值，只能存一条，现在改用列表保存同一个订单下具体的冲突信息

        //一条失败，不止这条对应的大订单，选择的所有订单都无法生成

        //       List<K3DataConflictDTO> conflictList = new ArrayList<>();

        //存储生成成功的订单id
        List<String> sucOrderList = new ArrayList<>();
        //存储生成成功的原子数据
        List<K3Orders> dbBillOrders = new ArrayList<>();
        //用来存储冲突信息
        List<String> conflictList = new ArrayList<>();
        //存储冲突的订单id，同一id多次重复，保留一条记录
        Map<String, String> conflictMap = new HashMap<String, String>();
        for (K3Orders order : billOrders) {
            order.setId(BaseServiceUtils.getId());
            //通过省市代码校验订单合同归属地
            String orderProvinceCode = order.getOrderProvinceCode();
            String orderCityCode = order.getOrderCityCode();
            String buyerProvinceMallCode = order.getBuyerProvinceMallCode();
            String buyerCityMallCode = order.getBuyerCityMallCode();
            String buyerCity = order.getBuyerCity();
            String buyerProvince = order.getBuyerProvince();
            log.info("遍历k3Order orderId = {}, orderProvince = {}, orderCity = {}, buyerProvince = {}, buyerCity = {}, buyerProvinceName = {}, buyerCityName = {}", order.getOrderId(), orderProvinceCode, orderCityCode,
                    buyerProvinceMallCode, buyerCityMallCode, buyerProvince, buyerCity);
            int statisType = order.getContractStatisEnum() == null ? -1 : order.getContractStatisEnum();
            if (ContractTypeEnum.City.getCode() == statisType) {
                //按地市
                log.info("按地市 orderByCity");
                if (!buyerProvinceMallCode.equals(orderProvinceCode) || !buyerCityMallCode.equals(orderCityCode)) {
                    String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 收入市编码为： " + order.getOrderCityCode()
                            + ", 与关联合同 " + order.getContractNum() + " 省份编码: " + buyerProvinceMallCode + "省份名称：" + buyerProvince + ", 地市编码: " + buyerCityMallCode + ", 地市名称 " + buyerCity + " 不一致";
                    log.info("按地市统计冲突：{}", errorInfo);
                    conflictMap.put(order.getOrderId(), errorInfo);
                    conflictList.add(errorInfo);
//                    K3DataConflictDTO conflictDTO = new K3DataConflictDTO();
//                    conflictDTO.setOrderId(order.getOrderId());
//                    conflictDTO.setMessage(errorInfo);
                    continue;
                } else {
                    order.setOrderCity(buyerCity);
                    order.setOrderProvince(buyerProvince);
                    //解密收货人电话
                    order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                    //合同类型转换
                    String contractType = order.getContractType();
                    if ("FRAMEWORK".equals(contractType)) {
                        order.setContractType("1");
                    } else if ("SINGLE_CONTRACT".equals(contractType)) {
                        order.setContractType("2");
                    } else {
                        log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                        order.setContractType("1");
                    }
                    //币种转换
                    order.setContractMoneyUnit("PRE001");
                    order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                    //销售单位写死100
                    order.setContractSellUnit("100");
                    dbBillOrders.add(order);
                }
            } else if (ContractTypeEnum.Province.getCode() == statisType) {
                //按省份
                log.info("按省份 orderByProvince");
                if (!buyerProvinceMallCode.equals(orderProvinceCode)) {
                    String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 与关联合同 " + order.getContractNum() + " 省份编码: "
                            + buyerProvinceMallCode + "; 省名称: " + buyerProvince + " 不一致";
                    log.info("按省份统计冲突：{}", errorInfo);
                    conflictList.add(errorInfo);
                    conflictMap.put(order.getOrderId(), errorInfo);
                    continue;
                } else {
                    if (order.getContractSettleMode() == null) {
                        log.error("按省统计的合同没有选择结算方式，contract statistics by province not have settlemode 合同编号 : " + order.getContractNum());
                        //如果数据库没有，默认这个订单是按省结算的
                        order.setContractSettleMode(1);
                    }
                    if (order.getContractSettleMode() == 0) {
                        //按地市结算，查询出订单的地市，作为合同的地市
                        String orderCity = contractCityInfoMapperExt.getCityNameByCode(orderCityCode);
                        order.setOrderCity(orderCity);
                    } else if (order.getContractSettleMode() == 1) {
                        //按省份结算
                        order.setOrderCity(buyerCity);
                    }
//                        order.setOrderCity(buyerCity);
                    order.setOrderProvince(buyerProvince);
                    //解密收货人电话
                    order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                    //合同类型转换
                    String contractType = order.getContractType();
                    if ("FRAMEWORK".equals(contractType)) {
                        order.setContractType("1");
                    } else if ("SINGLE_CONTRACT".equals(contractType)) {
                        order.setContractType("2");
                    } else {
                        log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                        order.setContractType("1");
                    }
                    //币种转换
                    order.setContractMoneyUnit("PRE001");
                    order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                    //销售单位写死100
                    order.setContractSellUnit("100");
                    dbBillOrders.add(order);
                }
            } else if (ContractTypeEnum.Contract.getCode() == statisType) {
                //按合同
                log.info("按合同 orderByContract");
                if (!buyerProvinceMallCode.equals(orderProvinceCode)) {
                    String errorInfo = "订单: " + order.getOrderId() + " 收入省编码为：" + order.getOrderProvinceCode() + ", 与关联合同 " + order.getContractNum() + " 省份编码: "
                            + buyerProvinceMallCode + "; 省名称: " + buyerProvince + " 不一致";
                    log.info("按合同统计冲突：{}", errorInfo);
                    conflictList.add(errorInfo);
                    conflictMap.put(order.getOrderId(), errorInfo);
                    continue;
                } else {
                    order.setOrderCity(buyerCity);
                    order.setOrderProvince(buyerProvince);
                    //解密收货人电话
                    order.setReceiverPhone(IOTEncodeUtils.decryptSM4(order.getReceiverPhone(), iotSm4Key, iotSm4Iv));
                    //合同类型转换
                    String contractType = order.getContractType();
                    if ("FRAMEWORK".equals(contractType)) {
                        order.setContractType("1");
                    } else if ("SINGLE_CONTRACT".equals(contractType)) {
                        order.setContractType("2");
                    } else {
                        log.info("生成同步订单，未知合同类型：genbill unknown contractType");
                        order.setContractType("1");
                    }
                    //币种转换
                    order.setContractMoneyUnit("PRE001");
                    order.setDeductPrice(IOTEncodeUtils.decryptSM4(order.getDeductPrice(), iotSm4Key, iotSm4Iv));
                    //销售单位写死100
                    order.setContractSellUnit("100");
                    dbBillOrders.add(order);
                }
            }

        }
        //TEST LOG
        for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
            log.info("before remove Key = {} ; value = {}", entry.getKey(), entry.getValue());
        }
        //END TEST LOG

        //所有循环完了，遍历成功的订单
        for (K3Orders item : dbBillOrders) {
            String itemOrderId = item.getOrderId();
            log.info("成功订单 orderId = {}, orderProvince = {}, contractNum = {}, buyerProvince = {}", itemOrderId, item.getOrderProvince(), item.getContractNum(), item.getBuyerProvince());
//            conflictMap.remove(item.getOrderId());
//            //只要冲突列表里面一个相同id，成功列表里面就该去掉它
//            if(conflictMap.containsKey(item.getOrderId())){
//
//            }
            //todo 获取插入成功的orderid列表，供生成分表数据使用
            if (sucOrderList.contains(itemOrderId)) {
                log.info("订单id：{}已经添加过了，列表不再重复添加", itemOrderId);
            } else {
                log.info("成功列表添加订单id {}", itemOrderId);
                sucOrderList.add(itemOrderId);
            }


        }
        //TEST LOG
//        for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
//            log.info("after remove Key = {} ; value = {}", entry.getKey(), entry.getValue());
//        }
        //END TEST LOG

        if (!conflictMap.isEmpty()) {
            for (Map.Entry<String, String> entry : conflictMap.entrySet()) {
                conflictList.add(entry.getValue());
            }
            answer.setStatus(StatusContant.K3_BILL_ORDERCONTRACT_CONFLICT);
            answer.setData(conflictList);
            return answer;
        }

        if (!dbBillOrders.isEmpty()) {
            //just for test
            long bfTotalCount = k3OrdersMapper.countByExample(new K3OrdersExample().createCriteria().example());
            log.info("gen k3 bill by order bfTotalCount = {}", bfTotalCount);
            int insertCount = k3OrdersMapper.batchInsert(dbBillOrders);
            log.info("gen k3billByOrder insertCount = {}", insertCount);
            //todo 优化，判断插入完毕才进行其他表的生成
        } else {
            log.warn("genbill, orderList tobe inserted to DB is Null");
        }


        List<UserK3> userList = userK3Mapper.selectByExample(new UserK3Example());
        if (userList == null || userList.size() == 0) {
            log.info("生成同步订单，获取销售员信息失败，sellerUserList is null");
            throw new BusinessException(StatusContant.K3_BILL_USERINFO_EMPTY);
        }
        UserK3 user = userList.get(0);

        //just for test
        long afTotalCount = k3OrdersMapper.countByExample(new K3OrdersExample().createCriteria().example());
        log.info("gen k3 bill by order afTotalCount = {}", afTotalCount);

        genProviceBillByOrder(user, sucOrderList);
        genCityBillByOrder(user, sucOrderList);
        genContractBillByOrder(user, sucOrderList);
        genProvinceCityBillByOrder(user, sucOrderList);
        genDepartmentBillByOrder(sucOrderList);


        return answer;
    }

    @Override
    public void updateSynData(List<SynK3ResultHandleDTO> k3ResultHandleDTOList) {
        k3ResultHandleDTOList.stream().forEach(synK3ResultHandleDTO -> {
            int statisType = synK3ResultHandleDTO.getStatisType() == null ? -1 : synK3ResultHandleDTO.getStatisType();
            UpdateSynK3DTO updateSynK3DTO = new UpdateSynK3DTO();
            BeanUtils.copyProperties(synK3ResultHandleDTO, updateSynK3DTO);
            if (statisType == ContractTypeEnum.City.getCode()) {
                k3syncStatisCityService.updateSynCity(updateSynK3DTO);
            } else if (statisType == ContractTypeEnum.Province.getCode()) {
                k3syncStatisProvinceService.updateSynProvince(updateSynK3DTO);
            } else if (statisType == ContractTypeEnum.Contract.getCode()) {
                k3syncStatisContractService.updateSynContract(updateSynK3DTO);
            } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
                k3syncStatisProcityService.updateSynProcity(updateSynK3DTO);
            }
        });
    }

    @Override
    public void updateSynCommitData(CommitK3ResultHandleDTO commitK3ResultHandleDTO) {
        int statisType = commitK3ResultHandleDTO.getStatisType() == null ? -1 : commitK3ResultHandleDTO.getStatisType();
        UpdateCommitK3DTO updateCommitK3DTO = new UpdateCommitK3DTO();
        BeanUtils.copyProperties(commitK3ResultHandleDTO, updateCommitK3DTO);
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3syncStatisCityService.updateCommitSynCity(updateCommitK3DTO);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3syncStatisProvinceService.updateCommitSynProvince(updateCommitK3DTO);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3syncStatisContractService.updateCommitSynContract(updateCommitK3DTO);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3syncStatisProcityService.updateCommitSynProcity(updateCommitK3DTO);
        }
    }


    private void genProviceBill(UserK3 user, Date monthBegin, Date monthEnd) {
        try {
            List<K3syncStatisProvince> list = k3OrdersMapperExt.genProvinceBill(monthBegin, monthEnd);
            List<K3syncStatisProvince> dbList = new ArrayList<>();

            for (K3syncStatisProvince item : list) {
                item.setId(BaseServiceUtils.getId());
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //todo 初始状态写入apollo
                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");

//                //设置客户编码
//                item.setCustomCode("*********");

                //解密order省市
//                item.setOrderProvinceName(IOTEncodeUtils.decryptSM4()(item.getOrderProvinceName(),iotSm4Key, iotSm4Iv));
                item.setOrderProvinceName(item.getOrderProvinceName());
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisProvinceMapper.batchInsert(dbList);
            } else {
                log.warn("genbill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }

    }


    private void genProviceBillByOrder(UserK3 user, List<String> orderIds) {
        try {
            List<K3syncStatisProvince> list = k3OrdersMapperExt.genProvinceBillByOrder(orderIds);
            List<K3syncStatisProvince> dbList = new ArrayList<>();

            String now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
            String proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0; i < listSize; i++) {
                K3syncStatisProvince item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
                        proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }

                String statisId = "pv" + BaseServiceUtils.getId();
                item.setId(statisId);
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
               // item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //todo 初始状态写入apollo
                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);
                item.setProMaterialStatus(0);

//                //设置客户编码
//                item.setCustomCode("*********");

                //解密order省市
//                item.setOrderProvinceName(IOTEncodeUtils.decryptSM4()(item.getOrderProvinceName(),iotSm4Key, iotSm4Iv));
                item.setOrderProvinceName(item.getOrderProvinceName());


                //将订单基础表的相关数据设置上同步数据标志位，如果一个订单属于同一条数据，设置该条数据id
                String ordersStr = item.getRelatedOrderIds();
                String k3OrdersStr = item.getRelatedK3OrderIds();
                List<String> reIdList = new ArrayList<>();
                List<String> reK3IdList = new ArrayList<>();
                if (StringUtils.isNotEmpty(ordersStr)) {
                    reIdList = Arrays.asList(ordersStr.split(","));
                }
                if (StringUtils.isNotEmpty(k3OrdersStr)) {
                    reK3IdList = Arrays.asList(k3OrdersStr.split(","));
                }
                for (String orderId : reIdList) {
                    //修改对应订单id的数据，把该订单置为已生成K3数据
                    Order2cInfo order2cInfo = new Order2cInfo();
                    order2cInfo.setOrderId(orderId);
//                    order2cInfo.setGenK3Bill(1);
//                    order2cInfo.setSyncK3Id(statisId);
                    order2cInfo.setSyncK3Id(proDataCode);
                    order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                }
                for (String k3OrderId : reK3IdList) {
                    //把生成数据的id设置到原始数据表对应的记录中
                    K3Orders korder = new K3Orders();
                    korder.setId(k3OrderId);
                    korder.setK3Num(statisId);
                    k3OrdersMapper.updateByPrimaryKeySelective(korder);
                }

                //todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisProvinceMapper.batchInsert(dbList);
            } else {
                log.warn("genbill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            log.error("生成k3省数据错误:{}",e);
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }

    }


    private void genCityBill(UserK3 user, Date monthBegin, Date monthEnd) {
        try {
            List<K3syncStatisCity> list = k3OrdersMapperExt.genCityBill(monthBegin, monthEnd);
            List<K3syncStatisCity> dbList = new ArrayList<>();

            String today = DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATE_FORMAT);
            String proDataCode = today.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0;i < listSize;i++){
                K3syncStatisCity item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        proDataCode = today.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }

                item.setId(BaseServiceUtils.getId());
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
               // item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);

//                //设置客户编码
//                item.setCustomCode("*********");

                //解密order省市
//                item.setOrderProvinceName(IOTEncodeUtils.decryptSM4()(item.getOrderProvinceName(),iotSm4Key, iotSm4Iv));
//                item.setOrderCityName(IOTEncodeUtils.decryptSM4()(item.getOrderCityName(),iotSm4Key, iotSm4Iv));
                item.setOrderProvinceName(item.getOrderProvinceName());
                item.setOrderCityName(item.getOrderCityName());

                // todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisCityMapper.batchInsert(dbList);
            } else {
                log.warn("genCity bill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }

    private void genCityBillByOrder(UserK3 user, List<String> orderIds) {
        try {
            List<K3syncStatisCity> list = k3OrdersMapperExt.genCityBillByOrder(orderIds);
            List<K3syncStatisCity> dbList = new ArrayList<>();

            String now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
            String proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0;i < listSize;i++){
                K3syncStatisCity item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
                        proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }


                String statisId = "ct" + BaseServiceUtils.getId();
                item.setId(statisId);
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);
                item.setProMaterialStatus(0);

//                //设置客户编码
//                item.setCustomCode("*********");

                //解密order省市
//                item.setOrderProvinceName(IOTEncodeUtils.decryptSM4()(item.getOrderProvinceName(),iotSm4Key, iotSm4Iv));
//                item.setOrderCityName(IOTEncodeUtils.decryptSM4()(item.getOrderCityName(),iotSm4Key, iotSm4Iv));
                item.setOrderProvinceName(item.getOrderProvinceName());
                item.setOrderCityName(item.getOrderCityName());

                //将订单基础表的相关数据设置上同步数据标志位，如果一个订单属于同一条数据，设置该条数据id
                //更新基础表关联的生成数据id
                String ordersStr = item.getRelatedOrderIds();
                String k3OrdersStr = item.getRelatedK3OrderIds();
                List<String> reIdList = new ArrayList<>();
                List<String> reK3IdList = new ArrayList<>();
                if (StringUtils.isNotEmpty(ordersStr)) {
                    reIdList = Arrays.asList(ordersStr.split(","));
                }
                if (StringUtils.isNotEmpty(k3OrdersStr)) {
                    reK3IdList = Arrays.asList(k3OrdersStr.split(","));
                }
                for (String orderId : reIdList) {
                    //修改对应订单id的数据，把该订单置为已生成K3数据
                    Order2cInfo order2cInfo = new Order2cInfo();
                    order2cInfo.setOrderId(orderId);
//                    order2cInfo.setGenK3Bill(1);
                    //order2cInfo.setSyncK3Id(statisId);
                    order2cInfo.setSyncK3Id(proDataCode);
                    order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                }
                for (String k3OrderId : reK3IdList) {
                    //把生成数据的id设置到原始数据表对应的记录中
                    K3Orders korder = new K3Orders();
                    korder.setId(k3OrderId);
                    korder.setK3Num(statisId);
                    k3OrdersMapper.updateByPrimaryKeySelective(korder);
                }
                // todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisCityMapper.batchInsert(dbList);
            } else {
                log.warn("genCity bill By Order, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }


    private void genProvinceCityBill(UserK3 user, Date monthBegin, Date monthEnd) {
        try {
            List<K3syncStatisProcity> list = k3OrdersMapperExt.genProCityBill(monthBegin, monthEnd);
            List<K3syncStatisProcity> dbList = new ArrayList<>();

            String today = DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATE_FORMAT);
            String proDataCode = today.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0;i < listSize;i++){
                K3syncStatisProcity item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        proDataCode = today.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }

                item.setId(BaseServiceUtils.getId());
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);

                item.setOrderProvinceName(item.getOrderProvinceName());
                item.setOrderCityName(item.getOrderCityName());
                // todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisProcityMapper.batchInsert(dbList);
            } else {
                log.warn("genProvinceCity bill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }

    private void genProvinceCityBillByOrder(UserK3 user, List<String> orderIds) {
        try {
            List<K3syncStatisProcity> list = k3OrdersMapperExt.genProCityBillByOrder(orderIds);
            List<K3syncStatisProcity> dbList = new ArrayList<>();

            String now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
            String proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0;i < listSize;i++){
                K3syncStatisProcity item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
                        proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }

                String statisId = "pc" + BaseServiceUtils.getId();
                item.setId(statisId);
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);
                item.setProMaterialStatus(0);

                item.setOrderProvinceName(item.getOrderProvinceName());
                item.setOrderCityName(item.getOrderCityName());

                //将订单基础表的相关数据设置上同步数据标志位，如果一个订单属于同一条数据，设置该条数据id
                String ordersStr = item.getRelatedOrderIds();
                String k3OrdersStr = item.getRelatedK3OrderIds();
                List<String> reIdList = new ArrayList<>();
                List<String> reK3IdList = new ArrayList<>();
                if (StringUtils.isNotEmpty(ordersStr)) {
                    reIdList = Arrays.asList(ordersStr.split(","));
                }
                if (StringUtils.isNotEmpty(k3OrdersStr)) {
                    reK3IdList = Arrays.asList(k3OrdersStr.split(","));
                }
                for (String orderId : reIdList) {
                    //修改对应订单id的数据，把该订单置为已生成K3数据
                    Order2cInfo order2cInfo = new Order2cInfo();
                    order2cInfo.setOrderId(orderId);
//                    order2cInfo.setGenK3Bill(1);
                    //order2cInfo.setSyncK3Id(statisId);
                    order2cInfo.setSyncK3Id(proDataCode);
                    order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                }
                for (String k3OrderId : reK3IdList) {
                    //把生成数据的id设置到原始数据表对应的记录中
                    K3Orders korder = new K3Orders();
                    korder.setId(k3OrderId);
                    korder.setK3Num(statisId);
                    k3OrdersMapper.updateByPrimaryKeySelective(korder);
                }
                //todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);

            }

            if (!dbList.isEmpty()) {
                k3syncStatisProcityMapper.batchInsert(dbList);
            } else {
                log.warn("genProvinceCity bill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }


    private void genContractBill(UserK3 user, Date monthBegin, Date monthEnd) {
        try {
            List<K3syncStatisContract> list = k3OrdersMapperExt.genContractBill(monthBegin, monthEnd);
            List<K3syncStatisContract> dbList = new ArrayList<>();

            String today = DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATE_FORMAT);
            String proDataCode = today.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for (K3syncStatisContract item : list) {
                item.setId(BaseServiceUtils.getId());
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);

                item.setOrderProvinceName(item.getOrderProvinceName());
                //todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisContractMapper.batchInsert(dbList);
            } else {
                log.warn("genContract bill, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }


    private void genContractBillByOrder(UserK3 user, List<String> orderIds) {
        try {
            List<K3syncStatisContract> list = k3OrdersMapperExt.genContractBillByOrder(orderIds);
            List<K3syncStatisContract> dbList = new ArrayList<>();

            String now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
            String proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
            String contractNumInit = "";
            String materialDeptInit = "";
            int listSize = list.size();
            for(int i = 0;i < listSize;i++){
                K3syncStatisContract item = list.get(i);
                String contractNum = item.getContractNum();
                String materialDept = item.getMaterialDept();

                if (i == 0){
                    item.setProDataCode(proDataCode);
                    contractNumInit = contractNum;
                    materialDeptInit = materialDept;
                }else {
                    // 合同相同还是同一个统计单据，则省销售数据编码相同
                    if (contractNumInit.equals(contractNum)){
                    }else {
                        now = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
                        proDataCode = now.concat(BaseServiceUtils.getId().substring(13));
                        contractNumInit = contractNum;
                    }
                    item.setProDataCode(proDataCode);
                }
                String statisId = "cr" + BaseServiceUtils.getId();
                item.setId(statisId);
                //销售员：新工号
                item.setContractSeller(user.getUsercode());
                //销售员团队
                item.setSellerTeamId(user.getSellerteam());
                //销售员部门
                item.setSellerDeptId(user.getSellerdept());
                //销售组
                //item.setSellerOrgId(user.getSellerorg());
                //销售员电话
                item.setSellerPhone(user.getPhone());
                //成本中心
                item.setCostCenter(user.getCostcenter());
                //项目
//                item.setProject(user.getProject());
                //销售单位：固定写成100
                item.setSellUnit("100");

                //初始化K3状态
                item.setK3CommitStatus("0");
                item.setK3SyncStatus("0");
                item.setProSyncStatus("0");
                item.setProSubmitAccountStatus("0");
                item.setK3Status(0);
                item.setProMaterialStatus(0);

                item.setOrderProvinceName(item.getOrderProvinceName());

                //将订单基础表的相关数据设置上同步数据标志位，如果一个订单属于同一条数据，设置该条数据id
                String ordersStr = item.getRelatedOrderIds();
                String k3OrdersStr = item.getRelatedK3OrderIds();
                List<String> reIdList = new ArrayList<>();
                List<String> reK3IdList = new ArrayList<>();
                if (StringUtils.isNotEmpty(ordersStr)) {
                    reIdList = Arrays.asList(ordersStr.split(","));
                }
                if (StringUtils.isNotEmpty(k3OrdersStr)) {
                    reK3IdList = Arrays.asList(k3OrdersStr.split(","));
                }
                for (String orderId : reIdList) {
                    //修改对应订单id的数据，把该订单置为已生成K3数据
                    Order2cInfo order2cInfo = new Order2cInfo();
                    order2cInfo.setOrderId(orderId);
//                    order2cInfo.setGenK3Bill(1);
                    //order2cInfo.setSyncK3Id(statisId);
                    order2cInfo.setSyncK3Id(proDataCode);
                    order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                }
                for (String k3OrderId : reK3IdList) {
                    //把生成数据的id设置到原始数据表对应的记录中
                    K3Orders korder = new K3Orders();
                    korder.setId(k3OrderId);
                    korder.setK3Num(statisId);
                    k3OrdersMapper.updateByPrimaryKeySelective(korder);
                }
                //todo 以后删除concat的冗余字段，以免数据库爆炸
                item.setRelatedOrderIds("");
                item.setRelatedK3OrderIds("");
                dbList.add(item);
            }

            if (!dbList.isEmpty()) {
                k3syncStatisContractMapper.batchInsert(dbList);
            } else {
                log.warn("genContract bill byOrder, orderList tobe inserted to DB is Null");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
    }

    /**
     * 生成k3部门相关数据
     *
     * @param orderIds
     */
    private void genDepartmentBillByOrder(List<String> orderIds) {
        List<K3syncStatisDepartment> departmentList = k3OrdersMapperExt.genDepartmentBillByOrder(orderIds);
        if (CollectionUtils.isNotEmpty(departmentList)) {
            Date date = new Date();
            departmentList.stream().forEach(k3syncStatisDepartment -> {
                String id = BaseServiceUtils.getId();
                k3syncStatisDepartment.setId(id);
                k3syncStatisDepartment.setCreateTime(date);
                k3syncStatisDepartment.setUpdateTime(date);


                List<String> reK3IdList = new ArrayList<>();
                String k3OrdersStr = k3syncStatisDepartment.getRelatedK3OrderIds();
                if (StringUtils.isNotEmpty(k3OrdersStr)) {
                    reK3IdList = Arrays.asList(k3OrdersStr.split(","));
                }

                //把生成数据的id设置到原始数据表对应的记录中
                K3OrdersExample k3OrdersExample = new K3OrdersExample();
                k3OrdersExample.createCriteria()
                        .andIdIn(reK3IdList);
                K3Orders korder = new K3Orders();
                korder.setK3SyncDepartmentId(id);
                k3OrdersMapper.updateByExampleSelective(korder, k3OrdersExample);
            });
            k3syncStatisDepartmentService.batchInsertK3Department(departmentList);
        }
    }

    private String removeProvinceSuffix(String str) {
        if (str.contains("省")) {
            return str.substring(0, str.indexOf("省"));
        }
        if (str.contains("市")) {
            return str.substring(0, str.indexOf("市"));
        }
        return str;
    }

    private String removeCitySuffix(String str) {
        if (str.contains("市")) {
            return str.substring(0, str.indexOf("市"));
        }
        return str;
    }


    @Override
    public BaseAnswer<K3SyncStatusVO> getSyncStatus(String saleDate) {
        if (StringUtils.isEmpty(saleDate)) {
            throw new BusinessException(StatusContant.K3_BILL_TIME_EMPTY);
        }
        BaseAnswer<K3SyncStatusVO> answer = new BaseAnswer<K3SyncStatusVO>();
        K3SyncStatusVO statusVO = new K3SyncStatusVO();
        try {
//            Date monthBegin = DateTimeUtil.getMonthBegin(saleDate);
//            Date monthEnd = DateTimeUtil.getMonthEnd(saleDate);

            Date currentDate = new Date();
            Date monthBegin = DateTimeUtil.getMonthBegin(currentDate);
            Date monthEnd = DateTimeUtil.getMonthEnd(currentDate);


            long syncProvinceCount = k3syncStatisProvinceMapper.countByExample(new K3syncStatisProvinceExample().createCriteria().andSyncSucTimeBetween(monthBegin, monthEnd).example());
            long syncCityCount = k3syncStatisCityMapper.countByExample(new K3syncStatisCityExample().createCriteria().andSyncSucTimeBetween(monthBegin, monthEnd).example());
            long syncContractCount = k3syncStatisContractMapper.countByExample(new K3syncStatisContractExample().createCriteria().andSyncSucTimeBetween(monthBegin, monthEnd).example());
            log.info("getSyncStatus syncProvinceCount = {}, syncCityCount = {}, syncContractCount = {}", syncProvinceCount, syncCityCount, syncContractCount);
            if (syncProvinceCount > 0 || syncCityCount > 0 || syncContractCount > 0) {
                statusVO.setSynced(true);
            } else {
                statusVO.setSynced(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getLocalizedMessage());
        }
        answer.setData(statusVO);
        return answer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> cancelGenbill(K3StatisDataParam cancelParam) {
        BaseAnswer<Void> answer = new BaseAnswer<>();
        int statisType = cancelParam.getStatisType();
        String proDataCode = cancelParam.getProDataCode();
        List k3NumList = new ArrayList();
        //删除相应tab页同步表中数据
        if (statisType == ContractTypeEnum.City.getCode()) {
            List<K3syncStatisCity> cityList = k3syncStatisCityService.listCityByProDataCode(proDataCode);
            if (CollectionUtils.isEmpty(cityList)){
                throw new BusinessException(BaseErrorConstant.CANCEL_K3_NOT_EXIST);
            }

            // 如果有不同部门的物料信息，则判断当前的状态是否都相同，如果不同，说明数据出现混乱
            if (cityList.size() > 1){
                List<GenK3DO> genK3DOList = k3syncStatisCityService.listK3CityByProDataCode(proDataCode);
                if (genK3DOList.size() > 1){
                    throw new BusinessException(BaseErrorConstant.INVALID_K3_STATUS);
                }
            }
            K3syncStatisCity k3syncStatisCity = cityList.get(0);
            k3NumList = cityList.stream().map(K3syncStatisCity::getId).collect(Collectors.toList());

            // 判断是否已经同步
            checkSyncStatus(k3syncStatisCity.getK3SyncStatus(), k3syncStatisCity.getProSyncStatus());
            //按地市统计
            k3syncStatisCityService.deleteK3CityByProDataCode(proDataCode);
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            List<K3syncStatisProvince> provinceList = k3syncStatisProvinceService.listProvinceByProDataCode(proDataCode);
            if (CollectionUtils.isEmpty(provinceList)){
                throw new BusinessException(BaseErrorConstant.CANCEL_K3_NOT_EXIST);
            }

            // 如果有不同部门的物料信息，则判断当前的状态是否都相同，如果不同，说明数据出现混乱
            if (provinceList.size() > 1){
                List<GenK3DO> genK3DOList = k3syncStatisProvinceService.listK3ProvinceByProDataCode(proDataCode);
                if (genK3DOList.size() > 1){
                    throw new BusinessException(BaseErrorConstant.INVALID_K3_STATUS);
                }
            }
            K3syncStatisProvince k3syncStatisProvince = provinceList.get(0);
            k3NumList = provinceList.stream().map(K3syncStatisProvince::getId).collect(Collectors.toList());
            // 判断是否已经同步
            checkSyncStatus(k3syncStatisProvince.getK3SyncStatus(), k3syncStatisProvince.getProSyncStatus());
            //按省份统计-省份结算
            k3syncStatisProvinceService.deleteK3ProvinceByProDataCode(proDataCode);
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            List<K3syncStatisContract> contractList = k3syncStatisContractService.listContractByProDataCode(proDataCode);
            if (CollectionUtils.isEmpty(contractList)){
                throw new BusinessException(BaseErrorConstant.CANCEL_K3_NOT_EXIST);
            }

            // 如果有不同部门的物料信息，则判断当前的状态是否都相同，如果不同，说明数据出现混乱
            if (contractList.size() > 1){
                List<GenK3DO> genK3DOList = k3syncStatisContractService.listK3ContractByProDataCode(proDataCode);
                if (genK3DOList.size() > 1){
                    throw new BusinessException(BaseErrorConstant.INVALID_K3_STATUS);
                }
            }
            K3syncStatisContract k3syncStatisContract = contractList.get(0);
            k3NumList = contractList.stream().map(K3syncStatisContract::getId).collect(Collectors.toList());

            // 判断是否已经同步
            checkSyncStatus(k3syncStatisContract.getK3SyncStatus(), k3syncStatisContract.getProSyncStatus());
            //按合同统计
            k3syncStatisContractService.deleteK3ContractByProDataCode(proDataCode);
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            List<K3syncStatisProcity> procityList = k3syncStatisProcityService.listProcityByProDataCode(proDataCode);
            if (CollectionUtils.isEmpty(procityList)){
                throw new BusinessException(BaseErrorConstant.CANCEL_K3_NOT_EXIST);
            }

            // 如果有不同部门的物料信息，则判断当前的状态是否都相同，如果不同，说明数据出现混乱
            if (procityList.size() > 1){
                List<GenK3DO> genK3DOList = k3syncStatisProcityService.listK3ProcityByProDataCode(proDataCode);
                if (genK3DOList.size() > 1){
                    throw new BusinessException(BaseErrorConstant.INVALID_K3_STATUS);
                }
            }
            K3syncStatisProcity k3syncStatisProcity = procityList.get(0);
            k3NumList = procityList.stream().map(K3syncStatisProcity::getId).collect(Collectors.toList());
            // 判断是否已经同步
            checkSyncStatus(k3syncStatisProcity.getK3SyncStatus(), k3syncStatisProcity.getProSyncStatus());
            //按省份统计-地市结算
            k3syncStatisProcityService.deleteK3ProcityByProDataCode(proDataCode);
        } else {
            log.error("cancelGenbill unknown datatype 取消K3生成数据数据类型错误");
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "取消K3生成数据数据类型错误");
        }


        //删除相关主订单的数据生成标志
        //产出该条数据关联的所有订单（从k3Orders里面查）
        K3OrdersExample k3OrdersExample = new K3OrdersExample();
        k3OrdersExample.createCriteria()
                .andK3NumIn(k3NumList);
        List<K3Orders> k3OrdersList = k3OrdersMapper.selectByExample(k3OrdersExample);
        if (CollectionUtils.isEmpty(k3OrdersList)) {
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "待取消的数据不包含任何订单!");
        }
        List<String> relatedOrders = k3OrdersList.stream().map(K3Orders::getOrderId).collect(Collectors.toList());
        Order2cInfo order2cInfo = new Order2cInfo();
        order2cInfo.setSyncK3Id("");
        Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        order2cInfoExample.createCriteria().andOrderIdIn(relatedOrders);
        order2cInfoMapper.updateByExampleSelective(order2cInfo, order2cInfoExample);

        // 存储k3sync_statis_department的主键与数量金额的关系
        Map<String, List> departmentMap = new HashMap<>();
        List<String> k3OrderIdList = new ArrayList<>();
        k3OrdersList.forEach(k3Orders -> {
            String k3SyncDepartmentId = k3Orders.getK3SyncDepartmentId();
            k3OrderIdList.add(k3SyncDepartmentId);
            List departmentList = departmentMap.get(k3SyncDepartmentId);
            Long totalPrice = k3Orders.getTotalPrice();
            // 判断是否存在，不存在则初始化数据
            if (CollectionUtils.isEmpty(departmentList)) {
                departmentList = new ArrayList();
                departmentList.add(0, 1);
            } else {
                Integer orderCount = (int) departmentList.get(0) + 1;
                departmentList.add(0, orderCount);

                totalPrice = Long.parseLong(departmentList.get(1)+"")  + totalPrice;
            }
            departmentList.add(1, totalPrice);
            departmentMap.put(k3SyncDepartmentId, departmentList);
        });

        // 获取k3sync_statis_department数据
        K3syncStatisDepartmentExample departmentExample = new K3syncStatisDepartmentExample();
        departmentExample.createCriteria().andIdIn(k3OrderIdList);
        List<K3syncStatisDepartment> k3DepartmentList = k3syncStatisDepartmentService.listK3syncStatisDepartmentByNeed(departmentExample);
        if (CollectionUtils.isNotEmpty(k3DepartmentList)) {
            Date date = new Date();
            k3DepartmentList.stream().forEach(department -> {
                String departmentId = department.getId();
                List departmentList = departmentMap.get(departmentId);
                if (CollectionUtils.isNotEmpty(departmentList)) {
                    K3syncStatisDepartment newDepartment = new K3syncStatisDepartment();
                    K3syncStatisDepartmentExample newDepartmentExample = new K3syncStatisDepartmentExample();
                    newDepartmentExample.createCriteria().andIdEqualTo(departmentId);
                    // 进行数据计算并回封数据
                    int orderCount = department.getOrderCount() - (int) departmentList.get(0);
                    newDepartment.setOrderCount(orderCount);

                    long totalPrice = department.getTotalPrice() - (long) departmentList.get(1);
                    newDepartment.setTotalPrice(totalPrice);
                    newDepartment.setUpdateTime(date);
                    k3syncStatisDepartmentService.updateNeedById(newDepartment, newDepartmentExample);
                }
            });
        }


        //直接硬删除
        int deleteCount = k3OrdersMapper.deleteByExample(new K3OrdersExample().createCriteria().andK3NumIn(k3NumList).example());
        log.info("取消K3数据,cancelGenbill delete 共删除 {} 条", deleteCount);
        return answer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> magicClean() {
        //userk3
        int deletedUserCount = userK3Mapper.deleteByExample(new UserK3Example());
        log.info("deletedUserCount = {}", deletedUserCount);
        //contract
        int deletedContractCount = contractMapper.deleteByExample(new ContractExample());
        log.info("deletedContractCount = {}", deletedContractCount);
        //contract_material
        int deletedCmCount = contractMaterialMapper.deleteByExample(new ContractMaterialExample());
        log.info("deletedCmCount = {}", deletedCmCount);
        //k3ProductMaterial
        int deletedk3PmCount = k3ProductMaterialMapper.deleteByExample(new K3ProductMaterialExample());
        log.info("deletedk3PmCount = {}", deletedk3PmCount);
        //k3Orders
        int deletedk3OrdersCount = k3OrdersMapper.deleteByExample(new K3OrdersExample());
        log.info("deletedk3OrdersCount = {}", deletedk3OrdersCount);
        //k3SyncCity
        int deletedk3CityCount = k3syncStatisCityMapper.deleteByExample(new K3syncStatisCityExample());
        log.info("deletedk3CityCount = {}", deletedk3CityCount);
        //k3SyncProvince
        int deletedk3ProviceCount = k3syncStatisProvinceMapper.deleteByExample(new K3syncStatisProvinceExample());
        log.info("deletedk3ProviceCount = {}", deletedk3ProviceCount);
        //k3SyncContract
        int deletedk3ContractCount = k3syncStatisContractMapper.deleteByExample(new K3syncStatisContractExample());
        log.info("deletedk3ContractCount = {}", deletedk3ContractCount);
        //k3SyncProvinceCity
        int deletedk3ProcityCount = k3syncStatisProcityMapper.deleteByExample(new K3syncStatisProcityExample());
        log.info("deletedk3ProvinceCityCount = {}", deletedk3ProcityCount);
        return new BaseAnswer<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> magicSingleClean(int type, String dataDate) {
        log.info("Magic Single Clean DeleteType = {}, dataDate = {}", type, dataDate);
        Date monthBegin = DateTimeUtil.getMonthBegin(dataDate);
        Date monthEnd = DateTimeUtil.getMonthEnd(dataDate);
        log.info("Magic Single Clean monthBegin = {}, monthEnd = {}", monthBegin, monthEnd);
        switch (type) {
            case 0:
                //userk3
                int deletedUserCount = userK3Mapper.deleteByExample(new UserK3Example());
                log.info("deletedUserCount = {}", deletedUserCount);
                break;
            case 1:
                //contract
                int deletedContractCount = contractMapper.deleteByExample(new ContractExample());
                log.info("deletedContractCount = {}", deletedContractCount);
                break;
            case 2:
                //contract_material
                int deletedCmCount = contractMaterialMapper.deleteByExample(new ContractMaterialExample());
                log.info("deletedCmCount = {}", deletedCmCount);
                break;
            case 3:
                //k3ProductMaterial
                int deletedk3PmCount = k3ProductMaterialMapper.deleteByExample(new K3ProductMaterialExample());
                log.info("deletedk3PmCount = {}", deletedk3PmCount);
                break;
            case 4:
                //k3Orders
                int deletedk3OrdersCount;
                if (StringUtils.isEmpty(dataDate)) {
                    deletedk3OrdersCount = k3OrdersMapper.deleteByExample(new K3OrdersExample());
                } else {
                    deletedk3OrdersCount = k3OrdersMapper.deleteByExample(new K3OrdersExample().createCriteria().andCreateTimeBetween(monthBegin, monthEnd).example());
                }
                log.info("deletedk3OrdersCount = {}", deletedk3OrdersCount);
                break;
            case 5:
                //k3SyncCity
                int deletedk3CityCount;
                if (StringUtils.isEmpty(dataDate)) {
                    deletedk3CityCount = k3syncStatisCityMapper.deleteByExample(new K3syncStatisCityExample());
                } else {
                    deletedk3CityCount = k3syncStatisCityMapper.deleteByExample(new K3syncStatisCityExample().createCriteria().andCreateTimeBetween(monthBegin, monthEnd).example());
                }
                log.info("deletedk3CityCount = {}", deletedk3CityCount);
                break;
            case 6:
                //k3SyncProvince
                int deletedk3ProviceCount;
                if (StringUtils.isEmpty(dataDate)) {
                    deletedk3ProviceCount = k3syncStatisProvinceMapper.deleteByExample(new K3syncStatisProvinceExample());
                } else {
                    deletedk3ProviceCount = k3syncStatisProvinceMapper.deleteByExample(new K3syncStatisProvinceExample().createCriteria().andCreateTimeBetween(monthBegin, monthEnd).example());
                }
                log.info("deletedk3ProviceCount = {}", deletedk3ProviceCount);
                break;
            case 7:
                //k3SyncContract
                int deletedk3ContractCount;
                if (StringUtils.isEmpty(dataDate)) {
                    deletedk3ContractCount = k3syncStatisContractMapper.deleteByExample(new K3syncStatisContractExample());
                } else {
                    deletedk3ContractCount = k3syncStatisContractMapper.deleteByExample(new K3syncStatisContractExample().createCriteria().andCreateTimeBetween(monthBegin, monthEnd).example());
                }
                log.info("deletedk3ContractCount = {}", deletedk3ContractCount);
                break;
            case 8:
                //k3SyncProvinceCity
                int deletedk3ProcityCount;
                if (StringUtils.isEmpty(dataDate)) {
                    deletedk3ProcityCount = k3syncStatisProcityMapper.deleteByExample(new K3syncStatisProcityExample());
                } else {
                    deletedk3ProcityCount = k3syncStatisProcityMapper.deleteByExample(new K3syncStatisProcityExample().createCriteria().andCreateTimeBetween(monthBegin, monthEnd).example());
                }
                log.info("deletedk3ProvinceCityCount = {}", deletedk3ProcityCount);
                break;
            default:
                log.info("Magic Single Clean Unknown Type!!!");
                break;
        }
        return new BaseAnswer<>();
    }


    @Override
    public PageData<K3OrgOrderVO> getK3OrderList(K3QueryListParam param) {
        BaseAnswer<List<K3OrgOrderVO>> baseAnswer = new BaseAnswer<>();
        PageData<K3OrgOrderVO> pageData = new PageData<>();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        Page<K3OrgOrderVO> page = new Page<>(pageNum, pageSize);
        //处理时间，起始时间不能大于结束时间，结束时间默认为现在
        log.info("查新K3订单开始 K3 OrderList = {}", param);
//        String spuOfferingClass
        String createTimeFrom = param.getCreateTimeFrom();
        String createTimeTo = param.getCreateTimeTo();
        String dealTimeFrom = param.getDealTimeFromStr();
        String dealTimeTo = param.getDealTimeToStr();
        if (StringUtils.isNotEmpty(createTimeFrom) && StringUtils.isNotEmpty(createTimeTo)) {
            DateDTO createDate = checkTime(createTimeFrom, createTimeTo);
            //转换成createttime的字符串
            String cstartTime = DateTimeUtil.getDbTimeStr(createDate.getStartTime());
            String cendTime = DateTimeUtil.getDbTimeStr(createDate.getEndTime());
            param.setCreateTimeFrom(cstartTime);
            param.setCreateTimeTo(cendTime);
        }
        if (StringUtils.isNotEmpty(dealTimeFrom) && StringUtils.isNotEmpty(dealTimeTo)) {
            DateDTO updateDate = checkTime(dealTimeFrom, dealTimeTo);
            String beginTimeStr = DateTimeUtil.getDbDefaultStr(updateDate.getStartTime());
            String endTimeStr = DateTimeUtil.getDbDefaultStr(updateDate.getEndTime());
            param.setDealTimeFromStr(beginTimeStr);
            param.setDealTimeToStr(endTimeStr);
            param.setDealTimeFrom(updateDate.getStartTime());
            param.setDealTimeTo(updateDate.getEndTime());
        }
        //将传入的省市汉字转换为省市code放入sql查询
        String provinceName = param.getProvinceName();
        if (StringUtils.isNotEmpty(provinceName)) {
            param.setProvinceCode(pcConfig.getProvinceNameCodeMap().get(provinceName));
        }
        String cityName = param.getCityName();
        if (StringUtils.isNotEmpty(cityName)) {
            param.setCityCode(pcConfig.getCityNameCodeMap().get(cityName));
        }

        //todo 同步了K3的数据to_k3展示为带勾的？
        List<K3OrgOrderVO> k3orders = k3OrdersMapperExt.getK3OrdersList(page, param);
        for (K3OrgOrderVO item : k3orders) {
            item.setOrderProvinceName(pcConfig.getProvinceCodeNameMap().get(item.getOrderProvince()));
            item.setOrderCityName(pcConfig.getCityCodeNameMap().get(item.getOrderCity()));
        }


        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(k3orders);

        return pageData;
    }

    private DateDTO checkTime(String startTimeStr, String endTimeStr) {
        Date startTime = null;
        Date endTime = null;
        try {
            if (StringUtils.isNotEmpty(startTimeStr)) {
                startTime = DateTimeUtil.getFormatDate(startTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            }
            if (StringUtils.isNotEmpty(endTimeStr)) {
                endTime = DateTimeUtil.getFormatDate(endTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            }
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "日期格式错误");
        }
        if (startTime != null && endTime != null && startTime.after(endTime)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "开始时间不能大于结束时间");
        }
        DateDTO dateDTO = new DateDTO();
        dateDTO.setStartTime(startTime);
        dateDTO.setEndTime(endTime);
        return dateDTO;
    }

    @Override
    public void exportRelatedOrders(K3StatisDataParam param) {

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

       /* String type = param.getDataType();
        String k3Num = param.getDataNum();

        String orderStr = k3OrdersMapperExt.getRelatedOrdersByStatisId(k3Num);
        if (StringUtils.isEmpty(orderStr)) {
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "待取消的数据不包含任何订单!");
        }
        List<String> relatedOrderIds = Arrays.asList(orderStr.split(","));
//        for(String orderId : relatedOrderIds){
//            //
//        }
        try {
            List<OrderExcelDTO> excelList = reCopyK3OrderToExport(relatedOrderIds);
            String excelName = "订单详情";
            excelName = URLEncoder.encode(excelName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + excelName + ".xlsx");
            response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), OrderExcelDTO.class).build();
            WriteSheet sheetWirter = EasyExcel.writerSheet(0, "sheet1").build();
            excelWriter.write(excelList, sheetWirter);
            excelWriter.finish();
        } catch (IOException ioe) {
            ioe.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "excel io 错误");
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }*/

    }

    @Override
    public void exportK3ProBill(K3ProBillParam k3ProBillParam, LoginIfo4Redis loginIfo4Redis) throws Exception {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        // 用于存储导出的对账单信息
        Map exportMap = new HashMap();

        int statisType = k3ProBillParam.getStatisType();
        String proDataCode = k3ProBillParam.getProDataCode();

        List k3NumList = new ArrayList();
        K3ProBillContractDO k3ProBillContractDO = null;
        if (statisType == ContractTypeEnum.City.getCode()) {
            k3ProBillContractDO = k3syncStatisCityService.getK3ProBillRelatedCity(k3ProBillParam);
            List<K3syncStatisCity> cityList = k3syncStatisCityService.listCityByProDataCode(proDataCode);
            k3NumList = cityList.stream().map(K3syncStatisCity::getId).collect(Collectors.toList());
        } else if (statisType == ContractTypeEnum.Province.getCode()) {
            k3ProBillContractDO = k3syncStatisProvinceService.getK3ProBillRelatedProvince(k3ProBillParam);
            List<K3syncStatisProvince> provinceList = k3syncStatisProvinceService.listProvinceByProDataCode(proDataCode);
            k3NumList = provinceList.stream().map(K3syncStatisProvince::getId).collect(Collectors.toList());
        } else if (statisType == ContractTypeEnum.Contract.getCode()) {
            k3ProBillContractDO = k3syncStatisContractService.getK3ProBillRelatedContract(k3ProBillParam);
            List<K3syncStatisContract> contractList = k3syncStatisContractService.listContractByProDataCode(proDataCode);
            k3NumList = contractList.stream().map(K3syncStatisContract::getId).collect(Collectors.toList());
        } else if (statisType == ContractTypeEnum.ProCity.getCode()) {
            k3ProBillContractDO = k3syncStatisProcityService.getK3ProBillRelatedProcity(k3ProBillParam);
            List<K3syncStatisProcity> procityList = k3syncStatisProcityService.listProcityByProDataCode(proDataCode);
            k3NumList = procityList.stream().map(K3syncStatisProcity::getId).collect(Collectors.toList());
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_STATIS_TYPE);
        }

        if (!Optional.ofNullable(k3ProBillContractDO).isPresent()){
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }
        String saleMainName = "中国移动通信集团"
                .concat(k3ProBillContractDO.getOrderProvinceName())
                .concat("有限公司");


        List<K3ProBillOrderDTO> k3ProBillOrderDTOList = k3OrdersMapperExt.listK3ProBillOrder(k3NumList);
        String reconciliationCycle = "";
        int saleTotalCount = 0;
        BigDecimal saleTotalPrice = new BigDecimal(0);
        BigDecimal taxSettleTotalPrice = new BigDecimal(0);
        BigDecimal taxTotalPrice = new BigDecimal(0);
        BigDecimal thousand = new BigDecimal(1000);

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();

        if (CollectionUtils.isEmpty(k3ProBillOrderDTOList)) {
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }

        List<String> relatedOrderIds = k3ProBillOrderDTOList.stream().map(K3ProBillOrderDTO::getOrderId).collect(Collectors.toList());
        // 设置最大最小的对账周期
        Date maxReconciliationCycle = null;
        Date minReconciliationCycle = null;
        for (int i = 0; i < k3ProBillOrderDTOList.size(); i++) {
            K3ProBillOrderDTO k3ProBillOrderDTO = k3ProBillOrderDTOList.get(i);
            // 订单交易完成时间
            Date orderFinishTime = k3ProBillOrderDTO.getOrderFinishTime();
            if (i == 0) {
                maxReconciliationCycle = orderFinishTime;
                minReconciliationCycle = orderFinishTime;
            }
            // 进行时间比较
            if (maxReconciliationCycle == null) {
                maxReconciliationCycle = orderFinishTime;
                minReconciliationCycle = orderFinishTime;
            } else {
                if (orderFinishTime.after(maxReconciliationCycle)) {
                    maxReconciliationCycle = orderFinishTime;
                }
                if (orderFinishTime.before(minReconciliationCycle)) {
                    minReconciliationCycle = orderFinishTime;
                }
            }

            BigDecimal atomSalePrice = k3ProBillOrderDTO.getAtomSalePrice() == null ? new BigDecimal(0) : k3ProBillOrderDTO.getAtomSalePrice();
            k3ProBillOrderDTO.setAtomSalePrice(atomSalePrice.divide(thousand, 2, RoundingMode.HALF_UP));
            BigDecimal salePrice = (k3ProBillOrderDTO.getSalePrice() == null ? new BigDecimal(0) : k3ProBillOrderDTO.getSalePrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3ProBillOrderDTO.setSalePrice(salePrice);
            BigDecimal settlePrice = (k3ProBillOrderDTO.getSettlePrice() == null ? new BigDecimal(0) : k3ProBillOrderDTO.getSettlePrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3ProBillOrderDTO.setSettlePrice(settlePrice);
            BigDecimal settleTotalPrice = (k3ProBillOrderDTO.getSettleTotalPrice() == null ? new BigDecimal(0) : k3ProBillOrderDTO.getSettleTotalPrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3ProBillOrderDTO.setSettleTotalPrice(settleTotalPrice);
            /*BigDecimal taxPrice = (k3ProBillOrderDTO.getTaxPrice() == null ? new BigDecimal(0) : k3ProBillOrderDTO.getTaxPrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);*/
            BigDecimal taxInclusiveUnivalence = k3ProBillOrderDTO.getTaxInclusiveUnivalence() == null ? new BigDecimal(0):k3ProBillOrderDTO.getTaxInclusiveUnivalence();
            BigDecimal taxExclusiveUnivalence = k3ProBillOrderDTO.getTaxExclusiveUnivalence() == null ? new BigDecimal(0):k3ProBillOrderDTO.getTaxExclusiveUnivalence();
            MathContext mathContext = new MathContext(2,RoundingMode.HALF_UP);
            BigDecimal taxPrice = settleTotalPrice.multiply(new BigDecimal(6)).divide(new BigDecimal(106),2,RoundingMode.HALF_UP);
            k3ProBillOrderDTO.setTaxPrice(taxPrice);
            Integer saleCount = k3ProBillOrderDTO.getSaleCount();
            saleTotalCount += saleCount == null ? 0 : saleCount;
            saleTotalPrice = saleTotalPrice.add(salePrice);
            taxSettleTotalPrice = taxSettleTotalPrice.add(settleTotalPrice);
            taxTotalPrice = taxTotalPrice.add(taxPrice);

            String mallProvinceCode = k3ProBillOrderDTO.getMallProvinceCode();
            if (StringUtils.isNotEmpty(mallProvinceCode)){
                String mallProvinceName = pcConfig.getProvinceCodeNameMap().get(mallProvinceCode);
                k3ProBillOrderDTO.setMallProvinceName(mallProvinceName);
            }
            String mallCityCode = k3ProBillOrderDTO.getMallCityCode();
            if (StringUtils.isNotEmpty(mallCityCode)){
                String mallCityName = pcConfig.getCityCodeNameMap().get(mallCityCode);
                k3ProBillOrderDTO.setMallCityName(mallCityName);
            }
        }
        reconciliationCycle = DateUtils.dateToStr(minReconciliationCycle, DateUtils.DEFAULT_DATE_FORMAT)
                .concat("---")
                .concat(DateUtils.dateToStr(maxReconciliationCycle, DateUtils.DEFAULT_DATE_FORMAT));

        Date date = new Date();
        // 对账单编号
        String billNum = DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
        exportMap.put("billNum", billNum);
        // 税率
        exportMap.put("taxRate", "6%");
        // 价格单位
        exportMap.put("priceUnit", "元");
        // 销售（采购）主体名称
        exportMap.put("saleMainName", saleMainName);
        // 供应商名称
        exportMap.put("supplierName", "中移物联网有限公司");
        // 对账周期
        exportMap.put("reconciliationCycle", reconciliationCycle);
        // 采购合同名称
        exportMap.put("purchaseContractName", k3ProBillContractDO.getProvinceContractName());
        // 采购合同编号
        exportMap.put("purchaseContractNum", k3ProBillContractDO.getProvinceContractNo());
        // 采购合同金额
        exportMap.put("purchaseContractPrice", k3ProBillContractDO.getAmountIncludingTax());
        // 采购合同失效日期
        Date endDate = k3ProBillContractDO.getEndDate();
        String endDateStr = DateUtils.dateToStr(endDate,DateUtils.DEFAULT_DATE_FORMAT);
        exportMap.put("purchaseContractEndDate", endDateStr);
        // 销售合同编号
        exportMap.put("saleContractNum", k3ProBillContractDO.getContractNum());
        // 订单金额
        BigDecimal orderTotalPrice = k3ProBillContractDO.getTotalPrice().divide(thousand, 2, RoundingMode.HALF_UP);
        exportMap.put("orderTotalPrice", orderTotalPrice);
        // 采购订单有效期
        exportMap.put("purchaseOrderValidCycle", "");
        // 销售总数量
        exportMap.put("saleTotalCount", saleTotalCount);
        // 税总额
        exportMap.put("taxTotalPrice", taxTotalPrice);
        // 销售总额
        exportMap.put("saleTotalPrice", saleTotalPrice);
        // 含税销售总额
        exportMap.put("taxSettleTotalPrice", taxSettleTotalPrice);

        List<Integer> needCellWriteHandlerList = new ArrayList<>();
        needCellWriteHandlerList.add(0);

        List<CellWriteHandler> cellWriteHandlerList = new ArrayList<>();
        //省份列合并
        int[] mergeColumnIndex = {1};
        //设置第几行开始合并
        int mergeRowIndex = 6;
        // Excel单元格行合并处理策略
        ExcelFillCellRowMergeStrategy sixRowOneColumnMergeStrategy = new ExcelFillCellRowMergeStrategy(mergeRowIndex, mergeColumnIndex);
        cellWriteHandlerList.add(sixRowOneColumnMergeStrategy);

        //地市列合并
        int[] mergeSecondColumnIndex = {2};
        // Excel单元格行合并处理策略
        ExcelFillCellRowMergeStrategy sixRowTwoColumnMergeStrategy = new ExcelFillCellRowMergeStrategy(mergeRowIndex, mergeSecondColumnIndex);
        cellWriteHandlerList.add(sixRowTwoColumnMergeStrategy);

        //标准产品列合并
        int[] mergeThridColumnIndex = {3};
        // Excel单元格行合并处理策略
        ExcelFillCellRowMergeStrategy sixRowThreeColumnMergeStrategy = new ExcelFillCellRowMergeStrategy(mergeRowIndex, mergeThridColumnIndex);
        cellWriteHandlerList.add(sixRowThreeColumnMergeStrategy);

        if (CollectionUtils.isEmpty(k3ProBillOrderDTOList)){
            k3ProBillOrderDTOList.add(new K3ProBillOrderDTO());
        }
        EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "对账单", "list",
                k3ProBillOrderDTOList, exportMap);
        easyExcelDTOList.add(easyExcelDTO);

        // 省侧订单明细数据
        List<OrderExcelDTO> excelList = reCopyK3OrderToExport(relatedOrderIds);
        if (CollectionUtils.isEmpty(excelList)){
            excelList.add(new OrderExcelDTO());
        }

        easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(1, "订单明细", "list",
                excelList, null);
        easyExcelDTOList.add(easyExcelDTO);


        String excelName = "对账单";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/province_os_bill_template.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出对账单
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,needCellWriteHandlerList,cellWriteHandlerList,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
    }

    @Override
    public void exportK3DepartmentBill(String dataNum) throws Exception {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        // 用于存储导出的对账单信息
        Map exportMap = new HashMap();
        String reconciliationCycle = "";
        BigDecimal saleTotalPrice = new BigDecimal(0);
        BigDecimal taxSettleTotalPrice = new BigDecimal(0);
        BigDecimal taxTotalPrice = new BigDecimal(0);
        int saleTotalCount = 0;
        BigDecimal thousand = new BigDecimal(1000);
        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
        K3syncStatisDepartment k3syncStatisDepartment = k3syncStatisDepartmentService.getK3DepartmentById(dataNum);
        if (!Optional.ofNullable(k3syncStatisDepartment).isPresent()) {
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }

        List<K3DepartmentBillOrderDTO> k3DepartmentBillOrderDTOList = k3OrdersMapperExt.listK3DepartmentBillOrder(dataNum);
        if (CollectionUtils.isEmpty(k3DepartmentBillOrderDTOList)) {
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }

        List<String> relatedOrderIds = k3DepartmentBillOrderDTOList.stream().map(K3DepartmentBillOrderDTO::getOrderId).collect(Collectors.toList());
        // 设置最大最小的对账周期
        Date maxReconciliationCycle = null;
        Date minReconciliationCycle = null;
        for (int i = 0; i < k3DepartmentBillOrderDTOList.size(); i++) {
            K3DepartmentBillOrderDTO k3DepartmentBillOrderDTO = k3DepartmentBillOrderDTOList.get(i);
            // 订单交易完成时间
            Date orderFinishTime = k3DepartmentBillOrderDTO.getOrderFinishTime();
            if (i == 0) {
                maxReconciliationCycle = orderFinishTime;
                minReconciliationCycle = orderFinishTime;
            }
            // 进行时间比较
            if (maxReconciliationCycle == null) {
                maxReconciliationCycle = orderFinishTime;
                minReconciliationCycle = orderFinishTime;
            } else {
                if (orderFinishTime.after(maxReconciliationCycle)) {
                    maxReconciliationCycle = orderFinishTime;
                }
                if (orderFinishTime.before(minReconciliationCycle)) {
                    minReconciliationCycle = orderFinishTime;
                }
            }

            BigDecimal salePrice = (k3DepartmentBillOrderDTO.getSalePrice() == null ? new BigDecimal(0) : k3DepartmentBillOrderDTO.getSalePrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3DepartmentBillOrderDTO.setSalePrice(salePrice);
            BigDecimal settlePrice = (k3DepartmentBillOrderDTO.getSettlePrice() == null ? new BigDecimal(0) : k3DepartmentBillOrderDTO.getSettlePrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3DepartmentBillOrderDTO.setSettlePrice(settlePrice);
            BigDecimal settleTotalPrice = (k3DepartmentBillOrderDTO.getSettleTotalPrice() == null ? new BigDecimal(0) : k3DepartmentBillOrderDTO.getSettleTotalPrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);
            k3DepartmentBillOrderDTO.setSettleTotalPrice(settleTotalPrice);
            /*BigDecimal taxPrice = (k3DepartmentBillOrderDTO.getTaxPrice() == null ? new BigDecimal(0) : k3DepartmentBillOrderDTO.getTaxPrice())
                    .divide(thousand, 2, RoundingMode.HALF_UP);*/
            BigDecimal taxInclusiveUnivalence = k3DepartmentBillOrderDTO.getTaxInclusiveUnivalence() == null ? new BigDecimal(0):k3DepartmentBillOrderDTO.getTaxInclusiveUnivalence();
            BigDecimal taxExclusiveUnivalence = k3DepartmentBillOrderDTO.getTaxExclusiveUnivalence() == null ? new BigDecimal(0):k3DepartmentBillOrderDTO.getTaxExclusiveUnivalence();
            MathContext mathContext = new MathContext(2,RoundingMode.HALF_UP);
            BigDecimal taxPrice = settleTotalPrice.multiply(new BigDecimal(6)).divide(new BigDecimal(106),2,RoundingMode.HALF_UP);
            k3DepartmentBillOrderDTO.setTaxPrice(taxPrice);
            saleTotalPrice = saleTotalPrice.add(salePrice);
            Integer saleCount = k3DepartmentBillOrderDTO.getSaleCount();
            saleTotalCount += saleCount == null ? 0 : saleCount;
            taxSettleTotalPrice = taxSettleTotalPrice.add(settleTotalPrice);
            taxTotalPrice = taxTotalPrice.add(taxPrice);
        }
        reconciliationCycle = DateUtils.dateToStr(minReconciliationCycle, DateUtils.DEFAULT_DATE_FORMAT)
                .concat("---")
                .concat(DateUtils.dateToStr(maxReconciliationCycle, DateUtils.DEFAULT_DATE_FORMAT));

        Date date = new Date();
        // 对账单编号
        String billNum = DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
        exportMap.put("billNum", billNum);
        // 税率
        exportMap.put("taxRate", "6%");
        // 价格单位
        exportMap.put("priceUnit", "元");
        // 产品部门
        exportMap.put("departmentName", k3syncStatisDepartment.getDepartmentName());
        // 商品类别
        exportMap.put("productTypeName", ProductTypeEnum.getDesc(k3syncStatisDepartment.getProductType()));
        // 对账周期
        exportMap.put("reconciliationCycle", reconciliationCycle);
        // 销售总数量
        exportMap.put("saleTotalCount", saleTotalCount);
        // 税总额
        exportMap.put("taxTotalPrice", taxTotalPrice);
        // 销售总额
        exportMap.put("saleTotalPrice", saleTotalPrice);
        // 含税销售总额
        exportMap.put("taxSettleTotalPrice", taxSettleTotalPrice);

        if (CollectionUtils.isEmpty(k3DepartmentBillOrderDTOList)){
            k3DepartmentBillOrderDTOList.add(new K3DepartmentBillOrderDTO());
        }
        EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "对账单", "list",
                k3DepartmentBillOrderDTOList, exportMap);
        easyExcelDTOList.add(easyExcelDTO);

        // 省侧订单明细数据
        List<OrderExcelDTO> excelList = reCopyK3OrderToExport(relatedOrderIds);
        if (CollectionUtils.isEmpty(excelList)){
            excelList.add(new OrderExcelDTO());
        }
        easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(1, "订单明细", "list",
                excelList, null);
        easyExcelDTOList.add(easyExcelDTO);


        String excelName = "对账单";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/department_os_bill_template.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出对账单
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
    }

    @Override
    public void genK3SynData(GenK3Param genK3Param) {
        int statisType = genK3Param.getStatisType();
        String proDataCode = genK3Param.getProDataCode();
        List<GenK3DO> genK3DOList = new ArrayList<>();
        boolean cityBoolean = statisType == ContractTypeEnum.City.getCode();
        boolean provinceBoolean = statisType == ContractTypeEnum.Province.getCode();
        boolean contractBoolean = statisType == ContractTypeEnum.Contract.getCode();
        boolean proCityBoolean = statisType == ContractTypeEnum.ProCity.getCode();
        if (cityBoolean) {
            genK3DOList = k3syncStatisCityService.listK3CityByProDataCode(proDataCode);
        } else if (provinceBoolean) {
            genK3DOList = k3syncStatisProvinceService.listK3ProvinceByProDataCode(proDataCode);
        } else if (contractBoolean) {
            genK3DOList = k3syncStatisContractService.listK3ContractByProDataCode(proDataCode);
        } else if (proCityBoolean) {
            genK3DOList = k3syncStatisProcityService.listK3ProcityByProDataCode(proDataCode);
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_STATIS_TYPE);
        }

        // 生成的k3销售单据只有一种状态，如果有多种状态说明数据混乱了
        if (genK3DOList.size() > 1){
            throw new BusinessException(BaseErrorConstant.INVALID_K3_STATUS);
        }

        GenK3DO genK3DO = genK3DOList.get(0);
        // 河南订单验证是否已同步省侧
        if ("河南".equals(genK3DO.getOrderProvinceName())
        && (cityBoolean || provinceBoolean)){
            String proSyncStatus = genK3DO.getProSyncStatus();
            if (!"1".equals(proSyncStatus)){
                throw new BusinessException(BaseErrorConstant.NOT_ALLOW_GEN_K3);
            }
        }

        // 验证是否已经生成过k3销售订单
        if (genK3DO.getK3Status() == 1){
            throw new BusinessException(BaseErrorConstant.NOT_ALLOW_REGEN_K3);
        }

        K3StatusQuery k3StatusQuery = new K3StatusQuery();
        k3StatusQuery.setK3Status(1);
        k3StatusQuery.setProDataCode(proDataCode);
        if (cityBoolean) {
            k3syncStatisCityService.updateCityK3StatusByProDataCode(k3StatusQuery);
        } else if (provinceBoolean) {
            k3syncStatisProvinceService.updateProvinceK3StatusByProDataCode(k3StatusQuery);
        } else if (contractBoolean) {
            k3syncStatisContractService.updateContractK3StatusByProDataCode(k3StatusQuery);
        } else if (proCityBoolean) {
            k3syncStatisProcityService.updateProcityK3StatusByProDataCode(k3StatusQuery);
        }
    }


    private String shirnkPrice(String price) {
        if (StringUtils.isEmpty(price)) {
            return "";
        }
        return String.valueOf(Double.parseDouble(price) / 1000);
    }

    /**
     * 检查是否已经同步
     *
     * @param k3SyncStatus
     * @param proSyncStatus
     */
    private void checkSyncStatus(String k3SyncStatus,
                                 String proSyncStatus) {
        if (K3SyncStatusConstant.SYN_SUCCESS.equals(k3SyncStatus)
                || K3SyncStatusConstant.SYN_SUCCESS.equals(proSyncStatus)) {
            throw new BusinessException(BaseErrorConstant.K3_NOT_ALLOW_CANCEL);
        }
    }

    /**
     * 数据导出赋值
     *
     * @param relatedOrderIds
     * @return
     */
    private List<OrderExcelDTO> reCopyK3OrderToExport(List<String> relatedOrderIds) {
        List<OrderExcelDTO> excelList = new ArrayList<>();
        List<OrderExportDTO> list = k3OrdersMapperExt.getRelatedOrderDetail(relatedOrderIds);
        for (OrderExportDTO item : list) {
            OrderExcelDTO excelDto = new OrderExcelDTO();
            BeanUtils.copyProperties(item, excelDto);

            //todo 处理查询原始数据
            //各种价格保留小数

            String addr1 = IOTEncodeUtils.decryptSM4(item.getEnAddr1(), iotSm4Key, iotSm4Iv);
            String addr2 = IOTEncodeUtils.decryptSM4(item.getEnAddr1(), iotSm4Key, iotSm4Iv);
            String addr3 = IOTEncodeUtils.decryptSM4(item.getEnAddr1(), iotSm4Key, iotSm4Iv);
            String addr4 = IOTEncodeUtils.decryptSM4(item.getEnAddr1(), iotSm4Key, iotSm4Iv);
            String rvcAddr = addr1 + addr2 + addr3 + addr4;
            String rvcName = IOTEncodeUtils.decryptSM4(item.getEnRvcName(), iotSm4Key, iotSm4Iv);
            String rvcPhone = IOTEncodeUtils.decryptSM4(item.getEnRvcPhone(), iotSm4Key, iotSm4Iv);

            String mallProvinceCode = item.getMallProvinceCode();
            String mallCityCode = item.getMallCityCode();
            String provinceName = pcConfig.getProvinceCodeNameMap().get(mallProvinceCode);
            String cityName = pcConfig.getCityCodeNameMap().get(mallCityCode);
            //               String deductPrice = IOTEncodeUtils.decryptSM4()(item.getDeductPrice(),iotSm4Key, iotSm4Iv);
            //原子结算单价
            String atomSettlePrice = shirnkPrice(item.getAtomSettleUnitPrice());
            //原子订购金额
            String atomPrice = shirnkPrice(item.getAtomPrice());
            //物料结算单价
            String materialUnitPrice = shirnkPrice(item.getMaterialUnitPrice());
            //物料结算金额
            String materialPrice = shirnkPrice(item.getMaterialPrice());
            //抵扣金额
            String deductPrice = shirnkPrice(IOTEncodeUtils.decryptSM4(item.getDeductPrice(), iotSm4Key, iotSm4Iv));

            String ctrProperty = item.getCtrProperty();
            String contractType = "";
            if (StringUtils.isEmpty(ctrProperty)) {
                contractType = "---";
            } else if ("FRAMEWORK".equals(ctrProperty)) {
                contractType = "框架协议";
            } else if ("SINGLE_CONTRACT".equals(ctrProperty)) {
                contractType = "单项合同";
            } else {
                contractType = "未知类型";
            }


            String ctrStatisType = item.getCtrStatisType();
            String contractStatisType = "";
            if (StringUtils.isEmpty(ctrStatisType)) {
                contractStatisType = "---";
            } else if (ctrStatisType.equals("0")) {
                contractStatisType = "按地市";
            } else if (ctrStatisType.equals("1")) {
                contractStatisType = "按省份";
            } else if (ctrStatisType.equals("2")) {
                contractStatisType = "按合同";
            } else {
                contractStatisType = "未知类型";
            }

            String atomOfferingClassName = AtomOfferingClassEnum.getDescribe(item.getAtomOfferingClass());

            /*excelDto.setRvcAddr(rvcAddr);
            excelDto.setRvcName(rvcName);
            excelDto.setRvcPhone(rvcPhone);*/
            excelDto.setOrderProvince(provinceName);
            excelDto.setOrderCity(cityName);
            excelDto.setContractType(contractType);
            excelDto.setContractStatisType(contractStatisType);
            excelDto.setAtomSettleUnitPrice(atomSettlePrice);
            excelDto.setAtomPrice(atomPrice);
            excelDto.setMaterialUnitPrice(materialUnitPrice);
            excelDto.setMaterialPrice(materialPrice);
            excelDto.setDeductPrice(deductPrice);
            excelDto.setAtomClassName(atomOfferingClassName);
//                if(StringUtils.isEmpty(deductPrice)){
//                    excelDto.setDeductPrice("");
//                }else{
//                    excelDto.setDeductPrice(String.valueOf(Double.parseDouble(deductPrice)/1000));
//                }
            excelList.add(excelDto);
        }
        return excelList;
    }

}
