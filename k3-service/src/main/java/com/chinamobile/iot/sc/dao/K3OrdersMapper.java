package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.K3Orders;
import com.chinamobile.iot.sc.pojo.entity.K3OrdersExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface K3OrdersMapper {
    long countByExample(K3OrdersExample example);

    int deleteByExample(K3OrdersExample example);

    int deleteByPrimaryKey(String id);

    int insert(K3Orders record);

    int insertSelective(K3Orders record);

    List<K3Orders> selectByExample(K3OrdersExample example);

    K3Orders selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") K3Orders record, @Param("example") K3OrdersExample example);

    int updateByExample(@Param("record") K3Orders record, @Param("example") K3OrdersExample example);

    int updateByPrimaryKeySelective(K3Orders record);

    int updateByPrimaryKey(K3Orders record);

    int batchInsert(@Param("list") List<K3Orders> list);

    int batchInsertSelective(@Param("list") List<K3Orders> list, @Param("selective") K3Orders.Column ... selective);
}