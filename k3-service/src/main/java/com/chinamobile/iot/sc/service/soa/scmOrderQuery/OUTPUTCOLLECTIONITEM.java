
package com.chinamobile.iot.sc.service.soa.scmOrderQuery;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for OUTPUTCOLLECTION_ITEM complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION_ITEM"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ORDER_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ERP_ORDER_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ORDER_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="COST_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_ORDER_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_DEPT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_EMAIL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_TEL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CREATE_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="CREATE_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CREATE_NAME_DEPT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CREATE_ORDER_MANAGER" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_STATE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MIS_BODY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OPEN_TYPE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MTL_TYPE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MTL_SMAALL_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="REQUEST_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SECOND_REQUEST_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ACCOUNTS_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_SIGN" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SIGN_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ARRIVAL_TIME_MODEL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ARRIVE_MESSA" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CURRENCY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="FREE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SYN_ERP_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_SPLIT_ORDER" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_SERVICE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SPLIT_ORDER_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PO_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="TAX_SUM" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="PO_AMOUNT_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="IN_NOTE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OUT_NOTE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CONTRACT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CONTRACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USABLE_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="CONTRAL_TOTAL_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="CREATED_BY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CONTRACT_LEVEL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_FRAME_CONTRACT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LEAD_TIME" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="ISSUED_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="VENDOR_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="VENDOR_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="VENDOR_CONTACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="VENDOR_CONTACT_EMAIL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="VENDOR_CONTACT_PHONE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SUPPLIER_SCALE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OPEN_COUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="CANCEL_COUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="OPERATION_STAFF" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_ELECT_SING" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SINGLE_DOUBLE_SIGNATURE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_MOBILE_SIGNATURE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CONTRACT_CONTENT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_VENDOR_INQUIRY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_VENDOR_CONFIRMATION" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_NOTIFY_VENDOR" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_GET_GROUP" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PO_LINE" type="{http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv}PO_LINE"/&gt;
 *         &lt;element name="INSIDE_APPLY_ATTACH" type="{http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv}INSIDE_APPLY_ATTACH"/&gt;
 *         &lt;element name="ATTR1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR3" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR4" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR5" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR6" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR7" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR8" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR9" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR10" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR11" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR12" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR13" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR14" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR15" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR16" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR17" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR18" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR19" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTR20" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OUTPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION_ITEM", propOrder = {
    "orderno",
    "erporderno",
    "applyno",
    "sourcefrom",
    "sourcefromno",
    "ordertype",
    "costtype",
    "applyordername",
    "applydept",
    "applyname",
    "applyemail",
    "applytel",
    "createdate",
    "createname",
    "createnamedept",
    "createordermanager",
    "applystate",
    "misbody",
    "opentypecode",
    "mtltypecode",
    "mtlsmaalltype",
    "requesttype",
    "secondrequesttype",
    "accountstype",
    "issign",
    "signname",
    "arrivaltimemodel",
    "arrivemessa",
    "currency",
    "freeflag",
    "synerpflag",
    "issplitorder",
    "isservice",
    "splitordertype",
    "poamount",
    "taxsum",
    "poamounttax",
    "innote",
    "outnote",
    "contractcode",
    "contractname",
    "usableamount",
    "contraltotalamount",
    "createdbyname",
    "contractlevel",
    "isframecontract",
    "leadtime",
    "issueddate",
    "vendorcode",
    "vendorname",
    "vendorcontactname",
    "vendorcontactemail",
    "vendorcontactphone",
    "supplierscale",
    "opencount",
    "cancelcount",
    "operationstaff",
    "iselectsing",
    "singledoublesignature",
    "ismobilesignature",
    "contractcontent",
    "isvendorinquiry",
    "isvendorconfirmation",
    "isnotifyvendor",
    "isgetgroup",
    "poline",
    "insideapplyattach",
    "attr1",
    "attr2",
    "attr3",
    "attr4",
    "attr5",
    "attr6",
    "attr7",
    "attr8",
    "attr9",
    "attr10",
    "attr11",
    "attr12",
    "attr13",
    "attr14",
    "attr15",
    "attr16",
    "attr17",
    "attr18",
    "attr19",
    "attr20",
    "outputext"
})
public class OUTPUTCOLLECTIONITEM {

    @XmlElement(name = "ORDER_NO", required = true, nillable = true)
    protected String orderno;
    @XmlElement(name = "ERP_ORDER_NO", required = true, nillable = true)
    protected String erporderno;
    @XmlElement(name = "APPLY_NO", required = true, nillable = true)
    protected String applyno;
    @XmlElement(name = "SOURCE_FROM", required = true, nillable = true)
    protected String sourcefrom;
    @XmlElement(name = "SOURCE_FROM_NO", required = true, nillable = true)
    protected String sourcefromno;
    @XmlElement(name = "ORDER_TYPE", required = true, nillable = true)
    protected String ordertype;
    @XmlElement(name = "COST_TYPE", required = true, nillable = true)
    protected String costtype;
    @XmlElement(name = "APPLY_ORDER_NAME", required = true, nillable = true)
    protected String applyordername;
    @XmlElement(name = "APPLY_DEPT", required = true, nillable = true)
    protected String applydept;
    @XmlElement(name = "APPLY_NAME", required = true, nillable = true)
    protected String applyname;
    @XmlElement(name = "APPLY_EMAIL", required = true, nillable = true)
    protected String applyemail;
    @XmlElement(name = "APPLY_TEL", required = true, nillable = true)
    protected String applytel;
    @XmlElement(name = "CREATE_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar createdate;
    @XmlElement(name = "CREATE_NAME", required = true, nillable = true)
    protected String createname;
    @XmlElement(name = "CREATE_NAME_DEPT", required = true, nillable = true)
    protected String createnamedept;
    @XmlElement(name = "CREATE_ORDER_MANAGER", required = true, nillable = true)
    protected String createordermanager;
    @XmlElement(name = "APPLY_STATE", required = true, nillable = true)
    protected String applystate;
    @XmlElement(name = "MIS_BODY", required = true, nillable = true)
    protected String misbody;
    @XmlElement(name = "OPEN_TYPE_CODE", required = true, nillable = true)
    protected String opentypecode;
    @XmlElement(name = "MTL_TYPE_CODE", required = true, nillable = true)
    protected String mtltypecode;
    @XmlElement(name = "MTL_SMAALL_TYPE", required = true, nillable = true)
    protected String mtlsmaalltype;
    @XmlElement(name = "REQUEST_TYPE", required = true, nillable = true)
    protected String requesttype;
    @XmlElement(name = "SECOND_REQUEST_TYPE", required = true, nillable = true)
    protected String secondrequesttype;
    @XmlElement(name = "ACCOUNTS_TYPE", required = true, nillable = true)
    protected String accountstype;
    @XmlElement(name = "IS_SIGN", required = true, nillable = true)
    protected String issign;
    @XmlElement(name = "SIGN_NAME", required = true, nillable = true)
    protected String signname;
    @XmlElement(name = "ARRIVAL_TIME_MODEL", required = true, nillable = true)
    protected String arrivaltimemodel;
    @XmlElement(name = "ARRIVE_MESSA", required = true, nillable = true)
    protected String arrivemessa;
    @XmlElement(name = "CURRENCY", required = true, nillable = true)
    protected String currency;
    @XmlElement(name = "FREE_FLAG", required = true, nillable = true)
    protected String freeflag;
    @XmlElement(name = "SYN_ERP_FLAG", required = true, nillable = true)
    protected String synerpflag;
    @XmlElement(name = "IS_SPLIT_ORDER", required = true, nillable = true)
    protected String issplitorder;
    @XmlElement(name = "IS_SERVICE", required = true, nillable = true)
    protected String isservice;
    @XmlElement(name = "SPLIT_ORDER_TYPE", required = true, nillable = true)
    protected String splitordertype;
    @XmlElement(name = "PO_AMOUNT", required = true, nillable = true)
    protected BigDecimal poamount;
    @XmlElement(name = "TAX_SUM", required = true, nillable = true)
    protected BigDecimal taxsum;
    @XmlElement(name = "PO_AMOUNT_TAX", required = true, nillable = true)
    protected BigDecimal poamounttax;
    @XmlElement(name = "IN_NOTE", required = true, nillable = true)
    protected String innote;
    @XmlElement(name = "OUT_NOTE", required = true, nillable = true)
    protected String outnote;
    @XmlElement(name = "CONTRACT_CODE", required = true, nillable = true)
    protected String contractcode;
    @XmlElement(name = "CONTRACT_NAME", required = true, nillable = true)
    protected String contractname;
    @XmlElement(name = "USABLE_AMOUNT", required = true, nillable = true)
    protected BigDecimal usableamount;
    @XmlElement(name = "CONTRAL_TOTAL_AMOUNT", required = true, nillable = true)
    protected BigDecimal contraltotalamount;
    @XmlElement(name = "CREATED_BY_NAME", required = true, nillable = true)
    protected String createdbyname;
    @XmlElement(name = "CONTRACT_LEVEL", required = true, nillable = true)
    protected String contractlevel;
    @XmlElement(name = "IS_FRAME_CONTRACT", required = true, nillable = true)
    protected String isframecontract;
    @XmlElement(name = "LEAD_TIME", required = true, nillable = true)
    protected BigDecimal leadtime;
    @XmlElement(name = "ISSUED_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar issueddate;
    @XmlElement(name = "VENDOR_CODE", required = true, nillable = true)
    protected String vendorcode;
    @XmlElement(name = "VENDOR_NAME", required = true, nillable = true)
    protected String vendorname;
    @XmlElement(name = "VENDOR_CONTACT_NAME", required = true, nillable = true)
    protected String vendorcontactname;
    @XmlElement(name = "VENDOR_CONTACT_EMAIL", required = true, nillable = true)
    protected String vendorcontactemail;
    @XmlElement(name = "VENDOR_CONTACT_PHONE", required = true, nillable = true)
    protected String vendorcontactphone;
    @XmlElement(name = "SUPPLIER_SCALE", required = true, nillable = true)
    protected String supplierscale;
    @XmlElement(name = "OPEN_COUNT", required = true, nillable = true)
    protected BigDecimal opencount;
    @XmlElement(name = "CANCEL_COUNT", required = true, nillable = true)
    protected BigDecimal cancelcount;
    @XmlElement(name = "OPERATION_STAFF", required = true, nillable = true)
    protected String operationstaff;
    @XmlElement(name = "IS_ELECT_SING", required = true, nillable = true)
    protected String iselectsing;
    @XmlElement(name = "SINGLE_DOUBLE_SIGNATURE", required = true, nillable = true)
    protected String singledoublesignature;
    @XmlElement(name = "IS_MOBILE_SIGNATURE", required = true, nillable = true)
    protected String ismobilesignature;
    @XmlElement(name = "CONTRACT_CONTENT", required = true, nillable = true)
    protected String contractcontent;
    @XmlElement(name = "IS_VENDOR_INQUIRY", required = true, nillable = true)
    protected String isvendorinquiry;
    @XmlElement(name = "IS_VENDOR_CONFIRMATION", required = true, nillable = true)
    protected String isvendorconfirmation;
    @XmlElement(name = "IS_NOTIFY_VENDOR", required = true, nillable = true)
    protected String isnotifyvendor;
    @XmlElement(name = "IS_GET_GROUP", required = true, nillable = true)
    protected String isgetgroup;
    @XmlElement(name = "PO_LINE", required = true)
    protected POLINE poline;
    @XmlElement(name = "INSIDE_APPLY_ATTACH", required = true)
    protected INSIDEAPPLYATTACH insideapplyattach;
    @XmlElement(name = "ATTR1", required = true, nillable = true)
    protected String attr1;
    @XmlElement(name = "ATTR2", required = true, nillable = true)
    protected String attr2;
    @XmlElement(name = "ATTR3", required = true, nillable = true)
    protected String attr3;
    @XmlElement(name = "ATTR4", required = true, nillable = true)
    protected String attr4;
    @XmlElement(name = "ATTR5", required = true, nillable = true)
    protected String attr5;
    @XmlElement(name = "ATTR6", required = true, nillable = true)
    protected String attr6;
    @XmlElement(name = "ATTR7", required = true, nillable = true)
    protected String attr7;
    @XmlElement(name = "ATTR8", required = true, nillable = true)
    protected String attr8;
    @XmlElement(name = "ATTR9", required = true, nillable = true)
    protected String attr9;
    @XmlElement(name = "ATTR10", required = true, nillable = true)
    protected String attr10;
    @XmlElement(name = "ATTR11", required = true, nillable = true)
    protected String attr11;
    @XmlElement(name = "ATTR12", required = true, nillable = true)
    protected String attr12;
    @XmlElement(name = "ATTR13", required = true, nillable = true)
    protected String attr13;
    @XmlElement(name = "ATTR14", required = true, nillable = true)
    protected String attr14;
    @XmlElement(name = "ATTR15", required = true, nillable = true)
    protected String attr15;
    @XmlElement(name = "ATTR16", required = true, nillable = true)
    protected String attr16;
    @XmlElement(name = "ATTR17", required = true, nillable = true)
    protected String attr17;
    @XmlElement(name = "ATTR18", required = true, nillable = true)
    protected String attr18;
    @XmlElement(name = "ATTR19", required = true, nillable = true)
    protected String attr19;
    @XmlElement(name = "ATTR20", required = true, nillable = true)
    protected String attr20;
    @XmlElement(name = "OUTPUT_EXT", required = true, nillable = true)
    protected String outputext;

    /**
     * Gets the value of the orderno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getORDERNO() {
        return orderno;
    }

    /**
     * Sets the value of the orderno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setORDERNO(String value) {
        this.orderno = value;
    }

    /**
     * Gets the value of the erporderno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getERPORDERNO() {
        return erporderno;
    }

    /**
     * Sets the value of the erporderno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setERPORDERNO(String value) {
        this.erporderno = value;
    }

    /**
     * Gets the value of the applyno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYNO() {
        return applyno;
    }

    /**
     * Sets the value of the applyno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYNO(String value) {
        this.applyno = value;
    }

    /**
     * Gets the value of the sourcefrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROM() {
        return sourcefrom;
    }

    /**
     * Sets the value of the sourcefrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROM(String value) {
        this.sourcefrom = value;
    }

    /**
     * Gets the value of the sourcefromno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROMNO() {
        return sourcefromno;
    }

    /**
     * Sets the value of the sourcefromno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROMNO(String value) {
        this.sourcefromno = value;
    }

    /**
     * Gets the value of the ordertype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getORDERTYPE() {
        return ordertype;
    }

    /**
     * Sets the value of the ordertype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setORDERTYPE(String value) {
        this.ordertype = value;
    }

    /**
     * Gets the value of the costtype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCOSTTYPE() {
        return costtype;
    }

    /**
     * Sets the value of the costtype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCOSTTYPE(String value) {
        this.costtype = value;
    }

    /**
     * Gets the value of the applyordername property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYORDERNAME() {
        return applyordername;
    }

    /**
     * Sets the value of the applyordername property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYORDERNAME(String value) {
        this.applyordername = value;
    }

    /**
     * Gets the value of the applydept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYDEPT() {
        return applydept;
    }

    /**
     * Sets the value of the applydept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYDEPT(String value) {
        this.applydept = value;
    }

    /**
     * Gets the value of the applyname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYNAME() {
        return applyname;
    }

    /**
     * Sets the value of the applyname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYNAME(String value) {
        this.applyname = value;
    }

    /**
     * Gets the value of the applyemail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYEMAIL() {
        return applyemail;
    }

    /**
     * Sets the value of the applyemail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYEMAIL(String value) {
        this.applyemail = value;
    }

    /**
     * Gets the value of the applytel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYTEL() {
        return applytel;
    }

    /**
     * Sets the value of the applytel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYTEL(String value) {
        this.applytel = value;
    }

    /**
     * Gets the value of the createdate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCREATEDATE() {
        return createdate;
    }

    /**
     * Sets the value of the createdate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCREATEDATE(XMLGregorianCalendar value) {
        this.createdate = value;
    }

    /**
     * Gets the value of the createname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCREATENAME() {
        return createname;
    }

    /**
     * Sets the value of the createname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCREATENAME(String value) {
        this.createname = value;
    }

    /**
     * Gets the value of the createnamedept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCREATENAMEDEPT() {
        return createnamedept;
    }

    /**
     * Sets the value of the createnamedept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCREATENAMEDEPT(String value) {
        this.createnamedept = value;
    }

    /**
     * Gets the value of the createordermanager property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCREATEORDERMANAGER() {
        return createordermanager;
    }

    /**
     * Sets the value of the createordermanager property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCREATEORDERMANAGER(String value) {
        this.createordermanager = value;
    }

    /**
     * Gets the value of the applystate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYSTATE() {
        return applystate;
    }

    /**
     * Sets the value of the applystate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYSTATE(String value) {
        this.applystate = value;
    }

    /**
     * Gets the value of the misbody property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMISBODY() {
        return misbody;
    }

    /**
     * Sets the value of the misbody property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMISBODY(String value) {
        this.misbody = value;
    }

    /**
     * Gets the value of the opentypecode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOPENTYPECODE() {
        return opentypecode;
    }

    /**
     * Sets the value of the opentypecode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOPENTYPECODE(String value) {
        this.opentypecode = value;
    }

    /**
     * Gets the value of the mtltypecode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMTLTYPECODE() {
        return mtltypecode;
    }

    /**
     * Sets the value of the mtltypecode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMTLTYPECODE(String value) {
        this.mtltypecode = value;
    }

    /**
     * Gets the value of the mtlsmaalltype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMTLSMAALLTYPE() {
        return mtlsmaalltype;
    }

    /**
     * Sets the value of the mtlsmaalltype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMTLSMAALLTYPE(String value) {
        this.mtlsmaalltype = value;
    }

    /**
     * Gets the value of the requesttype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getREQUESTTYPE() {
        return requesttype;
    }

    /**
     * Sets the value of the requesttype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setREQUESTTYPE(String value) {
        this.requesttype = value;
    }

    /**
     * Gets the value of the secondrequesttype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSECONDREQUESTTYPE() {
        return secondrequesttype;
    }

    /**
     * Sets the value of the secondrequesttype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSECONDREQUESTTYPE(String value) {
        this.secondrequesttype = value;
    }

    /**
     * Gets the value of the accountstype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getACCOUNTSTYPE() {
        return accountstype;
    }

    /**
     * Sets the value of the accountstype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setACCOUNTSTYPE(String value) {
        this.accountstype = value;
    }

    /**
     * Gets the value of the issign property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISSIGN() {
        return issign;
    }

    /**
     * Sets the value of the issign property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISSIGN(String value) {
        this.issign = value;
    }

    /**
     * Gets the value of the signname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSIGNNAME() {
        return signname;
    }

    /**
     * Sets the value of the signname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSIGNNAME(String value) {
        this.signname = value;
    }

    /**
     * Gets the value of the arrivaltimemodel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getARRIVALTIMEMODEL() {
        return arrivaltimemodel;
    }

    /**
     * Sets the value of the arrivaltimemodel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setARRIVALTIMEMODEL(String value) {
        this.arrivaltimemodel = value;
    }

    /**
     * Gets the value of the arrivemessa property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getARRIVEMESSA() {
        return arrivemessa;
    }

    /**
     * Sets the value of the arrivemessa property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setARRIVEMESSA(String value) {
        this.arrivemessa = value;
    }

    /**
     * Gets the value of the currency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCURRENCY() {
        return currency;
    }

    /**
     * Sets the value of the currency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCURRENCY(String value) {
        this.currency = value;
    }

    /**
     * Gets the value of the freeflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFREEFLAG() {
        return freeflag;
    }

    /**
     * Sets the value of the freeflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFREEFLAG(String value) {
        this.freeflag = value;
    }

    /**
     * Gets the value of the synerpflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSYNERPFLAG() {
        return synerpflag;
    }

    /**
     * Sets the value of the synerpflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSYNERPFLAG(String value) {
        this.synerpflag = value;
    }

    /**
     * Gets the value of the issplitorder property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISSPLITORDER() {
        return issplitorder;
    }

    /**
     * Sets the value of the issplitorder property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISSPLITORDER(String value) {
        this.issplitorder = value;
    }

    /**
     * Gets the value of the isservice property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISSERVICE() {
        return isservice;
    }

    /**
     * Sets the value of the isservice property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISSERVICE(String value) {
        this.isservice = value;
    }

    /**
     * Gets the value of the splitordertype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSPLITORDERTYPE() {
        return splitordertype;
    }

    /**
     * Sets the value of the splitordertype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSPLITORDERTYPE(String value) {
        this.splitordertype = value;
    }

    /**
     * Gets the value of the poamount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPOAMOUNT() {
        return poamount;
    }

    /**
     * Sets the value of the poamount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPOAMOUNT(BigDecimal value) {
        this.poamount = value;
    }

    /**
     * Gets the value of the taxsum property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTAXSUM() {
        return taxsum;
    }

    /**
     * Sets the value of the taxsum property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTAXSUM(BigDecimal value) {
        this.taxsum = value;
    }

    /**
     * Gets the value of the poamounttax property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPOAMOUNTTAX() {
        return poamounttax;
    }

    /**
     * Sets the value of the poamounttax property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPOAMOUNTTAX(BigDecimal value) {
        this.poamounttax = value;
    }

    /**
     * Gets the value of the innote property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getINNOTE() {
        return innote;
    }

    /**
     * Sets the value of the innote property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setINNOTE(String value) {
        this.innote = value;
    }

    /**
     * Gets the value of the outnote property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUTNOTE() {
        return outnote;
    }

    /**
     * Sets the value of the outnote property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUTNOTE(String value) {
        this.outnote = value;
    }

    /**
     * Gets the value of the contractcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCONTRACTCODE() {
        return contractcode;
    }

    /**
     * Sets the value of the contractcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCONTRACTCODE(String value) {
        this.contractcode = value;
    }

    /**
     * Gets the value of the contractname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCONTRACTNAME() {
        return contractname;
    }

    /**
     * Sets the value of the contractname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCONTRACTNAME(String value) {
        this.contractname = value;
    }

    /**
     * Gets the value of the usableamount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUSABLEAMOUNT() {
        return usableamount;
    }

    /**
     * Sets the value of the usableamount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUSABLEAMOUNT(BigDecimal value) {
        this.usableamount = value;
    }

    /**
     * Gets the value of the contraltotalamount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCONTRALTOTALAMOUNT() {
        return contraltotalamount;
    }

    /**
     * Sets the value of the contraltotalamount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCONTRALTOTALAMOUNT(BigDecimal value) {
        this.contraltotalamount = value;
    }

    /**
     * Gets the value of the createdbyname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCREATEDBYNAME() {
        return createdbyname;
    }

    /**
     * Sets the value of the createdbyname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCREATEDBYNAME(String value) {
        this.createdbyname = value;
    }

    /**
     * Gets the value of the contractlevel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCONTRACTLEVEL() {
        return contractlevel;
    }

    /**
     * Sets the value of the contractlevel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCONTRACTLEVEL(String value) {
        this.contractlevel = value;
    }

    /**
     * Gets the value of the isframecontract property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISFRAMECONTRACT() {
        return isframecontract;
    }

    /**
     * Sets the value of the isframecontract property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISFRAMECONTRACT(String value) {
        this.isframecontract = value;
    }

    /**
     * Gets the value of the leadtime property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLEADTIME() {
        return leadtime;
    }

    /**
     * Sets the value of the leadtime property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLEADTIME(BigDecimal value) {
        this.leadtime = value;
    }

    /**
     * Gets the value of the issueddate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getISSUEDDATE() {
        return issueddate;
    }

    /**
     * Sets the value of the issueddate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setISSUEDDATE(XMLGregorianCalendar value) {
        this.issueddate = value;
    }

    /**
     * Gets the value of the vendorcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVENDORCODE() {
        return vendorcode;
    }

    /**
     * Sets the value of the vendorcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVENDORCODE(String value) {
        this.vendorcode = value;
    }

    /**
     * Gets the value of the vendorname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVENDORNAME() {
        return vendorname;
    }

    /**
     * Sets the value of the vendorname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVENDORNAME(String value) {
        this.vendorname = value;
    }

    /**
     * Gets the value of the vendorcontactname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVENDORCONTACTNAME() {
        return vendorcontactname;
    }

    /**
     * Sets the value of the vendorcontactname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVENDORCONTACTNAME(String value) {
        this.vendorcontactname = value;
    }

    /**
     * Gets the value of the vendorcontactemail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVENDORCONTACTEMAIL() {
        return vendorcontactemail;
    }

    /**
     * Sets the value of the vendorcontactemail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVENDORCONTACTEMAIL(String value) {
        this.vendorcontactemail = value;
    }

    /**
     * Gets the value of the vendorcontactphone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVENDORCONTACTPHONE() {
        return vendorcontactphone;
    }

    /**
     * Sets the value of the vendorcontactphone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVENDORCONTACTPHONE(String value) {
        this.vendorcontactphone = value;
    }

    /**
     * Gets the value of the supplierscale property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSUPPLIERSCALE() {
        return supplierscale;
    }

    /**
     * Sets the value of the supplierscale property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSUPPLIERSCALE(String value) {
        this.supplierscale = value;
    }

    /**
     * Gets the value of the opencount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOPENCOUNT() {
        return opencount;
    }

    /**
     * Sets the value of the opencount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOPENCOUNT(BigDecimal value) {
        this.opencount = value;
    }

    /**
     * Gets the value of the cancelcount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCANCELCOUNT() {
        return cancelcount;
    }

    /**
     * Sets the value of the cancelcount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCANCELCOUNT(BigDecimal value) {
        this.cancelcount = value;
    }

    /**
     * Gets the value of the operationstaff property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOPERATIONSTAFF() {
        return operationstaff;
    }

    /**
     * Sets the value of the operationstaff property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOPERATIONSTAFF(String value) {
        this.operationstaff = value;
    }

    /**
     * Gets the value of the iselectsing property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISELECTSING() {
        return iselectsing;
    }

    /**
     * Sets the value of the iselectsing property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISELECTSING(String value) {
        this.iselectsing = value;
    }

    /**
     * Gets the value of the singledoublesignature property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSINGLEDOUBLESIGNATURE() {
        return singledoublesignature;
    }

    /**
     * Sets the value of the singledoublesignature property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSINGLEDOUBLESIGNATURE(String value) {
        this.singledoublesignature = value;
    }

    /**
     * Gets the value of the ismobilesignature property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISMOBILESIGNATURE() {
        return ismobilesignature;
    }

    /**
     * Sets the value of the ismobilesignature property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISMOBILESIGNATURE(String value) {
        this.ismobilesignature = value;
    }

    /**
     * Gets the value of the contractcontent property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCONTRACTCONTENT() {
        return contractcontent;
    }

    /**
     * Sets the value of the contractcontent property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCONTRACTCONTENT(String value) {
        this.contractcontent = value;
    }

    /**
     * Gets the value of the isvendorinquiry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISVENDORINQUIRY() {
        return isvendorinquiry;
    }

    /**
     * Sets the value of the isvendorinquiry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISVENDORINQUIRY(String value) {
        this.isvendorinquiry = value;
    }

    /**
     * Gets the value of the isvendorconfirmation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISVENDORCONFIRMATION() {
        return isvendorconfirmation;
    }

    /**
     * Sets the value of the isvendorconfirmation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISVENDORCONFIRMATION(String value) {
        this.isvendorconfirmation = value;
    }

    /**
     * Gets the value of the isnotifyvendor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISNOTIFYVENDOR() {
        return isnotifyvendor;
    }

    /**
     * Sets the value of the isnotifyvendor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISNOTIFYVENDOR(String value) {
        this.isnotifyvendor = value;
    }

    /**
     * Gets the value of the isgetgroup property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISGETGROUP() {
        return isgetgroup;
    }

    /**
     * Sets the value of the isgetgroup property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISGETGROUP(String value) {
        this.isgetgroup = value;
    }

    /**
     * Gets the value of the poline property.
     * 
     * @return
     *     possible object is
     *     {@link POLINE }
     *     
     */
    public POLINE getPOLINE() {
        return poline;
    }

    /**
     * Sets the value of the poline property.
     * 
     * @param value
     *     allowed object is
     *     {@link POLINE }
     *     
     */
    public void setPOLINE(POLINE value) {
        this.poline = value;
    }

    /**
     * Gets the value of the insideapplyattach property.
     * 
     * @return
     *     possible object is
     *     {@link INSIDEAPPLYATTACH }
     *     
     */
    public INSIDEAPPLYATTACH getINSIDEAPPLYATTACH() {
        return insideapplyattach;
    }

    /**
     * Sets the value of the insideapplyattach property.
     * 
     * @param value
     *     allowed object is
     *     {@link INSIDEAPPLYATTACH }
     *     
     */
    public void setINSIDEAPPLYATTACH(INSIDEAPPLYATTACH value) {
        this.insideapplyattach = value;
    }

    /**
     * Gets the value of the attr1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR1() {
        return attr1;
    }

    /**
     * Sets the value of the attr1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR1(String value) {
        this.attr1 = value;
    }

    /**
     * Gets the value of the attr2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR2() {
        return attr2;
    }

    /**
     * Sets the value of the attr2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR2(String value) {
        this.attr2 = value;
    }

    /**
     * Gets the value of the attr3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR3() {
        return attr3;
    }

    /**
     * Sets the value of the attr3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR3(String value) {
        this.attr3 = value;
    }

    /**
     * Gets the value of the attr4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR4() {
        return attr4;
    }

    /**
     * Sets the value of the attr4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR4(String value) {
        this.attr4 = value;
    }

    /**
     * Gets the value of the attr5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR5() {
        return attr5;
    }

    /**
     * Sets the value of the attr5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR5(String value) {
        this.attr5 = value;
    }

    /**
     * Gets the value of the attr6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR6() {
        return attr6;
    }

    /**
     * Sets the value of the attr6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR6(String value) {
        this.attr6 = value;
    }

    /**
     * Gets the value of the attr7 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR7() {
        return attr7;
    }

    /**
     * Sets the value of the attr7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR7(String value) {
        this.attr7 = value;
    }

    /**
     * Gets the value of the attr8 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR8() {
        return attr8;
    }

    /**
     * Sets the value of the attr8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR8(String value) {
        this.attr8 = value;
    }

    /**
     * Gets the value of the attr9 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR9() {
        return attr9;
    }

    /**
     * Sets the value of the attr9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR9(String value) {
        this.attr9 = value;
    }

    /**
     * Gets the value of the attr10 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR10() {
        return attr10;
    }

    /**
     * Sets the value of the attr10 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR10(String value) {
        this.attr10 = value;
    }

    /**
     * Gets the value of the attr11 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR11() {
        return attr11;
    }

    /**
     * Sets the value of the attr11 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR11(String value) {
        this.attr11 = value;
    }

    /**
     * Gets the value of the attr12 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR12() {
        return attr12;
    }

    /**
     * Sets the value of the attr12 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR12(String value) {
        this.attr12 = value;
    }

    /**
     * Gets the value of the attr13 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR13() {
        return attr13;
    }

    /**
     * Sets the value of the attr13 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR13(String value) {
        this.attr13 = value;
    }

    /**
     * Gets the value of the attr14 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR14() {
        return attr14;
    }

    /**
     * Sets the value of the attr14 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR14(String value) {
        this.attr14 = value;
    }

    /**
     * Gets the value of the attr15 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR15() {
        return attr15;
    }

    /**
     * Sets the value of the attr15 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR15(String value) {
        this.attr15 = value;
    }

    /**
     * Gets the value of the attr16 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR16() {
        return attr16;
    }

    /**
     * Sets the value of the attr16 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR16(String value) {
        this.attr16 = value;
    }

    /**
     * Gets the value of the attr17 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR17() {
        return attr17;
    }

    /**
     * Sets the value of the attr17 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR17(String value) {
        this.attr17 = value;
    }

    /**
     * Gets the value of the attr18 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR18() {
        return attr18;
    }

    /**
     * Sets the value of the attr18 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR18(String value) {
        this.attr18 = value;
    }

    /**
     * Gets the value of the attr19 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR19() {
        return attr19;
    }

    /**
     * Sets the value of the attr19 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR19(String value) {
        this.attr19 = value;
    }

    /**
     * Gets the value of the attr20 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTR20() {
        return attr20;
    }

    /**
     * Sets the value of the attr20 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTR20(String value) {
        this.attr20 = value;
    }

    /**
     * Gets the value of the outputext property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUTPUTEXT() {
        return outputext;
    }

    /**
     * Sets the value of the outputext property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUTPUTEXT(String value) {
        this.outputext = value;
    }

}
