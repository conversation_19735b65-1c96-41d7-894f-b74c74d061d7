package com.chinamobile.iot.sc.pojo.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/9/18 9:38
 * @description: 统一用户机构参数
 **/
@Data
public class OfficialOrganization {

    /**
     * 组织ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createdDt;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updatedDt;

    /**
     * 组织类型 1：公司，2：部门，3：分组，4：分公司，5：分公司部门，6：分公司团队
     */
    private Integer orgType;


    /**
     * 组织类型 1：公司，2：部门，3：分组，4：分公司，5：分公司部门，6：分公司团队
     */
    private Integer orgLevel;

    /**
     * 0 有效,   1 失效
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 组织全称
     */
    private String fullName;

    /**
     * 组织简称
     */
    private String shortName;

    /**取值create，update,delete表示用户变化，推送的时候才有值*/
    private String operationMode;
}
