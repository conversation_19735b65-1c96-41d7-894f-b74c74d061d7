package com.chinamobile.iot.sc.service.soa.orderStatusInfo;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.0.4
 * 2024-08-20T15:47:06.710+08:00
 * Generated source version: 3.0.4
 * 
 */
@WebService(targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrv", name = "OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrv")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface OSBSSCMZXHQPageInquiryOrderStateInfoSrv {

    @WebMethod(action = "process")
    @WebResult(name = "OutputParameters", targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrv", partName = "payload")
    public OutputParameters process(
        @WebParam(partName = "payload", name = "InputParameters", targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrv")
        InputParameters payload
    );
}
