package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProvinceContractInfoNewExample {
    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNewExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNewExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ProvinceContractInfoNewExample example = new ProvinceContractInfoNewExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNewExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNewExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(String value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(String value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(String value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(String value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLike(String value) {
            addCriterion("contract_type like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotLike(String value) {
            addCriterion("contract_type not like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<String> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<String> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(String value1, String value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(String value1, String value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIsNull() {
            addCriterion("internet_contract_code is null");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIsNotNull() {
            addCriterion("internet_contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeEqualTo(String value) {
            addCriterion("internet_contract_code =", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotEqualTo(String value) {
            addCriterion("internet_contract_code <>", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThan(String value) {
            addCriterion("internet_contract_code >", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("internet_contract_code >=", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThan(String value) {
            addCriterion("internet_contract_code <", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanOrEqualTo(String value) {
            addCriterion("internet_contract_code <=", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLike(String value) {
            addCriterion("internet_contract_code like", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotLike(String value) {
            addCriterion("internet_contract_code not like", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIn(List<String> values) {
            addCriterion("internet_contract_code in", values, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotIn(List<String> values) {
            addCriterion("internet_contract_code not in", values, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeBetween(String value1, String value2) {
            addCriterion("internet_contract_code between", value1, value2, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotBetween(String value1, String value2) {
            addCriterion("internet_contract_code not between", value1, value2, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeIsNull() {
            addCriterion("dept_code is null");
            return (Criteria) this;
        }

        public Criteria andDeptCodeIsNotNull() {
            addCriterion("dept_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeptCodeEqualTo(String value) {
            addCriterion("dept_code =", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeNotEqualTo(String value) {
            addCriterion("dept_code <>", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeGreaterThan(String value) {
            addCriterion("dept_code >", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("dept_code >=", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeLessThan(String value) {
            addCriterion("dept_code <", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("dept_code <=", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptCodeLike(String value) {
            addCriterion("dept_code like", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeNotLike(String value) {
            addCriterion("dept_code not like", value, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeIn(List<String> values) {
            addCriterion("dept_code in", values, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeNotIn(List<String> values) {
            addCriterion("dept_code not in", values, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeBetween(String value1, String value2) {
            addCriterion("dept_code between", value1, value2, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeNotBetween(String value1, String value2) {
            addCriterion("dept_code not between", value1, value2, "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNull() {
            addCriterion("dept_name is null");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNotNull() {
            addCriterion("dept_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeptNameEqualTo(String value) {
            addCriterion("dept_name =", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameNotEqualTo(String value) {
            addCriterion("dept_name <>", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThan(String value) {
            addCriterion("dept_name >", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("dept_name >=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThan(String value) {
            addCriterion("dept_name <", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanOrEqualTo(String value) {
            addCriterion("dept_name <=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("dept_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeptNameLike(String value) {
            addCriterion("dept_name like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotLike(String value) {
            addCriterion("dept_name not like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameIn(List<String> values) {
            addCriterion("dept_name in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotIn(List<String> values) {
            addCriterion("dept_name not in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameBetween(String value1, String value2) {
            addCriterion("dept_name between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotBetween(String value1, String value2) {
            addCriterion("dept_name not between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andCreatedIdIsNull() {
            addCriterion("created_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatedIdIsNotNull() {
            addCriterion("created_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedIdEqualTo(String value) {
            addCriterion("created_id =", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdNotEqualTo(String value) {
            addCriterion("created_id <>", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdGreaterThan(String value) {
            addCriterion("created_id >", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdGreaterThanOrEqualTo(String value) {
            addCriterion("created_id >=", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdLessThan(String value) {
            addCriterion("created_id <", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdLessThanOrEqualTo(String value) {
            addCriterion("created_id <=", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedIdLike(String value) {
            addCriterion("created_id like", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdNotLike(String value) {
            addCriterion("created_id not like", value, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdIn(List<String> values) {
            addCriterion("created_id in", values, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdNotIn(List<String> values) {
            addCriterion("created_id not in", values, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdBetween(String value1, String value2) {
            addCriterion("created_id between", value1, value2, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedIdNotBetween(String value1, String value2) {
            addCriterion("created_id not between", value1, value2, "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("created_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeIsNull() {
            addCriterion("mtl_type_code is null");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeIsNotNull() {
            addCriterion("mtl_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeEqualTo(String value) {
            addCriterion("mtl_type_code =", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeNotEqualTo(String value) {
            addCriterion("mtl_type_code <>", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeGreaterThan(String value) {
            addCriterion("mtl_type_code >", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("mtl_type_code >=", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLessThan(String value) {
            addCriterion("mtl_type_code <", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("mtl_type_code <=", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mtl_type_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLike(String value) {
            addCriterion("mtl_type_code like", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeNotLike(String value) {
            addCriterion("mtl_type_code not like", value, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeIn(List<String> values) {
            addCriterion("mtl_type_code in", values, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeNotIn(List<String> values) {
            addCriterion("mtl_type_code not in", values, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeBetween(String value1, String value2) {
            addCriterion("mtl_type_code between", value1, value2, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeNotBetween(String value1, String value2) {
            addCriterion("mtl_type_code not between", value1, value2, "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andSmallItemIsNull() {
            addCriterion("small_item is null");
            return (Criteria) this;
        }

        public Criteria andSmallItemIsNotNull() {
            addCriterion("small_item is not null");
            return (Criteria) this;
        }

        public Criteria andSmallItemEqualTo(String value) {
            addCriterion("small_item =", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemNotEqualTo(String value) {
            addCriterion("small_item <>", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemGreaterThan(String value) {
            addCriterion("small_item >", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemGreaterThanOrEqualTo(String value) {
            addCriterion("small_item >=", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemLessThan(String value) {
            addCriterion("small_item <", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemLessThanOrEqualTo(String value) {
            addCriterion("small_item <=", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("small_item <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSmallItemLike(String value) {
            addCriterion("small_item like", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemNotLike(String value) {
            addCriterion("small_item not like", value, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemIn(List<String> values) {
            addCriterion("small_item in", values, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemNotIn(List<String> values) {
            addCriterion("small_item not in", values, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemBetween(String value1, String value2) {
            addCriterion("small_item between", value1, value2, "smallItem");
            return (Criteria) this;
        }

        public Criteria andSmallItemNotBetween(String value1, String value2) {
            addCriterion("small_item not between", value1, value2, "smallItem");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("vendor_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andReqTypeIsNull() {
            addCriterion("req_type is null");
            return (Criteria) this;
        }

        public Criteria andReqTypeIsNotNull() {
            addCriterion("req_type is not null");
            return (Criteria) this;
        }

        public Criteria andReqTypeEqualTo(String value) {
            addCriterion("req_type =", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeNotEqualTo(String value) {
            addCriterion("req_type <>", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeGreaterThan(String value) {
            addCriterion("req_type >", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeGreaterThanOrEqualTo(String value) {
            addCriterion("req_type >=", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeLessThan(String value) {
            addCriterion("req_type <", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeLessThanOrEqualTo(String value) {
            addCriterion("req_type <=", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqTypeLike(String value) {
            addCriterion("req_type like", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeNotLike(String value) {
            addCriterion("req_type not like", value, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeIn(List<String> values) {
            addCriterion("req_type in", values, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeNotIn(List<String> values) {
            addCriterion("req_type not in", values, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeBetween(String value1, String value2) {
            addCriterion("req_type between", value1, value2, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqTypeNotBetween(String value1, String value2) {
            addCriterion("req_type not between", value1, value2, "reqType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeIsNull() {
            addCriterion("req_second_type is null");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeIsNotNull() {
            addCriterion("req_second_type is not null");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeEqualTo(String value) {
            addCriterion("req_second_type =", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeNotEqualTo(String value) {
            addCriterion("req_second_type <>", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeGreaterThan(String value) {
            addCriterion("req_second_type >", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeGreaterThanOrEqualTo(String value) {
            addCriterion("req_second_type >=", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLessThan(String value) {
            addCriterion("req_second_type <", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLessThanOrEqualTo(String value) {
            addCriterion("req_second_type <=", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("req_second_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLike(String value) {
            addCriterion("req_second_type like", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeNotLike(String value) {
            addCriterion("req_second_type not like", value, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeIn(List<String> values) {
            addCriterion("req_second_type in", values, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeNotIn(List<String> values) {
            addCriterion("req_second_type not in", values, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeBetween(String value1, String value2) {
            addCriterion("req_second_type between", value1, value2, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeNotBetween(String value1, String value2) {
            addCriterion("req_second_type not between", value1, value2, "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumIsNull() {
            addCriterion("rcv_user_num is null");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumIsNotNull() {
            addCriterion("rcv_user_num is not null");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumEqualTo(String value) {
            addCriterion("rcv_user_num =", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumNotEqualTo(String value) {
            addCriterion("rcv_user_num <>", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumGreaterThan(String value) {
            addCriterion("rcv_user_num >", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumGreaterThanOrEqualTo(String value) {
            addCriterion("rcv_user_num >=", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLessThan(String value) {
            addCriterion("rcv_user_num <", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLessThanOrEqualTo(String value) {
            addCriterion("rcv_user_num <=", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLike(String value) {
            addCriterion("rcv_user_num like", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumNotLike(String value) {
            addCriterion("rcv_user_num not like", value, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumIn(List<String> values) {
            addCriterion("rcv_user_num in", values, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumNotIn(List<String> values) {
            addCriterion("rcv_user_num not in", values, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumBetween(String value1, String value2) {
            addCriterion("rcv_user_num between", value1, value2, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumNotBetween(String value1, String value2) {
            addCriterion("rcv_user_num not between", value1, value2, "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserIsNull() {
            addCriterion("rcv_user is null");
            return (Criteria) this;
        }

        public Criteria andRcvUserIsNotNull() {
            addCriterion("rcv_user is not null");
            return (Criteria) this;
        }

        public Criteria andRcvUserEqualTo(String value) {
            addCriterion("rcv_user =", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserNotEqualTo(String value) {
            addCriterion("rcv_user <>", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserGreaterThan(String value) {
            addCriterion("rcv_user >", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserGreaterThanOrEqualTo(String value) {
            addCriterion("rcv_user >=", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserLessThan(String value) {
            addCriterion("rcv_user <", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserLessThanOrEqualTo(String value) {
            addCriterion("rcv_user <=", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_user <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvUserLike(String value) {
            addCriterion("rcv_user like", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserNotLike(String value) {
            addCriterion("rcv_user not like", value, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserIn(List<String> values) {
            addCriterion("rcv_user in", values, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserNotIn(List<String> values) {
            addCriterion("rcv_user not in", values, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserBetween(String value1, String value2) {
            addCriterion("rcv_user between", value1, value2, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvUserNotBetween(String value1, String value2) {
            addCriterion("rcv_user not between", value1, value2, "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneIsNull() {
            addCriterion("rcv_contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneIsNotNull() {
            addCriterion("rcv_contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneEqualTo(String value) {
            addCriterion("rcv_contact_phone =", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneNotEqualTo(String value) {
            addCriterion("rcv_contact_phone <>", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneGreaterThan(String value) {
            addCriterion("rcv_contact_phone >", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("rcv_contact_phone >=", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLessThan(String value) {
            addCriterion("rcv_contact_phone <", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("rcv_contact_phone <=", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_contact_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLike(String value) {
            addCriterion("rcv_contact_phone like", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneNotLike(String value) {
            addCriterion("rcv_contact_phone not like", value, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneIn(List<String> values) {
            addCriterion("rcv_contact_phone in", values, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneNotIn(List<String> values) {
            addCriterion("rcv_contact_phone not in", values, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneBetween(String value1, String value2) {
            addCriterion("rcv_contact_phone between", value1, value2, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneNotBetween(String value1, String value2) {
            addCriterion("rcv_contact_phone not between", value1, value2, "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressIsNull() {
            addCriterion("rcv_site_address is null");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressIsNotNull() {
            addCriterion("rcv_site_address is not null");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressEqualTo(String value) {
            addCriterion("rcv_site_address =", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressNotEqualTo(String value) {
            addCriterion("rcv_site_address <>", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressGreaterThan(String value) {
            addCriterion("rcv_site_address >", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressGreaterThanOrEqualTo(String value) {
            addCriterion("rcv_site_address >=", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLessThan(String value) {
            addCriterion("rcv_site_address <", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLessThanOrEqualTo(String value) {
            addCriterion("rcv_site_address <=", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("rcv_site_address <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLike(String value) {
            addCriterion("rcv_site_address like", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressNotLike(String value) {
            addCriterion("rcv_site_address not like", value, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressIn(List<String> values) {
            addCriterion("rcv_site_address in", values, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressNotIn(List<String> values) {
            addCriterion("rcv_site_address not in", values, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressBetween(String value1, String value2) {
            addCriterion("rcv_site_address between", value1, value2, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressNotBetween(String value1, String value2) {
            addCriterion("rcv_site_address not between", value1, value2, "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andMisBodyIsNull() {
            addCriterion("mis_body is null");
            return (Criteria) this;
        }

        public Criteria andMisBodyIsNotNull() {
            addCriterion("mis_body is not null");
            return (Criteria) this;
        }

        public Criteria andMisBodyEqualTo(String value) {
            addCriterion("mis_body =", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyNotEqualTo(String value) {
            addCriterion("mis_body <>", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyGreaterThan(String value) {
            addCriterion("mis_body >", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyGreaterThanOrEqualTo(String value) {
            addCriterion("mis_body >=", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyLessThan(String value) {
            addCriterion("mis_body <", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyLessThanOrEqualTo(String value) {
            addCriterion("mis_body <=", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("mis_body <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMisBodyLike(String value) {
            addCriterion("mis_body like", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyNotLike(String value) {
            addCriterion("mis_body not like", value, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyIn(List<String> values) {
            addCriterion("mis_body in", values, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyNotIn(List<String> values) {
            addCriterion("mis_body not in", values, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyBetween(String value1, String value2) {
            addCriterion("mis_body between", value1, value2, "misBody");
            return (Criteria) this;
        }

        public Criteria andMisBodyNotBetween(String value1, String value2) {
            addCriterion("mis_body not between", value1, value2, "misBody");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(String value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(String value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(String value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(String value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(String value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("org_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgIdLike(String value) {
            addCriterion("org_id like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotLike(String value) {
            addCriterion("org_id not like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<String> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<String> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(String value1, String value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(String value1, String value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andExpTypeIsNull() {
            addCriterion("exp_type is null");
            return (Criteria) this;
        }

        public Criteria andExpTypeIsNotNull() {
            addCriterion("exp_type is not null");
            return (Criteria) this;
        }

        public Criteria andExpTypeEqualTo(String value) {
            addCriterion("exp_type =", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeNotEqualTo(String value) {
            addCriterion("exp_type <>", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeGreaterThan(String value) {
            addCriterion("exp_type >", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeGreaterThanOrEqualTo(String value) {
            addCriterion("exp_type >=", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeLessThan(String value) {
            addCriterion("exp_type <", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeLessThanOrEqualTo(String value) {
            addCriterion("exp_type <=", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("exp_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpTypeLike(String value) {
            addCriterion("exp_type like", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeNotLike(String value) {
            addCriterion("exp_type not like", value, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeIn(List<String> values) {
            addCriterion("exp_type in", values, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeNotIn(List<String> values) {
            addCriterion("exp_type not in", values, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeBetween(String value1, String value2) {
            addCriterion("exp_type between", value1, value2, "expType");
            return (Criteria) this;
        }

        public Criteria andExpTypeNotBetween(String value1, String value2) {
            addCriterion("exp_type not between", value1, value2, "expType");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeIsNull() {
            addCriterion("reimbursement_mode is null");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeIsNotNull() {
            addCriterion("reimbursement_mode is not null");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeEqualTo(String value) {
            addCriterion("reimbursement_mode =", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeNotEqualTo(String value) {
            addCriterion("reimbursement_mode <>", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeGreaterThan(String value) {
            addCriterion("reimbursement_mode >", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeGreaterThanOrEqualTo(String value) {
            addCriterion("reimbursement_mode >=", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLessThan(String value) {
            addCriterion("reimbursement_mode <", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLessThanOrEqualTo(String value) {
            addCriterion("reimbursement_mode <=", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("reimbursement_mode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLike(String value) {
            addCriterion("reimbursement_mode like", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeNotLike(String value) {
            addCriterion("reimbursement_mode not like", value, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeIn(List<String> values) {
            addCriterion("reimbursement_mode in", values, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeNotIn(List<String> values) {
            addCriterion("reimbursement_mode not in", values, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeBetween(String value1, String value2) {
            addCriterion("reimbursement_mode between", value1, value2, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeNotBetween(String value1, String value2) {
            addCriterion("reimbursement_mode not between", value1, value2, "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelIsNull() {
            addCriterion("arrival_time_model is null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelIsNotNull() {
            addCriterion("arrival_time_model is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelEqualTo(String value) {
            addCriterion("arrival_time_model =", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelNotEqualTo(String value) {
            addCriterion("arrival_time_model <>", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelGreaterThan(String value) {
            addCriterion("arrival_time_model >", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelGreaterThanOrEqualTo(String value) {
            addCriterion("arrival_time_model >=", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLessThan(String value) {
            addCriterion("arrival_time_model <", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLessThanOrEqualTo(String value) {
            addCriterion("arrival_time_model <=", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_time_model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLike(String value) {
            addCriterion("arrival_time_model like", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelNotLike(String value) {
            addCriterion("arrival_time_model not like", value, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelIn(List<String> values) {
            addCriterion("arrival_time_model in", values, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelNotIn(List<String> values) {
            addCriterion("arrival_time_model not in", values, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelBetween(String value1, String value2) {
            addCriterion("arrival_time_model between", value1, value2, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelNotBetween(String value1, String value2) {
            addCriterion("arrival_time_model not between", value1, value2, "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysIsNull() {
            addCriterion("arrival_days is null");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysIsNotNull() {
            addCriterion("arrival_days is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysEqualTo(Integer value) {
            addCriterion("arrival_days =", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysNotEqualTo(Integer value) {
            addCriterion("arrival_days <>", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysGreaterThan(Integer value) {
            addCriterion("arrival_days >", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("arrival_days >=", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysLessThan(Integer value) {
            addCriterion("arrival_days <", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysLessThanOrEqualTo(Integer value) {
            addCriterion("arrival_days <=", value, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("arrival_days <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andArrivalDaysIn(List<Integer> values) {
            addCriterion("arrival_days in", values, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysNotIn(List<Integer> values) {
            addCriterion("arrival_days not in", values, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysBetween(Integer value1, Integer value2) {
            addCriterion("arrival_days between", value1, value2, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andArrivalDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("arrival_days not between", value1, value2, "arrivalDays");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIsNull() {
            addCriterion("organization_code is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIsNotNull() {
            addCriterion("organization_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeEqualTo(String value) {
            addCriterion("organization_code =", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotEqualTo(String value) {
            addCriterion("organization_code <>", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThan(String value) {
            addCriterion("organization_code >", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("organization_code >=", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThan(String value) {
            addCriterion("organization_code <", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThanOrEqualTo(String value) {
            addCriterion("organization_code <=", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("organization_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLike(String value) {
            addCriterion("organization_code like", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotLike(String value) {
            addCriterion("organization_code not like", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIn(List<String> values) {
            addCriterion("organization_code in", values, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotIn(List<String> values) {
            addCriterion("organization_code not in", values, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeBetween(String value1, String value2) {
            addCriterion("organization_code between", value1, value2, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotBetween(String value1, String value2) {
            addCriterion("organization_code not between", value1, value2, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNull() {
            addCriterion("item_type is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNotNull() {
            addCriterion("item_type is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualTo(String value) {
            addCriterion("item_type =", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualTo(String value) {
            addCriterion("item_type <>", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThan(String value) {
            addCriterion("item_type >", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("item_type >=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThan(String value) {
            addCriterion("item_type <", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualTo(String value) {
            addCriterion("item_type <=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("item_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andItemTypeLike(String value) {
            addCriterion("item_type like", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotLike(String value) {
            addCriterion("item_type not like", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIn(List<String> values) {
            addCriterion("item_type in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotIn(List<String> values) {
            addCriterion("item_type not in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeBetween(String value1, String value2) {
            addCriterion("item_type between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotBetween(String value1, String value2) {
            addCriterion("item_type not between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andBudgetIdIsNull() {
            addCriterion("budget_id is null");
            return (Criteria) this;
        }

        public Criteria andBudgetIdIsNotNull() {
            addCriterion("budget_id is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetIdEqualTo(String value) {
            addCriterion("budget_id =", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdNotEqualTo(String value) {
            addCriterion("budget_id <>", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdGreaterThan(String value) {
            addCriterion("budget_id >", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdGreaterThanOrEqualTo(String value) {
            addCriterion("budget_id >=", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdLessThan(String value) {
            addCriterion("budget_id <", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdLessThanOrEqualTo(String value) {
            addCriterion("budget_id <=", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetIdLike(String value) {
            addCriterion("budget_id like", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdNotLike(String value) {
            addCriterion("budget_id not like", value, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdIn(List<String> values) {
            addCriterion("budget_id in", values, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdNotIn(List<String> values) {
            addCriterion("budget_id not in", values, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdBetween(String value1, String value2) {
            addCriterion("budget_id between", value1, value2, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetIdNotBetween(String value1, String value2) {
            addCriterion("budget_id not between", value1, value2, "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetYearIsNull() {
            addCriterion("budget_year is null");
            return (Criteria) this;
        }

        public Criteria andBudgetYearIsNotNull() {
            addCriterion("budget_year is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetYearEqualTo(String value) {
            addCriterion("budget_year =", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearNotEqualTo(String value) {
            addCriterion("budget_year <>", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearGreaterThan(String value) {
            addCriterion("budget_year >", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearGreaterThanOrEqualTo(String value) {
            addCriterion("budget_year >=", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearLessThan(String value) {
            addCriterion("budget_year <", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearLessThanOrEqualTo(String value) {
            addCriterion("budget_year <=", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("budget_year <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBudgetYearLike(String value) {
            addCriterion("budget_year like", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearNotLike(String value) {
            addCriterion("budget_year not like", value, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearIn(List<String> values) {
            addCriterion("budget_year in", values, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearNotIn(List<String> values) {
            addCriterion("budget_year not in", values, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearBetween(String value1, String value2) {
            addCriterion("budget_year between", value1, value2, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andBudgetYearNotBetween(String value1, String value2) {
            addCriterion("budget_year not between", value1, value2, "budgetYear");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("activity_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterIsNull() {
            addCriterion("cost_center is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIsNotNull() {
            addCriterion("cost_center is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterEqualTo(String value) {
            addCriterion("cost_center =", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNotEqualTo(String value) {
            addCriterion("cost_center <>", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThan(String value) {
            addCriterion("cost_center >", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center >=", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThan(String value) {
            addCriterion("cost_center <", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanOrEqualTo(String value) {
            addCriterion("cost_center <=", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_center <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLike(String value) {
            addCriterion("cost_center like", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotLike(String value) {
            addCriterion("cost_center not like", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterIn(List<String> values) {
            addCriterion("cost_center in", values, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotIn(List<String> values) {
            addCriterion("cost_center not in", values, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterBetween(String value1, String value2) {
            addCriterion("cost_center between", value1, value2, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotBetween(String value1, String value2) {
            addCriterion("cost_center not between", value1, value2, "costCenter");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountIsNull() {
            addCriterion("expense_account is null");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountIsNotNull() {
            addCriterion("expense_account is not null");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountEqualTo(String value) {
            addCriterion("expense_account =", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountNotEqualTo(String value) {
            addCriterion("expense_account <>", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountGreaterThan(String value) {
            addCriterion("expense_account >", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountGreaterThanOrEqualTo(String value) {
            addCriterion("expense_account >=", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLessThan(String value) {
            addCriterion("expense_account <", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLessThanOrEqualTo(String value) {
            addCriterion("expense_account <=", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("expense_account <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLike(String value) {
            addCriterion("expense_account like", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountNotLike(String value) {
            addCriterion("expense_account not like", value, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountIn(List<String> values) {
            addCriterion("expense_account in", values, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountNotIn(List<String> values) {
            addCriterion("expense_account not in", values, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountBetween(String value1, String value2) {
            addCriterion("expense_account between", value1, value2, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountNotBetween(String value1, String value2) {
            addCriterion("expense_account not between", value1, value2, "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andCostSubjectIsNull() {
            addCriterion("cost_subject is null");
            return (Criteria) this;
        }

        public Criteria andCostSubjectIsNotNull() {
            addCriterion("cost_subject is not null");
            return (Criteria) this;
        }

        public Criteria andCostSubjectEqualTo(String value) {
            addCriterion("cost_subject =", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectNotEqualTo(String value) {
            addCriterion("cost_subject <>", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectGreaterThan(String value) {
            addCriterion("cost_subject >", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("cost_subject >=", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectLessThan(String value) {
            addCriterion("cost_subject <", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectLessThanOrEqualTo(String value) {
            addCriterion("cost_subject <=", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("cost_subject <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostSubjectLike(String value) {
            addCriterion("cost_subject like", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectNotLike(String value) {
            addCriterion("cost_subject not like", value, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectIn(List<String> values) {
            addCriterion("cost_subject in", values, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectNotIn(List<String> values) {
            addCriterion("cost_subject not in", values, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectBetween(String value1, String value2) {
            addCriterion("cost_subject between", value1, value2, "costSubject");
            return (Criteria) this;
        }

        public Criteria andCostSubjectNotBetween(String value1, String value2) {
            addCriterion("cost_subject not between", value1, value2, "costSubject");
            return (Criteria) this;
        }

        public Criteria andManageActivityIsNull() {
            addCriterion("manage_activity is null");
            return (Criteria) this;
        }

        public Criteria andManageActivityIsNotNull() {
            addCriterion("manage_activity is not null");
            return (Criteria) this;
        }

        public Criteria andManageActivityEqualTo(String value) {
            addCriterion("manage_activity =", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityNotEqualTo(String value) {
            addCriterion("manage_activity <>", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityGreaterThan(String value) {
            addCriterion("manage_activity >", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityGreaterThanOrEqualTo(String value) {
            addCriterion("manage_activity >=", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityLessThan(String value) {
            addCriterion("manage_activity <", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityLessThanOrEqualTo(String value) {
            addCriterion("manage_activity <=", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_activity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageActivityLike(String value) {
            addCriterion("manage_activity like", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityNotLike(String value) {
            addCriterion("manage_activity not like", value, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityIn(List<String> values) {
            addCriterion("manage_activity in", values, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityNotIn(List<String> values) {
            addCriterion("manage_activity not in", values, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityBetween(String value1, String value2) {
            addCriterion("manage_activity between", value1, value2, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageActivityNotBetween(String value1, String value2) {
            addCriterion("manage_activity not between", value1, value2, "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageMarketIsNull() {
            addCriterion("manage_market is null");
            return (Criteria) this;
        }

        public Criteria andManageMarketIsNotNull() {
            addCriterion("manage_market is not null");
            return (Criteria) this;
        }

        public Criteria andManageMarketEqualTo(String value) {
            addCriterion("manage_market =", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketNotEqualTo(String value) {
            addCriterion("manage_market <>", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketGreaterThan(String value) {
            addCriterion("manage_market >", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketGreaterThanOrEqualTo(String value) {
            addCriterion("manage_market >=", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketLessThan(String value) {
            addCriterion("manage_market <", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketLessThanOrEqualTo(String value) {
            addCriterion("manage_market <=", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_market <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageMarketLike(String value) {
            addCriterion("manage_market like", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketNotLike(String value) {
            addCriterion("manage_market not like", value, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketIn(List<String> values) {
            addCriterion("manage_market in", values, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketNotIn(List<String> values) {
            addCriterion("manage_market not in", values, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketBetween(String value1, String value2) {
            addCriterion("manage_market between", value1, value2, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageMarketNotBetween(String value1, String value2) {
            addCriterion("manage_market not between", value1, value2, "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageProductIsNull() {
            addCriterion("manage_product is null");
            return (Criteria) this;
        }

        public Criteria andManageProductIsNotNull() {
            addCriterion("manage_product is not null");
            return (Criteria) this;
        }

        public Criteria andManageProductEqualTo(String value) {
            addCriterion("manage_product =", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductNotEqualTo(String value) {
            addCriterion("manage_product <>", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductGreaterThan(String value) {
            addCriterion("manage_product >", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductGreaterThanOrEqualTo(String value) {
            addCriterion("manage_product >=", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductLessThan(String value) {
            addCriterion("manage_product <", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductLessThanOrEqualTo(String value) {
            addCriterion("manage_product <=", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("manage_product <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageProductLike(String value) {
            addCriterion("manage_product like", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductNotLike(String value) {
            addCriterion("manage_product not like", value, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductIn(List<String> values) {
            addCriterion("manage_product in", values, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductNotIn(List<String> values) {
            addCriterion("manage_product not in", values, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductBetween(String value1, String value2) {
            addCriterion("manage_product between", value1, value2, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andManageProductNotBetween(String value1, String value2) {
            addCriterion("manage_product not between", value1, value2, "manageProduct");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProvinceContractInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andContractTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_type) like", value.toUpperCase(), "contractType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(province_code) like", value.toUpperCase(), "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeLikeInsensitive(String value) {
            addCriterion("upper(city_code) like", value.toUpperCase(), "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLikeInsensitive(String value) {
            addCriterion("upper(internet_contract_code) like", value.toUpperCase(), "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andDeptCodeLikeInsensitive(String value) {
            addCriterion("upper(dept_code) like", value.toUpperCase(), "deptCode");
            return (Criteria) this;
        }

        public Criteria andDeptNameLikeInsensitive(String value) {
            addCriterion("upper(dept_name) like", value.toUpperCase(), "deptName");
            return (Criteria) this;
        }

        public Criteria andCreatedIdLikeInsensitive(String value) {
            addCriterion("upper(created_id) like", value.toUpperCase(), "createdId");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLikeInsensitive(String value) {
            addCriterion("upper(created_name) like", value.toUpperCase(), "createdName");
            return (Criteria) this;
        }

        public Criteria andMtlTypeCodeLikeInsensitive(String value) {
            addCriterion("upper(mtl_type_code) like", value.toUpperCase(), "mtlTypeCode");
            return (Criteria) this;
        }

        public Criteria andSmallItemLikeInsensitive(String value) {
            addCriterion("upper(small_item) like", value.toUpperCase(), "smallItem");
            return (Criteria) this;
        }

        public Criteria andContractCodeLikeInsensitive(String value) {
            addCriterion("upper(contract_code) like", value.toUpperCase(), "contractCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLikeInsensitive(String value) {
            addCriterion("upper(vendor_code) like", value.toUpperCase(), "vendorCode");
            return (Criteria) this;
        }

        public Criteria andReqTypeLikeInsensitive(String value) {
            addCriterion("upper(req_type) like", value.toUpperCase(), "reqType");
            return (Criteria) this;
        }

        public Criteria andReqSecondTypeLikeInsensitive(String value) {
            addCriterion("upper(req_second_type) like", value.toUpperCase(), "reqSecondType");
            return (Criteria) this;
        }

        public Criteria andRcvUserNumLikeInsensitive(String value) {
            addCriterion("upper(rcv_user_num) like", value.toUpperCase(), "rcvUserNum");
            return (Criteria) this;
        }

        public Criteria andRcvUserLikeInsensitive(String value) {
            addCriterion("upper(rcv_user) like", value.toUpperCase(), "rcvUser");
            return (Criteria) this;
        }

        public Criteria andRcvContactPhoneLikeInsensitive(String value) {
            addCriterion("upper(rcv_contact_phone) like", value.toUpperCase(), "rcvContactPhone");
            return (Criteria) this;
        }

        public Criteria andRcvSiteAddressLikeInsensitive(String value) {
            addCriterion("upper(rcv_site_address) like", value.toUpperCase(), "rcvSiteAddress");
            return (Criteria) this;
        }

        public Criteria andMisBodyLikeInsensitive(String value) {
            addCriterion("upper(mis_body) like", value.toUpperCase(), "misBody");
            return (Criteria) this;
        }

        public Criteria andOrgIdLikeInsensitive(String value) {
            addCriterion("upper(org_id) like", value.toUpperCase(), "orgId");
            return (Criteria) this;
        }

        public Criteria andExpTypeLikeInsensitive(String value) {
            addCriterion("upper(exp_type) like", value.toUpperCase(), "expType");
            return (Criteria) this;
        }

        public Criteria andReimbursementModeLikeInsensitive(String value) {
            addCriterion("upper(reimbursement_mode) like", value.toUpperCase(), "reimbursementMode");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeModelLikeInsensitive(String value) {
            addCriterion("upper(arrival_time_model) like", value.toUpperCase(), "arrivalTimeModel");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLikeInsensitive(String value) {
            addCriterion("upper(organization_code) like", value.toUpperCase(), "organizationCode");
            return (Criteria) this;
        }

        public Criteria andItemTypeLikeInsensitive(String value) {
            addCriterion("upper(item_type) like", value.toUpperCase(), "itemType");
            return (Criteria) this;
        }

        public Criteria andBudgetIdLikeInsensitive(String value) {
            addCriterion("upper(budget_id) like", value.toUpperCase(), "budgetId");
            return (Criteria) this;
        }

        public Criteria andBudgetYearLikeInsensitive(String value) {
            addCriterion("upper(budget_year) like", value.toUpperCase(), "budgetYear");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLikeInsensitive(String value) {
            addCriterion("upper(activity_code) like", value.toUpperCase(), "activityCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterLikeInsensitive(String value) {
            addCriterion("upper(cost_center) like", value.toUpperCase(), "costCenter");
            return (Criteria) this;
        }

        public Criteria andExpenseAccountLikeInsensitive(String value) {
            addCriterion("upper(expense_account) like", value.toUpperCase(), "expenseAccount");
            return (Criteria) this;
        }

        public Criteria andCostSubjectLikeInsensitive(String value) {
            addCriterion("upper(cost_subject) like", value.toUpperCase(), "costSubject");
            return (Criteria) this;
        }

        public Criteria andManageActivityLikeInsensitive(String value) {
            addCriterion("upper(manage_activity) like", value.toUpperCase(), "manageActivity");
            return (Criteria) this;
        }

        public Criteria andManageMarketLikeInsensitive(String value) {
            addCriterion("upper(manage_market) like", value.toUpperCase(), "manageMarket");
            return (Criteria) this;
        }

        public Criteria andManageProductLikeInsensitive(String value) {
            addCriterion("upper(manage_product) like", value.toUpperCase(), "manageProduct");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 17 11:12:31 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private ProvinceContractInfoNewExample example;

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        protected Criteria(ProvinceContractInfoNewExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public ProvinceContractInfoNewExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Mar 17 11:12:31 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNewExample example);
    }
}