package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 * @description 导出线上管理采购订单省侧对账单表头实体类
 */
@Data
public class ExportOnlineProvinceBillTitleDTO {

    /**
     * 合同编码
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 订单归属省份
     */
    private String provinceName;

    /**
     * 合同含税金额
     */
    private BigDecimal amountIncludingTax;

    /**
     * 合同失效日期
     */
    private Date contractEndDate;

    /**
     * 订单总金额
     */
    private Long settlePrice;

    /**
     * 订单交易完成时间
     */
    private Date orderSuccessTime;

    /**
     * 省侧合同编码
     */
    private String provinceContractCode;

}
