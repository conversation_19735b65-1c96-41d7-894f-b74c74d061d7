package com.chinamobile.iot.sc.task;

import com.chinamobile.iot.sc.enums.OrderTypeEnum;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample;
import com.chinamobile.iot.sc.pojo.param.OsOrderToPurchaseOrderParam;
import com.chinamobile.iot.sc.service.OnlineSettlementOsOrderService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@EnableScheduling

@Slf4j
public class OnlineOsOrderToPurchaseOrderTask {

    @Resource
    private OnlineSettlementOsOrderService onlineSettlementOsOrderService;

    /**
     * 每月一号一点执行一次 把上月的线上交易完成结算订单生成采购订单
     */
    @Scheduled(cron = "0 0 1 1 * ?")
//    @Scheduled(cron = "0 0/5 * * * ?")
    public void work() {
        log.info("上月的线上交易完成结算订单生成采购订单定时任务开始");
        Date date = new Date();
        Date lastMonth = DateUtils.addMonth(date, -1);
//        Date lastMonth = new Date();
        Date monthBegin = DateTimeUtil.getMonthBegin(lastMonth);
        Date monthEnd = DateTimeUtil.getMonthEnd(lastMonth);

        OnlineSettlementOsOrderExample onlineSettlementOsOrderExample = new OnlineSettlementOsOrderExample();
        onlineSettlementOsOrderExample.createCriteria()
                .andOrderSuccessTimeBetween(monthBegin,monthEnd)
                .andPurchaseStatusEqualTo(0)
                .andOrderTypeEqualTo(OrderTypeEnum.normal.code);
        List<OnlineSettlementOsOrder> onlineSettlementOsOrderList
                = onlineSettlementOsOrderService.getOnlineSettlementOsOrderByNeed(onlineSettlementOsOrderExample);
        if (CollectionUtils.isNotEmpty(onlineSettlementOsOrderList)){
            List<String> orderIdList = onlineSettlementOsOrderList.stream()
                    .map(OnlineSettlementOsOrder::getOrderId)
                    .collect(Collectors.toList());
            log.info("定时任务可以用于生成采购订单的订单id有:{}",orderIdList.stream().collect(Collectors.joining(", ")));
            OsOrderToPurchaseOrderParam osOrderToPurchaseOrderParam = new OsOrderToPurchaseOrderParam();
            osOrderToPurchaseOrderParam.setOrderIdList(orderIdList);
            onlineSettlementOsOrderService.generateOsOrderToPurchaseOrder(osOrderToPurchaseOrderParam,"1",null);
        }
    }

}
