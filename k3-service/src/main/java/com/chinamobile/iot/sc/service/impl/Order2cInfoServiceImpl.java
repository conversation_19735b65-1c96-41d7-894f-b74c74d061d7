package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.pojo.entity.Order2cInfo;
import com.chinamobile.iot.sc.pojo.entity.Order2cInfoExample;
import com.chinamobile.iot.sc.service.Order2cInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/10
 * @description 订单service实现类
 */
@Service
public class Order2cInfoServiceImpl implements Order2cInfoService {

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Override
    public void updateOrder2cInfoByExample(Order2cInfo order2cInfo,
                                           Order2cInfoExample example) {
        order2cInfoMapper.updateByExampleSelective(order2cInfo,example);
    }

    @Override
    public List<Order2cInfo> listOrder2cInfo(Order2cInfoExample example) {
        return order2cInfoMapper.selectByExample(example);
    }
}
