package com.chinamobile.iot.sc.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/2 09:19
 * @description 批量导入K3合同失败信息
 */
@Data
@AllArgsConstructor
public class OuterContractMaterialImportErrorVO {

    /**
     * 合同编号
     */
    @ExcelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 物料编码
     */
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String reason;

    /**
     * Excel数据行号，当合同编号为空时，用于提示录入失败原因
     */
    @ExcelProperty(value = "导入Excel所在行号")
    private Integer rowNum;
}
