package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/18
 * @description 线上结算采购订单参数类
 */
@Data
public class OnlineSettlementPurchaseOrderParam extends BasePageQuery {

    /**
     * SCM采购订单编号
     */
    private String scmOrderNum;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 省侧合同编码
     */
    private String contractCode;

    /**
     * 省侧合同类型
     */
    private String contractType;

    /**
     * 订单收入归属省份
     */
    private String beId;

    /**
     * 订单收入归属地市
     */
    private String location;

    /**
     * 同步状态0--未同步 1--同步
     */
    private Integer synStatus;

    /**
     * tab栏 1--按省结算 2--按地市结算
     */
    @NotNull(message = "按省结算或按地市结算tab栏选择不能为空")
    private Integer tabIndex;

    /**
     * 当前用户所在省份编码
     */
    private String userBeId;

    /**
     * 当前用户所在地市编码
     */
    private String userLocation;
}
