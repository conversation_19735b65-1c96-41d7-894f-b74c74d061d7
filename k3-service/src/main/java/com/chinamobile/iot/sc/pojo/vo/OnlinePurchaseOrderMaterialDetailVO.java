package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/19
 * @description 采购订单详情展示类
 */
@Data
public class OnlinePurchaseOrderMaterialDetailVO {

    /**
     * 省物料信息主键id
     */
    private String provinceMaterialInfoId;

    /**
     * 物联网公司合同编码
     */
    private String internetContractCode;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 主物料编码
     */
    private String materialCode;

    /**
     * 主物料描述
     */
    private String materialName;

    /**
     * 扩展物料编码  8位集团物料+省代码+3位顺序码
     */
    private String attr2;

    /**
     * 扩展物料描述
     */
    private String attr3;

    /**
     * 单位(项)
     */
    private String unit;

    /**
     * 需求数量
     */
    private Integer needQuantity;

    /**
     * 不含税单价
     */
    private String unitPrice;

    private BigDecimal unitPriceDec;


    /**
     * 税码 固定值
     */
    private String taxCode;

    /**
     * 税率 固定值
     */
    private BigDecimal taxRate;

    /**
     * 行不含税金额
     */
    private Long unitTotalPrice;

    private BigDecimal unitTotalPriceDec;

    /**
     * 行含税金额
     */
    private String taxInclusivePrice;

    /**
     * 行含税单价金额
     */
    private Long taxInclusiveTotalPrice;

    private BigDecimal taxInclusiveTotalPriceDec;

    /**
     * 行税额
     */
    private BigDecimal taxTotalPrice;

    /**
     * 物联网物料编码
     */
    private String internetMaterialCode;

    /**
     * 物联网物料名称
     */
    private String internetMaterialName;

    /**
     * 要求到货天数
     */
    private Integer arrivalDays;

    /**
     * 发运组织代码
     */
    private String organizationCode;

    /**
     * 分配类型 INVENTORY：库存 EXPENSE：费用
     */
    private String itemType;

    private String itemTypeName;

    /**
     * 是否直发  N/Y
     */
    private String sdProjectFlag;

    /**
     * 预算组合ID "当“开支类型”为“成本类”时必\n填"
     */
    private String budgetId;

    /**
     * 预算年份 "当“开支类型”为“成本类”时必填"
     */
    private String budgetYear;

    /**
     * 业务活动编码  "当“开支类型”为“成本类”时必填默认预算组织中的业务活动"
     */
    private String activityCode;

    /**
     * 管会业务活动 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageActivity;

    /**
     * 管会市场维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageMarket;

    /**
     * 管会产品维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageProduct;

    /**
     * 接收人员工编号  新员工编号，11位
     */
    private String rcvUserNum;

    /**
     * 接收人姓名
     */
    private String rcvUser;

    /**
     * 接收人电话
     */
    private String rcvContactPhone;

    /**
     * 接收人地址
     */
    private String rcvSiteAddress;

}
