package com.chinamobile.iot.sc.service.soa.orderUpdate;


import com.chinamobile.iot.sc.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@Service
@javax.jws.WebService
(serviceName = "OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrv", 
portName = "OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvPort", 
targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrv", 
endpointInterface = "com.cmcc.soa.osb_sscm_zx_hq_importorderupdateorcancelsrv.OSBSSCMZXHQImportOrderUpdateOrCancelSrv")
public class OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvPortImpl implements OSBSSCMZXHQImportOrderUpdateOrCancelSrv {

    @Resource
    private OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvClient client;

    public OutputParameters process(InputParameters payload) {

        OutputParameters _return = new OutputParameters();
        try {
            // TODO 非空校验
            // .....
            // TODO 服务业务逻辑
            // .....
            // TODO 输出结果
            _return.setBIZSERVICEFLAG("TRUE");
            _return.setBIZRETURNMESSAGE("成功");
            return _return;
        } catch (java.lang.Exception ex) {
            // TODO 异常处理
            _return.setBIZRETURNCODE("BIZ-04");
            _return.setBIZSERVICEFLAG("FALSE");
            _return.setBIZRETURNMESSAGE("服务调用失败" + ex.getMessage());

        }
        return _return;
    }

    public void testOrderUpdate(){
        InputParameters request = new InputParameters();
        MSGHEADER msgHeader = client.buildMSGHEADER();
        request.setMSGHEADER(msgHeader);

        INPUTCOLLECTION inputcollection = new INPUTCOLLECTION();
        INPUTCOLLECTIONITEM inputcollectionitem = new INPUTCOLLECTIONITEM();
        inputcollectionitem.setDOFLAG("Y");
        inputcollectionitem.setPRIKEY("8191366");
        inputcollectionitem.setORGID(new BigDecimal("146"));
        inputcollectionitem.setSCMPONUM("WT-QY-20231114-0096");
        inputcollectionitem.setACTION("U");
        inputcollectionitem.setAGENTNUM("21230234");
        inputcollectionitem.setDESCRIPTION("测试单据");
        inputcollectionitem.setTAXSUM(new BigDecimal("194.17"));
        inputcollectionitem.setPOAMOUNT(new BigDecimal("3430.33"));
        inputcollectionitem.setEXCLUDINGTAXTOTAL(new BigDecimal("3236.16"));
        inputcollectionitem.setINPUTEXT("{\"FILE_TYPE_05\":\"GD_MFT_SSCM_ZX_HQ_00005_20250120093424044.zip\",\"FILE_TYPE_06\":\"GD_MFT_SSCM_ZX_HQ_00005_20250120093424044_06.zip\"}");

        Integer lineNum = 1;
        POLINEINFO poline = new POLINEINFO();
        POLINEINFOITEM polineitem = new POLINEINFOITEM();
        polineitem.setACTION("U");
        polineitem.setPRIKEY("12016040");
        polineitem.setREQULINEID(new BigDecimal("1"));
        polineitem.setITEMCODE("10462187");
        polineitem.setUNIT("项");
        polineitem.setQUANTITY(new BigDecimal("323616"));
        polineitem.setUNITPRICE(new BigDecimal("0.01"));
        polineitem.setTAXAMOUNT(new BigDecimal("194.17"));
        polineitem.setTAXINCLUDEDTOTAL(new BigDecimal("3430.33"));
        polineitem.setRCVUSER("冯*文");
        polineitem.setRCVUSERNUM("21230234");
        polineitem.setARRIVALDAYS(new BigDecimal("30"));
        polineitem.setATTR2("10462187GD002");
        polineitem.setATTR3("1行通信工程勘察设计(一采)_折扣47%");

        poline.getPOLINEINFOITEM().add(polineitem);
        inputcollectionitem.setPOLINEINFO(poline);
        inputcollection.getINPUTCOLLECTIONITEM().add(inputcollectionitem);
        log.info("testOrderUpdate request = {}",request);
        request.setINPUTCOLLECTION(inputcollection);

        OSBSSCMZXHQImportOrderUpdateOrCancelSrv port = client.buildServiceClient();
        OutputParameters response = port.process(request);
        log.info("testOrderUpdate response = {}",response);

        if(!"TRUE".equals(response.getESBFLAG())){
            throw new BusinessException("500", response.getESBRETURNMESSAGE());
        }
        if(!"TRUE".equals(response.getBIZSERVICEFLAG())){
            throw new BusinessException("500", response.getBIZRETURNMESSAGE());
        }
        // 输出服务调用结果
        System.out.println(response.getBIZSERVICEFLAG());
        System.out.println(response.getBIZRETURNMESSAGE());

        if(response.getRESPONSECOLLECTION()!=null
            && CollectionUtils.isNotEmpty(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM())){

        }

    }


}
