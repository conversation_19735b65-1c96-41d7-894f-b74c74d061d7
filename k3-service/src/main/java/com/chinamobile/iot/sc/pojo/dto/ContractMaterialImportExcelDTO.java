package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/7 09:06
 * @description TODO
 */
@Data
public class ContractMaterialImportExcelDTO {

    @ExcelProperty(value="合同编号",index=0)
    private String contractNumber;

   /* @ExcelProperty(value="服务包名称",index=1)
    private String servicePackName;*/

    @ExcelProperty(value="物料编码",index=1)
    private String materialNumber;

    @ExcelProperty(value="结算单价（元）",index=2)
    private String settlePrice;
}
