
package com.chinamobile.iot.sc.service.soa.orderReturn;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for INPUTCOLLECTION_ITEM complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="INPUTCOLLECTION_ITEM"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="PRI_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ORG_ID" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="SOURCE_REQ_DOC" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DESCRIPTION" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OPR_EMPLOYEE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RETURN_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RETURN_DATE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BACK_REASON" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="INPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "INPUTCOLLECTION_ITEM", propOrder = {
    "prikey",
    "orgid",
    "sourcereqdoc",
    "description",
    "deptcode",
    "opremployeecode",
    "returnname",
    "returndate",
    "backreason",
    "inputext"
})
public class INPUTCOLLECTIONITEM {

    @XmlElement(name = "PRI_KEY", required = true)
    protected String prikey;
    @XmlElement(name = "ORG_ID", required = true)
    protected BigDecimal orgid;
    @XmlElement(name = "SOURCE_REQ_DOC", required = true)
    protected String sourcereqdoc;
    @XmlElement(name = "DESCRIPTION", required = true, nillable = true)
    protected String description;
    @XmlElement(name = "DEPT_CODE", required = true, nillable = true)
    protected String deptcode;
    @XmlElement(name = "OPR_EMPLOYEE_CODE", required = true, nillable = true)
    protected String opremployeecode;
    @XmlElement(name = "RETURN_NAME", required = true)
    protected String returnname;
    @XmlElement(name = "RETURN_DATE", required = true)
    protected String returndate;
    @XmlElement(name = "BACK_REASON", required = true)
    protected String backreason;
    @XmlElement(name = "INPUT_EXT", required = true, nillable = true)
    protected String inputext;

    /**
     * Gets the value of the prikey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPRIKEY() {
        return prikey;
    }

    /**
     * Sets the value of the prikey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPRIKEY(String value) {
        this.prikey = value;
    }

    /**
     * Gets the value of the orgid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getORGID() {
        return orgid;
    }

    /**
     * Sets the value of the orgid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setORGID(BigDecimal value) {
        this.orgid = value;
    }

    /**
     * Gets the value of the sourcereqdoc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEREQDOC() {
        return sourcereqdoc;
    }

    /**
     * Sets the value of the sourcereqdoc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEREQDOC(String value) {
        this.sourcereqdoc = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDESCRIPTION() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDESCRIPTION(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the deptcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTCODE() {
        return deptcode;
    }

    /**
     * Sets the value of the deptcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTCODE(String value) {
        this.deptcode = value;
    }

    /**
     * Gets the value of the opremployeecode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOPREMPLOYEECODE() {
        return opremployeecode;
    }

    /**
     * Sets the value of the opremployeecode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOPREMPLOYEECODE(String value) {
        this.opremployeecode = value;
    }

    /**
     * Gets the value of the returnname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRETURNNAME() {
        return returnname;
    }

    /**
     * Sets the value of the returnname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRETURNNAME(String value) {
        this.returnname = value;
    }

    /**
     * Gets the value of the returndate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRETURNDATE() {
        return returndate;
    }

    /**
     * Sets the value of the returndate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRETURNDATE(String value) {
        this.returndate = value;
    }

    /**
     * Gets the value of the backreason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBACKREASON() {
        return backreason;
    }

    /**
     * Sets the value of the backreason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBACKREASON(String value) {
        this.backreason = value;
    }

    /**
     * Gets the value of the inputext property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getINPUTEXT() {
        return inputext;
    }

    /**
     * Sets the value of the inputext property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setINPUTEXT(String value) {
        this.inputext = value;
    }

}
