package com.chinamobile.iot.sc.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/10
 * @description k3同步数据库查询
 */
@Data
public class K3SynQuery {

    /**
     * 同步年份
     */
    private String saleDate;

    /**
     * 同步合同编号
     */
    //private List<String> contractNumList;

    private List<String> idList;
}
