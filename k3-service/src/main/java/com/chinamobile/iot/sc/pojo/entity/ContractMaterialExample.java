package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class ContractMaterialExample {
    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterialExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterialExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterialExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ContractMaterialExample example = new ContractMaterialExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterialExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterialExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractNumberIsNull() {
            addCriterion("contract_number is null");
            return (Criteria) this;
        }

        public Criteria andContractNumberIsNotNull() {
            addCriterion("contract_number is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumberEqualTo(String value) {
            addCriterion("contract_number =", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberNotEqualTo(String value) {
            addCriterion("contract_number <>", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThan(String value) {
            addCriterion("contract_number >", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThanOrEqualTo(String value) {
            addCriterion("contract_number >=", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThan(String value) {
            addCriterion("contract_number <", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThanOrEqualTo(String value) {
            addCriterion("contract_number <=", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("contract_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumberLike(String value) {
            addCriterion("contract_number like", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotLike(String value) {
            addCriterion("contract_number not like", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberIn(List<String> values) {
            addCriterion("contract_number in", values, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotIn(List<String> values) {
            addCriterion("contract_number not in", values, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberBetween(String value1, String value2) {
            addCriterion("contract_number between", value1, value2, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotBetween(String value1, String value2) {
            addCriterion("contract_number not between", value1, value2, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberIsNull() {
            addCriterion("material_number is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberIsNotNull() {
            addCriterion("material_number is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberEqualTo(String value) {
            addCriterion("material_number =", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberNotEqualTo(String value) {
            addCriterion("material_number <>", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberGreaterThan(String value) {
            addCriterion("material_number >", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberGreaterThanOrEqualTo(String value) {
            addCriterion("material_number >=", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLessThan(String value) {
            addCriterion("material_number <", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLessThanOrEqualTo(String value) {
            addCriterion("material_number <=", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLike(String value) {
            addCriterion("material_number like", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberNotLike(String value) {
            addCriterion("material_number not like", value, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberIn(List<String> values) {
            addCriterion("material_number in", values, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberNotIn(List<String> values) {
            addCriterion("material_number not in", values, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberBetween(String value1, String value2) {
            addCriterion("material_number between", value1, value2, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberNotBetween(String value1, String value2) {
            addCriterion("material_number not between", value1, value2, "materialNumber");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNull() {
            addCriterion("owner_department is null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNotNull() {
            addCriterion("owner_department is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentEqualTo(String value) {
            addCriterion("owner_department =", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotEqualTo(String value) {
            addCriterion("owner_department <>", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThan(String value) {
            addCriterion("owner_department >", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("owner_department >=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThan(String value) {
            addCriterion("owner_department <", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThanOrEqualTo(String value) {
            addCriterion("owner_department <=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("owner_department <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLike(String value) {
            addCriterion("owner_department like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotLike(String value) {
            addCriterion("owner_department not like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIn(List<String> values) {
            addCriterion("owner_department in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotIn(List<String> values) {
            addCriterion("owner_department not in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentBetween(String value1, String value2) {
            addCriterion("owner_department between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotBetween(String value1, String value2) {
            addCriterion("owner_department not between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("product_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNull() {
            addCriterion("material_unit is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNotNull() {
            addCriterion("material_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualTo(String value) {
            addCriterion("material_unit =", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualTo(String value) {
            addCriterion("material_unit <>", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThan(String value) {
            addCriterion("material_unit >", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualTo(String value) {
            addCriterion("material_unit >=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThan(String value) {
            addCriterion("material_unit <", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualTo(String value) {
            addCriterion("material_unit <=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLike(String value) {
            addCriterion("material_unit like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotLike(String value) {
            addCriterion("material_unit not like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIn(List<String> values) {
            addCriterion("material_unit in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotIn(List<String> values) {
            addCriterion("material_unit not in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitBetween(String value1, String value2) {
            addCriterion("material_unit between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotBetween(String value1, String value2) {
            addCriterion("material_unit not between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyIsNull() {
            addCriterion("plan_strategy is null");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyIsNotNull() {
            addCriterion("plan_strategy is not null");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyEqualTo(String value) {
            addCriterion("plan_strategy =", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyNotEqualTo(String value) {
            addCriterion("plan_strategy <>", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyGreaterThan(String value) {
            addCriterion("plan_strategy >", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyGreaterThanOrEqualTo(String value) {
            addCriterion("plan_strategy >=", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLessThan(String value) {
            addCriterion("plan_strategy <", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLessThanOrEqualTo(String value) {
            addCriterion("plan_strategy <=", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("plan_strategy <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLike(String value) {
            addCriterion("plan_strategy like", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyNotLike(String value) {
            addCriterion("plan_strategy not like", value, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyIn(List<String> values) {
            addCriterion("plan_strategy in", values, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyNotIn(List<String> values) {
            addCriterion("plan_strategy not in", values, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyBetween(String value1, String value2) {
            addCriterion("plan_strategy between", value1, value2, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyNotBetween(String value1, String value2) {
            addCriterion("plan_strategy not between", value1, value2, "planStrategy");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNull() {
            addCriterion("settle_price is null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNotNull() {
            addCriterion("settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualTo(Long value) {
            addCriterion("settle_price =", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualTo(Long value) {
            addCriterion("settle_price <>", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThan(Long value) {
            addCriterion("settle_price >", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("settle_price >=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThan(Long value) {
            addCriterion("settle_price <", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("settle_price <=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceIn(List<Long> values) {
            addCriterion("settle_price in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotIn(List<Long> values) {
            addCriterion("settle_price not in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceBetween(Long value1, Long value2) {
            addCriterion("settle_price between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("settle_price not between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIsNull() {
            addCriterion("service_pack_id is null");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIsNotNull() {
            addCriterion("service_pack_id is not null");
            return (Criteria) this;
        }

        public Criteria andServicePackIdEqualTo(String value) {
            addCriterion("service_pack_id =", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotEqualTo(String value) {
            addCriterion("service_pack_id <>", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThan(String value) {
            addCriterion("service_pack_id >", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanOrEqualTo(String value) {
            addCriterion("service_pack_id >=", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThan(String value) {
            addCriterion("service_pack_id <", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanOrEqualTo(String value) {
            addCriterion("service_pack_id <=", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLike(String value) {
            addCriterion("service_pack_id like", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotLike(String value) {
            addCriterion("service_pack_id not like", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIn(List<String> values) {
            addCriterion("service_pack_id in", values, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotIn(List<String> values) {
            addCriterion("service_pack_id not in", values, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdBetween(String value1, String value2) {
            addCriterion("service_pack_id between", value1, value2, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotBetween(String value1, String value2) {
            addCriterion("service_pack_id not between", value1, value2, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedIsNull() {
            addCriterion("service_quota_used is null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedIsNotNull() {
            addCriterion("service_quota_used is not null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedEqualTo(Long value) {
            addCriterion("service_quota_used =", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedNotEqualTo(Long value) {
            addCriterion("service_quota_used <>", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedGreaterThan(Long value) {
            addCriterion("service_quota_used >", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedGreaterThanOrEqualTo(Long value) {
            addCriterion("service_quota_used >=", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedLessThan(Long value) {
            addCriterion("service_quota_used <", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedLessThanOrEqualTo(Long value) {
            addCriterion("service_quota_used <=", value, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_used <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedIn(List<Long> values) {
            addCriterion("service_quota_used in", values, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedNotIn(List<Long> values) {
            addCriterion("service_quota_used not in", values, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedBetween(Long value1, Long value2) {
            addCriterion("service_quota_used between", value1, value2, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaUsedNotBetween(Long value1, Long value2) {
            addCriterion("service_quota_used not between", value1, value2, "serviceQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainIsNull() {
            addCriterion("service_quota_remain is null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainIsNotNull() {
            addCriterion("service_quota_remain is not null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainEqualTo(Long value) {
            addCriterion("service_quota_remain =", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainNotEqualTo(Long value) {
            addCriterion("service_quota_remain <>", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainGreaterThan(Long value) {
            addCriterion("service_quota_remain >", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainGreaterThanOrEqualTo(Long value) {
            addCriterion("service_quota_remain >=", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainLessThan(Long value) {
            addCriterion("service_quota_remain <", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainLessThanOrEqualTo(Long value) {
            addCriterion("service_quota_remain <=", value, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_remain <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainIn(List<Long> values) {
            addCriterion("service_quota_remain in", values, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainNotIn(List<Long> values) {
            addCriterion("service_quota_remain not in", values, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainBetween(Long value1, Long value2) {
            addCriterion("service_quota_remain between", value1, value2, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaRemainNotBetween(Long value1, Long value2) {
            addCriterion("service_quota_remain not between", value1, value2, "serviceQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseIsNull() {
            addCriterion("service_quota_reverse is null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseIsNotNull() {
            addCriterion("service_quota_reverse is not null");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseEqualTo(Long value) {
            addCriterion("service_quota_reverse =", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseNotEqualTo(Long value) {
            addCriterion("service_quota_reverse <>", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseGreaterThan(Long value) {
            addCriterion("service_quota_reverse >", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseGreaterThanOrEqualTo(Long value) {
            addCriterion("service_quota_reverse >=", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseLessThan(Long value) {
            addCriterion("service_quota_reverse <", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseLessThanOrEqualTo(Long value) {
            addCriterion("service_quota_reverse <=", value, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("service_quota_reverse <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseIn(List<Long> values) {
            addCriterion("service_quota_reverse in", values, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseNotIn(List<Long> values) {
            addCriterion("service_quota_reverse not in", values, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseBetween(Long value1, Long value2) {
            addCriterion("service_quota_reverse between", value1, value2, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andServiceQuotaReverseNotBetween(Long value1, Long value2) {
            addCriterion("service_quota_reverse not between", value1, value2, "serviceQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedIsNull() {
            addCriterion("material_quota_used is null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedIsNotNull() {
            addCriterion("material_quota_used is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedEqualTo(Long value) {
            addCriterion("material_quota_used =", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedNotEqualTo(Long value) {
            addCriterion("material_quota_used <>", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedGreaterThan(Long value) {
            addCriterion("material_quota_used >", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedGreaterThanOrEqualTo(Long value) {
            addCriterion("material_quota_used >=", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedLessThan(Long value) {
            addCriterion("material_quota_used <", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedLessThanOrEqualTo(Long value) {
            addCriterion("material_quota_used <=", value, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_used <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedIn(List<Long> values) {
            addCriterion("material_quota_used in", values, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedNotIn(List<Long> values) {
            addCriterion("material_quota_used not in", values, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedBetween(Long value1, Long value2) {
            addCriterion("material_quota_used between", value1, value2, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaUsedNotBetween(Long value1, Long value2) {
            addCriterion("material_quota_used not between", value1, value2, "materialQuotaUsed");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainIsNull() {
            addCriterion("material_quota_remain is null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainIsNotNull() {
            addCriterion("material_quota_remain is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainEqualTo(Long value) {
            addCriterion("material_quota_remain =", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainNotEqualTo(Long value) {
            addCriterion("material_quota_remain <>", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainGreaterThan(Long value) {
            addCriterion("material_quota_remain >", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainGreaterThanOrEqualTo(Long value) {
            addCriterion("material_quota_remain >=", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainLessThan(Long value) {
            addCriterion("material_quota_remain <", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainLessThanOrEqualTo(Long value) {
            addCriterion("material_quota_remain <=", value, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_remain <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainIn(List<Long> values) {
            addCriterion("material_quota_remain in", values, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainNotIn(List<Long> values) {
            addCriterion("material_quota_remain not in", values, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainBetween(Long value1, Long value2) {
            addCriterion("material_quota_remain between", value1, value2, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaRemainNotBetween(Long value1, Long value2) {
            addCriterion("material_quota_remain not between", value1, value2, "materialQuotaRemain");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseIsNull() {
            addCriterion("material_quota_reverse is null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseIsNotNull() {
            addCriterion("material_quota_reverse is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseEqualTo(Long value) {
            addCriterion("material_quota_reverse =", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseNotEqualTo(Long value) {
            addCriterion("material_quota_reverse <>", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseGreaterThan(Long value) {
            addCriterion("material_quota_reverse >", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseGreaterThanOrEqualTo(Long value) {
            addCriterion("material_quota_reverse >=", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseLessThan(Long value) {
            addCriterion("material_quota_reverse <", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseLessThanOrEqualTo(Long value) {
            addCriterion("material_quota_reverse <=", value, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_quota_reverse <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseIn(List<Long> values) {
            addCriterion("material_quota_reverse in", values, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseNotIn(List<Long> values) {
            addCriterion("material_quota_reverse not in", values, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseBetween(Long value1, Long value2) {
            addCriterion("material_quota_reverse between", value1, value2, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotaReverseNotBetween(Long value1, Long value2) {
            addCriterion("material_quota_reverse not between", value1, value2, "materialQuotaReverse");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(Integer value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(Integer value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(Integer value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(Integer value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(Integer value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("material_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<Integer> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<Integer> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(Integer value1, Integer value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andLabelTaxIsNull() {
            addCriterion("label_tax is null");
            return (Criteria) this;
        }

        public Criteria andLabelTaxIsNotNull() {
            addCriterion("label_tax is not null");
            return (Criteria) this;
        }

        public Criteria andLabelTaxEqualTo(String value) {
            addCriterion("label_tax =", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxNotEqualTo(String value) {
            addCriterion("label_tax <>", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxNotEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxGreaterThan(String value) {
            addCriterion("label_tax >", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxGreaterThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxGreaterThanOrEqualTo(String value) {
            addCriterion("label_tax >=", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxGreaterThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxLessThan(String value) {
            addCriterion("label_tax <", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxLessThanColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxLessThanOrEqualTo(String value) {
            addCriterion("label_tax <=", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxLessThanOrEqualToColumn(ContractMaterial.Column column) {
            addCriterion(new StringBuilder("label_tax <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLabelTaxLike(String value) {
            addCriterion("label_tax like", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxNotLike(String value) {
            addCriterion("label_tax not like", value, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxIn(List<String> values) {
            addCriterion("label_tax in", values, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxNotIn(List<String> values) {
            addCriterion("label_tax not in", values, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxBetween(String value1, String value2) {
            addCriterion("label_tax between", value1, value2, "labelTax");
            return (Criteria) this;
        }

        public Criteria andLabelTaxNotBetween(String value1, String value2) {
            addCriterion("label_tax not between", value1, value2, "labelTax");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andContractNumberLikeInsensitive(String value) {
            addCriterion("upper(contract_number) like", value.toUpperCase(), "contractNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialNumberLikeInsensitive(String value) {
            addCriterion("upper(material_number) like", value.toUpperCase(), "materialNumber");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(name) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLikeInsensitive(String value) {
            addCriterion("upper(owner_department) like", value.toUpperCase(), "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andProductIdLikeInsensitive(String value) {
            addCriterion("upper(product_id) like", value.toUpperCase(), "productId");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLikeInsensitive(String value) {
            addCriterion("upper(material_unit) like", value.toUpperCase(), "materialUnit");
            return (Criteria) this;
        }

        public Criteria andPlanStrategyLikeInsensitive(String value) {
            addCriterion("upper(plan_strategy) like", value.toUpperCase(), "planStrategy");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLikeInsensitive(String value) {
            addCriterion("upper(service_pack_id) like", value.toUpperCase(), "servicePackId");
            return (Criteria) this;
        }

        public Criteria andLabelTaxLikeInsensitive(String value) {
            addCriterion("upper(label_tax) like", value.toUpperCase(), "labelTax");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Sep 24 10:04:04 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private ContractMaterialExample example;

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        protected Criteria(ContractMaterialExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public ContractMaterialExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Sep 24 10:04:04 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ContractMaterialExample example);
    }
}