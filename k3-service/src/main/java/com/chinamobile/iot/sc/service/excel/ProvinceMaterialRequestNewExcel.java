package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xie<PERSON>oh<PERSON>
 * @date : 2024/8/29 16:55
 * @description: 省物料实体类
 **/
@Data
public class ProvinceMaterialRequestNewExcel {



    /**
     * 合同类型
     */
    @ExcelProperty(value = "合同类型",index = 0)
    private String contractType;

    /**
     * 物联网合同编码
     */
    @ExcelProperty(value = "物联网合同编码",index = 1)
    private String internetContractCode;

    /**
     * 省份编码
     *
     */
    private String provinceCode;


    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份",index = 2)
    private String provinceName;

    /**
     * 地市编码
     *
     */
    private String cityCode;

    /**
     * 地市名称
     */
    @ExcelProperty(value = "地市",index = 3)
    private String cityName;


    /**
     * 主物料编码
     */
    @ExcelProperty(value = "主物料编码",index = 4)
    private String materialCode;


    /**
     * 主物料描述
     */
    @ExcelProperty(value = "主物料描述",index = 5)
    private String materialName;



    @ExcelProperty(value = "合同编码",index = 6)
    private String contractCode;

    /**
     * 扩展物料编码  8位集团物料+省代码+3位顺序码
     */
    @ExcelProperty(value = "扩展物料编码",index = 7)
    private String attr2;

    /**
     * 扩展物料描述
     *
     */
    @ExcelProperty(value = "扩展物料描述",index = 8)
    private String attr3;

    /**
     * 单位(项)
     */
    @ExcelProperty(value = "单位",index = 9)
    private String unit;

    /**
     * 不含税单价
     */
    @ExcelProperty(value = "不含税单价",index = 10)
    private String unitPrice;

    /**
     * 含税单价
     */
    @ExcelProperty(value = "含税单价",index = 11)
    private String taxInclusivePrice;

    /**
     * 税码 固定值
     *
     */
    @ExcelProperty(value = "税码",index = 12)
    private String taxCode;

    /**
     * 税率 固定值
     */
    @ExcelProperty(value = "税率",index = 13)
    private BigDecimal taxRate;

    /**
     * 是否直发  N/Y
     */
    @ExcelProperty(value = "是否直发",index = 14)
    private String sdProjectFlag;

    /**
     * 物联网物料编码
     */
    @ExcelProperty(value = "物联网物料编码",index = 15)
    private String internetMaterialCode;

    /**
     * 物联网物料名称
     *
     */
    @ExcelProperty(value = "物联网物料名称",index = 16)
    private String internetMaterialName;


}
