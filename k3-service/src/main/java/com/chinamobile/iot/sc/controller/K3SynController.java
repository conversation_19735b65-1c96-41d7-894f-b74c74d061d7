package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.param.K3SynParam;
import com.chinamobile.iot.sc.pojo.query.SupplyChainImportPoDraftIOTQuery;
import com.chinamobile.iot.sc.service.K3SynService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/7
 * @description k3同步controller类
 */
@RestController
@RequestMapping(value = "/k3serv/syn")
public class K3SynController {

    @Resource
    private K3SynService k3SynService;

    /**
     * 同步保存月账单
     *
     * @param k3SynParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/save")
    public BaseAnswer synSaveMonthBillToK3(@Valid @RequestBody K3SynParam k3SynParam,
                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws Exception {
        /*BaseAnswer baseAnswer = CustomUtils.validCustomParam(k3SynParam.getSaleDate());
        if (!baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
            return baseAnswer;
        }*/
        BaseAnswer baseAnswer = new BaseAnswer();
        Map<String, Object> synMap = k3SynService.synSaveMonthBillToK3(k3SynParam,loginIfo4Redis);
        baseAnswer.setData(synMap);
        return baseAnswer;
    }

    /**
     * 提交月账单
     *
     * @param k3SynParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/submit")
    public BaseAnswer synSubmitMonthBillToK3(@Valid @RequestBody K3SynParam k3SynParam,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws Exception {
        /*BaseAnswer baseAnswer = CustomUtils.validCustomParam(k3SynParam.getSaleDate());
        if (!baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
            return baseAnswer;
        }*/
        BaseAnswer baseAnswer = new BaseAnswer();
        Map<String, Object> map = k3SynService.synSubmitMonthBillToK3(k3SynParam,loginIfo4Redis);
        baseAnswer.setData(map);
        return baseAnswer;
    }


    /**
     * 导入草稿
     * @param request
     * @return
     */

    @PostMapping("/importPoDraft")
    public BaseAnswer<Void> importPoDraft(@RequestBody SupplyChainImportPoDraftIOTQuery request){
        return k3SynService.importPoDraft(request);

    }
}
