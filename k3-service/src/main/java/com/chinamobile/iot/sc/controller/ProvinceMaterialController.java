package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo;
import com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew;
import com.chinamobile.iot.sc.pojo.entity.ProvinceMaterialInfo;
import com.chinamobile.iot.sc.pojo.param.ProvinceContractUpdateParam;
import com.chinamobile.iot.sc.pojo.param.ProvincialContractMaterialParam;
import com.chinamobile.iot.sc.pojo.vo.PageVO;
import com.chinamobile.iot.sc.pojo.vo.ProvinceMaterialVO;
import com.chinamobile.iot.sc.service.ProvinceMaterialService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.K3_PROVINCE_MATERIAL;

/**
 * <AUTHOR> xiemaohua
 * @date : 2023/3/23 9:46
 * @description: 省物料配置控制类
 **/
@RestController
@RequestMapping("/k3serv")
public class ProvinceMaterialController {

    @Resource
    private ProvinceMaterialService provinceMaterialService;

    /**
     * 省物料配置导入
     * @param file
     */
    @PostMapping("/province/material/import/new")
    public void saveImportOneNetMaterialRequest(@RequestParam("file") MultipartFile file){
        provinceMaterialService.importOneNetMaterialNewRequest(file);
    }

    /**
     * 分页查询省合同信息
     * @param contractMaterialParam
     * @return
     */
    @GetMapping("/province/contract/list/new")
    public BaseAnswer<PageVO<ProvinceContractInfoNew>> queryProvinceContractsList(ProvincialContractMaterialParam contractMaterialParam){
     return provinceMaterialService.queryListProvinceContracts(contractMaterialParam);
    }

    /**
     * 查询省合同对应物料信息
     * @param internetContractCode
     * @return
     */
    @GetMapping("/province/material/list/new")
    public  BaseAnswer<List<ProvinceMaterialVO>> queryProvinceMaterialList(@RequestParam("internetContractCode") String internetContractCode
            ,@RequestParam("provinceName") String provinceName, @RequestParam(value = "cityName",required = false)String cityName,
                                                                           @RequestParam("contractType")String contractType){
        return provinceMaterialService.getProvinceMaterialList(internetContractCode,provinceName,cityName,contractType);
    }

    /**
     * 删除合同信息
     * @param id
     * @return
     */
    @DeleteMapping("/province/contract/delete/new")
    public  BaseAnswer<Void> deleteProvinceContractsMessage(@RequestParam("id") String id){
        return provinceMaterialService.deleteProvinceContracts(id);
    }

    /**
     * 修改省合同信息
     * @param param
     * @return
     */
    @PostMapping("/province/contract/update/new")
    public  BaseAnswer<Void> updateProvinceContractsMessage(@RequestBody @Valid ProvinceContractUpdateParam param){
        return provinceMaterialService.updateProvinceContracts(param);
    }


}
