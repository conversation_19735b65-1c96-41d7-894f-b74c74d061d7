package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @AUTHOR: HWF
 * @DATE: 2023/2/14
 */
@Data
public class K3QueryListParam extends BasePageQuery {

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 下单起始时间
     */
    private String createTimeFrom;

    /**
     * 下单截止时间
     */
    private String createTimeTo;

    /**
     * 交易完成起始时间
     */
    private String dealTimeFromStr;

    /**
     * 交易完成截止时间
     */
    private String dealTimeToStr;

    private Date dealTimeFrom;

    private Date dealTimeTo;

    /**
     * 订单收入归属省编码
     */
    private String provinceCode;

    private String provinceName;

    /**
     * 订单输入归属地市编码
     */
    private String cityCode;

    private String cityName;
}
