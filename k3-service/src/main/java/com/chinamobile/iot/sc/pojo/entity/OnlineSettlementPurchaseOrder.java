package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 线上结算管理采购订单表
 *
 * <AUTHOR>
public class OnlineSettlementPurchaseOrder implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String id;

    /**
     * 同步草稿生成的prikey
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.prikey
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String prikey;

    /**
     * 采购订单生成方式1--自动 2--手动
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.source_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String sourceType;

    /**
     * SCM采购订单编号
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.scm_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String scmOrderNum;

    /**
     * 正式的采购订单编号
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.po_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String poOrderNum;

    /**
     * 结算方式1--省结算 2--地市结算
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.settle_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String settleType;

    /**
     * 合同编号
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.contract_number
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String contractNumber;

    /**
     * 合同名称
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.contract_name
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String contractName;

    /**
     * 合同类型 A08：OneNET，A09：OnePark，A10：OneTraffic，A15：千里眼独立服务，A16：和对讲独立服务，A17：云视讯独立服务
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.contract_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String contractType;

    /**
     * 个人客户所属省份。
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.be_id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String beId;

    /**
     * 个人客户所属归属地市编码
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.location
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String location;

    /**
     * 订单总金额(结算金额)
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private Long settlePrice;

    /**
     * 结算状态6--待发起  7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消 12--同步失败
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.settle_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String settleStatus;

    /**
     * 含税总价(结算金额)
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.tax_inclusive_total_settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private BigDecimal taxInclusiveTotalSettlePrice;

    /**
     * 同步状态0--未同步 1--已同步
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.syn_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private Integer synStatus;

    /**
     * 省份代码
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.province_code
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private String provinceCode;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.create_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..online_settlement_purchase_order.update_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..online_settlement_purchase_order
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.id
     *
     * @return the value of supply_chain..online_settlement_purchase_order.id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.id
     *
     * @param id the value for supply_chain..online_settlement_purchase_order.id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.prikey
     *
     * @return the value of supply_chain..online_settlement_purchase_order.prikey
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getPrikey() {
        return prikey;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withPrikey(String prikey) {
        this.setPrikey(prikey);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.prikey
     *
     * @param prikey the value for supply_chain..online_settlement_purchase_order.prikey
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setPrikey(String prikey) {
        this.prikey = prikey == null ? null : prikey.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.source_type
     *
     * @return the value of supply_chain..online_settlement_purchase_order.source_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getSourceType() {
        return sourceType;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withSourceType(String sourceType) {
        this.setSourceType(sourceType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.source_type
     *
     * @param sourceType the value for supply_chain..online_settlement_purchase_order.source_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setSourceType(String sourceType) {
        this.sourceType = sourceType == null ? null : sourceType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.scm_order_num
     *
     * @return the value of supply_chain..online_settlement_purchase_order.scm_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getScmOrderNum() {
        return scmOrderNum;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withScmOrderNum(String scmOrderNum) {
        this.setScmOrderNum(scmOrderNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.scm_order_num
     *
     * @param scmOrderNum the value for supply_chain..online_settlement_purchase_order.scm_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setScmOrderNum(String scmOrderNum) {
        this.scmOrderNum = scmOrderNum == null ? null : scmOrderNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.po_order_num
     *
     * @return the value of supply_chain..online_settlement_purchase_order.po_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getPoOrderNum() {
        return poOrderNum;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withPoOrderNum(String poOrderNum) {
        this.setPoOrderNum(poOrderNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.po_order_num
     *
     * @param poOrderNum the value for supply_chain..online_settlement_purchase_order.po_order_num
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setPoOrderNum(String poOrderNum) {
        this.poOrderNum = poOrderNum == null ? null : poOrderNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.settle_type
     *
     * @return the value of supply_chain..online_settlement_purchase_order.settle_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getSettleType() {
        return settleType;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withSettleType(String settleType) {
        this.setSettleType(settleType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.settle_type
     *
     * @param settleType the value for supply_chain..online_settlement_purchase_order.settle_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setSettleType(String settleType) {
        this.settleType = settleType == null ? null : settleType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.contract_number
     *
     * @return the value of supply_chain..online_settlement_purchase_order.contract_number
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getContractNumber() {
        return contractNumber;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withContractNumber(String contractNumber) {
        this.setContractNumber(contractNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.contract_number
     *
     * @param contractNumber the value for supply_chain..online_settlement_purchase_order.contract_number
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber == null ? null : contractNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.contract_name
     *
     * @return the value of supply_chain..online_settlement_purchase_order.contract_name
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getContractName() {
        return contractName;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withContractName(String contractName) {
        this.setContractName(contractName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.contract_name
     *
     * @param contractName the value for supply_chain..online_settlement_purchase_order.contract_name
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.contract_type
     *
     * @return the value of supply_chain..online_settlement_purchase_order.contract_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.contract_type
     *
     * @param contractType the value for supply_chain..online_settlement_purchase_order.contract_type
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.be_id
     *
     * @return the value of supply_chain..online_settlement_purchase_order.be_id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.be_id
     *
     * @param beId the value for supply_chain..online_settlement_purchase_order.be_id
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.location
     *
     * @return the value of supply_chain..online_settlement_purchase_order.location
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.location
     *
     * @param location the value for supply_chain..online_settlement_purchase_order.location
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.settle_price
     *
     * @return the value of supply_chain..online_settlement_purchase_order.settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public Long getSettlePrice() {
        return settlePrice;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withSettlePrice(Long settlePrice) {
        this.setSettlePrice(settlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.settle_price
     *
     * @param settlePrice the value for supply_chain..online_settlement_purchase_order.settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setSettlePrice(Long settlePrice) {
        this.settlePrice = settlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.settle_status
     *
     * @return the value of supply_chain..online_settlement_purchase_order.settle_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getSettleStatus() {
        return settleStatus;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withSettleStatus(String settleStatus) {
        this.setSettleStatus(settleStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.settle_status
     *
     * @param settleStatus the value for supply_chain..online_settlement_purchase_order.settle_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setSettleStatus(String settleStatus) {
        this.settleStatus = settleStatus == null ? null : settleStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.tax_inclusive_total_settle_price
     *
     * @return the value of supply_chain..online_settlement_purchase_order.tax_inclusive_total_settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public BigDecimal getTaxInclusiveTotalSettlePrice() {
        return taxInclusiveTotalSettlePrice;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withTaxInclusiveTotalSettlePrice(BigDecimal taxInclusiveTotalSettlePrice) {
        this.setTaxInclusiveTotalSettlePrice(taxInclusiveTotalSettlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.tax_inclusive_total_settle_price
     *
     * @param taxInclusiveTotalSettlePrice the value for supply_chain..online_settlement_purchase_order.tax_inclusive_total_settle_price
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setTaxInclusiveTotalSettlePrice(BigDecimal taxInclusiveTotalSettlePrice) {
        this.taxInclusiveTotalSettlePrice = taxInclusiveTotalSettlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.syn_status
     *
     * @return the value of supply_chain..online_settlement_purchase_order.syn_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public Integer getSynStatus() {
        return synStatus;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withSynStatus(Integer synStatus) {
        this.setSynStatus(synStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.syn_status
     *
     * @param synStatus the value for supply_chain..online_settlement_purchase_order.syn_status
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setSynStatus(Integer synStatus) {
        this.synStatus = synStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.province_code
     *
     * @return the value of supply_chain..online_settlement_purchase_order.province_code
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.province_code
     *
     * @param provinceCode the value for supply_chain..online_settlement_purchase_order.province_code
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.create_time
     *
     * @return the value of supply_chain..online_settlement_purchase_order.create_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.create_time
     *
     * @param createTime the value for supply_chain..online_settlement_purchase_order.create_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_purchase_order.update_time
     *
     * @return the value of supply_chain..online_settlement_purchase_order.update_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public OnlineSettlementPurchaseOrder withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_purchase_order.update_time
     *
     * @param updateTime the value for supply_chain..online_settlement_purchase_order.update_time
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", prikey=").append(prikey);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", scmOrderNum=").append(scmOrderNum);
        sb.append(", poOrderNum=").append(poOrderNum);
        sb.append(", settleType=").append(settleType);
        sb.append(", contractNumber=").append(contractNumber);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractType=").append(contractType);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", settlePrice=").append(settlePrice);
        sb.append(", settleStatus=").append(settleStatus);
        sb.append(", taxInclusiveTotalSettlePrice=").append(taxInclusiveTotalSettlePrice);
        sb.append(", synStatus=").append(synStatus);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OnlineSettlementPurchaseOrder other = (OnlineSettlementPurchaseOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPrikey() == null ? other.getPrikey() == null : this.getPrikey().equals(other.getPrikey()))
            && (this.getSourceType() == null ? other.getSourceType() == null : this.getSourceType().equals(other.getSourceType()))
            && (this.getScmOrderNum() == null ? other.getScmOrderNum() == null : this.getScmOrderNum().equals(other.getScmOrderNum()))
            && (this.getPoOrderNum() == null ? other.getPoOrderNum() == null : this.getPoOrderNum().equals(other.getPoOrderNum()))
            && (this.getSettleType() == null ? other.getSettleType() == null : this.getSettleType().equals(other.getSettleType()))
            && (this.getContractNumber() == null ? other.getContractNumber() == null : this.getContractNumber().equals(other.getContractNumber()))
            && (this.getContractName() == null ? other.getContractName() == null : this.getContractName().equals(other.getContractName()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getSettlePrice() == null ? other.getSettlePrice() == null : this.getSettlePrice().equals(other.getSettlePrice()))
            && (this.getSettleStatus() == null ? other.getSettleStatus() == null : this.getSettleStatus().equals(other.getSettleStatus()))
            && (this.getTaxInclusiveTotalSettlePrice() == null ? other.getTaxInclusiveTotalSettlePrice() == null : this.getTaxInclusiveTotalSettlePrice().equals(other.getTaxInclusiveTotalSettlePrice()))
            && (this.getSynStatus() == null ? other.getSynStatus() == null : this.getSynStatus().equals(other.getSynStatus()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPrikey() == null) ? 0 : getPrikey().hashCode());
        result = prime * result + ((getSourceType() == null) ? 0 : getSourceType().hashCode());
        result = prime * result + ((getScmOrderNum() == null) ? 0 : getScmOrderNum().hashCode());
        result = prime * result + ((getPoOrderNum() == null) ? 0 : getPoOrderNum().hashCode());
        result = prime * result + ((getSettleType() == null) ? 0 : getSettleType().hashCode());
        result = prime * result + ((getContractNumber() == null) ? 0 : getContractNumber().hashCode());
        result = prime * result + ((getContractName() == null) ? 0 : getContractName().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getSettlePrice() == null) ? 0 : getSettlePrice().hashCode());
        result = prime * result + ((getSettleStatus() == null) ? 0 : getSettleStatus().hashCode());
        result = prime * result + ((getTaxInclusiveTotalSettlePrice() == null) ? 0 : getTaxInclusiveTotalSettlePrice().hashCode());
        result = prime * result + ((getSynStatus() == null) ? 0 : getSynStatus().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..online_settlement_purchase_order
     *
     * @mbg.generated Thu May 22 15:31:26 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        prikey("prikey", "prikey", "VARCHAR", false),
        sourceType("source_type", "sourceType", "VARCHAR", false),
        scmOrderNum("scm_order_num", "scmOrderNum", "VARCHAR", false),
        poOrderNum("po_order_num", "poOrderNum", "VARCHAR", false),
        settleType("settle_type", "settleType", "VARCHAR", false),
        contractNumber("contract_number", "contractNumber", "VARCHAR", false),
        contractName("contract_name", "contractName", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        settlePrice("settle_price", "settlePrice", "BIGINT", false),
        settleStatus("settle_status", "settleStatus", "VARCHAR", false),
        taxInclusiveTotalSettlePrice("tax_inclusive_total_settle_price", "taxInclusiveTotalSettlePrice", "DECIMAL", false),
        synStatus("syn_status", "synStatus", "INTEGER", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..online_settlement_purchase_order
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu May 22 15:31:26 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}