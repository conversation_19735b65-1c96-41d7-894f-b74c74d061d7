package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.K3DepartmentParam;
import com.chinamobile.iot.sc.pojo.vo.K3DepartmentVO;
import com.chinamobile.iot.sc.service.K3syncStatisDepartmentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/24
 * @description k3部门信息controller类
 */
@RestController
@RequestMapping(value = "/k3serv")
public class K3syncStatisDepartmentController {

    @Resource
    private K3syncStatisDepartmentService k3syncStatisDepartmentService;

    /**
     * 分页获取按产品部门对账单
     * @param k3DepartmentParam
     * @return
     */
    @GetMapping(value = "/pageK3Department")
    public BaseAnswer<PageData<K3DepartmentVO>> pageK3Department(K3DepartmentParam k3DepartmentParam){
        BaseAnswer<PageData<K3DepartmentVO>> baseAnswer = new BaseAnswer<>();
        PageData<K3DepartmentVO> pageData = k3syncStatisDepartmentService.pageK3DepartmentVO(k3DepartmentParam);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

}
