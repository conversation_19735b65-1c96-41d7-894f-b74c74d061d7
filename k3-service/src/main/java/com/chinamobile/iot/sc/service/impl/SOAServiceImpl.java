package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.controller.OnlineSettlementPurchaseOrderController;
import com.chinamobile.iot.sc.dao.OnlineSettlementOsOrderMapper;
import com.chinamobile.iot.sc.dao.OnlineSettlementPurchaseOrderMapper;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.dao.ext.OnlineSettlementOsOrderMapperExt;
import com.chinamobile.iot.sc.dao.ext.OnlineSettlementPurchaseOrderMapperExt;
import com.chinamobile.iot.sc.enums.*;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OnlineSettleOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrderExample;
import com.chinamobile.iot.sc.pojo.entity.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.param.SOADownloadParam;
import com.chinamobile.iot.sc.pojo.vo.SettleOrderStatusVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.SOAService;
import com.chinamobile.iot.sc.service.soa.orderImport.*;
import com.chinamobile.iot.sc.service.soa.orderImport.INPUTCOLLECTION;
import com.chinamobile.iot.sc.service.soa.orderImport.INPUTCOLLECTIONITEM;
import com.chinamobile.iot.sc.service.soa.orderImport.InputParameters;
import com.chinamobile.iot.sc.service.soa.orderImport.MSGHEADER;
import com.chinamobile.iot.sc.service.soa.orderImport.OutputParameters;
import com.chinamobile.iot.sc.service.soa.orderReturn.*;
import com.chinamobile.iot.sc.service.soa.orderReturn.OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvClient;
import com.chinamobile.iot.sc.service.soa.orderStatusInfo.OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrvClient;
import com.chinamobile.iot.sc.service.soa.orderUpdate.OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvClient;
import com.chinamobile.iot.sc.service.soa.orderUpdate.OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvPortImpl;
import com.chinamobile.iot.sc.service.soa.scmOrderQuery.OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvClient;
import com.chinamobile.iot.sc.service.soa.scmOrderQuery.OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvPortImpl;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.SFTPUtil;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SERVER_INTERNAL_ERROR;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * @AUTHOR: HWF
 * @DATE: 2025/1/17
 */
@Service
@Slf4j
public class SOAServiceImpl implements SOAService {

    /**
     * OSB_SSCM_ZX_HQ_00002 导入订单草稿信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrvClient importOrderDraftClient;

    /**
     * OSB_SSCM_ZX_HQ_00003 导入采购订单草稿退回信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvClient importRetReqInfoClient;

    /**
     * OSB_SSCM_ZX_HQ_00004 导入采购订单修改或取消信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvClient updateOrCancelClient;

    /**
     * OSB_SSCM_ZX_HQ_00005 查询订单在哪个台信息服务（分页）
     */
    @Resource
    private OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrvClient queryOrderStateClient;

    @Resource
    private OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvPortImpl updateService;

    @Resource
    private OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvPortImpl sscmOrderService;


    /**
     * OSB_SSCM_ZX_HQ_00012 查询SCM采购订单信息服务（分页）
     */
    @Resource
    private OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvClient scmOrderQueryClient;

    @Value("${sftp.name}")
    private String sftpUserName;
    @Value("${sftp.password}")
    private String sftpPassword;
    @Value("${sftp.host}")
    private String sftpHost;
    @Value("${sftp.port}")
    private Integer sftpPort;
    @Value("${sftp.osPath}")
    private String sftpOsPath;
    @Value("${sftp.soaPath}")
    private String sftpSoaPath;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private OnlineSettlementPurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private OnlineSettlementPurchaseOrderMapperExt purchaseOrderMapperExt;

    @Resource
    private OnlineSettlementOsOrderMapper osOrderMapper;

    @Resource
    private OnlineSettlementOsOrderMapperExt osOrderMapperExt;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private LogService logService;



    @Override
    public void testOrderDraftImport() {
        InputParameters request = new InputParameters();
        MSGHEADER msgHeader = importOrderDraftClient.buildMSGHEADER();
        request.setMSGHEADER(msgHeader);


        INPUTCOLLECTION inputcollection = new INPUTCOLLECTION();
        INPUTCOLLECTIONITEM inputcollectionitem = new INPUTCOLLECTIONITEM();
//        inputcollectionitem.setPRIKEY("2025011000001");
        inputcollectionitem.setPRIKEY(BaseServiceUtils.getId());
        inputcollectionitem.setPROVINCECODE("CMIOT");
        inputcollectionitem.setDESCRIPTION("T100采购订单");
        inputcollectionitem.setSOURCEFROM("CMIOT-MS2");
        inputcollectionitem.setSOURCEFROMNO("XSKJ-1503-2501100001");
        inputcollectionitem.setMISBODY(new BigDecimal(7816));
        inputcollectionitem.setEXPTYPE("Opex");
        inputcollectionitem.setREIMBURSEMENTMODE("1");
        inputcollectionitem.setDEPTCODE("00760061000800130000");
        inputcollectionitem.setDEPTNAME("芯片运营组");
        inputcollectionitem.setCREATEDID("<EMAIL>");
        inputcollectionitem.setCREATEDNAME("刘*延");
        inputcollectionitem.setMTLTYPECODE("C");
        inputcollectionitem.setCONTRACTCODE("CMIOT-*********");
        inputcollectionitem.setVENDORCODE("MDM_100002377");
        inputcollectionitem.setREQTYPE("1");
        inputcollectionitem.setARRIVALTIMEMODEL("DATE");
        inputcollectionitem.setCURRENCYCODE("CNY");
        inputcollectionitem.setAMOUNT(new BigDecimal("55000"));
        inputcollectionitem.setTAXSUM(new BigDecimal("3300"));
        inputcollectionitem.setAMOUNTTAX(new BigDecimal("58300"));
        inputcollectionitem.setISFULLPRESENT("N");

        Integer lineNum = 1;
        ORDERLINE orderline = new ORDERLINE();
//        for(SupplyChainIotLineInfoQuery item : queryParam.getOrderLine()){
        ORDERLINEITEM orderlineitem = new ORDERLINEITEM();
        orderlineitem.setPRIKEY(BaseServiceUtils.getId());
//        orderlineitem.setPRIKEY("2025011000001");
        orderlineitem.setLINENUM(new BigDecimal(1));
        orderlineitem.setMATERIALCODE("10468474");
        orderlineitem.setMATERIALNAME("CM6620芯片-before FT");
        orderlineitem.setUNIT("个");
        orderlineitem.setQUANTITY(new BigDecimal("110000"));
        orderlineitem.setUNITPRICE(new BigDecimal("0.5"));
        orderlineitem.setTAXRATE(new BigDecimal("0.06"));
        orderlineitem.setTAXCODE("VAT6");
        orderlineitem.setLINEAMT(new BigDecimal("55000"));
        orderlineitem.setLINETAX(new BigDecimal("3300"));
        orderlineitem.setLINEAMTTAX(new BigDecimal("58300"));
        orderlineitem.setORGANIZATIONCODE("D71");
        orderlineitem.setITEMTYPE("EXPENSE");
        orderlineitem.setRCVUSERNUM("E1000024624");
        orderlineitem.setRCVUSER("金*");
        orderlineitem.setRCVCONTACTPHONE("13681479512");
        orderlineitem.setRCVSITEADDRESS("北京");

        orderline.getORDERLINEITEM().add(orderlineitem);
//            lineNum++;
//        }
        inputcollectionitem.setORDERLINE(orderline);

        inputcollection.getINPUTCOLLECTIONITEM().add(inputcollectionitem);

        log.info("importPoDraft request = {}",request);
        request.setINPUTCOLLECTION(inputcollection);
        // 设置服务客户端对象
        OSBSSCMZXHQImportPoOrderDraftInfoSrv port = importOrderDraftClient.buildServiceClient();
        // 调用服务接口
        OutputParameters response = port.process(request);

        log.info("importPoDraft response = {}",response);
        if (!"TRUE".equals(response.getESBFLAG())) {
            throw new BusinessException("500", response.getESBRETURNMESSAGE());
        }

        if (!"TRUE".equals(response.getBIZSERVICEFLAG())) {
            throw new BusinessException("500", response.getBIZRETURNMESSAGE());
        }
        // 输出服务调用结果
        System.out.println(response.getBIZSERVICEFLAG());
        System.out.println(response.getBIZRETURNMESSAGE());
        SupplyChainImportPoDraftDTO supplyChainImportPoDraftDTO = new SupplyChainImportPoDraftDTO();

        if (response.getRESPONSECOLLECTION() != null
                && CollectionUtils.isNotEmpty(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM())) {
            supplyChainImportPoDraftDTO.setScmPoNum(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getSCMPONUM());
            if(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM() != null
                    &&CollectionUtils.isNotEmpty(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM().getLINENUMITEM())){
                List<String> scmlinenumList = response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM().getLINENUMITEM().stream().map(item->item.getSCMLINENUM()).collect(Collectors.toList());
                supplyChainImportPoDraftDTO.setScmLineNum(String.join(",", scmlinenumList));
            }
        }

    }

    @Override
    public void testOrderDraftReturn() {
        com.chinamobile.iot.sc.service.soa.orderReturn.InputParameters request = new com.chinamobile.iot.sc.service.soa.orderReturn.InputParameters();
        com.chinamobile.iot.sc.service.soa.orderReturn.MSGHEADER msgHeader = importRetReqInfoClient.buildMSGHEADER();
        request.setMSGHEADER(msgHeader);

        com.chinamobile.iot.sc.service.soa.orderReturn.INPUTCOLLECTION inputcollection = new com.chinamobile.iot.sc.service.soa.orderReturn.INPUTCOLLECTION();
        com.chinamobile.iot.sc.service.soa.orderReturn.INPUTCOLLECTIONITEM inputcollectionitem = new com.chinamobile.iot.sc.service.soa.orderReturn.INPUTCOLLECTIONITEM();
        inputcollectionitem.setPRIKEY("1077680583133364224");
        inputcollectionitem.setORGID(new BigDecimal("10319"));
        inputcollectionitem.setRETURNNAME("周*");
        inputcollectionitem.setRETURNDATE("2025-01-20 09:17:18");
        inputcollectionitem.setBACKREASON("退回");


        inputcollection.getINPUTCOLLECTIONITEM().add(inputcollectionitem);

        log.info("testOrderDraftReturn request = {}",request);
        request.setINPUTCOLLECTION(inputcollection);
        // 设置服务客户端对象
        OSBSSCMZXHQImportRetSourceReqInfoSrv port = importRetReqInfoClient.buildServiceClient();
        // 调用服务接口
        com.chinamobile.iot.sc.service.soa.orderReturn.OutputParameters response = port.process(request);

        log.info("testOrderDraftReturn response = {}",response);
        if (!"TRUE".equals(response.getESBFLAG())) {
            throw new BusinessException("500", response.getESBRETURNMESSAGE());
        }

        if (!"TRUE".equals(response.getBIZSERVICEFLAG())) {
            throw new BusinessException("500", response.getBIZRETURNMESSAGE());
        }
        // 输出服务调用结果
        System.out.println(response.getBIZSERVICEFLAG());
        System.out.println(response.getBIZRETURNMESSAGE());

    }

    @Override
    public void testOrderUpdate() {
        updateService.testOrderUpdate();
    }

    @Override
    public void testOrderStatus() {
        sscmOrderService.testScmOrderQuery();
    }

    @Override
    public void testScmOrderQuery() {

    }

    @Override
    public void testFtpFileExist(String url, String filename) {

        String filepath1 = url+"/"+filename;
        String filepath2 = sftpOsPath+"/"+filename;
        log.info("testFtpFileExist filePath1 = {}",filepath1);
        log.info("testFtpFileExist filePath2 = {}",filepath1);
        //test 一个非法文件是否存在
//        String fname1 = "haahaha.txt";    //不存在
//        String fname2 = "shuimian.jpeg";  //存在

        //用全路径名来判断
        File file1 = new File(filepath1);
        File file2 = new File(filepath2);
        log.info("use file path file1 exists = {}; file2 exist = {}",file1.exists(),file2.exists());

        //用是否有数据流来判断
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        InputStream is = null;

        //进入目录是否成功来判断
//        try{
//            sftp.cd(directory); //进入目录
//        }catch(SftpException sException){
//            if(sftp.SSH_FX_NO_SUCH_FILE == sException.id){ //指定上传路径不存在
//                sftp.mkdir(directory);//创建目录
//                sftp.cd(directory);  //进入目录
//            }
//        }


        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
//            log.info("FTP登录成功！");
            //todo 进入目录判断是否存在
            //用是否有数据流来判断
//            is = sftpUtil.getInputStream(sftpOsPath, filename);

            //进入目录是否成功来判断,只能判断路径是否存在，不能cd文件，输正确文件名还是会报错
//        try{
//            sftp.cd(directory); //进入目录
//        }catch(SftpException sException){
//            if(sftp.SSH_FX_NO_SUCH_FILE == sException.id){ //指定上传路径不存在
//                sftp.mkdir(directory);//创建目录
//                sftp.cd(directory);  //进入目录
//            }
//        }
            sftpUtil.cd(url);



            log.info("文件流 is = {}",is);
        }catch(SftpException fe){
//            fe.printStackTrace();
            log.info("error code = {}",fe.id);  //2
            log.info("error msg = {}",fe.getMessage());  //No such file

        }catch(Exception e){
            e.printStackTrace();
        }finally {
            try{
                if(is!=null){
                    is.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }

        }
    }

    @Override
    public void testDownloadStream(String url, String filename) {
        String filepath1 = url+"/"+filename;
        InputStream is = null;
        OutputStream os = null;

//        int index = url.lastIndexOf("/");
//        String xx = url.substring(url.lastIndexOf("/"));
//        log.info("xx = {}, index = {}",xx,index);

        //用是否有数据流来判断
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
//            log.info("FTP登录成功！");
            log.info("path = {}, filename = {}",sftpSoaPath, filename);
            is = sftpUtil.getInputStream("/MFTT/HQ_SSCMtoIOTMallOS_CMIOT/MftXfePOInfoFileSrv", filename);

            final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            final HttpServletResponse response = requestAttr.getResponse();
//            response.reset();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));

            //成功失败用head头反馈
            response.addHeader("stateCode", "00000");
            response.addHeader("message", "success");

            os = response.getOutputStream();
            byte[] b = new byte[1024];
            int len;
            while((len = is.read(b))>0){
                os.write(b, 0, len);
            }
            os.flush();

            log.info("文件流 is = {}",is);
        }catch(SftpException fe){
//            fe.printStackTrace();
            log.info("error code = {}",fe.id);  //2
            log.info("error msg = {}",fe.getMessage());  //No such file
            if(fe.id==2){
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "找不到对应文件");
            }
        }catch(Exception e){
            e.printStackTrace();
        }finally{
            try{
                if(is!=null){
                    is.close();
                }
                if(os!=null){
                    os.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    @Override
    public void testExceptionRollback() {
        handleExceptionA();
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleExceptionA(){

    }


    @Transactional(rollbackFor = Exception.class)
//    @Transactional(rollbackFor = Throwable.class)
    public void handleExceptionB(){

    }


    @Override
    public void downloadSftpFile() {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("downloadSftpFile 下载sftp文件，name:{}",sftpUserName);
        String testSaveDir = "D:\\save-supply-chain\\";
//        String testSaveDir = "/d";

        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            File path = new File(ResourceUtils.getURL("classpath:").getPath());
//            String testSaveDir = ResourceUtils.getURL("classpath:").getPath();
            log.info("path = {}",path);
            Vector files = sftpUtil.listFiles(sftpSoaPath);
            if(files == null || files.isEmpty()){
                return;
            }
            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext();){
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                log.info("downloadSftpFile filename = {}, abfilename = {}", filename,str.getAttrs().isDir());
                if(".".equals(filename)||"..".equals(filename)){
                    continue;
                }
                File f = new File(filename);
                log.info("f is directory：{}, f absolut：{}, f isFile:{}",f.isDirectory(),f.isAbsolute(),f.isFile());
                if(str.getAttrs().isDir()){
                    log.info("pass directory {}",filename);
                    continue;
                }
                log.info("开始下载文件原名：{}",filename);

//                File fileCache = new File(path.getAbsolutePath(), ""+System.currentTimeMillis());
//                fileCache.mkdirs();
                log.info("testSaveDir = {}",testSaveDir);
//                sftpUtil.download(sftpSoaPath, filename, fileCache.getPath()+filename);
                sftpUtil.download(sftpSoaPath, filename, testSaveDir+filename);
                log.info("saveFile = {}",testSaveDir+filename);
//                try{
//                    byte[] bytes = sftpUtil.download("/",filename);
//
//                }catch(Exception e){
//                    e.printStackTrace();
//                }
//
//                InputStream is = sftpUtil.getInputStream(sftpSoaPath, filename);
//                log.info("InputStream数据流: {}", is);
//
//                BufferedReader br = new BufferedReader(new InputStreamReader(is));
//                log.info("BufferedReader数据流：{}",br);
//
//                while(br.readLine()!=null){
//
//                }

            }



        }catch(Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public void uploadSftpFile(MultipartFile multipartFile) {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("uploadSftpFile fileName = {}, fileOrgName = {}",multipartFile.getName(), multipartFile.getOriginalFilename());
        String sftpFileName = multipartFile.getOriginalFilename();
        log.info("username = {}, password = {}, host = {}, port = {}",sftpUserName, sftpPassword, sftpHost, sftpPort);

        //老式写法
//        FileInputStream inputStream = null;
//        try{
//            File file = multipartFileToFile(multipartFile);
//
//            inputStream = Files.newInputStream(file.toPath());
//            sftpUtil.upload("/", sftpFileName, );
//        }catch(Exception e){
//            e.printStackTrace();
//        }finally{
//            if(inputStream != null){
//                try{
//                    inputStream.close();
//                }catch(IOException e){
//                    e.printStackTrace();
//                }
//            }
//        }

        //try-with-resources 自动关闭资源
        boolean ret = false;
        File file = null;
        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            //这里会在服务根目录生成一个临时文件，待删除
            file = multipartFileToFile(multipartFile);
            try(FileInputStream inputStream = new FileInputStream(file)){
                ret = sftpUtil.upload(sftpOsPath, sftpFileName, inputStream);
                log.info("uploadSftpFile 上传完成 ret = {}",ret);
            }catch(Exception e){
                e.printStackTrace();
                log.info("上传文件异常");
            }
            //todo 尝试大文件，会不会先被删除？生成临时文件，重启服务会不会被清空？
            log.info("before delete");
            Files.delete(file.getAbsoluteFile().toPath());
            log.info("after delete");
        }catch(Exception e){
            e.printStackTrace();
            log.info("multipartFileToFile 失败！");
        }
//        if(!ret){
//
//        }
        log.info("uploadSftpFile 上传文件成功 = {}",ret);

    }

    @Override
    public void listSftpFiles() {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("test info = {}, {}, {}, {}",sftpUserName,sftpPassword,sftpHost,sftpPort);
        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");

            Vector files = sftpUtil.listFiles(sftpOsPath);
            if(files == null || files.isEmpty()){
                return;
            }


            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext();) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                log.info("listSftpFiles filename = {}, abfilename = {}", filename, str.getAttrs().isDir());
                if (".".equals(filename) || "..".equals(filename)) {
                    continue;
                }

                if (str.getAttrs().isDir()) {
                    log.info("pass directory {}", filename);
                    continue;
                }
                log.info("listed: fileName = {}",filename);
            }
        }catch(Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public void deleteSftpFile(String type, String filename) {
        //todo 测试删除一个不存在的文件
//        File testf = new File("hwf.txt");
//        try{
//            Files.delete(testf.toPath());
//        }catch(Exception e){
//            //java.nio.file.NoSuchFileException: hwf.txt
//            e.printStackTrace();
//        }

        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        try {
            if (!sftpUtil.login()) {
                log.info("FTP登录失败！");
                return;
            }
            if("os".equals(type)){
                sftpUtil.delete(sftpOsPath, filename);
            }else if("soa".equals(type)){
                sftpUtil.delete(sftpSoaPath, filename);
            }
        } catch(SftpException fe){
            if(fe.id==2){
                log.info("回退草稿时未找到未取走的文件，不用删除");
            }
        }catch(Exception e){
            log.info("DELETE_FILE_ERROR: type:{}, filename:{}",type,filename);
            e.printStackTrace();
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> importDraft(String id) {
        //todo 不用写死的，用导入表格的信息
        log.info("importDraft Enter input id = {}",id);
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();

        //查出草稿单省编码
        OnlineSettlementPurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        if(purchaseOrder == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到采购单");
        }
        String provinceCode = purchaseOrder.getProvinceCode();
        if(StringUtils.isEmpty(provinceCode)){
            provinceCode = "HN";
        }

        //先查出采购单id关联的子订单，及相应的合同物料信息
        List<DraftSubOrderInfoDTO> draftOrderList = osOrderMapperExt.listDraftOsOrders(id);
        if(CollectionUtils.isEmpty(draftOrderList)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到草稿单对应的子订单");
        }

        SOAImportDraftDTO soaImportDraftDTO = new SOAImportDraftDTO();

        //随意取一条，把值copy进去
        BeanUtils.copyProperties(draftOrderList.get(0), soaImportDraftDTO);
        log.info("importDraft copied soaDraftDto = {}",soaImportDraftDTO);
        //todo 目前写死湖南，后面建商城省份映射表，根据beid查出来传
        String prikey = BaseServiceUtils.getId();
        soaImportDraftDTO.setPriKey(prikey);
        //todo

        soaImportDraftDTO.setProvinceCode(provinceCode);
//        soaImportDraftDTO.setProvinceCode("YDXY");
        soaImportDraftDTO.setSourceFromNo(prikey);
//        soaImportDraftDTO.setSourceFromNo(draftOrderList.get(0).getDraftId());
        //todo 设置非表格搜集的默认值
        soaImportDraftDTO.setExpType("Opex");
        //todo 上线改回来
        soaImportDraftDTO.setReimbursementMode("2");
//        soaImportDraftDTO.setReimbursementMode("1");
        soaImportDraftDTO.setArrivalTimeModel("EFFECT");
        soaImportDraftDTO.setCurrencyCode("CNY");
        soaImportDraftDTO.setIsFullPresent("N");

        //组装OrderLine
        List<SOAImportDraftOrderLineDTO> orderLineList = new ArrayList<>();



        //遍历订单聚合物料
        Map<String, SOAImportDraftOrderLineDTO> materialGroupMap = new HashMap<>();
        for(DraftSubOrderInfoDTO item: draftOrderList){
            log.info("test item = {}",item);
            //new added begin 先做物料合并
            String materialGroupKey = item.getAttr2()+"_"+item.getMaterialCode()+"_"+item.getContractCode();

            SOAImportDraftOrderLineDTO mapMaterial =  materialGroupMap.get(materialGroupKey);
            if(mapMaterial!=null){
                //由于订单多，物料少，这里只对数量做处理，后面再便利物料进行价格计算
                mapMaterial.setQuantity(mapMaterial.getQuantity().add(item.getQuantity()));
            }else{
                SOAImportDraftOrderLineDTO soaImportDraftOrderLineDTO = new SOAImportDraftOrderLineDTO();
                BeanUtils.copyProperties(item, soaImportDraftOrderLineDTO);
                materialGroupMap.put(materialGroupKey,soaImportDraftOrderLineDTO);
            }
            //new added end
        }

        //遍历物料，计算行信息（一行物料一个信息）
        int lineNum = 1;
        BigDecimal amount = new BigDecimal("0.00");
        BigDecimal taxsum = new BigDecimal("0.00");
        BigDecimal amounttax = new BigDecimal("0.00");
        for(String key : materialGroupMap.keySet()){
            log.info("遍历草稿map key = {}",key);
            SOAImportDraftOrderLineDTO soaImportDraftOrderLineDTO = materialGroupMap.get(key);
            soaImportDraftOrderLineDTO.setPriKey(BaseServiceUtils.getId());
            soaImportDraftOrderLineDTO.setLineNum(BigDecimal.valueOf(lineNum));
            soaImportDraftOrderLineDTO.setTaxCode("VAT6");
            soaImportDraftOrderLineDTO.setTaxRate(new BigDecimal("0.06"));
            //不含税单价
            BigDecimal unitPrice = new BigDecimal(soaImportDraftOrderLineDTO.getUnitPrice());
            //不含税总价
            BigDecimal lineamt = getRound8Dec(unitPrice.multiply(soaImportDraftOrderLineDTO.getQuantity()));
            amount = amount.add(lineamt);
            BigDecimal linetax = getRound8Dec(lineamt.multiply(new BigDecimal("0.06")));
            taxsum = taxsum.add(linetax);
            BigDecimal lineamttax = lineamt.add(linetax);
            soaImportDraftOrderLineDTO.setLineAmt(lineamt);
            soaImportDraftOrderLineDTO.setLineTax(linetax);
            soaImportDraftOrderLineDTO.setLineAmtTax(lineamttax);
            soaImportDraftOrderLineDTO.setArrivalDays(new BigDecimal(String.valueOf(soaImportDraftOrderLineDTO.getArrivalDays())));
            log.info("soaImportDraftOrderLineDTO = {}",soaImportDraftOrderLineDTO);

            orderLineList.add(soaImportDraftOrderLineDTO);
            lineNum++;
        }


        /*for(DraftSubOrderInfoDTO item: draftOrderList){
            log.info("test item = {}",item);
            //查出来就是以订单合同物料最小维度分组，不用做行合并处理
            SOAImportDraftOrderLineDTO soaImportDraftOrderLineDTO = new SOAImportDraftOrderLineDTO();
            BeanUtils.copyProperties(item, soaImportDraftOrderLineDTO);
            soaImportDraftOrderLineDTO.setPriKey(BaseServiceUtils.getId());
            soaImportDraftOrderLineDTO.setLineNum(BigDecimal.valueOf(lineNum));
//            soaImportDraftOrderLineDTO.setUnit();
//            soaImportDraftOrderLineDTO.setQuantity();
//            soaImportDraftOrderLineDTO.setUnitPrice(unitPrice);
            //todo 上线改回来：暂时手动改成税率0
            *//*soaImportDraftOrderLineDTO.setTaxCode("VAT6");
            soaImportDraftOrderLineDTO.setTaxRate(new BigDecimal("0.06"));*//*
            soaImportDraftOrderLineDTO.setTaxCode("VAT6");
            soaImportDraftOrderLineDTO.setTaxRate(new BigDecimal("0.06"));

            //不含税单价 unit_price
            BigDecimal unitPrice = new BigDecimal(item.getUnitPrice());

            //不含税总价
            BigDecimal lineamt = getRound8Dec(unitPrice.multiply(item.getQuantity()));
            amount = amount.add(lineamt);
            //todo 上线改回来：确定税率计算范式，感觉有点不对？税率啥的导入了还用写死的？
//            BigDecimal linetax = getRound8Dec(unitPrice.multiply(item.getQuantity()).multiply(new BigDecimal("0.06")));
//            BigDecimal linetax = getRound8Dec(unitPrice.multiply(item.getQuantity()).multiply(new BigDecimal("0.06")));
            //行税额改成四舍五入后的单价计算
            BigDecimal linetax = getRound8Dec(lineamt.multiply(new BigDecimal("0.06")));

            taxsum = taxsum.add(linetax);
            BigDecimal lineamttax = lineamt.add(linetax);

            soaImportDraftOrderLineDTO.setLineAmt(lineamt);
            soaImportDraftOrderLineDTO.setLineTax(linetax);
            soaImportDraftOrderLineDTO.setLineAmtTax(lineamttax);
//            //todo 上线改回来
//            soaImportDraftOrderLineDTO.setMaterialCode("10041643");
//            soaImportDraftOrderLineDTO.setMaterialName("普通光缆GYTSG.652D12芯富通");

            //todo 到货时间修改后根据模式来取不同字段，目前写死
            soaImportDraftOrderLineDTO.setArrivalDays(new BigDecimal(String.valueOf(item.getArrivalDays())));
//            soaImportDraftOrderLineDTO.setOrganizationCode(item.getOrganizationCode());
//            soaImportDraftOrderLineDTO.setItemType(item.getItemType());
//            soaImportDraftOrderLineDTO.setBudgetId(item.getBudgetId());
//            soaImportDraftOrderLineDTO.setBudgetYear(item.getBudgetYear());
//            soaImportDraftOrderLineDTO.setActivityCode(item.getActivityCode());
            log.info("soaImportDraftOrderLineDTO = {}",soaImportDraftOrderLineDTO);


            orderLineList.add(soaImportDraftOrderLineDTO);
            //todo 计算草稿单总价
            lineNum++;
        }*/
        amounttax = amount.add(taxsum);
        soaImportDraftDTO.setAmount(amount);
        soaImportDraftDTO.setTaxSum(taxsum);
        soaImportDraftDTO.setAmountTax(amounttax);
        soaImportDraftDTO.setOrderLine(orderLineList);

        //todo 文件大了，分包
        //生成附件订单，上传ftp成功后，将名字写到接口里
        //todo 如果文件存在，就不传了，暂不校验crc，留后台手动传输覆盖接口！
        ZipOutputStream out = null;
        FileInputStream fileInputStream = null;
        //生成excel文件
        String excelname = genDraftFile(id);
        String zipName = "statement_"+id+"_"+prikey+".zip";
        File zipFile = new File(zipName);
        File srcFile = new File(excelname);
        try{
            //todo 记得关闭流
            //打包成zip
            //todo 确认必须要打包吗,可以直接流输入到ftp，不用

            out = new ZipOutputStream(new FileOutputStream(zipFile));
//            out = new ZipOutputStream(Files.newOutputStream(Path.zipName));
            fileInputStream = new FileInputStream(srcFile);
            byte[] buffer = new byte[1024];
            out.putNextEntry(new ZipEntry(excelname));
            int length;
            while((length = fileInputStream.read(buffer))>0){
                out.write(buffer,0,length);
            }
            out.closeEntry();
            out.close();
            //上传到ftp
            uploadDraftFile(zipName);

        }catch(Exception e){
            log.info("打包文件出错");

            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "打包文件出错");
        }finally {
            try{
                if(out!=null){
                    out.flush();
                    out.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
            try{
                if(fileInputStream!=null){
                    fileInputStream.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
            if(srcFile.exists()){
                boolean dr = srcFile.delete();
                if(!dr){
                    log.info("DELETE_FILE_ERROR: name = {}",excelname);
                }
            }
            if(zipFile.exists()){
                boolean zipdr = zipFile.delete();
                if(!zipdr){
                    log.info("DELETE_ZIP_FILE_ERROR: name = {}",zipName);
                }
            }
        }


        JSONObject json = new JSONObject();
        json.put("FILE_TYPE_01",zipName);
        log.info("importDraft, jsonString = {}",json.toJSONString());
        soaImportDraftDTO.setInputText(json.toJSONString());

//        String excelName = "draft_"+soaImportDraftDTO.getPriKey();
        //empty file, just for test
//        genSubOrderExcel(excelName);
        soaImportDraftDTO.setOrderLine(orderLineList);
        //todo build header、input and handel exceptions

        try{
            SupplyChainImportPoDraftDTO supplyChainImportPoDraftDTO =  importOrderDraftClient.importDraft(soaImportDraftDTO);
            //todo 错误处理
            //todo 更新订单编号到表
            //todo 成功的话要更新 sync和settleStatus
            OnlineSettlementPurchaseOrder updateOrder = new OnlineSettlementPurchaseOrder();
            updateOrder.setId(id);
            updateOrder.setPrikey(supplyChainImportPoDraftDTO.getPriKey());
            updateOrder.setScmOrderNum(supplyChainImportPoDraftDTO.getScmPoNum());
            updateOrder.setSynStatus(1);
            updateOrder.setSettleStatus(SmartChainStatusEnum.STATUS_DRAFT.getOsStatus());
            updateOrder.setUpdateTime(new Date());
            purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);
        } catch(Exception e){
            log.info("importDraft 错误：{}",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, e.getMessage());
        }


        //记录日志
        try{
            String content = "【发起采购】\n"
                    .concat("合同编号").concat(soaImportDraftDTO.getContractCode())
                    .concat("采购订单唯一标识").concat(id);
            logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                    OnlineSettleOperateEnum.PURCHASE_ORDER_OPERATE.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);
        }catch(Exception e){
            e.printStackTrace();
        }

        return baseAnswer;
    }


    public void cancelDraft(String id) {
        log.info("returnDraft Enter, id = {}",id);
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();

        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
//        log.info("getConfigedAreaList userId = {}, loginIfo4Redis = {}", userId, loginIfo4Redis);
        log.info("getConfigedAreaList userId = {}", userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }

//        List<OnlineSettlementPurchaseOrder> draftList = purchaseOrderMapper.selectByExample(new OnlineSettlementPurchaseOrderExample().createCriteria()
//                .andIdEqualTo(id).andSettleStatusEqualTo(String.valueOf(SmartChainStatusEnum.STATUS_DRAFT.getOsStatus())).andSynStatusEqualTo(1).example());
//        if(CollectionUtils.isEmpty(draftList)){
//            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到待回退状态的草稿单");
//        }
//        OnlineSettlementPurchaseOrder purchaseOrder = draftList.get(0);

        List<DraftBaseInfoDTO> draftBaseInfoList = osOrderMapperExt.listDraftBaseInfo(id, SmartChainStatusEnum.STATUS_DRAFT.getOsStatus());
        if(CollectionUtils.isEmpty(draftBaseInfoList)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到符合回退要求的订单");
        }

        DraftBaseInfoDTO baseInfoDTO = draftBaseInfoList.get(0);

        String prikey = baseInfoDTO.getPrikey();
        if(StringUtils.isEmpty(prikey)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到回退prikey");
        }


        SOAReturnDraftDTO returnDraftDTO = new SOAReturnDraftDTO();
        returnDraftDTO.setOrgId(baseInfoDTO.getOrgId());
        returnDraftDTO.setPriKey(baseInfoDTO.getPrikey());
        returnDraftDTO.setSourceOrderId(prikey);
//        returnDraftDTO.setSourceOrderId(baseInfoDTO.getSourceOrderId());
        returnDraftDTO.setReturnName(getMaskingName(loginIfo4Redis.getUserName()));
        returnDraftDTO.setReturnTime(DateTimeUtil.getDbDefaultStr(new Date()));
        returnDraftDTO.setReturnReason("采购数据错误");
        log.info("returnDraft returnDraftDTO = {}",returnDraftDTO);
        boolean retSuc = true;
        try{
            importRetReqInfoClient.returnDraft(returnDraftDTO);
        }catch(Exception e){
            //回退失败
            log.info("cancelDraft 调用soa接口回退草稿失败：{}",e.getMessage());
            retSuc = false;
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, e.getMessage());
        }
        if(retSuc){
            //todo 看看服务器上草稿被取走没，没取走，要删除


            //修改草稿单状态
            OnlineSettlementPurchaseOrder updateOrder = new OnlineSettlementPurchaseOrder();
            updateOrder.setId(id);
//            updateOrder.setPrikey("");
//            updateOrder.setScmOrderNum("");
            updateOrder.setSynStatus(0);
            updateOrder.setSettleStatus(SmartChainStatusEnum.STATUS_CANCELED.getOsStatus());
            updateOrder.setUpdateTime(new Date());
            purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);
            //todo 让回滚不相互影响
            String filename = "statement_"+id+".zip";
            deleteSftpFile("os",filename);
        }
    }

    public void cancelScm(String id) {
        log.info("cancelScm Enter, id = {}",id);
        boolean retResult = true;
        List<DraftBaseInfoDTO> draftBaseInfoList = osOrderMapperExt.listDraftBaseInfo(id, SmartChainStatusEnum.STATUS_APPROVALED.getOsStatus());
        if(CollectionUtils.isEmpty(draftBaseInfoList)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到符合退回条件的订单");
        }

        DraftBaseInfoDTO baseInfoDTO = draftBaseInfoList.get(0);
        String scmPoNum = baseInfoDTO.getScmPoNum();
        if(StringUtils.isEmpty(scmPoNum)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到正式的PO单号");
        }

        SOACancelOrderDTO cancelOrderDTO = new SOACancelOrderDTO();
        cancelOrderDTO.setPriKey(baseInfoDTO.getPrikey());
        cancelOrderDTO.setDoFlag("Y");
        cancelOrderDTO.setAction("C");
        cancelOrderDTO.setOrgId(baseInfoDTO.getOrgId());
        cancelOrderDTO.setScmOrderId(scmPoNum);
        cancelOrderDTO.setAgentNum(baseInfoDTO.getRcvUserNum());
        log.info("cancelScm cancelOrderDTO = {}",cancelOrderDTO);

        try{
            updateOrCancelClient.cancelScmOrder(cancelOrderDTO);
        }catch(Exception e){
            log.info("cancelScm Error e = {}",e);
            retResult = false;
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, e.getMessage());
        }
        if(retResult){
            OnlineSettlementPurchaseOrder updateOrder = new OnlineSettlementPurchaseOrder();
            updateOrder.setId(id);
//            updateOrder.setPrikey(null);
//            updateOrder.setScmOrderNum(null);
            updateOrder.setSynStatus(0);
            updateOrder.setSettleStatus(SmartChainStatusEnum.STATUS_CANCELED.getOsStatus());
            updateOrder.setUpdateTime(new Date());
            purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);
        }
    }


    private SOAStatusRetDTO queryOrderStatus(String sourceOrderId){
        if(StringUtils.isEmpty(sourceOrderId)){
            //todo 查询全量的，具体规则方式待定
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到待查询订单号");
        }

        //查出草稿单省编码
        List<OnlineSettlementPurchaseOrder> purchaseOrders = purchaseOrderMapper.selectByExample(new OnlineSettlementPurchaseOrderExample()
                .createCriteria().andPrikeyEqualTo(sourceOrderId).example());
        if(CollectionUtils.isEmpty(purchaseOrders)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到采购单");
        }
        String provinceCode = purchaseOrders.get(0).getProvinceCode();
        if(StringUtils.isEmpty(provinceCode)){
            provinceCode = "HN";
        }

        SOAOrderStatusDTO orderStatusDTO = new SOAOrderStatusDTO();
        //todo 后面通过导入信息查
//        orderStatusDTO.setProvinceCode("HN");
        orderStatusDTO.setProvinceCode(provinceCode);
        orderStatusDTO.setSourceOrderId(sourceOrderId);
//        orderStatusDTO.setScmOrderId(scmOrderId);
        String status = "";
        SOAStatusRetDTO ret = null;
        try{
            ret = queryOrderStateClient.queryOrderStatus(orderStatusDTO);
            //todo 更新order状态同时更新到草稿单表和订单表

        }catch(Exception e){
            log.info("queryOrderStatus Error e = {}",e.getMessage());
            e.printStackTrace();
        }
        return ret;
    }


    private List<SOAAttachmentRetDTO> queryOrderAttachment(String id){

        //查出草稿单省编码
        OnlineSettlementPurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        if(purchaseOrder == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到采购单");
        }
        String provinceCode = purchaseOrder.getProvinceCode();
        if(StringUtils.isEmpty(provinceCode)){
            provinceCode = "HN";
        }

        SOAOrderAttachmentDTO attachmentDTO = new SOAOrderAttachmentDTO();
        attachmentDTO.setProvinceCode(provinceCode);

//        //用prikey代替sourceId
//        OnlineSettlementPurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
//        if(purchaseOrder == null){
//            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到符下载要求的订单号");
//        }

        String poNum = purchaseOrder.getPoOrderNum();
        if(StringUtils.isEmpty(poNum)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未生成正式订单号，请稍候再试");
        }
        attachmentDTO.setSourceOrderId(purchaseOrder.getPoOrderNum());
        //查询订单附件路径
        List<SOAAttachmentRetDTO> attachmentList = new ArrayList<>();

        try{
            attachmentList = scmOrderQueryClient.queryOrderAttachment(attachmentDTO);
        }catch(Exception e){
            e.printStackTrace();
            log.info("queryOrderAttachment Error e = {}",e.getMessage());
        }
        return attachmentList;
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> cancelOrder(String id) {
        log.info("cancelOrder id = {}",id);
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        //先查出订单，根据订单状态判断是退回草稿单还是采购单
        List<OnlineSettlementPurchaseOrder> orders = purchaseOrderMapper.selectByExample(new OnlineSettlementPurchaseOrderExample().createCriteria().andIdEqualTo(id).example());
        if(CollectionUtils.isEmpty(orders)){
            log.info("cancelOrder Error : 未找到待取消的订单");
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到待取消的订单");
        }

        OnlineSettlementPurchaseOrder purchaseOrder = orders.get(0);
        String osStatus = purchaseOrder.getSettleStatus();
        //todo 现在是以数据库状态来，也可以先查询一下
        log.info("cancelOrder 采购单状态：{}",osStatus);
        if(SmartChainStatusEnum.STATUS_DRAFT.getOsStatus().equals(osStatus)){
            //只有草稿审批前时才能退回草稿，如果是审批中这种那边会校验的状态，我们都不用调接口
            cancelDraft(id);
        }else if(SmartChainStatusEnum.STATUS_APPROVALED.getOsStatus().equals(osStatus)){
            //只有草稿单已审批后状态时才能取消采购单，否则应该是调草稿单相关接口
            cancelScm(id);
        }else{
            //不支持的状态，有可能状态更新滞后，这个时候提示用户手动刷新后，才能判断到底调用哪个接口
            log.info("cancelOrder 该状态：{}不支持取消操作",osStatus);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "该状态不支持取消操作");
        }
        //记录日志
        try{
            String content = "【取消采购】\n"
                    .concat("合同编号").concat(purchaseOrder.getContractNumber())
                    .concat("采购订单唯一标识").concat(id);
            logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                    OnlineSettleOperateEnum.PURCHASE_ORDER_OPERATE.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);
        }catch(Exception e){
            e.printStackTrace();
        }

        return baseAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<SettleOrderStatusVO> refreshScmStatus(String id) {
        log.info("refreshScmStatus id = {}",id);
        BaseAnswer<SettleOrderStatusVO> answer = new BaseAnswer<SettleOrderStatusVO>();
        SettleOrderStatusVO settleOrderStatusVO = new SettleOrderStatusVO();
        //查询生成单问题
        List<OnlineSettlementPurchaseOrder> orders = purchaseOrderMapper.selectByExample(new OnlineSettlementPurchaseOrderExample().createCriteria().andIdEqualTo(id).example());
        if(CollectionUtils.isEmpty(orders)){
            log.info("refreshScmStatus Error : 未找到待更新的订单");
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到待取消的订单");
        }
        OnlineSettlementPurchaseOrder purchaseOrder = orders.get(0);
        String prikey = purchaseOrder.getPrikey();
        if(StringUtils.isEmpty(prikey)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到待取消订单来源系统单号");
        }

        //如果是草稿单以前的订单，不可查询
        String osStatus = purchaseOrder.getSettleStatus();
        log.info("refreshScmStatus status = {}",osStatus);
        if(Integer.parseInt(osStatus)==6){
            //还不是草稿单情况下，不用查询，直接返回待发起
            settleOrderStatusVO.setScmStatusCode(SmartChainStatusEnum.STATUS_DRAFT.getOsStatus());
            //todo check
            settleOrderStatusVO.setScmStatusDes(SmartChainStatusEnum.STATUS_DRAFT.getSoaDes());
            answer.setData(settleOrderStatusVO);
            return answer;
        }else if(Integer.parseInt(osStatus) <6 ){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "当前处于不可查询的scm采购状态");
        }
        if(StringUtils.isEmpty(purchaseOrder.getScmOrderNum())){
            log.info("refresh empty scmOrderNum");
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到采购订单号，刷新失败");
        }
        SOAStatusRetDTO ret = queryOrderStatus(prikey);
        if(ret == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "查询SOA状态失败");
        }
        String status = ret.getStatus();
        String poNum = ret.getPoNum();
        //审批过的订单，用scm单号查询
//        String status = queryOrderStatus(purchaseOrder.getScmOrderNum());
        log.info("refreshScmStatus status = {}, poNum = {}",status,poNum);
        if(StringUtils.isEmpty(status)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "查询订单状态失败");
        }
        //更新数据库
        OnlineSettlementPurchaseOrder updateOrder = new OnlineSettlementPurchaseOrder();
        updateOrder.setId(id);
        String upStatus = SmartChainStatusEnum.getOsStatus(status);
        log.info("refreshAllScmStatus osStatus = {}",upStatus);
        updateOrder.setSettleStatus(upStatus);
        if(StringUtils.isNotEmpty(poNum)){
            updateOrder.setPoOrderNum(poNum);
            updateOrder.setUpdateTime(new Date());
        }
        purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);


        settleOrderStatusVO.setScmStatusCode(upStatus);
        //todo check
        settleOrderStatusVO.setScmStatusDes(SmartChainStatusEnum.getDesBySoa(status));
        answer.setData(settleOrderStatusVO);

        //记录日志
        String endStatus = SmartChainStatusEnum.getOsStatus(status);
        try{
            String content = "【刷新】\n"
                    .concat("采购订单结算状态从").concat(purchaseOrder.getSettleStatus())
                    .concat("变为").concat(endStatus==null?"unknown":endStatus);
            logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                    OnlineSettleOperateEnum.PURCHASE_ORDER_OPERATE.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);
        }catch(Exception e){
            e.printStackTrace();
        }

        return answer;
    }


    @Override
    public void refreshAllScmStatus() {
        log.info("refreshAllScmStatus Enter");
        //todo or条件怎么传
        List<OnlineSettlementPurchaseOrder> orders = purchaseOrderMapper.selectByExample(new OnlineSettlementPurchaseOrderExample().createCriteria()
                .andSettleStatusNotEqualTo("6").example());
        if(CollectionUtils.isEmpty(orders)){
            log.info("refreshScmStatus Error : 自动更新未找到待更新的采购订单");
        }
        long currentTimeMillis = System.currentTimeMillis();
        log.info("refreshAllScmStatus traverse begin {}", currentTimeMillis);
        for(OnlineSettlementPurchaseOrder item : orders){
            //对每个orders单独去查询更新
            String priKey = item.getPrikey();
//            String scmId = item.getScmOrderNum();
//            String sourceId = item.getId();
            SOAStatusRetDTO ret = queryOrderStatus(priKey);
            if(ret == null){
                log.info("refreshAllScmStatus Error: Empty query ret, 查询SOA订单失败");
                return;
            }
            String status = ret.getStatus();
            String poNum = ret.getPoNum();
            log.info("refreshed sourceId:{}, priKey:{}, status is {}, poNum: {}",item.getId(),priKey, ret.getStatus(),ret.getPoNum());
            if(StringUtils.isEmpty(status)){
                log.info("refreshAllScmStatus 自动更新采购单，查询状态失败");
                continue;
            }
            OnlineSettlementPurchaseOrder updateOrder = new OnlineSettlementPurchaseOrder();
            updateOrder.setId(item.getId());
//            updateOrder.setScmOrderNum(scmId);
            String osStatus = SmartChainStatusEnum.getOsStatus(status);
            log.info("refreshAllScmStatus osStatus = {}",osStatus);
            updateOrder.setSettleStatus(osStatus);
            if(StringUtils.isNotEmpty(poNum)){
                updateOrder.setPoOrderNum(poNum);
                updateOrder.setUpdateTime(new Date());
            }
            purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);
        }
        log.info("refreshAllScmStatus traverse end {}",System.currentTimeMillis()-currentTimeMillis);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downLoadAttachments(SOADownloadParam param) {
        String id = param.getId();
        String pwd = param.getPwd();
        log.info("downLoadAttachments Enter id = {}",id);

//        OnlineSettlementPurchaseOrder order = purchaseOrderMapper.selectByPrimaryKey(id);
//        if(order==null){
//            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未找到对应的订单");
//        }
//        String poNum = order.getPoOrderNum();
//        if(StringUtils.isEmpty(poNum)){
//            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "未生成正式订单号，请稍候再试");
//        }


        List<SOAAttachmentRetDTO> attachmentList = queryOrderAttachment(id);
        if(CollectionUtils.isEmpty(attachmentList)){
            log.info("queryOrderAttachment 采购单附件列表为空");
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "供应链系统还未生成附件，请稍后再试！");
        }
        log.info("queryOrderAttachment attachmentListSize = {}",attachmentList.size());

        //照对方说法，一个单子只有一个附件，先按单个做
        //todo 后续处理分包
        SOAAttachmentRetDTO retAttDTO = attachmentList.get(0);
        String fileName = retAttDTO.getName();
        String url = retAttDTO.getUrl();
        downloadSftpFile(url, fileName);

        //记录日志
        try{
            String content = "【下载】\n"
                    .concat("下载订单zip,采购订单唯一标识").concat(id);
            logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                    OnlineSettleOperateEnum.PURCHASE_ORDER_OPERATE.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);
        }catch(Exception e){
            e.printStackTrace();
        }

        //临时调试接口用
//        testDownloadStream("sftp://sftp_mft@**********/soa","shuimian.rar");
//        testDownloadStream("sftp://sftp_mft@**********/MFTT/HQ_SSCMtoIOTMal","b19ed4864e0e48618b8909e9d1e2a582.zip");

    }


    boolean checkSOAPath(String url){
        if(StringUtils.isEmpty(url)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "找不到对应文件");
        }
        String soaPath = url.substring(url.lastIndexOf("/"));
        if(StringUtils.isEmpty(soaPath)){
            return false;
        }else return soaPath.equals(sftpSoaPath);
    }


    public void downloadSftpFile(String url, String filename) {
        //TODO 判断返回的url跟soapath是否一样，不一样的返回错误
        log.info("downloadSftpFile url = {}; filename = {}",url,filename);
        //由于对方乱传路径，约定我们自己写死，不做验证
//        if(!checkSOAPath(url)){
//            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "局方下载路径错误");
//        }
        InputStream is = null;
        OutputStream os = null;

        //用是否有数据流来判断
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);

        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletResponse response = requestAttr.getResponse();
        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
//            log.info("FTP登录成功！");
            is = sftpUtil.getInputStream(sftpSoaPath, filename);
            log.info("downloadSftpFile 下载附件 url = {}, filename = {}",sftpSoaPath, filename);

//            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.addHeader("stateCode", SUCCESS.getStateCode());
            response.addHeader("message", "success");
            //成功失败用head头反馈
//            response.addHeader("stateCode", stateCode);
//            response.addHeader("message", URLEncoder.encode(message, "UTF-8"));

            os = response.getOutputStream();
            byte[] b = new byte[1024];
            int len;
            while((len = is.read(b))>0){
                os.write(b, 0, len);
            }
            os.flush();

            log.info("文件流 is = {}",is);
        }catch(SftpException fe){
//            fe.printStackTrace();
            log.info("error code = {}",fe.id);  //2
            log.info("error msg = {}",fe.getMessage());  //No such file
            if(fe.id==2){
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "找不到对应文件");
            }
        }catch(Exception e){
            e.printStackTrace();
            response.addHeader("stateCode", SERVER_INTERNAL_ERROR.getStateCode());
            response.addHeader("message", "failed");
        }finally{
            try{
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            try{
                if(os!=null){
                    os.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
//            try{
//                sftpUtil.delete(sftpSoaPath, filename);
//            }catch(Exception e){
//                log.info("DELETE_FILE_ERROR: sftp file name = {}",filename);
//            }
        }
    }

    private String genDraftFile(String id) {

        List<ExportOnlineProvinceBillTitleDTO> billTitleDTOList = purchaseOrderMapperExt.listOnlineProvinceBillTitle(id);
        if (CollectionUtils.isEmpty(billTitleDTOList)){
            throw new BusinessException("10004","没有获取到该采购订单对应的信息");
        }

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();

        // 获取第一个和最后一个数据
        ExportOnlineProvinceBillTitleDTO billTitleDTO = billTitleDTOList.get(0);
        ExportOnlineProvinceBillTitleDTO lastBillTitleDTO = billTitleDTOList.get(billTitleDTOList.size() - 1);
        // 用于存储导出的对账单信息
        Map exportMap = new HashMap();
        Date date = new Date();
        String saleMainName = "中国移动通信集团"
                .concat(billTitleDTO.getProvinceName())
                .concat("有限公司");
        String reconciliationCycle = DateUtils.dateToStr(lastBillTitleDTO.getOrderSuccessTime(),DateUtils.DEFAULT_DATE_FORMAT)
                .concat("--").concat(DateUtils.dateToStr(billTitleDTO.getOrderSuccessTime(),DateUtils.DEFAULT_DATE_FORMAT));
        // 对账单编号
        String billNum = DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL_A);
        exportMap.put("billNum", billNum);
        // 销售（采购）主体名称
        exportMap.put("saleMainName", saleMainName);
        // 对账周期
        exportMap.put("reconciliationCycle", reconciliationCycle);
        // 采购合同名称
//        exportMap.put("purchaseContractName", billTitleDTO.getContractName());
        // 采购合同编号
        exportMap.put("purchaseContractNum", billTitleDTO.getProvinceContractCode());
        // 采购合同金额
        exportMap.put("purchaseContractPrice", billTitleDTO.getAmountIncludingTax());
        // 采购合同失效日期
        Date endDate = billTitleDTO.getContractEndDate();
        String endDateStr = DateUtils.dateToStr(endDate,DateUtils.DEFAULT_DATE_FORMAT);
        exportMap.put("purchaseContractEndDate", endDateStr);
        // 订单金额
        BigDecimal orderTotalPrice = new BigDecimal(billTitleDTO.getSettlePrice()).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
        exportMap.put("orderTotalPrice", orderTotalPrice);

        // 总的销售数量
        Integer saleTotalCount = 0;
        // 总的不含税金额
        BigDecimal unitTaxSettleTotalPrice = new BigDecimal(0);
        // 总的含税金额
        BigDecimal taxSettleTotalPrice = new BigDecimal(0);

        BigDecimal thousand = new BigDecimal(1000);
        // 获取导出省侧对账单行数据
        List<ExportOnlineProvinceBillColumnDTO> billColumnDTOList = purchaseOrderMapperExt.listOnlineProvinceBillColumn(id);
        if (CollectionUtils.isNotEmpty(billColumnDTOList)){
            for (int i = 0; i < billColumnDTOList.size(); i++) {
                ExportOnlineProvinceBillColumnDTO billColumnDTO = billColumnDTOList.get(i);
                Integer needQuantity = billColumnDTO.getNeedQuantity();
                if (needQuantity == null){
                    needQuantity = 0;
                }

                String taxInclusivePrice = billColumnDTO.getTaxInclusivePrice();
                if (StringUtils.isEmpty(taxInclusivePrice)){
                    taxInclusivePrice = "0";
                }

                BigDecimal taxInclusiveTotalPriceDec = new BigDecimal(needQuantity).multiply(new BigDecimal(taxInclusivePrice));
                billColumnDTO.setTaxInclusiveTotalPriceDec(taxInclusiveTotalPriceDec);
                taxSettleTotalPrice = taxSettleTotalPrice.add(taxInclusiveTotalPriceDec);

                String unitPrice = billColumnDTO.getUnitPrice();
                if (StringUtils.isEmpty(unitPrice)){
                    unitPrice = "0";
                }

                BigDecimal unitTotalPriceDec = new BigDecimal(needQuantity).multiply(new BigDecimal(unitPrice));
                billColumnDTO.setUnitTotalPriceDec(unitTotalPriceDec);
                unitTaxSettleTotalPrice = unitTaxSettleTotalPrice.add(unitTotalPriceDec);

                billColumnDTO.setTaxPrice(taxInclusiveTotalPriceDec.add(unitTotalPriceDec.multiply(new BigDecimal("-1"))));

                saleTotalCount = saleTotalCount+ needQuantity;
            }
        }else{
            billColumnDTOList.add(new ExportOnlineProvinceBillColumnDTO());
        }

        // 总的税额
        BigDecimal taxTotalPrice = taxSettleTotalPrice.add(unitTaxSettleTotalPrice.multiply(new BigDecimal("-1")));
        exportMap.put("taxSettleTotalPrice", taxSettleTotalPrice);
        exportMap.put("unitTaxSettleTotalPrice", unitTaxSettleTotalPrice);
        exportMap.put("taxTotalPrice", taxTotalPrice);
        exportMap.put("saleTotalCount", saleTotalCount);

        EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "对账单（省侧-对账单）", "list",
                billColumnDTOList, exportMap);
        easyExcelDTOList.add(easyExcelDTO);

        // 省侧订单明细数据
        List<ExportOnlineProvinceBillOrderDTO> billOrderDTOList = purchaseOrderMapperExt.listOnlineProvinceBilOrder(id);
        if (CollectionUtils.isNotEmpty(billOrderDTOList)){
            billOrderDTOList.forEach(billOrderDTO->{
                Date orderCreateTime = billOrderDTO.getOrderCreateTime();
                billOrderDTO.setOrderCreateTimeStr(DateUtils.dateToStr(orderCreateTime,DateUtils.DEFAULT_DATETIME_FORMAT));

                Date orderSuccessTime = billOrderDTO.getOrderSuccessTime();
                billOrderDTO.setOrderSuccessTimeStr(DateUtils.dateToStr(orderSuccessTime,DateUtils.DEFAULT_DATETIME_FORMAT));

                String spuOfferingClass = billOrderDTO.getSpuOfferingClass();
                billOrderDTO.setSpuOfferingClassName(SPUOfferingClassEnum.getDescribe(spuOfferingClass));

                String atomOfferingClass = billOrderDTO.getAtomOfferingClass();
                billOrderDTO.setAtomOfferingClassName(AtomOfferingClassEnum.getDescribe(atomOfferingClass));

                Long settlePrice = billOrderDTO.getSettlePrice();
                if (settlePrice != null){
                    billOrderDTO.setSettlePriceDec(new BigDecimal(settlePrice).divide(thousand,2,RoundingMode.HALF_UP));
                }

                Long atomTotalPrice = billOrderDTO.getAtomTotalPrice();
                if (atomTotalPrice != null){
                    billOrderDTO.setAtomTotalPriceDec(new BigDecimal(atomTotalPrice).divide(thousand,2,RoundingMode.HALF_UP));
                }

                Long atomTotalSettlePrice = billOrderDTO.getAtomTotalSettlePrice();
                if (atomTotalSettlePrice != null){
                    billOrderDTO.setAtomTotalSettlePriceDec(new BigDecimal(atomTotalSettlePrice).divide(thousand,2,RoundingMode.HALF_UP));
                }

                Long materialSettlePrice = billOrderDTO.getMaterialSettlePrice();
                if (materialSettlePrice != null){
                    billOrderDTO.setMaterialSettlePriceDec(new BigDecimal(materialSettlePrice).divide(thousand,2,RoundingMode.HALF_UP));
                }

                Long materialTotalSettlePrice = billOrderDTO.getMaterialTotalSettlePrice();
                if (materialTotalSettlePrice != null){
                    billOrderDTO.setMaterialTotalSettlePriceDec(new BigDecimal(materialTotalSettlePrice).divide(thousand,2,RoundingMode.HALF_UP));
                }

                String contactPersonName = billOrderDTO.getContactPersonName();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(contactPersonName)){
                    contactPersonName = IOTEncodeUtils.decryptSM4(contactPersonName, iotSm4Key, iotSm4Iv);
                    billOrderDTO.setContactPersonName(contactPersonName);
                }

                String contactPersonAddr = "";
                String addr1 = IOTEncodeUtils.decryptSM4(billOrderDTO.getAddr1(), iotSm4Key, iotSm4Iv);
                contactPersonAddr = contactPersonAddr.concat(addr1);
                String addr2 = billOrderDTO.getAddr2();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(addr2)){
                    addr2 = IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv);
                    contactPersonAddr = contactPersonAddr.concat(addr2);
                }
                String addr3 = billOrderDTO.getAddr3();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(addr3)){
                    addr3 = IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv);
                    contactPersonAddr = contactPersonAddr.concat(addr3);
                }
                String addr4 = billOrderDTO.getAddr4();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(addr4)){
                    addr4 = IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv);
                    contactPersonAddr = contactPersonAddr.concat(addr4);
                }
                String usaddr = billOrderDTO.getUsaddr();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(usaddr)){
                    usaddr = IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
                    contactPersonAddr = contactPersonAddr.concat(usaddr);
                }
                billOrderDTO.setContactPersonAddr(contactPersonAddr);

                String contactPhone = billOrderDTO.getContactPhone();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(contactPhone)){
                    contactPhone = IOTEncodeUtils.decryptSM4(contactPhone, iotSm4Key, iotSm4Iv);
                    billOrderDTO.setContactPhone(contactPhone);
                }

                Integer contractType = billOrderDTO.getContractType();
                billOrderDTO.setContractTypeName(ContractTypeEnum.getNameByCode(contractType));

                Integer countType = billOrderDTO.getCountType();
                billOrderDTO.setCountTypeName(CountTypeEnum.getDescByType(countType));

            });
        }else{
            billOrderDTOList.add(new ExportOnlineProvinceBillOrderDTO());
        }

        easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(1, "对账单（省侧-订单明细）", "list",
                billOrderDTOList, null);
        easyExcelDTOList.add(easyExcelDTO);

//        String fileDate = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
//        String excelName = "对账单_"+id+fileDate;
        String excelName = "statement_"+id+".xlsx";

        InputStream templateFileName = null;
        FileOutputStream outputStream = null;
        try{
            excelName = URLEncoder.encode(excelName, "UTF-8");
            ClassPathResource classPathResource = new ClassPathResource("template/province_bill_template.xlsx");
            templateFileName = classPathResource.getInputStream();
            // 导出对账单

            outputStream = new FileOutputStream(excelName);
            EasyExcelUtils.exportManySheetExcel2OutputStream(outputStream, easyExcelDTOList,
                    templateFileName ,new ArrayList<>(), new ArrayList<>(), null, HorizontalAlignment.LEFT);
        }catch(Exception e){

        }finally{
            try{
                if(templateFileName!=null){
                    templateFileName.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
            try{
                if(outputStream!=null){
                    outputStream.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }

        }

        return excelName;
    }


    public void uploadDraftFile(String zipName) {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        File src = new File(zipName);
        FileInputStream inputStream = null;
        boolean ret = false;
        try{
            if(!sftpUtil.login()){
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            inputStream = new FileInputStream(src);
            ret = sftpUtil.upload(sftpOsPath, zipName, inputStream);
            log.info("uploadSftpFile 上传完成 ret = {}",ret);

//            Files.delete(src.getAbsoluteFile().toPath());
        }catch(Exception e){
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传草稿附件失败");
        }finally{
            try{
                if(inputStream!=null){
                    inputStream.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
            //删除本地zip文件
            log.info("finally enter src.exists = {}, inputstream = {}",src.exists(),inputStream);
            if(src.exists()){
                try{
                    boolean dr = src.delete();
                    log.info("DELETE_ZIP_FILE dr = {}， abpath = {}",dr,src.getAbsolutePath());
                }catch(Exception e){
                    e.printStackTrace();
                }
                log.info("DELETE_FILE_ERROR: name = {}",zipName);
            }
        }
        log.info("uploadSftpFile1 上传文件成功 = {}",ret);
    }



    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private BigDecimal getRound8Dec (BigDecimal num) {
        //todo 确定位数和四舍五入方式
        return num.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private void genSubOrderExcel(String name){

    }


    private String getMaskingName(String name){
        //todo 姓名脱敏
        String maskingName = "";
        if(StringUtils.isEmpty(name)){
            return "";
        }
        if(name.length()==2){

        }else if(name.length()>2){

        }
        return name;
    }


}
