
package com.chinamobile.iot.sc.service.soa.orderImport;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for RESPONSECOLLECTION complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="RESPONSECOLLECTION"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="RESPONSECOLLECTION_ITEM" type="{http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv}RESPONSECOLLECTION_ITEM" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RESPONSECOLLECTION", propOrder = {
    "responsecollectionitem"
})
public class RESPONSECOLLECTION {

    @XmlElement(name = "RESPONSECOLLECTION_ITEM")
    protected List<RESPONSECOLLECTIONITEM> responsecollectionitem;

    /**
     * Gets the value of the responsecollectionitem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the responsecollectionitem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRESPONSECOLLECTIONITEM().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RESPONSECOLLECTIONITEM }
     * 
     * 
     */
    public List<RESPONSECOLLECTIONITEM> getRESPONSECOLLECTIONITEM() {
        if (responsecollectionitem == null) {
            responsecollectionitem = new ArrayList<RESPONSECOLLECTIONITEM>();
        }
        return this.responsecollectionitem;
    }

}
