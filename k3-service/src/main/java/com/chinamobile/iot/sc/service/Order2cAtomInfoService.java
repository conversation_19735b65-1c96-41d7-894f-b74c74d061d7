package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.entity.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.param.UpdateSettleStatusParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/11
 * @description 原子订单service接口类
 */
public interface Order2cAtomInfoService {

    /**
     * 更新原子订单表结算状态等字段
     *
     * @param updateSettleStatusParam
     */
    void updateSettleStatus(UpdateSettleStatusParam updateSettleStatusParam);

    /**
     * 根据需要获取订单信息
     * @param order2cAtomInfoExample
     * @return
     */
    List<Order2cAtomInfo> listOrder2cAtomInfoByNeed(Order2cAtomInfoExample order2cAtomInfoExample);

    /**
     * 根据需要更新订单信息
     * @param order2cAtomInfo
     * @param order2cAtomInfoExample
     */
    void updateOrder2cAtomInfoByNeed(Order2cAtomInfo order2cAtomInfo,
                                     Order2cAtomInfoExample order2cAtomInfoExample);

}
