package com.chinamobile.iot.sc.pojo.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProvinceMaterialInfoNewExample {
    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNewExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNewExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ProvinceMaterialInfoNewExample example = new ProvinceMaterialInfoNewExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNewExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNewExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIsNull() {
            addCriterion("internet_contract_code is null");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIsNotNull() {
            addCriterion("internet_contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeEqualTo(String value) {
            addCriterion("internet_contract_code =", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotEqualTo(String value) {
            addCriterion("internet_contract_code <>", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThan(String value) {
            addCriterion("internet_contract_code >", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("internet_contract_code >=", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThan(String value) {
            addCriterion("internet_contract_code <", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanOrEqualTo(String value) {
            addCriterion("internet_contract_code <=", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_contract_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLike(String value) {
            addCriterion("internet_contract_code like", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotLike(String value) {
            addCriterion("internet_contract_code not like", value, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeIn(List<String> values) {
            addCriterion("internet_contract_code in", values, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotIn(List<String> values) {
            addCriterion("internet_contract_code not in", values, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeBetween(String value1, String value2) {
            addCriterion("internet_contract_code between", value1, value2, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeNotBetween(String value1, String value2) {
            addCriterion("internet_contract_code not between", value1, value2, "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(String value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(String value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(String value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(String value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLike(String value) {
            addCriterion("contract_type like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotLike(String value) {
            addCriterion("contract_type not like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<String> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<String> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(String value1, String value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(String value1, String value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("contract_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("material_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("material_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("material_code =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("material_code <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("material_code >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_code >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("material_code <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("material_code <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("material_code like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("material_code not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("material_code in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("material_code not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("material_code between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("material_code not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNull() {
            addCriterion("material_name is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNotNull() {
            addCriterion("material_name is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualTo(String value) {
            addCriterion("material_name =", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualTo(String value) {
            addCriterion("material_name <>", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThan(String value) {
            addCriterion("material_name >", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualTo(String value) {
            addCriterion("material_name >=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThan(String value) {
            addCriterion("material_name <", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualTo(String value) {
            addCriterion("material_name <=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("material_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLike(String value) {
            addCriterion("material_name like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotLike(String value) {
            addCriterion("material_name not like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIn(List<String> values) {
            addCriterion("material_name in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotIn(List<String> values) {
            addCriterion("material_name not in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameBetween(String value1, String value2) {
            addCriterion("material_name between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotBetween(String value1, String value2) {
            addCriterion("material_name not between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andAttr2IsNull() {
            addCriterion("attr2 is null");
            return (Criteria) this;
        }

        public Criteria andAttr2IsNotNull() {
            addCriterion("attr2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr2EqualTo(String value) {
            addCriterion("attr2 =", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2EqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2NotEqualTo(String value) {
            addCriterion("attr2 <>", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThan(String value) {
            addCriterion("attr2 >", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThanOrEqualTo(String value) {
            addCriterion("attr2 >=", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2LessThan(String value) {
            addCriterion("attr2 <", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2LessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2LessThanOrEqualTo(String value) {
            addCriterion("attr2 <=", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2LessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr2 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr2Like(String value) {
            addCriterion("attr2 like", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotLike(String value) {
            addCriterion("attr2 not like", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2In(List<String> values) {
            addCriterion("attr2 in", values, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotIn(List<String> values) {
            addCriterion("attr2 not in", values, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2Between(String value1, String value2) {
            addCriterion("attr2 between", value1, value2, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotBetween(String value1, String value2) {
            addCriterion("attr2 not between", value1, value2, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr3IsNull() {
            addCriterion("attr3 is null");
            return (Criteria) this;
        }

        public Criteria andAttr3IsNotNull() {
            addCriterion("attr3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr3EqualTo(String value) {
            addCriterion("attr3 =", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3EqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3NotEqualTo(String value) {
            addCriterion("attr3 <>", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThan(String value) {
            addCriterion("attr3 >", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThanOrEqualTo(String value) {
            addCriterion("attr3 >=", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3LessThan(String value) {
            addCriterion("attr3 <", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3LessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3LessThanOrEqualTo(String value) {
            addCriterion("attr3 <=", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3LessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("attr3 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttr3Like(String value) {
            addCriterion("attr3 like", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotLike(String value) {
            addCriterion("attr3 not like", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3In(List<String> values) {
            addCriterion("attr3 in", values, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotIn(List<String> values) {
            addCriterion("attr3 not in", values, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3Between(String value1, String value2) {
            addCriterion("attr3 between", value1, value2, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotBetween(String value1, String value2) {
            addCriterion("attr3 not between", value1, value2, "attr3");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNull() {
            addCriterion("unit_price is null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNotNull() {
            addCriterion("unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceEqualTo(String value) {
            addCriterion("unit_price =", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotEqualTo(String value) {
            addCriterion("unit_price <>", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThan(String value) {
            addCriterion("unit_price >", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanOrEqualTo(String value) {
            addCriterion("unit_price >=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThan(String value) {
            addCriterion("unit_price <", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanOrEqualTo(String value) {
            addCriterion("unit_price <=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("unit_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLike(String value) {
            addCriterion("unit_price like", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotLike(String value) {
            addCriterion("unit_price not like", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIn(List<String> values) {
            addCriterion("unit_price in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotIn(List<String> values) {
            addCriterion("unit_price not in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceBetween(String value1, String value2) {
            addCriterion("unit_price between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotBetween(String value1, String value2) {
            addCriterion("unit_price not between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceIsNull() {
            addCriterion("tax_inclusive_price is null");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceIsNotNull() {
            addCriterion("tax_inclusive_price is not null");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceEqualTo(String value) {
            addCriterion("tax_inclusive_price =", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceNotEqualTo(String value) {
            addCriterion("tax_inclusive_price <>", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceGreaterThan(String value) {
            addCriterion("tax_inclusive_price >", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceGreaterThanOrEqualTo(String value) {
            addCriterion("tax_inclusive_price >=", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLessThan(String value) {
            addCriterion("tax_inclusive_price <", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLessThanOrEqualTo(String value) {
            addCriterion("tax_inclusive_price <=", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLike(String value) {
            addCriterion("tax_inclusive_price like", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceNotLike(String value) {
            addCriterion("tax_inclusive_price not like", value, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceIn(List<String> values) {
            addCriterion("tax_inclusive_price in", values, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceNotIn(List<String> values) {
            addCriterion("tax_inclusive_price not in", values, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceBetween(String value1, String value2) {
            addCriterion("tax_inclusive_price between", value1, value2, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceNotBetween(String value1, String value2) {
            addCriterion("tax_inclusive_price not between", value1, value2, "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxCodeIsNull() {
            addCriterion("tax_code is null");
            return (Criteria) this;
        }

        public Criteria andTaxCodeIsNotNull() {
            addCriterion("tax_code is not null");
            return (Criteria) this;
        }

        public Criteria andTaxCodeEqualTo(String value) {
            addCriterion("tax_code =", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeNotEqualTo(String value) {
            addCriterion("tax_code <>", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeGreaterThan(String value) {
            addCriterion("tax_code >", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeGreaterThanOrEqualTo(String value) {
            addCriterion("tax_code >=", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeLessThan(String value) {
            addCriterion("tax_code <", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeLessThanOrEqualTo(String value) {
            addCriterion("tax_code <=", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxCodeLike(String value) {
            addCriterion("tax_code like", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeNotLike(String value) {
            addCriterion("tax_code not like", value, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeIn(List<String> values) {
            addCriterion("tax_code in", values, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeNotIn(List<String> values) {
            addCriterion("tax_code not in", values, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeBetween(String value1, String value2) {
            addCriterion("tax_code between", value1, value2, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxCodeNotBetween(String value1, String value2) {
            addCriterion("tax_code not between", value1, value2, "taxCode");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("tax_rate <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagIsNull() {
            addCriterion("sd_project_flag is null");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagIsNotNull() {
            addCriterion("sd_project_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagEqualTo(String value) {
            addCriterion("sd_project_flag =", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagNotEqualTo(String value) {
            addCriterion("sd_project_flag <>", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagGreaterThan(String value) {
            addCriterion("sd_project_flag >", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagGreaterThanOrEqualTo(String value) {
            addCriterion("sd_project_flag >=", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLessThan(String value) {
            addCriterion("sd_project_flag <", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLessThanOrEqualTo(String value) {
            addCriterion("sd_project_flag <=", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("sd_project_flag <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLike(String value) {
            addCriterion("sd_project_flag like", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagNotLike(String value) {
            addCriterion("sd_project_flag not like", value, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagIn(List<String> values) {
            addCriterion("sd_project_flag in", values, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagNotIn(List<String> values) {
            addCriterion("sd_project_flag not in", values, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagBetween(String value1, String value2) {
            addCriterion("sd_project_flag between", value1, value2, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagNotBetween(String value1, String value2) {
            addCriterion("sd_project_flag not between", value1, value2, "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIsNull() {
            addCriterion("internet_material_code is null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIsNotNull() {
            addCriterion("internet_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeEqualTo(String value) {
            addCriterion("internet_material_code =", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotEqualTo(String value) {
            addCriterion("internet_material_code <>", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThan(String value) {
            addCriterion("internet_material_code >", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("internet_material_code >=", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThan(String value) {
            addCriterion("internet_material_code <", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("internet_material_code <=", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLike(String value) {
            addCriterion("internet_material_code like", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotLike(String value) {
            addCriterion("internet_material_code not like", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIn(List<String> values) {
            addCriterion("internet_material_code in", values, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotIn(List<String> values) {
            addCriterion("internet_material_code not in", values, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeBetween(String value1, String value2) {
            addCriterion("internet_material_code between", value1, value2, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("internet_material_code not between", value1, value2, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameIsNull() {
            addCriterion("internet_material_name is null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameIsNotNull() {
            addCriterion("internet_material_name is not null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameEqualTo(String value) {
            addCriterion("internet_material_name =", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameNotEqualTo(String value) {
            addCriterion("internet_material_name <>", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameGreaterThan(String value) {
            addCriterion("internet_material_name >", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameGreaterThanOrEqualTo(String value) {
            addCriterion("internet_material_name >=", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLessThan(String value) {
            addCriterion("internet_material_name <", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLessThanOrEqualTo(String value) {
            addCriterion("internet_material_name <=", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("internet_material_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLike(String value) {
            addCriterion("internet_material_name like", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameNotLike(String value) {
            addCriterion("internet_material_name not like", value, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameIn(List<String> values) {
            addCriterion("internet_material_name in", values, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameNotIn(List<String> values) {
            addCriterion("internet_material_name not in", values, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameBetween(String value1, String value2) {
            addCriterion("internet_material_name between", value1, value2, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameNotBetween(String value1, String value2) {
            addCriterion("internet_material_name not between", value1, value2, "internetMaterialName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProvinceMaterialInfoNew.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andInternetContractCodeLikeInsensitive(String value) {
            addCriterion("upper(internet_contract_code) like", value.toUpperCase(), "internetContractCode");
            return (Criteria) this;
        }

        public Criteria andContractTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_type) like", value.toUpperCase(), "contractType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(province_code) like", value.toUpperCase(), "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andCityCodeLikeInsensitive(String value) {
            addCriterion("upper(city_code) like", value.toUpperCase(), "cityCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLikeInsensitive(String value) {
            addCriterion("upper(contract_code) like", value.toUpperCase(), "contractCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLikeInsensitive(String value) {
            addCriterion("upper(material_code) like", value.toUpperCase(), "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLikeInsensitive(String value) {
            addCriterion("upper(material_name) like", value.toUpperCase(), "materialName");
            return (Criteria) this;
        }

        public Criteria andAttr2LikeInsensitive(String value) {
            addCriterion("upper(attr2) like", value.toUpperCase(), "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr3LikeInsensitive(String value) {
            addCriterion("upper(attr3) like", value.toUpperCase(), "attr3");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLikeInsensitive(String value) {
            addCriterion("upper(unit_price) like", value.toUpperCase(), "unitPrice");
            return (Criteria) this;
        }

        public Criteria andTaxInclusivePriceLikeInsensitive(String value) {
            addCriterion("upper(tax_inclusive_price) like", value.toUpperCase(), "taxInclusivePrice");
            return (Criteria) this;
        }

        public Criteria andTaxCodeLikeInsensitive(String value) {
            addCriterion("upper(tax_code) like", value.toUpperCase(), "taxCode");
            return (Criteria) this;
        }

        public Criteria andSdProjectFlagLikeInsensitive(String value) {
            addCriterion("upper(sd_project_flag) like", value.toUpperCase(), "sdProjectFlag");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLikeInsensitive(String value) {
            addCriterion("upper(internet_material_code) like", value.toUpperCase(), "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialNameLikeInsensitive(String value) {
            addCriterion("upper(internet_material_name) like", value.toUpperCase(), "internetMaterialName");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 10 15:14:39 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private ProvinceMaterialInfoNewExample example;

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        protected Criteria(ProvinceMaterialInfoNewExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public ProvinceMaterialInfoNewExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Mar 10 15:14:39 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ProvinceMaterialInfoNewExample example);
    }
}