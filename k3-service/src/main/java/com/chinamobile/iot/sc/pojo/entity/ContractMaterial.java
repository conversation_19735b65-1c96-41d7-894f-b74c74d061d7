package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class ContractMaterial implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String id;

    /**
     * 合同编码
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String contractNumber;

    /**
     * 物料编码
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String materialNumber;

    /**
     * 物料名称
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String name;

    /**
     * 物料归属部门
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String ownerDepartment;

    /**
     * 型号
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String model;

    /**
     * 所属产品编码
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String productId;

    /**
     * 物料单位
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String materialUnit;

    /**
     * 最小销售数量
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String planStrategy;

    /**
     * 结算单价(厘)
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long settlePrice;

    /**
     * 服务包ID
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String servicePackId;

    /**
     * 服务包已经使用额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long serviceQuotaUsed;

    /**
     * 服务包剩余额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long serviceQuotaRemain;

    /**
     * 服务包预占额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long serviceQuotaReverse;

    /**
     * 物料已经使用额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long materialQuotaUsed;

    /**
     * 物料剩余额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long materialQuotaRemain;

    /**
     * 物料预占额度
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Long materialQuotaReverse;

    /**
     * 物料类型，1-内部物料（通过查询K3的物料），2-外部物料
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private Integer materialType;

    /**
     * 销项税率
     *
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private String labelTax;

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..contract_material.id
     *
     * @return the value of supply_chain..contract_material.id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.id
     *
     * @param id the value for supply_chain..contract_material.id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.contract_number
     *
     * @return the value of supply_chain..contract_material.contract_number
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getContractNumber() {
        return contractNumber;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withContractNumber(String contractNumber) {
        this.setContractNumber(contractNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.contract_number
     *
     * @param contractNumber the value for supply_chain..contract_material.contract_number
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_number
     *
     * @return the value of supply_chain..contract_material.material_number
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getMaterialNumber() {
        return materialNumber;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialNumber(String materialNumber) {
        this.setMaterialNumber(materialNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_number
     *
     * @param materialNumber the value for supply_chain..contract_material.material_number
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialNumber(String materialNumber) {
        this.materialNumber = materialNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.name
     *
     * @return the value of supply_chain..contract_material.name
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.name
     *
     * @param name the value for supply_chain..contract_material.name
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.owner_department
     *
     * @return the value of supply_chain..contract_material.owner_department
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getOwnerDepartment() {
        return ownerDepartment;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withOwnerDepartment(String ownerDepartment) {
        this.setOwnerDepartment(ownerDepartment);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.owner_department
     *
     * @param ownerDepartment the value for supply_chain..contract_material.owner_department
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setOwnerDepartment(String ownerDepartment) {
        this.ownerDepartment = ownerDepartment;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.model
     *
     * @return the value of supply_chain..contract_material.model
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.model
     *
     * @param model the value for supply_chain..contract_material.model
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.product_id
     *
     * @return the value of supply_chain..contract_material.product_id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getProductId() {
        return productId;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withProductId(String productId) {
        this.setProductId(productId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.product_id
     *
     * @param productId the value for supply_chain..contract_material.product_id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_unit
     *
     * @return the value of supply_chain..contract_material.material_unit
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getMaterialUnit() {
        return materialUnit;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialUnit(String materialUnit) {
        this.setMaterialUnit(materialUnit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_unit
     *
     * @param materialUnit the value for supply_chain..contract_material.material_unit
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.plan_strategy
     *
     * @return the value of supply_chain..contract_material.plan_strategy
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getPlanStrategy() {
        return planStrategy;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withPlanStrategy(String planStrategy) {
        this.setPlanStrategy(planStrategy);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.plan_strategy
     *
     * @param planStrategy the value for supply_chain..contract_material.plan_strategy
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setPlanStrategy(String planStrategy) {
        this.planStrategy = planStrategy;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.settle_price
     *
     * @return the value of supply_chain..contract_material.settle_price
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getSettlePrice() {
        return settlePrice;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withSettlePrice(Long settlePrice) {
        this.setSettlePrice(settlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.settle_price
     *
     * @param settlePrice the value for supply_chain..contract_material.settle_price
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setSettlePrice(Long settlePrice) {
        this.settlePrice = settlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.service_pack_id
     *
     * @return the value of supply_chain..contract_material.service_pack_id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getServicePackId() {
        return servicePackId;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withServicePackId(String servicePackId) {
        this.setServicePackId(servicePackId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.service_pack_id
     *
     * @param servicePackId the value for supply_chain..contract_material.service_pack_id
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setServicePackId(String servicePackId) {
        this.servicePackId = servicePackId;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.service_quota_used
     *
     * @return the value of supply_chain..contract_material.service_quota_used
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getServiceQuotaUsed() {
        return serviceQuotaUsed;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withServiceQuotaUsed(Long serviceQuotaUsed) {
        this.setServiceQuotaUsed(serviceQuotaUsed);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.service_quota_used
     *
     * @param serviceQuotaUsed the value for supply_chain..contract_material.service_quota_used
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setServiceQuotaUsed(Long serviceQuotaUsed) {
        this.serviceQuotaUsed = serviceQuotaUsed;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.service_quota_remain
     *
     * @return the value of supply_chain..contract_material.service_quota_remain
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getServiceQuotaRemain() {
        return serviceQuotaRemain;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withServiceQuotaRemain(Long serviceQuotaRemain) {
        this.setServiceQuotaRemain(serviceQuotaRemain);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.service_quota_remain
     *
     * @param serviceQuotaRemain the value for supply_chain..contract_material.service_quota_remain
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setServiceQuotaRemain(Long serviceQuotaRemain) {
        this.serviceQuotaRemain = serviceQuotaRemain;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.service_quota_reverse
     *
     * @return the value of supply_chain..contract_material.service_quota_reverse
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getServiceQuotaReverse() {
        return serviceQuotaReverse;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withServiceQuotaReverse(Long serviceQuotaReverse) {
        this.setServiceQuotaReverse(serviceQuotaReverse);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.service_quota_reverse
     *
     * @param serviceQuotaReverse the value for supply_chain..contract_material.service_quota_reverse
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setServiceQuotaReverse(Long serviceQuotaReverse) {
        this.serviceQuotaReverse = serviceQuotaReverse;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_quota_used
     *
     * @return the value of supply_chain..contract_material.material_quota_used
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getMaterialQuotaUsed() {
        return materialQuotaUsed;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialQuotaUsed(Long materialQuotaUsed) {
        this.setMaterialQuotaUsed(materialQuotaUsed);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_quota_used
     *
     * @param materialQuotaUsed the value for supply_chain..contract_material.material_quota_used
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialQuotaUsed(Long materialQuotaUsed) {
        this.materialQuotaUsed = materialQuotaUsed;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_quota_remain
     *
     * @return the value of supply_chain..contract_material.material_quota_remain
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getMaterialQuotaRemain() {
        return materialQuotaRemain;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialQuotaRemain(Long materialQuotaRemain) {
        this.setMaterialQuotaRemain(materialQuotaRemain);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_quota_remain
     *
     * @param materialQuotaRemain the value for supply_chain..contract_material.material_quota_remain
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialQuotaRemain(Long materialQuotaRemain) {
        this.materialQuotaRemain = materialQuotaRemain;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_quota_reverse
     *
     * @return the value of supply_chain..contract_material.material_quota_reverse
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Long getMaterialQuotaReverse() {
        return materialQuotaReverse;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialQuotaReverse(Long materialQuotaReverse) {
        this.setMaterialQuotaReverse(materialQuotaReverse);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_quota_reverse
     *
     * @param materialQuotaReverse the value for supply_chain..contract_material.material_quota_reverse
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialQuotaReverse(Long materialQuotaReverse) {
        this.materialQuotaReverse = materialQuotaReverse;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.material_type
     *
     * @return the value of supply_chain..contract_material.material_type
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public Integer getMaterialType() {
        return materialType;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withMaterialType(Integer materialType) {
        this.setMaterialType(materialType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.material_type
     *
     * @param materialType the value for supply_chain..contract_material.material_type
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_material.label_tax
     *
     * @return the value of supply_chain..contract_material.label_tax
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public String getLabelTax() {
        return labelTax;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public ContractMaterial withLabelTax(String labelTax) {
        this.setLabelTax(labelTax);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_material.label_tax
     *
     * @param labelTax the value for supply_chain..contract_material.label_tax
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public void setLabelTax(String labelTax) {
        this.labelTax = labelTax;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractNumber=").append(contractNumber);
        sb.append(", materialNumber=").append(materialNumber);
        sb.append(", name=").append(name);
        sb.append(", ownerDepartment=").append(ownerDepartment);
        sb.append(", model=").append(model);
        sb.append(", productId=").append(productId);
        sb.append(", materialUnit=").append(materialUnit);
        sb.append(", planStrategy=").append(planStrategy);
        sb.append(", settlePrice=").append(settlePrice);
        sb.append(", servicePackId=").append(servicePackId);
        sb.append(", serviceQuotaUsed=").append(serviceQuotaUsed);
        sb.append(", serviceQuotaRemain=").append(serviceQuotaRemain);
        sb.append(", serviceQuotaReverse=").append(serviceQuotaReverse);
        sb.append(", materialQuotaUsed=").append(materialQuotaUsed);
        sb.append(", materialQuotaRemain=").append(materialQuotaRemain);
        sb.append(", materialQuotaReverse=").append(materialQuotaReverse);
        sb.append(", materialType=").append(materialType);
        sb.append(", labelTax=").append(labelTax);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContractMaterial other = (ContractMaterial) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContractNumber() == null ? other.getContractNumber() == null : this.getContractNumber().equals(other.getContractNumber()))
            && (this.getMaterialNumber() == null ? other.getMaterialNumber() == null : this.getMaterialNumber().equals(other.getMaterialNumber()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getOwnerDepartment() == null ? other.getOwnerDepartment() == null : this.getOwnerDepartment().equals(other.getOwnerDepartment()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getProductId() == null ? other.getProductId() == null : this.getProductId().equals(other.getProductId()))
            && (this.getMaterialUnit() == null ? other.getMaterialUnit() == null : this.getMaterialUnit().equals(other.getMaterialUnit()))
            && (this.getPlanStrategy() == null ? other.getPlanStrategy() == null : this.getPlanStrategy().equals(other.getPlanStrategy()))
            && (this.getSettlePrice() == null ? other.getSettlePrice() == null : this.getSettlePrice().equals(other.getSettlePrice()))
            && (this.getServicePackId() == null ? other.getServicePackId() == null : this.getServicePackId().equals(other.getServicePackId()))
            && (this.getServiceQuotaUsed() == null ? other.getServiceQuotaUsed() == null : this.getServiceQuotaUsed().equals(other.getServiceQuotaUsed()))
            && (this.getServiceQuotaRemain() == null ? other.getServiceQuotaRemain() == null : this.getServiceQuotaRemain().equals(other.getServiceQuotaRemain()))
            && (this.getServiceQuotaReverse() == null ? other.getServiceQuotaReverse() == null : this.getServiceQuotaReverse().equals(other.getServiceQuotaReverse()))
            && (this.getMaterialQuotaUsed() == null ? other.getMaterialQuotaUsed() == null : this.getMaterialQuotaUsed().equals(other.getMaterialQuotaUsed()))
            && (this.getMaterialQuotaRemain() == null ? other.getMaterialQuotaRemain() == null : this.getMaterialQuotaRemain().equals(other.getMaterialQuotaRemain()))
            && (this.getMaterialQuotaReverse() == null ? other.getMaterialQuotaReverse() == null : this.getMaterialQuotaReverse().equals(other.getMaterialQuotaReverse()))
            && (this.getMaterialType() == null ? other.getMaterialType() == null : this.getMaterialType().equals(other.getMaterialType()))
            && (this.getLabelTax() == null ? other.getLabelTax() == null : this.getLabelTax().equals(other.getLabelTax()));
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContractNumber() == null) ? 0 : getContractNumber().hashCode());
        result = prime * result + ((getMaterialNumber() == null) ? 0 : getMaterialNumber().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getOwnerDepartment() == null) ? 0 : getOwnerDepartment().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getProductId() == null) ? 0 : getProductId().hashCode());
        result = prime * result + ((getMaterialUnit() == null) ? 0 : getMaterialUnit().hashCode());
        result = prime * result + ((getPlanStrategy() == null) ? 0 : getPlanStrategy().hashCode());
        result = prime * result + ((getSettlePrice() == null) ? 0 : getSettlePrice().hashCode());
        result = prime * result + ((getServicePackId() == null) ? 0 : getServicePackId().hashCode());
        result = prime * result + ((getServiceQuotaUsed() == null) ? 0 : getServiceQuotaUsed().hashCode());
        result = prime * result + ((getServiceQuotaRemain() == null) ? 0 : getServiceQuotaRemain().hashCode());
        result = prime * result + ((getServiceQuotaReverse() == null) ? 0 : getServiceQuotaReverse().hashCode());
        result = prime * result + ((getMaterialQuotaUsed() == null) ? 0 : getMaterialQuotaUsed().hashCode());
        result = prime * result + ((getMaterialQuotaRemain() == null) ? 0 : getMaterialQuotaRemain().hashCode());
        result = prime * result + ((getMaterialQuotaReverse() == null) ? 0 : getMaterialQuotaReverse().hashCode());
        result = prime * result + ((getMaterialType() == null) ? 0 : getMaterialType().hashCode());
        result = prime * result + ((getLabelTax() == null) ? 0 : getLabelTax().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Sep 24 10:04:04 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        contractNumber("contract_number", "contractNumber", "VARCHAR", false),
        materialNumber("material_number", "materialNumber", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        ownerDepartment("owner_department", "ownerDepartment", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        productId("product_id", "productId", "VARCHAR", false),
        materialUnit("material_unit", "materialUnit", "VARCHAR", false),
        planStrategy("plan_strategy", "planStrategy", "VARCHAR", false),
        settlePrice("settle_price", "settlePrice", "BIGINT", false),
        servicePackId("service_pack_id", "servicePackId", "VARCHAR", false),
        serviceQuotaUsed("service_quota_used", "serviceQuotaUsed", "BIGINT", false),
        serviceQuotaRemain("service_quota_remain", "serviceQuotaRemain", "BIGINT", false),
        serviceQuotaReverse("service_quota_reverse", "serviceQuotaReverse", "BIGINT", false),
        materialQuotaUsed("material_quota_used", "materialQuotaUsed", "BIGINT", false),
        materialQuotaRemain("material_quota_remain", "materialQuotaRemain", "BIGINT", false),
        materialQuotaReverse("material_quota_reverse", "materialQuotaReverse", "BIGINT", false),
        materialType("material_type", "materialType", "INTEGER", false),
        labelTax("label_tax", "labelTax", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Sep 24 10:04:04 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}