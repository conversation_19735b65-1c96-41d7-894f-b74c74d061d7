package com.chinamobile.iot.sc.service.soa;

import com.chinamobile.iot.sc.dao.SoaTokenMapper;
import com.chinamobile.iot.sc.pojo.entity.SoaToken;
import com.chinamobile.iot.sc.pojo.entity.SoaTokenExample;
import com.chinamobile.iot.sc.service.soa.token.OSBBPSOAHQInquiryServiceTokenSrvClient;
import jodd.system.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 数据库保存的SOA token
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DatabaseSoaToken implements InitializingBean {
    @Resource
    private SoaTokenMapper soaTokenMapper;

    @Resource
    private OSBBPSOAHQInquiryServiceTokenSrvClient tokenSrvClient;

    private String databaseToken;
    private ScheduledExecutorService scheduledExecutorService;
    private Runnable queryTokenTask;
    private int retryCount = 0;

    @Override
    public void afterPropertiesSet() throws Exception {
        readTokenAndCreateUpdateTask();
    }

    private void readTokenAndCreateUpdateTask() {
        List<SoaToken> tokenList = soaTokenMapper.selectByExample(new SoaTokenExample());
        SoaToken soaToken = tokenList.get(0);
        databaseToken = soaToken.getToken();

        createUpdateTokenTask(soaToken);
    }

    public String getToken() {
        return databaseToken;
    }

   /**
    * 创建更新token定时任务
    * */
    private void createUpdateTokenTask(SoaToken soaToken) {
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        long DAY_IN_MS = 1000 * 60 * 60 * 24;
        long HOUR_IN_MS = 1000 * 60 * 60;
        queryTokenTask = () -> {
            long delayMS = 30 * 1000;
            //更新token成功则按过期时间前7天的2:00再次设置定时任务
            //更新失败则30秒之后重试更新，重试5次
            try {
                SoaToken newToken = tokenSrvClient.queryToken();
                if (newToken != null) {
                    soaTokenMapper.deleteByExample(new SoaTokenExample());
                    soaTokenMapper.insertSelective(newToken);
                    databaseToken = newToken.getToken();

                    retryCount = 0;
                    delayMS = newToken.getInvalidPeriod().getTime() - System.currentTimeMillis()
                            - 7 * DAY_IN_MS + 2 * HOUR_IN_MS; //过期时间前7天的2点左右刷新token
                }

            } catch (Exception exception) {
                log.error("更新SOA Token失败，"+ exception.getMessage());
            }

            if (retryCount < 5) {
                scheduledExecutorService.schedule(queryTokenTask, delayMS, TimeUnit.MICROSECONDS);
                retryCount++;
            } else {
                log.error("更新SOA Token失败 5次，定时任务终止");
            }
        };

        long delayMS = soaToken.getInvalidPeriod().getTime() - System.currentTimeMillis()
                - 7 * DAY_IN_MS + 2 * HOUR_IN_MS; //过期时间前7天的2点左右刷新token
        scheduledExecutorService.schedule(queryTokenTask, delayMS, TimeUnit.MILLISECONDS);
    }
}
