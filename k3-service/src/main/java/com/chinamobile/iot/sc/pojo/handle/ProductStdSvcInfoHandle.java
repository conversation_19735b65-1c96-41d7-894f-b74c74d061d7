package com.chinamobile.iot.sc.pojo.handle;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: YSC
 * @Date: 2021/11/11 18:19
 * @Description:
 */
@Data
public class ProductStdSvcInfoHandle implements Serializable {
    /**
     * 原子商品ID
     */
    private String id;
    /**
     * 商品类型
     */
    private String spuOfferingClass;
//    /**
//     * 商品类型
//     */
//    private String skuComposition;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private String  spuOfferingStatus;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 单价
     */
    private Long atomPrice;

    /**
     * 原子商品销售目录价
     */
    private Long atomSalePrice;
    /**
     * 配置状态
     */
    private Integer configStatus;

    /**
     * 配置时间
     */
    private Date configTime;
    /**
     * 所属合作伙伴
     */
    private String cooperatorName;

    private String cooperatorId;

    /**
     * 是否主合作伙伴
     */
    private Boolean isPrimary;
    /**
     * 合作伙伴名
     */
    private String partnerName;

    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 标准服务配置id
     */
    private String configId;

    /**
     * 标准服务名称
     */
    private String stdSvcName;

    /**
     * 产品部门
     */
    private String productDept;

    /**
     * 实质性产品名称
     */
    private String realProductName;

    /**
     * 产品属性
     */
    private String productProperty;

    private static final long serialVersionUID = 1L;

}
