package com.chinamobile.iot.sc.exception;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 16:27
 * @description TODO
 */
public class StatusContant extends BaseErrorConstant {

    private static final  String PREF = "50";

    public static final ExcepStatus MATERIAL_QUERY_FAILED = createInstance(PREF + "001", "物料系统调用异常");

    public static final ExcepStatus K3USER_NULL = createInstance(PREF + "002", "用户系统未返回用户详细数据");

    public static final ExcepStatus DB_DUPLICATE = createInstance(PREF + "003", "数据库已有该条记录");

    public static final ExcepStatus INTERNAL_ERROR = createInstance(PREF + "004", "内部错误");

    public static final ExcepStatus STANDARD_SERVICE_NOT_EXIST = createInstance(PREF + "005", "标准服务不存在或已被删除");

    public static final ExcepStatus PARAM_ERROR = createInstance(PREF + "006", "分页参数错误");

    public static final ExcepStatus EXCEL_EXPORT_ERROR = createInstance(PREF + "007", "Excel导出失败");

    public static final ExcepStatus MATERIAL_NOT_EXIST = createInstance(PREF + "008", "物料不存在");

    public static final ExcepStatus CONTRACT_BIND_PRODUCT = createInstance(PREF + "009", "合同绑定了产品");

    public static final ExcepStatus MATERIAL_BIND_PRODUCT = createInstance(PREF + "010", "物料绑定了产品");

    public static final ExcepStatus SAME_CITY_CONTRACT_BIND_MATERIAL = createInstance(PREF + "011", "同省市下合同已经配置了物料");

    public static final ExcepStatus STD_SVC_CANNOT_DELETE = createInstance(PREF + "012", "标准服务不能删除");

    public static final ExcepStatus K3_BILL_TIME_EMPTY = createInstance(PREF + "013", "K3账单时间为空");

    public static final ExcepStatus K3_BILL_USERINFO_EMPTY = createInstance(PREF + "014", "无销售员相关信息");

    public static final ExcepStatus K3_BILL_ORDERCONTRACT_CONFLICT = createInstance(PREF + "015", "订单收入省市与合同省市冲突");

    public static final ExcepStatus K3_GENBILL_EMPTY = createInstance(PREF + "016","无相应的订单数据");

    public static final ExcepStatus PROVINCE_FOUND_NOT_CAPITAL = createInstance(PREF + "017", "找不到默认的省会城市");

    public static final ExcepStatus K3_BILL_ORDERUNBIND = createInstance(PREF + "018", "无有效的可生成订单的数据");

    public static final ExcepStatus CONTRACT_NOT_EXISTED = createInstance(PREF + "019", "合同不存在");

    public static final ExcepStatus CONTRACT_ACTIVE_EXISTED = createInstance(PREF + "020", "已存在生效合同");

    public static final ExcepStatus MATERIAL_NOT_SALABLE = createInstance(PREF + "021", "物料不可销售，无法添加");

    public static final ExcepStatus NATIONWIDE_USER_ERROR = createInstance(PREF + "022", "全国的用户需是生态渠道运营部人员");

    public static final ExcepStatus OSS_UPLOAD_ERROR = createInstance(PREF+"201", "上传文件失败");
    public static final ExcepStatus OSS_DEL_OR_SETEXPIRED_ERROR = createInstance(PREF+"202", "删除(或过期设置)文件失败");

}
