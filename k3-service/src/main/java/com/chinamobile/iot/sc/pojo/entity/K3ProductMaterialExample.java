package com.chinamobile.iot.sc.pojo.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class K3ProductMaterialExample {
    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterialExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterialExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterialExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        K3ProductMaterialExample example = new K3ProductMaterialExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterialExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterialExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNull() {
            addCriterion("atom_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNotNull() {
            addCriterion("atom_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualTo(String value) {
            addCriterion("atom_id =", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualTo(String value) {
            addCriterion("atom_id <>", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThan(String value) {
            addCriterion("atom_id >", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_id >=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThan(String value) {
            addCriterion("atom_id <", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualTo(String value) {
            addCriterion("atom_id <=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLike(String value) {
            addCriterion("atom_id like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotLike(String value) {
            addCriterion("atom_id not like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdIn(List<String> values) {
            addCriterion("atom_id in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotIn(List<String> values) {
            addCriterion("atom_id not in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdBetween(String value1, String value2) {
            addCriterion("atom_id between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotBetween(String value1, String value2) {
            addCriterion("atom_id not between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNull() {
            addCriterion("atom_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNotNull() {
            addCriterion("atom_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualTo(String value) {
            addCriterion("atom_offering_name =", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualTo(String value) {
            addCriterion("atom_offering_name <>", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThan(String value) {
            addCriterion("atom_offering_name >", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_name >=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThan(String value) {
            addCriterion("atom_offering_name <", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_name <=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLike(String value) {
            addCriterion("atom_offering_name like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotLike(String value) {
            addCriterion("atom_offering_name not like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIn(List<String> values) {
            addCriterion("atom_offering_name in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotIn(List<String> values) {
            addCriterion("atom_offering_name not in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameBetween(String value1, String value2) {
            addCriterion("atom_offering_name between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotBetween(String value1, String value2) {
            addCriterion("atom_offering_name not between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNull() {
            addCriterion("atom_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNotNull() {
            addCriterion("atom_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualTo(String value) {
            addCriterion("atom_offering_code =", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualTo(String value) {
            addCriterion("atom_offering_code <>", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThan(String value) {
            addCriterion("atom_offering_code >", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_code >=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThan(String value) {
            addCriterion("atom_offering_code <", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_code <=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLike(String value) {
            addCriterion("atom_offering_code like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotLike(String value) {
            addCriterion("atom_offering_code not like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIn(List<String> values) {
            addCriterion("atom_offering_code in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotIn(List<String> values) {
            addCriterion("atom_offering_code not in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeBetween(String value1, String value2) {
            addCriterion("atom_offering_code between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("atom_offering_code not between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNull() {
            addCriterion("material_num is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNotNull() {
            addCriterion("material_num is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualTo(String value) {
            addCriterion("material_num =", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualTo(String value) {
            addCriterion("material_num <>", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThan(String value) {
            addCriterion("material_num >", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualTo(String value) {
            addCriterion("material_num >=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThan(String value) {
            addCriterion("material_num <", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualTo(String value) {
            addCriterion("material_num <=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLike(String value) {
            addCriterion("material_num like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotLike(String value) {
            addCriterion("material_num not like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIn(List<String> values) {
            addCriterion("material_num in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotIn(List<String> values) {
            addCriterion("material_num not in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumBetween(String value1, String value2) {
            addCriterion("material_num between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotBetween(String value1, String value2) {
            addCriterion("material_num not between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNull() {
            addCriterion("material_name is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNotNull() {
            addCriterion("material_name is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualTo(String value) {
            addCriterion("material_name =", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualTo(String value) {
            addCriterion("material_name <>", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThan(String value) {
            addCriterion("material_name >", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualTo(String value) {
            addCriterion("material_name >=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThan(String value) {
            addCriterion("material_name <", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualTo(String value) {
            addCriterion("material_name <=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLike(String value) {
            addCriterion("material_name like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotLike(String value) {
            addCriterion("material_name not like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIn(List<String> values) {
            addCriterion("material_name in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotIn(List<String> values) {
            addCriterion("material_name not in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameBetween(String value1, String value2) {
            addCriterion("material_name between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotBetween(String value1, String value2) {
            addCriterion("material_name not between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNull() {
            addCriterion("material_dept is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNotNull() {
            addCriterion("material_dept is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualTo(String value) {
            addCriterion("material_dept =", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualTo(String value) {
            addCriterion("material_dept <>", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThan(String value) {
            addCriterion("material_dept >", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualTo(String value) {
            addCriterion("material_dept >=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThan(String value) {
            addCriterion("material_dept <", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualTo(String value) {
            addCriterion("material_dept <=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLike(String value) {
            addCriterion("material_dept like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotLike(String value) {
            addCriterion("material_dept not like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIn(List<String> values) {
            addCriterion("material_dept in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotIn(List<String> values) {
            addCriterion("material_dept not in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptBetween(String value1, String value2) {
            addCriterion("material_dept between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotBetween(String value1, String value2) {
            addCriterion("material_dept not between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialModelIsNull() {
            addCriterion("material_model is null");
            return (Criteria) this;
        }

        public Criteria andMaterialModelIsNotNull() {
            addCriterion("material_model is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialModelEqualTo(String value) {
            addCriterion("material_model =", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelNotEqualTo(String value) {
            addCriterion("material_model <>", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelGreaterThan(String value) {
            addCriterion("material_model >", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelGreaterThanOrEqualTo(String value) {
            addCriterion("material_model >=", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelLessThan(String value) {
            addCriterion("material_model <", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelLessThanOrEqualTo(String value) {
            addCriterion("material_model <=", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialModelLike(String value) {
            addCriterion("material_model like", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelNotLike(String value) {
            addCriterion("material_model not like", value, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelIn(List<String> values) {
            addCriterion("material_model in", values, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelNotIn(List<String> values) {
            addCriterion("material_model not in", values, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelBetween(String value1, String value2) {
            addCriterion("material_model between", value1, value2, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialModelNotBetween(String value1, String value2) {
            addCriterion("material_model not between", value1, value2, "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeIsNull() {
            addCriterion("material_pcode is null");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeIsNotNull() {
            addCriterion("material_pcode is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeEqualTo(String value) {
            addCriterion("material_pcode =", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeNotEqualTo(String value) {
            addCriterion("material_pcode <>", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeGreaterThan(String value) {
            addCriterion("material_pcode >", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_pcode >=", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLessThan(String value) {
            addCriterion("material_pcode <", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLessThanOrEqualTo(String value) {
            addCriterion("material_pcode <=", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_pcode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLike(String value) {
            addCriterion("material_pcode like", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeNotLike(String value) {
            addCriterion("material_pcode not like", value, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeIn(List<String> values) {
            addCriterion("material_pcode in", values, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeNotIn(List<String> values) {
            addCriterion("material_pcode not in", values, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeBetween(String value1, String value2) {
            addCriterion("material_pcode between", value1, value2, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeNotBetween(String value1, String value2) {
            addCriterion("material_pcode not between", value1, value2, "materialPcode");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNull() {
            addCriterion("contract_num is null");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNotNull() {
            addCriterion("contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualTo(String value) {
            addCriterion("contract_num =", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualTo(String value) {
            addCriterion("contract_num <>", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThan(String value) {
            addCriterion("contract_num >", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("contract_num >=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThan(String value) {
            addCriterion("contract_num <", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualTo(String value) {
            addCriterion("contract_num <=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLike(String value) {
            addCriterion("contract_num like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotLike(String value) {
            addCriterion("contract_num not like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumIn(List<String> values) {
            addCriterion("contract_num in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotIn(List<String> values) {
            addCriterion("contract_num not in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumBetween(String value1, String value2) {
            addCriterion("contract_num between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotBetween(String value1, String value2) {
            addCriterion("contract_num not between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractPropIsNull() {
            addCriterion("contract_prop is null");
            return (Criteria) this;
        }

        public Criteria andContractPropIsNotNull() {
            addCriterion("contract_prop is not null");
            return (Criteria) this;
        }

        public Criteria andContractPropEqualTo(String value) {
            addCriterion("contract_prop =", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropNotEqualTo(String value) {
            addCriterion("contract_prop <>", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropGreaterThan(String value) {
            addCriterion("contract_prop >", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropGreaterThanOrEqualTo(String value) {
            addCriterion("contract_prop >=", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropLessThan(String value) {
            addCriterion("contract_prop <", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropLessThanOrEqualTo(String value) {
            addCriterion("contract_prop <=", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_prop <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPropLike(String value) {
            addCriterion("contract_prop like", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropNotLike(String value) {
            addCriterion("contract_prop not like", value, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropIn(List<String> values) {
            addCriterion("contract_prop in", values, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropNotIn(List<String> values) {
            addCriterion("contract_prop not in", values, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropBetween(String value1, String value2) {
            addCriterion("contract_prop between", value1, value2, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractPropNotBetween(String value1, String value2) {
            addCriterion("contract_prop not between", value1, value2, "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNull() {
            addCriterion("contract_status is null");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNotNull() {
            addCriterion("contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatusEqualTo(String value) {
            addCriterion("contract_status =", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusNotEqualTo(String value) {
            addCriterion("contract_status <>", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThan(String value) {
            addCriterion("contract_status >", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanOrEqualTo(String value) {
            addCriterion("contract_status >=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThan(String value) {
            addCriterion("contract_status <", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanOrEqualTo(String value) {
            addCriterion("contract_status <=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatusLike(String value) {
            addCriterion("contract_status like", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotLike(String value) {
            addCriterion("contract_status not like", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusIn(List<String> values) {
            addCriterion("contract_status in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotIn(List<String> values) {
            addCriterion("contract_status not in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusBetween(String value1, String value2) {
            addCriterion("contract_status between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotBetween(String value1, String value2) {
            addCriterion("contract_status not between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNull() {
            addCriterion("buyer_name is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNotNull() {
            addCriterion("buyer_name is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualTo(String value) {
            addCriterion("buyer_name =", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualTo(String value) {
            addCriterion("buyer_name <>", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThan(String value) {
            addCriterion("buyer_name >", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_name >=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThan(String value) {
            addCriterion("buyer_name <", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualTo(String value) {
            addCriterion("buyer_name <=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("buyer_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLike(String value) {
            addCriterion("buyer_name like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotLike(String value) {
            addCriterion("buyer_name not like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIn(List<String> values) {
            addCriterion("buyer_name in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotIn(List<String> values) {
            addCriterion("buyer_name not in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameBetween(String value1, String value2) {
            addCriterion("buyer_name between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotBetween(String value1, String value2) {
            addCriterion("buyer_name not between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameIsNull() {
            addCriterion("seller_name is null");
            return (Criteria) this;
        }

        public Criteria andSellerNameIsNotNull() {
            addCriterion("seller_name is not null");
            return (Criteria) this;
        }

        public Criteria andSellerNameEqualTo(String value) {
            addCriterion("seller_name =", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameNotEqualTo(String value) {
            addCriterion("seller_name <>", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThan(String value) {
            addCriterion("seller_name >", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanOrEqualTo(String value) {
            addCriterion("seller_name >=", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThan(String value) {
            addCriterion("seller_name <", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanOrEqualTo(String value) {
            addCriterion("seller_name <=", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLike(String value) {
            addCriterion("seller_name like", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotLike(String value) {
            addCriterion("seller_name not like", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameIn(List<String> values) {
            addCriterion("seller_name in", values, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotIn(List<String> values) {
            addCriterion("seller_name not in", values, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameBetween(String value1, String value2) {
            addCriterion("seller_name between", value1, value2, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotBetween(String value1, String value2) {
            addCriterion("seller_name not between", value1, value2, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIsNull() {
            addCriterion("seller_dept is null");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIsNotNull() {
            addCriterion("seller_dept is not null");
            return (Criteria) this;
        }

        public Criteria andSellerDeptEqualTo(String value) {
            addCriterion("seller_dept =", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptNotEqualTo(String value) {
            addCriterion("seller_dept <>", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptGreaterThan(String value) {
            addCriterion("seller_dept >", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptGreaterThanOrEqualTo(String value) {
            addCriterion("seller_dept >=", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptLessThan(String value) {
            addCriterion("seller_dept <", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptLessThanOrEqualTo(String value) {
            addCriterion("seller_dept <=", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("seller_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptLike(String value) {
            addCriterion("seller_dept like", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptNotLike(String value) {
            addCriterion("seller_dept not like", value, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIn(List<String> values) {
            addCriterion("seller_dept in", values, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptNotIn(List<String> values) {
            addCriterion("seller_dept not in", values, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptBetween(String value1, String value2) {
            addCriterion("seller_dept between", value1, value2, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andSellerDeptNotBetween(String value1, String value2) {
            addCriterion("seller_dept not between", value1, value2, "sellerDept");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeIsNull() {
            addCriterion("money_type is null");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeIsNotNull() {
            addCriterion("money_type is not null");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeEqualTo(String value) {
            addCriterion("money_type =", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeNotEqualTo(String value) {
            addCriterion("money_type <>", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeGreaterThan(String value) {
            addCriterion("money_type >", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeGreaterThanOrEqualTo(String value) {
            addCriterion("money_type >=", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLessThan(String value) {
            addCriterion("money_type <", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLessThanOrEqualTo(String value) {
            addCriterion("money_type <=", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLike(String value) {
            addCriterion("money_type like", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeNotLike(String value) {
            addCriterion("money_type not like", value, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeIn(List<String> values) {
            addCriterion("money_type in", values, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeNotIn(List<String> values) {
            addCriterion("money_type not in", values, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeBetween(String value1, String value2) {
            addCriterion("money_type between", value1, value2, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeNotBetween(String value1, String value2) {
            addCriterion("money_type not between", value1, value2, "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIsNull() {
            addCriterion("money_unit is null");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIsNotNull() {
            addCriterion("money_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitEqualTo(String value) {
            addCriterion("money_unit =", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotEqualTo(String value) {
            addCriterion("money_unit <>", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThan(String value) {
            addCriterion("money_unit >", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanOrEqualTo(String value) {
            addCriterion("money_unit >=", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThan(String value) {
            addCriterion("money_unit <", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanOrEqualTo(String value) {
            addCriterion("money_unit <=", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("money_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLike(String value) {
            addCriterion("money_unit like", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotLike(String value) {
            addCriterion("money_unit not like", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIn(List<String> values) {
            addCriterion("money_unit in", values, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotIn(List<String> values) {
            addCriterion("money_unit not in", values, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitBetween(String value1, String value2) {
            addCriterion("money_unit between", value1, value2, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotBetween(String value1, String value2) {
            addCriterion("money_unit not between", value1, value2, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractPriceIsNull() {
            addCriterion("contract_price is null");
            return (Criteria) this;
        }

        public Criteria andContractPriceIsNotNull() {
            addCriterion("contract_price is not null");
            return (Criteria) this;
        }

        public Criteria andContractPriceEqualTo(String value) {
            addCriterion("contract_price =", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceNotEqualTo(String value) {
            addCriterion("contract_price <>", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceGreaterThan(String value) {
            addCriterion("contract_price >", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceGreaterThanOrEqualTo(String value) {
            addCriterion("contract_price >=", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceLessThan(String value) {
            addCriterion("contract_price <", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceLessThanOrEqualTo(String value) {
            addCriterion("contract_price <=", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractPriceLike(String value) {
            addCriterion("contract_price like", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceNotLike(String value) {
            addCriterion("contract_price not like", value, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceIn(List<String> values) {
            addCriterion("contract_price in", values, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceNotIn(List<String> values) {
            addCriterion("contract_price not in", values, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceBetween(String value1, String value2) {
            addCriterion("contract_price between", value1, value2, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andContractPriceNotBetween(String value1, String value2) {
            addCriterion("contract_price not between", value1, value2, "contractPrice");
            return (Criteria) this;
        }

        public Criteria andExpiredDateIsNull() {
            addCriterion("expired_date is null");
            return (Criteria) this;
        }

        public Criteria andExpiredDateIsNotNull() {
            addCriterion("expired_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpiredDateEqualTo(String value) {
            addCriterion("expired_date =", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateNotEqualTo(String value) {
            addCriterion("expired_date <>", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateGreaterThan(String value) {
            addCriterion("expired_date >", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateGreaterThanOrEqualTo(String value) {
            addCriterion("expired_date >=", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateLessThan(String value) {
            addCriterion("expired_date <", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateLessThanOrEqualTo(String value) {
            addCriterion("expired_date <=", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("expired_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpiredDateLike(String value) {
            addCriterion("expired_date like", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateNotLike(String value) {
            addCriterion("expired_date not like", value, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateIn(List<String> values) {
            addCriterion("expired_date in", values, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateNotIn(List<String> values) {
            addCriterion("expired_date not in", values, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateBetween(String value1, String value2) {
            addCriterion("expired_date between", value1, value2, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andExpiredDateNotBetween(String value1, String value2) {
            addCriterion("expired_date not between", value1, value2, "expiredDate");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveIsNull() {
            addCriterion("contract_effective is null");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveIsNotNull() {
            addCriterion("contract_effective is not null");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveEqualTo(Integer value) {
            addCriterion("contract_effective =", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveNotEqualTo(Integer value) {
            addCriterion("contract_effective <>", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveGreaterThan(Integer value) {
            addCriterion("contract_effective >", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_effective >=", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveLessThan(Integer value) {
            addCriterion("contract_effective <", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveLessThanOrEqualTo(Integer value) {
            addCriterion("contract_effective <=", value, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_effective <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractEffectiveIn(List<Integer> values) {
            addCriterion("contract_effective in", values, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveNotIn(List<Integer> values) {
            addCriterion("contract_effective not in", values, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveBetween(Integer value1, Integer value2) {
            addCriterion("contract_effective between", value1, value2, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andContractEffectiveNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_effective not between", value1, value2, "contractEffective");
            return (Criteria) this;
        }

        public Criteria andMaterialCountIsNull() {
            addCriterion("material_count is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCountIsNotNull() {
            addCriterion("material_count is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCountEqualTo(BigDecimal value) {
            addCriterion("material_count =", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotEqualTo(BigDecimal value) {
            addCriterion("material_count <>", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThan(BigDecimal value) {
            addCriterion("material_count >", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_count >=", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThan(BigDecimal value) {
            addCriterion("material_count <", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_count <=", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCountIn(List<BigDecimal> values) {
            addCriterion("material_count in", values, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotIn(List<BigDecimal> values) {
            addCriterion("material_count not in", values, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_count between", value1, value2, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_count not between", value1, value2, "materialCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(Integer value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(Integer value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(Integer value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(Integer value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<Integer> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<Integer> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(Integer value1, Integer value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceIsNull() {
            addCriterion("material_settle_price is null");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceIsNotNull() {
            addCriterion("material_settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceEqualTo(Long value) {
            addCriterion("material_settle_price =", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceNotEqualTo(Long value) {
            addCriterion("material_settle_price <>", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceGreaterThan(Long value) {
            addCriterion("material_settle_price >", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("material_settle_price >=", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceLessThan(Long value) {
            addCriterion("material_settle_price <", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("material_settle_price <=", value, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("material_settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceIn(List<Long> values) {
            addCriterion("material_settle_price in", values, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceNotIn(List<Long> values) {
            addCriterion("material_settle_price not in", values, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceBetween(Long value1, Long value2) {
            addCriterion("material_settle_price between", value1, value2, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("material_settle_price not between", value1, value2, "materialSettlePrice");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIsNull() {
            addCriterion("service_pack_id is null");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIsNotNull() {
            addCriterion("service_pack_id is not null");
            return (Criteria) this;
        }

        public Criteria andServicePackIdEqualTo(String value) {
            addCriterion("service_pack_id =", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotEqualTo(String value) {
            addCriterion("service_pack_id <>", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThan(String value) {
            addCriterion("service_pack_id >", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanOrEqualTo(String value) {
            addCriterion("service_pack_id >=", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThan(String value) {
            addCriterion("service_pack_id <", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanOrEqualTo(String value) {
            addCriterion("service_pack_id <=", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackIdLike(String value) {
            addCriterion("service_pack_id like", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotLike(String value) {
            addCriterion("service_pack_id not like", value, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdIn(List<String> values) {
            addCriterion("service_pack_id in", values, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotIn(List<String> values) {
            addCriterion("service_pack_id not in", values, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdBetween(String value1, String value2) {
            addCriterion("service_pack_id between", value1, value2, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackIdNotBetween(String value1, String value2) {
            addCriterion("service_pack_id not between", value1, value2, "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackNameIsNull() {
            addCriterion("service_pack_name is null");
            return (Criteria) this;
        }

        public Criteria andServicePackNameIsNotNull() {
            addCriterion("service_pack_name is not null");
            return (Criteria) this;
        }

        public Criteria andServicePackNameEqualTo(String value) {
            addCriterion("service_pack_name =", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameNotEqualTo(String value) {
            addCriterion("service_pack_name <>", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameNotEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameGreaterThan(String value) {
            addCriterion("service_pack_name >", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameGreaterThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_pack_name >=", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameGreaterThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameLessThan(String value) {
            addCriterion("service_pack_name <", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameLessThanColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameLessThanOrEqualTo(String value) {
            addCriterion("service_pack_name <=", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameLessThanOrEqualToColumn(K3ProductMaterial.Column column) {
            addCriterion(new StringBuilder("service_pack_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackNameLike(String value) {
            addCriterion("service_pack_name like", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameNotLike(String value) {
            addCriterion("service_pack_name not like", value, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameIn(List<String> values) {
            addCriterion("service_pack_name in", values, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameNotIn(List<String> values) {
            addCriterion("service_pack_name not in", values, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameBetween(String value1, String value2) {
            addCriterion("service_pack_name between", value1, value2, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andServicePackNameNotBetween(String value1, String value2) {
            addCriterion("service_pack_name not between", value1, value2, "servicePackName");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAtomIdLikeInsensitive(String value) {
            addCriterion("upper(atom_id) like", value.toUpperCase(), "atomId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_name) like", value.toUpperCase(), "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_code) like", value.toUpperCase(), "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLikeInsensitive(String value) {
            addCriterion("upper(material_num) like", value.toUpperCase(), "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLikeInsensitive(String value) {
            addCriterion("upper(material_name) like", value.toUpperCase(), "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLikeInsensitive(String value) {
            addCriterion("upper(material_dept) like", value.toUpperCase(), "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialModelLikeInsensitive(String value) {
            addCriterion("upper(material_model) like", value.toUpperCase(), "materialModel");
            return (Criteria) this;
        }

        public Criteria andMaterialPcodeLikeInsensitive(String value) {
            addCriterion("upper(material_pcode) like", value.toUpperCase(), "materialPcode");
            return (Criteria) this;
        }

        public Criteria andContractNumLikeInsensitive(String value) {
            addCriterion("upper(contract_num) like", value.toUpperCase(), "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameLikeInsensitive(String value) {
            addCriterion("upper(contract_name) like", value.toUpperCase(), "contractName");
            return (Criteria) this;
        }

        public Criteria andContractPropLikeInsensitive(String value) {
            addCriterion("upper(contract_prop) like", value.toUpperCase(), "contractProp");
            return (Criteria) this;
        }

        public Criteria andContractStatusLikeInsensitive(String value) {
            addCriterion("upper(contract_status) like", value.toUpperCase(), "contractStatus");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLikeInsensitive(String value) {
            addCriterion("upper(buyer_name) like", value.toUpperCase(), "buyerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLikeInsensitive(String value) {
            addCriterion("upper(seller_name) like", value.toUpperCase(), "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerDeptLikeInsensitive(String value) {
            addCriterion("upper(seller_dept) like", value.toUpperCase(), "sellerDept");
            return (Criteria) this;
        }

        public Criteria andMoneyTypeLikeInsensitive(String value) {
            addCriterion("upper(money_type) like", value.toUpperCase(), "moneyType");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLikeInsensitive(String value) {
            addCriterion("upper(money_unit) like", value.toUpperCase(), "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractPriceLikeInsensitive(String value) {
            addCriterion("upper(contract_price) like", value.toUpperCase(), "contractPrice");
            return (Criteria) this;
        }

        public Criteria andExpiredDateLikeInsensitive(String value) {
            addCriterion("upper(expired_date) like", value.toUpperCase(), "expiredDate");
            return (Criteria) this;
        }

        public Criteria andServicePackIdLikeInsensitive(String value) {
            addCriterion("upper(service_pack_id) like", value.toUpperCase(), "servicePackId");
            return (Criteria) this;
        }

        public Criteria andServicePackNameLikeInsensitive(String value) {
            addCriterion("upper(service_pack_name) like", value.toUpperCase(), "servicePackName");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Nov 08 15:19:01 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private K3ProductMaterialExample example;

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        protected Criteria(K3ProductMaterialExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public K3ProductMaterialExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Nov 08 15:19:01 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.entity.K3ProductMaterialExample example);
    }
}