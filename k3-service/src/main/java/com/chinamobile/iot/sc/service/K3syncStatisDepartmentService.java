package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisDepartment;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisDepartmentExample;
import com.chinamobile.iot.sc.pojo.param.K3DepartmentParam;
import com.chinamobile.iot.sc.pojo.vo.K3DepartmentVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/23
 * @description k3部门信息service接口类
 */
public interface K3syncStatisDepartmentService {

    /**
     * 批量新增k3部门相关数据
     *
     * @param departmentList
     */
    void batchInsertK3Department(List<K3syncStatisDepartment> departmentList);

    /**
     * 根据需要获取k3部门相关数据
     *
     * @param departmentExample
     * @return
     */
    List<K3syncStatisDepartment> listK3syncStatisDepartmentByNeed(K3syncStatisDepartmentExample departmentExample);

    /**
     * 根据需要更新数据
     *
     * @param department
     * @param departmentExample
     */
    void updateNeedById(K3syncStatisDepartment department,
                        K3syncStatisDepartmentExample departmentExample);

    /**
     * 分页查询按产品部门的对账单
     *
     * @param k3DepartmentParam
     * @return
     */
    PageData<K3DepartmentVO> pageK3DepartmentVO(K3DepartmentParam k3DepartmentParam);

    /**
     * 根据主键id获取k3部门数据
     * @param id
     * @return
     */
    K3syncStatisDepartment getK3DepartmentById(String id);
}
