package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.dao.UserK3Mapper;
import com.chinamobile.iot.sc.exception.StatusContant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.K3UserFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.dto.ProvinceNameCodeDTO;
import com.chinamobile.iot.sc.pojo.dto.UserK3DTO;
import com.chinamobile.iot.sc.pojo.entity.UserK3;
import com.chinamobile.iot.sc.pojo.entity.UserK3Example;
import com.chinamobile.iot.sc.pojo.vo.K3SellerVO;
import com.chinamobile.iot.sc.pojo.vo.OsK3SellerVO;
import com.chinamobile.iot.sc.pojo.vo.PageVO;
import com.chinamobile.iot.sc.service.UserSysService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.chinamobile.iot.sc.constant.K3SyncStatusConstant.USER_DEPARTMENT;

/**
 * <AUTHOR>
 * @date 2022/6/8 18:03
 */
@Slf4j
@Service
public class UserSysServiceImpl implements UserSysService {

    @Autowired
    K3UserFeignClient userFeignClient;

    @Resource
    private UserFeignClient baseUserFeignClient;

    @Resource
    UserK3Mapper userMapper;


    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Override
    public BaseAnswer<PageVO<K3SellerVO>> getK3Sellers(int type, String id, int page, int num) {
        String resp = null;
        if(type == 0){
            //按账号查找
            resp = userFeignClient.getK3UserByAccount(id);

        }else if(type == 1){
            //按工号查找
            resp = userFeignClient.getK3UserByCode(id);
        }
        if(resp==null){
            log.info("查询用户系统数据为空");
            throw new BusinessException(StatusContant.K3USER_NULL);
        }
        log.info("resp = {}",resp);
        JSONObject answer = JSON.parseObject(resp);
        PageVO<K3SellerVO> pageRespVo = new PageVO<>();
        List<K3SellerVO> userList = new ArrayList<>();





        JSONArray organizations = answer.getJSONArray("organizationJobs");
        if(organizations != null){
            for(int i=0;i<organizations.size();i++){
                K3SellerVO userVo = new K3SellerVO();
                userVo.setAccount(answer.getString("username"));
                userVo.setName(answer.getString("cn"));
                userVo.setEmail(answer.getString("email"));
                userVo.setPhone(answer.getString("firstPhone"));
                userVo.setUsercode(answer.getString("userOldCode"));
                userVo.setIhrusercode(answer.getString("ihrUserCode"));
                String orgCode = JSONObject.parseObject(organizations.get(i).toString()).getString("orgCode");
                UserK3DTO userDto = new UserK3DTO();
                getUserDept(orgCode, userDto);
                userVo.setSellerteam(userDto.getTeamCode());
                userVo.setSellerdept(userDto.getDeptCode());
                userVo.setDepartment(userDto.getDeptName());
                userVo.setTeam(userDto.getTeamName());
                userList.add(userVo);
            }
        }else{
            log.error("user Department is null");
            K3SellerVO userVo = new K3SellerVO();
            userVo.setAccount(answer.getString("username"));
            userVo.setName(answer.getString("cn"));
            userVo.setEmail(answer.getString("email"));
            userVo.setPhone(answer.getString("firstPhone"));
            userVo.setUsercode(answer.getString("userOldCode"));
            userVo.setIhrusercode(answer.getString("ihrUserCode"));
            userVo.setDepartment("");
            userList.add(userVo);
        }
        pageRespVo.setPageCount(5);
        pageRespVo.setCurrentPage(1);
        pageRespVo.setTotalCount(userList.size());
        pageRespVo.setList(userList);


        BaseAnswer<PageVO<K3SellerVO>> ret = new BaseAnswer<PageVO<K3SellerVO>>();
        ret.setData(pageRespVo);
        return ret;
    }

    private void getUserDept(String orgCode, UserK3DTO userK3DTO){
        String depresp = userFeignClient.getK3UserDept(orgCode);
        if(depresp==null){
            log.info("查询用户部门信息失败");
            throw new BusinessException(StatusContant.K3USER_NULL);
        }
        JSONObject deptanswer = JSON.parseObject(depresp);
        int level = deptanswer.getInteger("orgLevel");
        Integer initLevel = userK3DTO.getInitLevel();
        if(initLevel==null){
            //未初始化
            userK3DTO.setInitLevel(level);
            if(level<3){
                //如果用户属于三级目录内，团队部门都一样
                userK3DTO.setDeptCode(deptanswer.getString("orgCode"));
                userK3DTO.setDeptName(deptanswer.getString("shortName"));
                userK3DTO.setTeamCode(deptanswer.getString("orgCode"));
                userK3DTO.setTeamName(deptanswer.getString("shortName"));
            }
        }

        if(level==3){
            userK3DTO.setTeamCode(deptanswer.getString("orgCode"));
            userK3DTO.setTeamName(deptanswer.getString("shortName"));
        }
        if(level==2){
            userK3DTO.setDeptCode(deptanswer.getString("orgCode"));
            userK3DTO.setDeptName(deptanswer.getString("shortName"));
        }
        if(level>2){
            String parentCode = deptanswer.getString("parentCode");
            getUserDept(parentCode, userK3DTO);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer saveOsK3Seller(OsK3SellerVO osK3Seller, String userid) {

        UserK3 userEntity = new UserK3();

        long existCount = userMapper.countByExample(new UserK3Example().createCriteria().andAccountEqualTo(osK3Seller.getAccount()).
                andNameEqualTo(osK3Seller.getName()).andDepartmentEqualTo(osK3Seller.getDepartment()).andIhrusercodeEqualTo(osK3Seller.getIhrusercode()).example());
        if(existCount>0){
            throw new BusinessException(StatusContant.DB_DUPLICATE);
        }else{
            BeanUtils.copyProperties(osK3Seller, userEntity);
            String id = BaseServiceUtils.getId();
            userEntity.setId(id);
            BaseAnswer<Data4User> data4UserBaseAnswer = baseUserFeignClient.userInfoById(userid);
            String creatorName = data4UserBaseAnswer.getData().getName();
            userEntity.setCreateor(creatorName);
            userEntity.setCreatetime(new Date());
            userEntity.setUpdatetime(new Date());
            int num = userMapper.insert(userEntity);
            log.info("Insert user num = {}",num);
        }
        return new BaseAnswer();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<PageVO<UserK3>> getOsK3Seller() {
        PageVO<UserK3> pageRespVo = new PageVO<>();
        BaseAnswer<PageVO<UserK3>> answer = new BaseAnswer<>();
        try{
            List<UserK3> k3UserList = userMapper.selectByExample(new UserK3Example());
            if(k3UserList!=null){
                pageRespVo.setTotalCount(k3UserList.size());
                pageRespVo.setCurrentPage(1);
                pageRespVo.setPageCount(5);
                pageRespVo.setList(k3UserList);
            }
            answer.setData(pageRespVo);
        }catch(Exception e){
            e.printStackTrace();
            answer.setStatus(StatusContant.INTERNAL_ERROR);
            throw new BusinessException(StatusContant.INTERNAL_ERROR);
        }
        return answer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer modifyOsK3Seller(UserK3 userK3, String userid) {
        BaseAnswer answer = new BaseAnswer();
        log.info("modifyOsK3Seller osUserId = {}", userK3);
//        UserK3Example example = new UserK3Example();
//        example.createCriteria().andIdEqualTo(userK3.getId()).example();
//        example.createCriteria().andIdEqualTo(userK3.getId());
//        userMapper.updateByExampleSelective(userK3, example);

        BaseAnswer<Data4User> data4UserBaseAnswer = baseUserFeignClient.userInfoById(userid);
        String creatorName = data4UserBaseAnswer.getData().getName();
        userK3.setUpdatetime(new Date());
        userK3.setCreateor(creatorName);
        try{
            log.info("modifyOsK3Seller osUser = {}", userK3);
            UserK3 userK3Old = userMapper.selectByPrimaryKey(userK3.getId());
            String provinceName = userK3Old.getProvincename();
            String provinceNameNew = userK3.getProvincename();
            String costcenter = userK3.getCostcenter();
            String costcentername = userK3.getCostcentername();
            if (StringUtils.isNotEmpty(provinceNameNew) && !provinceName.equals(provinceNameNew)){
                throw new BusinessException(StatusContant.NATIONWIDE_USER_ERROR,"省份信息不可修改");
            }
            if (StringUtils.isEmpty(costcenter) || StringUtils.isEmpty(costcentername)){
                throw new BusinessException(StatusContant.NATIONWIDE_USER_ERROR,"成本中心编码及名称不能为空");
            }

            String provinceCode = userK3Old.getProvincecode();
            if ("000".equals(provinceCode)){
                String department = userK3.getDepartment();
                if (StringUtils.isEmpty(department) || !USER_DEPARTMENT.equals(department)){
                    throw new BusinessException(StatusContant.NATIONWIDE_USER_ERROR);
                }
            }
            userMapper.updateByPrimaryKeySelective(userK3);
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        }catch(Exception e){
            //answer.setStatus(StatusContant.INTERNAL_ERROR);
            throw new BusinessException(StatusContant.INTERNAL_ERROR);
        }
        return answer;
    }

    @Override
    public BaseAnswer<Void> saveOsK3SellerDefault(String userid) {
        HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
        HashMap<String, String> provinceCodeNameMap = provinceCityConfig.getProvinceCodeNameMap();
        List<ProvinceNameCodeDTO> list = new ArrayList<>();
        for(Map.Entry<String,String> entry : provinceNameCodeMap.entrySet()){
            ProvinceNameCodeDTO provinceNameCodeDTO = new ProvinceNameCodeDTO();
            String provinceName = entry.getKey();
            String provinceCode = entry.getValue();
            provinceNameCodeDTO.setProvinceName(provinceName);
            provinceNameCodeDTO.setProvinceCode(provinceCode);
            list.add(provinceNameCodeDTO);
        }
        //默认一个全国的
        ProvinceNameCodeDTO provinceNameCodeDTO = new ProvinceNameCodeDTO();
        provinceNameCodeDTO.setProvinceName("全国");
        provinceNameCodeDTO.setProvinceCode("000");
        list.add(provinceNameCodeDTO);
        BaseAnswer<Data4User> data4UserBaseAnswer = baseUserFeignClient.userInfoById(userid);
        String creatorName = data4UserBaseAnswer.getData().getName();
        Date date = new Date();
        list.forEach(provinceNameCode -> {
            UserK3 userEntity = new UserK3();
            String provinceName = provinceNameCode.getProvinceName();
            String provinceCode = provinceNameCode.getProvinceCode();
            userEntity.setId(BaseServiceUtils.getId());
            userEntity.setCreateor(creatorName);
            userEntity.setProvincename(provinceName);
            userEntity.setProvincecode(provinceCode);
            userEntity.setUsertype("1");
            userEntity.setCreatetime(date);
            userEntity.setUpdatetime(date);
            userMapper.insert(userEntity);
        });
        return new BaseAnswer<>();
    }


}
