
package com.chinamobile.iot.sc.service.soa.orderImport;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ERRORCOLLECTION complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ERRORCOLLECTION"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ERRORCOLLECTION_ITEM" type="{http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv}ERRORCOLLECTION_ITEM" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ERRORCOLLECTION", propOrder = {
    "errorcollectionitem"
})
public class ERRORCOLLECTION {

    @XmlElement(name = "ERRORCOLLECTION_ITEM")
    protected List<ERRORCOLLECTIONITEM> errorcollectionitem;

    /**
     * Gets the value of the errorcollectionitem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the errorcollectionitem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getERRORCOLLECTIONITEM().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ERRORCOLLECTIONITEM }
     * 
     * 
     */
    public List<ERRORCOLLECTIONITEM> getERRORCOLLECTIONITEM() {
        if (errorcollectionitem == null) {
            errorcollectionitem = new ArrayList<ERRORCOLLECTIONITEM>();
        }
        return this.errorcollectionitem;
    }

}
