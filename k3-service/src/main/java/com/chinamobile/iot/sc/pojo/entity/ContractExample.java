package com.chinamobile.iot.sc.pojo.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ContractExample {
    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public ContractExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public ContractExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public ContractExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ContractExample example = new ContractExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public ContractExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public ContractExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(String value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(String value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(String value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(String value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(String value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(String value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLike(String value) {
            addCriterion("number like", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotLike(String value) {
            addCriterion("number not like", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<String> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<String> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(String value1, String value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(String value1, String value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andPropertyIsNull() {
            addCriterion("property is null");
            return (Criteria) this;
        }

        public Criteria andPropertyIsNotNull() {
            addCriterion("property is not null");
            return (Criteria) this;
        }

        public Criteria andPropertyEqualTo(String value) {
            addCriterion("property =", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyNotEqualTo(String value) {
            addCriterion("property <>", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyGreaterThan(String value) {
            addCriterion("property >", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyGreaterThanOrEqualTo(String value) {
            addCriterion("property >=", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyLessThan(String value) {
            addCriterion("property <", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyLessThanOrEqualTo(String value) {
            addCriterion("property <=", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("property <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPropertyLike(String value) {
            addCriterion("property like", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyNotLike(String value) {
            addCriterion("property not like", value, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyIn(List<String> values) {
            addCriterion("property in", values, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyNotIn(List<String> values) {
            addCriterion("property not in", values, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyBetween(String value1, String value2) {
            addCriterion("property between", value1, value2, "property");
            return (Criteria) this;
        }

        public Criteria andPropertyNotBetween(String value1, String value2) {
            addCriterion("property not between", value1, value2, "property");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("vendor_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameIsNull() {
            addCriterion("create_company_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameIsNotNull() {
            addCriterion("create_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameEqualTo(String value) {
            addCriterion("create_company_name =", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameNotEqualTo(String value) {
            addCriterion("create_company_name <>", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameGreaterThan(String value) {
            addCriterion("create_company_name >", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_company_name >=", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLessThan(String value) {
            addCriterion("create_company_name <", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("create_company_name <=", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_company_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLike(String value) {
            addCriterion("create_company_name like", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameNotLike(String value) {
            addCriterion("create_company_name not like", value, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameIn(List<String> values) {
            addCriterion("create_company_name in", values, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameNotIn(List<String> values) {
            addCriterion("create_company_name not in", values, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameBetween(String value1, String value2) {
            addCriterion("create_company_name between", value1, value2, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameNotBetween(String value1, String value2) {
            addCriterion("create_company_name not between", value1, value2, "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameIsNull() {
            addCriterion("create_dept_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameIsNotNull() {
            addCriterion("create_dept_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameEqualTo(String value) {
            addCriterion("create_dept_name =", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameNotEqualTo(String value) {
            addCriterion("create_dept_name <>", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameGreaterThan(String value) {
            addCriterion("create_dept_name >", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_dept_name >=", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLessThan(String value) {
            addCriterion("create_dept_name <", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLessThanOrEqualTo(String value) {
            addCriterion("create_dept_name <=", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("create_dept_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLike(String value) {
            addCriterion("create_dept_name like", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameNotLike(String value) {
            addCriterion("create_dept_name not like", value, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameIn(List<String> values) {
            addCriterion("create_dept_name in", values, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameNotIn(List<String> values) {
            addCriterion("create_dept_name not in", values, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameBetween(String value1, String value2) {
            addCriterion("create_dept_name between", value1, value2, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameNotBetween(String value1, String value2) {
            addCriterion("create_dept_name not between", value1, value2, "createDeptName");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIsNull() {
            addCriterion("amount_type is null");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIsNotNull() {
            addCriterion("amount_type is not null");
            return (Criteria) this;
        }

        public Criteria andAmountTypeEqualTo(String value) {
            addCriterion("amount_type =", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotEqualTo(String value) {
            addCriterion("amount_type <>", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThan(String value) {
            addCriterion("amount_type >", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThanOrEqualTo(String value) {
            addCriterion("amount_type >=", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThan(String value) {
            addCriterion("amount_type <", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThanOrEqualTo(String value) {
            addCriterion("amount_type <=", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountTypeLike(String value) {
            addCriterion("amount_type like", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotLike(String value) {
            addCriterion("amount_type not like", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIn(List<String> values) {
            addCriterion("amount_type in", values, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotIn(List<String> values) {
            addCriterion("amount_type not in", values, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeBetween(String value1, String value2) {
            addCriterion("amount_type between", value1, value2, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotBetween(String value1, String value2) {
            addCriterion("amount_type not between", value1, value2, "amountType");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("currency <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxIsNull() {
            addCriterion("amount_including_tax is null");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxIsNotNull() {
            addCriterion("amount_including_tax is not null");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxEqualTo(BigDecimal value) {
            addCriterion("amount_including_tax =", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxNotEqualTo(BigDecimal value) {
            addCriterion("amount_including_tax <>", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxGreaterThan(BigDecimal value) {
            addCriterion("amount_including_tax >", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_including_tax >=", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxLessThan(BigDecimal value) {
            addCriterion("amount_including_tax <", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_including_tax <=", value, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("amount_including_tax <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxIn(List<BigDecimal> values) {
            addCriterion("amount_including_tax in", values, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxNotIn(List<BigDecimal> values) {
            addCriterion("amount_including_tax not in", values, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_including_tax between", value1, value2, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andAmountIncludingTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_including_tax not between", value1, value2, "amountIncludingTax");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("end_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIsNull() {
            addCriterion("province_k3_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIsNotNull() {
            addCriterion("province_k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeEqualTo(String value) {
            addCriterion("province_k3_code =", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotEqualTo(String value) {
            addCriterion("province_k3_code <>", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThan(String value) {
            addCriterion("province_k3_code >", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_k3_code >=", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThan(String value) {
            addCriterion("province_k3_code <", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanOrEqualTo(String value) {
            addCriterion("province_k3_code <=", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLike(String value) {
            addCriterion("province_k3_code like", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotLike(String value) {
            addCriterion("province_k3_code not like", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIn(List<String> values) {
            addCriterion("province_k3_code in", values, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotIn(List<String> values) {
            addCriterion("province_k3_code not in", values, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeBetween(String value1, String value2) {
            addCriterion("province_k3_code between", value1, value2, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotBetween(String value1, String value2) {
            addCriterion("province_k3_code not between", value1, value2, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIsNull() {
            addCriterion("province_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIsNotNull() {
            addCriterion("province_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeEqualTo(String value) {
            addCriterion("province_mall_code =", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotEqualTo(String value) {
            addCriterion("province_mall_code <>", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThan(String value) {
            addCriterion("province_mall_code >", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_mall_code >=", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThan(String value) {
            addCriterion("province_mall_code <", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanOrEqualTo(String value) {
            addCriterion("province_mall_code <=", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLike(String value) {
            addCriterion("province_mall_code like", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotLike(String value) {
            addCriterion("province_mall_code not like", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIn(List<String> values) {
            addCriterion("province_mall_code in", values, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotIn(List<String> values) {
            addCriterion("province_mall_code not in", values, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeBetween(String value1, String value2) {
            addCriterion("province_mall_code between", value1, value2, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotBetween(String value1, String value2) {
            addCriterion("province_mall_code not between", value1, value2, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIsNull() {
            addCriterion("province_mall_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIsNotNull() {
            addCriterion("province_mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameEqualTo(String value) {
            addCriterion("province_mall_name =", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotEqualTo(String value) {
            addCriterion("province_mall_name <>", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThan(String value) {
            addCriterion("province_mall_name >", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_mall_name >=", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThan(String value) {
            addCriterion("province_mall_name <", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanOrEqualTo(String value) {
            addCriterion("province_mall_name <=", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("province_mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLike(String value) {
            addCriterion("province_mall_name like", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotLike(String value) {
            addCriterion("province_mall_name not like", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIn(List<String> values) {
            addCriterion("province_mall_name in", values, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotIn(List<String> values) {
            addCriterion("province_mall_name not in", values, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameBetween(String value1, String value2) {
            addCriterion("province_mall_name between", value1, value2, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotBetween(String value1, String value2) {
            addCriterion("province_mall_name not between", value1, value2, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeIsNull() {
            addCriterion("city_k3_code is null");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeIsNotNull() {
            addCriterion("city_k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeEqualTo(String value) {
            addCriterion("city_k3_code =", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeNotEqualTo(String value) {
            addCriterion("city_k3_code <>", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeGreaterThan(String value) {
            addCriterion("city_k3_code >", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_k3_code >=", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLessThan(String value) {
            addCriterion("city_k3_code <", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLessThanOrEqualTo(String value) {
            addCriterion("city_k3_code <=", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLike(String value) {
            addCriterion("city_k3_code like", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeNotLike(String value) {
            addCriterion("city_k3_code not like", value, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeIn(List<String> values) {
            addCriterion("city_k3_code in", values, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeNotIn(List<String> values) {
            addCriterion("city_k3_code not in", values, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeBetween(String value1, String value2) {
            addCriterion("city_k3_code between", value1, value2, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeNotBetween(String value1, String value2) {
            addCriterion("city_k3_code not between", value1, value2, "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeIsNull() {
            addCriterion("city_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeIsNotNull() {
            addCriterion("city_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeEqualTo(String value) {
            addCriterion("city_mall_code =", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeNotEqualTo(String value) {
            addCriterion("city_mall_code <>", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeGreaterThan(String value) {
            addCriterion("city_mall_code >", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_mall_code >=", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLessThan(String value) {
            addCriterion("city_mall_code <", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLessThanOrEqualTo(String value) {
            addCriterion("city_mall_code <=", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLike(String value) {
            addCriterion("city_mall_code like", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeNotLike(String value) {
            addCriterion("city_mall_code not like", value, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeIn(List<String> values) {
            addCriterion("city_mall_code in", values, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeNotIn(List<String> values) {
            addCriterion("city_mall_code not in", values, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeBetween(String value1, String value2) {
            addCriterion("city_mall_code between", value1, value2, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeNotBetween(String value1, String value2) {
            addCriterion("city_mall_code not between", value1, value2, "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallNameIsNull() {
            addCriterion("city_mall_name is null");
            return (Criteria) this;
        }

        public Criteria andCityMallNameIsNotNull() {
            addCriterion("city_mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityMallNameEqualTo(String value) {
            addCriterion("city_mall_name =", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameNotEqualTo(String value) {
            addCriterion("city_mall_name <>", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameGreaterThan(String value) {
            addCriterion("city_mall_name >", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_mall_name >=", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameLessThan(String value) {
            addCriterion("city_mall_name <", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameLessThanOrEqualTo(String value) {
            addCriterion("city_mall_name <=", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("city_mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityMallNameLike(String value) {
            addCriterion("city_mall_name like", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameNotLike(String value) {
            addCriterion("city_mall_name not like", value, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameIn(List<String> values) {
            addCriterion("city_mall_name in", values, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameNotIn(List<String> values) {
            addCriterion("city_mall_name not in", values, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameBetween(String value1, String value2) {
            addCriterion("city_mall_name between", value1, value2, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCityMallNameNotBetween(String value1, String value2) {
            addCriterion("city_mall_name not between", value1, value2, "cityMallName");
            return (Criteria) this;
        }

        public Criteria andCountTypeIsNull() {
            addCriterion("count_type is null");
            return (Criteria) this;
        }

        public Criteria andCountTypeIsNotNull() {
            addCriterion("count_type is not null");
            return (Criteria) this;
        }

        public Criteria andCountTypeEqualTo(Integer value) {
            addCriterion("count_type =", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeNotEqualTo(Integer value) {
            addCriterion("count_type <>", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeGreaterThan(Integer value) {
            addCriterion("count_type >", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("count_type >=", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeLessThan(Integer value) {
            addCriterion("count_type <", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("count_type <=", value, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("count_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCountTypeIn(List<Integer> values) {
            addCriterion("count_type in", values, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeNotIn(List<Integer> values) {
            addCriterion("count_type not in", values, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeBetween(Integer value1, Integer value2) {
            addCriterion("count_type between", value1, value2, "countType");
            return (Criteria) this;
        }

        public Criteria andCountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("count_type not between", value1, value2, "countType");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNull() {
            addCriterion("sub_project is null");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNotNull() {
            addCriterion("sub_project is not null");
            return (Criteria) this;
        }

        public Criteria andSubProjectEqualTo(String value) {
            addCriterion("sub_project =", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectNotEqualTo(String value) {
            addCriterion("sub_project <>", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThan(String value) {
            addCriterion("sub_project >", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanOrEqualTo(String value) {
            addCriterion("sub_project >=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThan(String value) {
            addCriterion("sub_project <", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanOrEqualTo(String value) {
            addCriterion("sub_project <=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sub_project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLike(String value) {
            addCriterion("sub_project like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotLike(String value) {
            addCriterion("sub_project not like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectIn(List<String> values) {
            addCriterion("sub_project in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotIn(List<String> values) {
            addCriterion("sub_project not in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectBetween(String value1, String value2) {
            addCriterion("sub_project between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotBetween(String value1, String value2) {
            addCriterion("sub_project not between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andFNumberIsNull() {
            addCriterion("f_number is null");
            return (Criteria) this;
        }

        public Criteria andFNumberIsNotNull() {
            addCriterion("f_number is not null");
            return (Criteria) this;
        }

        public Criteria andFNumberEqualTo(String value) {
            addCriterion("f_number =", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberNotEqualTo(String value) {
            addCriterion("f_number <>", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberGreaterThan(String value) {
            addCriterion("f_number >", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberGreaterThanOrEqualTo(String value) {
            addCriterion("f_number >=", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberLessThan(String value) {
            addCriterion("f_number <", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberLessThanOrEqualTo(String value) {
            addCriterion("f_number <=", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("f_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFNumberLike(String value) {
            addCriterion("f_number like", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberNotLike(String value) {
            addCriterion("f_number not like", value, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberIn(List<String> values) {
            addCriterion("f_number in", values, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberNotIn(List<String> values) {
            addCriterion("f_number not in", values, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberBetween(String value1, String value2) {
            addCriterion("f_number between", value1, value2, "fNumber");
            return (Criteria) this;
        }

        public Criteria andFNumberNotBetween(String value1, String value2) {
            addCriterion("f_number not between", value1, value2, "fNumber");
            return (Criteria) this;
        }

        public Criteria andSettlementModeIsNull() {
            addCriterion("settlement_mode is null");
            return (Criteria) this;
        }

        public Criteria andSettlementModeIsNotNull() {
            addCriterion("settlement_mode is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementModeEqualTo(Integer value) {
            addCriterion("settlement_mode =", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeNotEqualTo(Integer value) {
            addCriterion("settlement_mode <>", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeGreaterThan(Integer value) {
            addCriterion("settlement_mode >", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("settlement_mode >=", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeLessThan(Integer value) {
            addCriterion("settlement_mode <", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeLessThanOrEqualTo(Integer value) {
            addCriterion("settlement_mode <=", value, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("settlement_mode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementModeIn(List<Integer> values) {
            addCriterion("settlement_mode in", values, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeNotIn(List<Integer> values) {
            addCriterion("settlement_mode not in", values, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeBetween(Integer value1, Integer value2) {
            addCriterion("settlement_mode between", value1, value2, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andSettlementModeNotBetween(Integer value1, Integer value2) {
            addCriterion("settlement_mode not between", value1, value2, "settlementMode");
            return (Criteria) this;
        }

        public Criteria andActiveIsNull() {
            addCriterion("active is null");
            return (Criteria) this;
        }

        public Criteria andActiveIsNotNull() {
            addCriterion("active is not null");
            return (Criteria) this;
        }

        public Criteria andActiveEqualTo(Boolean value) {
            addCriterion("active =", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveNotEqualTo(Boolean value) {
            addCriterion("active <>", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveGreaterThan(Boolean value) {
            addCriterion("active >", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveGreaterThanOrEqualTo(Boolean value) {
            addCriterion("active >=", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveLessThan(Boolean value) {
            addCriterion("active <", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveLessThanOrEqualTo(Boolean value) {
            addCriterion("active <=", value, "active");
            return (Criteria) this;
        }

        public Criteria andActiveLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("active <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActiveIn(List<Boolean> values) {
            addCriterion("active in", values, "active");
            return (Criteria) this;
        }

        public Criteria andActiveNotIn(List<Boolean> values) {
            addCriterion("active not in", values, "active");
            return (Criteria) this;
        }

        public Criteria andActiveBetween(Boolean value1, Boolean value2) {
            addCriterion("active between", value1, value2, "active");
            return (Criteria) this;
        }

        public Criteria andActiveNotBetween(Boolean value1, Boolean value2) {
            addCriterion("active not between", value1, value2, "active");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(Integer value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(Integer value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(Integer value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(Integer value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<Integer> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<Integer> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(Integer value1, Integer value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractSourceIsNull() {
            addCriterion("contract_source is null");
            return (Criteria) this;
        }

        public Criteria andContractSourceIsNotNull() {
            addCriterion("contract_source is not null");
            return (Criteria) this;
        }

        public Criteria andContractSourceEqualTo(Integer value) {
            addCriterion("contract_source =", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceNotEqualTo(Integer value) {
            addCriterion("contract_source <>", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceGreaterThan(Integer value) {
            addCriterion("contract_source >", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_source >=", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceLessThan(Integer value) {
            addCriterion("contract_source <", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceLessThanOrEqualTo(Integer value) {
            addCriterion("contract_source <=", value, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("contract_source <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSourceIn(List<Integer> values) {
            addCriterion("contract_source in", values, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceNotIn(List<Integer> values) {
            addCriterion("contract_source not in", values, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceBetween(Integer value1, Integer value2) {
            addCriterion("contract_source between", value1, value2, "contractSource");
            return (Criteria) this;
        }

        public Criteria andContractSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_source not between", value1, value2, "contractSource");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigIsNull() {
            addCriterion("quota_config is null");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigIsNotNull() {
            addCriterion("quota_config is not null");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigEqualTo(Integer value) {
            addCriterion("quota_config =", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigNotEqualTo(Integer value) {
            addCriterion("quota_config <>", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigGreaterThan(Integer value) {
            addCriterion("quota_config >", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigGreaterThanOrEqualTo(Integer value) {
            addCriterion("quota_config >=", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigLessThan(Integer value) {
            addCriterion("quota_config <", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigLessThanOrEqualTo(Integer value) {
            addCriterion("quota_config <=", value, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("quota_config <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuotaConfigIn(List<Integer> values) {
            addCriterion("quota_config in", values, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigNotIn(List<Integer> values) {
            addCriterion("quota_config not in", values, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigBetween(Integer value1, Integer value2) {
            addCriterion("quota_config between", value1, value2, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andQuotaConfigNotBetween(Integer value1, Integer value2) {
            addCriterion("quota_config not between", value1, value2, "quotaConfig");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeIsNull() {
            addCriterion("sale_order_type is null");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeIsNotNull() {
            addCriterion("sale_order_type is not null");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeEqualTo(Integer value) {
            addCriterion("sale_order_type =", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeNotEqualTo(Integer value) {
            addCriterion("sale_order_type <>", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeGreaterThan(Integer value) {
            addCriterion("sale_order_type >", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sale_order_type >=", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeLessThan(Integer value) {
            addCriterion("sale_order_type <", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sale_order_type <=", value, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("sale_order_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeIn(List<Integer> values) {
            addCriterion("sale_order_type in", values, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeNotIn(List<Integer> values) {
            addCriterion("sale_order_type not in", values, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("sale_order_type between", value1, value2, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andSaleOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sale_order_type not between", value1, value2, "saleOrderType");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberIsNull() {
            addCriterion("relevancy_history_number is null");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberIsNotNull() {
            addCriterion("relevancy_history_number is not null");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberEqualTo(String value) {
            addCriterion("relevancy_history_number =", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberNotEqualTo(String value) {
            addCriterion("relevancy_history_number <>", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberNotEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberGreaterThan(String value) {
            addCriterion("relevancy_history_number >", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberGreaterThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberGreaterThanOrEqualTo(String value) {
            addCriterion("relevancy_history_number >=", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberGreaterThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLessThan(String value) {
            addCriterion("relevancy_history_number <", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLessThanColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLessThanOrEqualTo(String value) {
            addCriterion("relevancy_history_number <=", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLessThanOrEqualToColumn(Contract.Column column) {
            addCriterion(new StringBuilder("relevancy_history_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLike(String value) {
            addCriterion("relevancy_history_number like", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberNotLike(String value) {
            addCriterion("relevancy_history_number not like", value, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberIn(List<String> values) {
            addCriterion("relevancy_history_number in", values, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberNotIn(List<String> values) {
            addCriterion("relevancy_history_number not in", values, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberBetween(String value1, String value2) {
            addCriterion("relevancy_history_number between", value1, value2, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberNotBetween(String value1, String value2) {
            addCriterion("relevancy_history_number not between", value1, value2, "relevancyHistoryNumber");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andNumberLikeInsensitive(String value) {
            addCriterion("upper(number) like", value.toUpperCase(), "number");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(name) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andPropertyLikeInsensitive(String value) {
            addCriterion("upper(property) like", value.toUpperCase(), "property");
            return (Criteria) this;
        }

        public Criteria andStatusLikeInsensitive(String value) {
            addCriterion("upper(status) like", value.toUpperCase(), "status");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLikeInsensitive(String value) {
            addCriterion("upper(vendor_code) like", value.toUpperCase(), "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameLikeInsensitive(String value) {
            addCriterion("upper(vendor_name) like", value.toUpperCase(), "vendorName");
            return (Criteria) this;
        }

        public Criteria andCreateCompanyNameLikeInsensitive(String value) {
            addCriterion("upper(create_company_name) like", value.toUpperCase(), "createCompanyName");
            return (Criteria) this;
        }

        public Criteria andCreateDeptNameLikeInsensitive(String value) {
            addCriterion("upper(create_dept_name) like", value.toUpperCase(), "createDeptName");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLikeInsensitive(String value) {
            addCriterion("upper(amount_type) like", value.toUpperCase(), "amountType");
            return (Criteria) this;
        }

        public Criteria andCurrencyLikeInsensitive(String value) {
            addCriterion("upper(currency) like", value.toUpperCase(), "currency");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLikeInsensitive(String value) {
            addCriterion("upper(province_k3_code) like", value.toUpperCase(), "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLikeInsensitive(String value) {
            addCriterion("upper(province_mall_code) like", value.toUpperCase(), "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLikeInsensitive(String value) {
            addCriterion("upper(province_mall_name) like", value.toUpperCase(), "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andCityK3CodeLikeInsensitive(String value) {
            addCriterion("upper(city_k3_code) like", value.toUpperCase(), "cityK3Code");
            return (Criteria) this;
        }

        public Criteria andCityMallCodeLikeInsensitive(String value) {
            addCriterion("upper(city_mall_code) like", value.toUpperCase(), "cityMallCode");
            return (Criteria) this;
        }

        public Criteria andCityMallNameLikeInsensitive(String value) {
            addCriterion("upper(city_mall_name) like", value.toUpperCase(), "cityMallName");
            return (Criteria) this;
        }

        public Criteria andProjectLikeInsensitive(String value) {
            addCriterion("upper(project) like", value.toUpperCase(), "project");
            return (Criteria) this;
        }

        public Criteria andSubProjectLikeInsensitive(String value) {
            addCriterion("upper(sub_project) like", value.toUpperCase(), "subProject");
            return (Criteria) this;
        }

        public Criteria andFNumberLikeInsensitive(String value) {
            addCriterion("upper(f_number) like", value.toUpperCase(), "fNumber");
            return (Criteria) this;
        }

        public Criteria andRelevancyHistoryNumberLikeInsensitive(String value) {
            addCriterion("upper(relevancy_history_number) like", value.toUpperCase(), "relevancyHistoryNumber");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated do_not_delete_during_merge Tue May 13 11:05:38 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private ContractExample example;

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        protected Criteria(ContractExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public ContractExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue May 13 11:05:38 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ContractExample example);
    }
}