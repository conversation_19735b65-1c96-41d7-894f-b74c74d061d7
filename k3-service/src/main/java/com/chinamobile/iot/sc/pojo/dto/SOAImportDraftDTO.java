package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @AUTHOR: HWF
 * @DATE: 2025/2/13
 */
@Data
public class SOAImportDraftDTO {

    /**
     * 记录唯一关键字
     */
    private String priKey;


    /**
     * 省公司编码
     */
    private String provinceCode;

    /**
     * 申请单名称
     */
    private String description;

    /**
     * 来源系统
     */
    private String sourceFrom;

    /**
     * 来源系统单号
     */
    private String sourceFromNo;

    /**
     * MIS主体
     */
    private BigDecimal misBody;

    /**
     * 开支类型
     */
    private String expType;

    /**
     * 报账模式
     */
    private String reimbursementMode;

    /**
     * 申请部门代码
     */
    private String deptCode;

    /**
     * 申请部门名称
     */
    private String deptName;

    /**
     * 申请人ID
     */
    private String createdId;

    /**
     * 申请人姓名
     */
    private String createdName;

    /**
     * 物资类别
     */
    private String mtlTypeCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 需求类型
     */
    private String reqType;

    /**
     * 要求到货时间模式
     */
    private String arrivalTimeModel;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 不含税金额(元)(各行不含税金额的合计)
     */
    private BigDecimal amount;

    /**
     * 税额(元)(各行税额合计)
     */
    private BigDecimal taxSum;

    /**
     * 含税金额(元)（行不含税金额+行税额）
     */
    private BigDecimal amountTax;

    /**
     * 是否全额赠送
     */
    private String isFullPresent;

    /**
     * 扩展字段，传json附件名称
     */
    private String inputText;


    /**
     * 结算单行信息
     */
    private List<SOAImportDraftOrderLineDTO> orderLine;

}
