package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManageExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OnlineSettlementProvinceManageMapper {
    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    long countByExample(OnlineSettlementProvinceManageExample example);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int deleteByExample(OnlineSettlementProvinceManageExample example);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int insert(OnlineSettlementProvinceManage record);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int insertSelective(OnlineSettlementProvinceManage record);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    List<OnlineSettlementProvinceManage> selectByExample(OnlineSettlementProvinceManageExample example);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    OnlineSettlementProvinceManage selectByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int updateByExampleSelective(@Param("record") OnlineSettlementProvinceManage record, @Param("example") OnlineSettlementProvinceManageExample example);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int updateByExample(@Param("record") OnlineSettlementProvinceManage record, @Param("example") OnlineSettlementProvinceManageExample example);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int updateByPrimaryKeySelective(OnlineSettlementProvinceManage record);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int updateByPrimaryKey(OnlineSettlementProvinceManage record);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int batchInsert(@Param("list") List<OnlineSettlementProvinceManage> list);

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    int batchInsertSelective(@Param("list") List<OnlineSettlementProvinceManage> list, @Param("selective") OnlineSettlementProvinceManage.Column ... selective);
}