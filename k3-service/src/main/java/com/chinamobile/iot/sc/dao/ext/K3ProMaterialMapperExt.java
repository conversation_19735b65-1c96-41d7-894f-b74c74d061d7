package com.chinamobile.iot.sc.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.vo.ContractMaterialDetailItemVO;
import com.chinamobile.iot.sc.pojo.vo.PromaterialVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/18 15:31
 */
@Mapper
public interface K3ProMaterialMapperExt {

    List<PromaterialVO>  selectProMaterialByQuery(@Param("page") Page page, String spuOfferingName, String spuOfferingClass,
                                                  String skuOfferingName, String atomOfferingName, String materialName, Integer configStatus);

    String selectAtomName(String atomCode, String spuCode, String skuCode);

    List<ContractMaterialDetailItemVO> getMaterialDetailItem(String contractId, String contractNum);
}
