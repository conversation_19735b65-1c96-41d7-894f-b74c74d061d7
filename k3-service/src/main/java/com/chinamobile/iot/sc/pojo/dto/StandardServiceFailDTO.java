package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/11/4 14:39
 * @description:
 **/
@Data
public class StandardServiceFailDTO {

    /**
     * 标准服务编码
     */
    @ExcelProperty(value = "标准服务编码",index = 0)
    private String id;


    /**
     * 标准服务名称
     */
    @ExcelProperty(value = "标准服务名称",index = 1)
    private String name;

    /**
     * 产品部门
     */
    @ExcelProperty(value = "产品部门",index = 2)
    private String departmentName;

    /**
     * 实质性产品名称
     */
    @ExcelProperty(value = "实质性产品名称",index = 3)
    private String realProductName;

    /**
     * 产品属性
     */
    @ExcelProperty(value = "产品属性",index = 4)
    private String productProperty;

    /**
     * 备注1
     */
    @ExcelProperty(value = "备注1",index = 5)
    private String remark1;

    /**
     * 备注2
     */
    @ExcelProperty(value = "备注2",index = 6)
    private String remark2;


    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因",index = 7)
    private String failedReason;
}
