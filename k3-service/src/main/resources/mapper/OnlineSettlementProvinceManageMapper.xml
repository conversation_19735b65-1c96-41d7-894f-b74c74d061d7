<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OnlineSettlementProvinceManageMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, be_id, province_name, settle_status, province_code, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from online_settlement_province_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from online_settlement_province_manage
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from online_settlement_province_manage
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManageExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from online_settlement_province_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_province_manage (id, be_id, province_name, 
      settle_status, province_code, create_time, 
      update_time)
    values (#{id,jdbcType=INTEGER}, #{beId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{settleStatus,jdbcType=INTEGER}, #{provinceCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_province_manage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from online_settlement_province_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_province_manage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.settleStatus != null">
        settle_status = #{record.settleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_province_manage
    set id = #{record.id,jdbcType=INTEGER},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      settle_status = #{record.settleStatus,jdbcType=INTEGER},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_province_manage
    <set>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementProvinceManage">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_province_manage
    set be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      settle_status = #{settleStatus,jdbcType=INTEGER},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_province_manage
    (id, be_id, province_name, settle_status, province_code, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=INTEGER}, #{item.beId,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, 
        #{item.settleStatus,jdbcType=INTEGER}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 11:24:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_province_manage (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=INTEGER}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'settle_status'.toString() == column.value">
          #{item.settleStatus,jdbcType=INTEGER}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>