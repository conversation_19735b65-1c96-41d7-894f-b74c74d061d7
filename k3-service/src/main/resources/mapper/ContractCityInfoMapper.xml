<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ContractCityInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ContractCityInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="mall_code" jdbcType="VARCHAR" property="mallCode" />
    <result column="mall_name" jdbcType="VARCHAR" property="mallName" />
    <result column="province_mall_code" jdbcType="VARCHAR" property="provinceMallCode" />
    <result column="province_mall_name" jdbcType="VARCHAR" property="provinceMallName" />
    <result column="province_k3_code" jdbcType="VARCHAR" property="provinceK3Code" />
    <result column="k3_code" jdbcType="VARCHAR" property="k3Code" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    mall_code, mall_name, province_mall_code, province_mall_name, province_k3_code, k3_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract_city_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from contract_city_info
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from contract_city_info
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from contract_city_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into contract_city_info (mall_code, mall_name, province_mall_code, 
      province_mall_name, province_k3_code, k3_code
      )
    values (#{mallCode,jdbcType=VARCHAR}, #{mallName,jdbcType=VARCHAR}, #{provinceMallCode,jdbcType=VARCHAR}, 
      #{provinceMallName,jdbcType=VARCHAR}, #{provinceK3Code,jdbcType=VARCHAR}, #{k3Code,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into contract_city_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mallCode != null">
        mall_code,
      </if>
      <if test="mallName != null">
        mall_name,
      </if>
      <if test="provinceMallCode != null">
        province_mall_code,
      </if>
      <if test="provinceMallName != null">
        province_mall_name,
      </if>
      <if test="provinceK3Code != null">
        province_k3_code,
      </if>
      <if test="k3Code != null">
        k3_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mallCode != null">
        #{mallCode,jdbcType=VARCHAR},
      </if>
      <if test="mallName != null">
        #{mallName,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallCode != null">
        #{provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallName != null">
        #{provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="provinceK3Code != null">
        #{provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="k3Code != null">
        #{k3Code,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from contract_city_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    update contract_city_info
    <set>
      <if test="record.mallCode != null">
        mall_code = #{record.mallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mallName != null">
        mall_name = #{record.mallName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceMallCode != null">
        province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceMallName != null">
        province_mall_name = #{record.provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceK3Code != null">
        province_k3_code = #{record.provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="record.k3Code != null">
        k3_code = #{record.k3Code,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    update contract_city_info
    set mall_code = #{record.mallCode,jdbcType=VARCHAR},
      mall_name = #{record.mallName,jdbcType=VARCHAR},
      province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR},
      province_mall_name = #{record.provinceMallName,jdbcType=VARCHAR},
      province_k3_code = #{record.provinceK3Code,jdbcType=VARCHAR},
      k3_code = #{record.k3Code,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    update contract_city_info
    <set>
      <if test="mallName != null">
        mall_name = #{mallName,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallCode != null">
        province_mall_code = #{provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallName != null">
        province_mall_name = #{provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="provinceK3Code != null">
        province_k3_code = #{provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="k3Code != null">
        k3_code = #{k3Code,jdbcType=VARCHAR},
      </if>
    </set>
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractCityInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    update contract_city_info
    set mall_name = #{mallName,jdbcType=VARCHAR},
      province_mall_code = #{provinceMallCode,jdbcType=VARCHAR},
      province_mall_name = #{provinceMallName,jdbcType=VARCHAR},
      province_k3_code = #{provinceK3Code,jdbcType=VARCHAR},
      k3_code = #{k3Code,jdbcType=VARCHAR}
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into contract_city_info
    (mall_code, mall_name, province_mall_code, province_mall_name, province_k3_code, 
      k3_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mallCode,jdbcType=VARCHAR}, #{item.mallName,jdbcType=VARCHAR}, #{item.provinceMallCode,jdbcType=VARCHAR}, 
        #{item.provinceMallName,jdbcType=VARCHAR}, #{item.provinceK3Code,jdbcType=VARCHAR}, 
        #{item.k3Code,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 06 15:50:35 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into contract_city_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'mall_code'.toString() == column.value">
          #{item.mallCode,jdbcType=VARCHAR}
        </if>
        <if test="'mall_name'.toString() == column.value">
          #{item.mallName,jdbcType=VARCHAR}
        </if>
        <if test="'province_mall_code'.toString() == column.value">
          #{item.provinceMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_mall_name'.toString() == column.value">
          #{item.provinceMallName,jdbcType=VARCHAR}
        </if>
        <if test="'province_k3_code'.toString() == column.value">
          #{item.provinceK3Code,jdbcType=VARCHAR}
        </if>
        <if test="'k3_code'.toString() == column.value">
          #{item.k3Code,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>