<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.K3ProMaterialMapperExt">

    <select id="selectProMaterialByQuery" resultType="com.chinamobile.iot.sc.pojo.vo.PromaterialVO">
        select atom.id as atomId,
        spu.offering_name as spuOfferingName,
        spu.offering_code as spuOfferingCode,
        --         case
        --         when ci.offering_class = 'A04' then 'DICT产品增值服务包'
        --         when ci.offering_class = 'A08' then 'OneNET独立服务'
        --         else ci.offering_class
        --         end as spuOfferingClass,
        ci.offering_class as spuOfferingClass,
        sku.offering_name as skuOfferingName,
        sku.offering_code as skuOfferingCode,
        atom.offering_name as atomOfferingName,
        case
        when atom.offering_class = 'O' then 'OneNET独立服务'
        when atom.offering_class = 'D' then 'DICT产品增值服务包'
        when atom.offering_class = 'P' then 'OnePark独立服务'
        when atom.offering_class = 'F' then '行车卫士标准产品'
        else atom.offering_class
        end as atomOfferingClass,
        atom.offering_code as atomOfferingCode,
        atom.model as model,
        atom.color as color,
        ap.partner_name as partnerName,
        kpm.material_num as materialNum,
        kpm.material_name as materialName,
        kpm.id as id,
        IF((kpm.update_time is not NULL), 1, 0) as configStatus,
        kpm.update_time as configTime,
        spu.offering_status spuOfferingStatus,
        sku.offering_status skuOfferingStatus
        from atom_offering_info atom
        left join (select * from k3_product_material kp group by kp.atom_id) kpm on atom.id = kpm.atom_id
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        left join sku_offering_info sku on atom.spu_code = sku.spu_code and atom.sku_code = sku.offering_code and sku.delete_time is null
        left join category_info ci on atom.spu_id=ci.spu_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
        aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary)
        from
        atom_offering_cooperator_relation aocr,
        atom_offering_info aoi,
        user_partner up
        where
        aocr.atom_offering_id = aoi.id
        and aocr.cooperator_id = up.user_id
        group by aocr.atom_offering_id
        ) ap on ap.atom_offering_id = atom.id
        where atom.delete_time is null and atom.offering_class !='S'
        and (ci.offering_class='A04' or ci.offering_class='A08' or ci.offering_class ='A09' or ci.offering_class ='A12')
        <if test="configStatus == 0">
            and 1=1
        </if>
        <if test="configStatus == 1">
            and kpm.create_time is null
        </if>
        <if test="configStatus == 2">
            and kpm.create_time is not null
        </if>
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and atom.offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <if test="materialName != null and materialName != ''">
            and kpm.material_name like concat ('%',#{materialName},'%')
        </if>
        ORDER BY atom.update_time DESC
    </select>


    <select id="selectAtomName" resultType="java.lang.String">
        select atom.offering_name as atomName
        from atom_offering_info atom
        where atom.offering_code = #{atomCode}
        and atom.spu_code = #{spuCode}
        and atom.sku_code = #{skuCode}
    </select>

<!--    <resultMap id="configedMaterails" type="com.chinamobile.iot.sc.pojo.vo.ProductMaterialsItemVO">-->
<!--        <id column="materialNum" jdbcType="VARCHAR" property="materialNum"/>-->
<!--        <collection property="materialArray" column="materialNumber"-->
<!--                    ofType="com.chinamobile.com.iot.sc.pojo.vo.ProductMaterialDetailItemVO"-->
<!--                    select="getConfigedMaterailDetail">-->
<!--            <result column="materialNum" jdbcType="VARCHAR" property="materialNum"/>-->
<!--            <result column="materialCount" jdbcType="VARCHAR" property="materialCount"/>-->
<!--            <result column="settlePrice" jdbcType="VARCHAR" property="settlePrice"/>-->
<!--            <result column="materialName" jdbcType="VARCHAR" property="materialName"/>-->
<!--            <result column="materialDept" jdbcType="VARCHAR" property="materialDept"/>-->
<!--            <result column="contractNum" jdbcType="VARCHAR" property="contractNum"/>-->
<!--            <result column="contractName" jdbcType="VARCHAR" property="contractName"/>-->
<!--            <result column="buyerName" jdbcType="VARCHAR" property="buyerName"/>-->
<!--            <result column="contractPrice" jdbcType="VARCHAR" property="contractPrice"/>-->
<!--            <result column="expiredDate" jdbcType="VARCHAR" property="expiredDate"/>-->
<!--            <result column="contractEffective" jdbcType="VARCHAR" property="contractEffective"/>-->
<!--            <result column="binded" jdbcType="VARCHAR" property="binded"/>-->
<!--        </collection>-->
<!--    </resultMap>-->


<!--    <select id="getConfigedMaterialDetail" parameterType="String"-->
<!--            resultType="com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO">-->
<!--        select-->
<!--            cm.material_number as cm_materialNum,-->
<!--            cm.contract_number as cm_contract_num,-->
<!--            case-->
<!--                when kpm.atom_id is not	NULL then TRUE-->
<!--                when kpm.atom_id is NULL then FALSE-->
<!--                end as binded,-->
<!--            kpm.atom_id,-->
<!--            kpm.material_num as k_materialNum,-->
<!--            kpm.contract_num as k_contractNum-->
<!--        from-->
<!--            k3_product_material	kpm-->
<!--            right join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number-->
<!--        where-->
<!--            cm.material_number = #{material_number}-->
<!--        order by cm.material_number	asc-->
<!--    </select>-->

<!--   <select id="configedMaterails" parameterType="String"-->
<!--           resultMap="configedMaterails">-->
<!--        select-->
<!--            material_num as materialNum-->
<!--        from-->
<!--            k3_product_material kpm-->
<!--        where-->
<!--            material_num = #{atomId}-->
<!--        group by material_num-->
<!--   </select>-->

    <select id="getMaterialDetailItem" parameterType="String"
            resultType="com.chinamobile.iot.sc.pojo.vo.ContractMaterialDetailItemVO">
        select
            kpm.id  kpmId,
            pci.province_code provinceCode,
            pci.mis_body misBody,
            pci.exp_type expType,
            pci.reimbursement_mode reimbursementMode,
            pci.dept_code deptCode,
            pci.dept_name deptName,
            pci.created_id createdId,
            pci.created_name createdName,
            pci.mtl_type_code mtlTypeCode,
            pci.contract_code contractCode,
            pci.vendor_code vendorCode,
            pci.req_type reqType,
--          合同信息非必填
            pci.arrival_time_model arrivalTimeModel,
            pci.req_second_type reqSecondType,
            pci.arrival_days arriveMessa,
--          物料信息非必填
            pci.budget_id budgetId,
            pci.budget_year budgetYear,
            pci.activity_code activityCode,
            pci.cost_center costCenter,
            pci.expense_account expenseAccount,
            pci.cost_subject costSubject,
            pci.manage_activity manageActivity,
            pci.manage_market manageMarket,
            pci.manage_product manageProduct,
--          物料信息必填
            pmi.attr2,
            pmi.attr3,
            pmi.unit,
            pmi.unit_price unitPrice,
            pmi.tax_rate taxRate,
            pmi.tax_code taxCode,
            pmi.sd_project_flag sdProjectFlag,
            pmi.project_code projectCode,
            pmi.task_code taskCode,
            pmi.expenditure_type expenditureType,

            pmi.material_code materialCode,
            pmi.material_name materialName,
            kpm.material_count * o2ai.sku_quantity * o2ai.atom_quantity  quantity,
--          物料信息必填但是在合同表里导入的
            pci.organization_code organizationCode,
            pci.item_type itemType,
            pci.rcv_user_num rcvUserNum,
            pci.rcv_user rcvUser,
            pci.rcv_contact_phone rcvContactPhone,
            pci.rcv_site_address rcvSiteAddress


--             pmi.tax_exclusive_univalence  unitPrice,
--             pmi.tax_inclusive_univalence  discountedUnitPrice,
--             pmi.tax_rate  taxRate,
--             pmi.material_code  materialsCode,
--             pmi.material_describe  materialsDesc,
--             pmi.unit  materialsUnit,
--             pmi.nested_code  offerSetCode,
--             pmi.part_code  offerItemCode,
--             pmi.part_name  offerItemName,
--
--             kpm.create_time  createDate,
--             kpm.update_time  lastUpdateDate,
--             cci.scm_code  ouCode,
--             cci.mall_name  ouName
        from
            order_2c_info o2i
                left join order_2c_atom_info o2ai on o2ai.order_id = o2i.order_id
                left join k3_product_material kpm on kpm.atom_offering_code = o2ai.atom_offering_code and kpm.spu_code = o2ai.spu_offering_code and kpm.sku_code = o2ai.sku_offering_code
                left join contract_city_info cci on cci.mall_code = o2i.location
                left join province_material_info_new pmi on pmi.material_code = kpm.material_num
                left join province_contract_info_new pci on pci.id = pmi.province_contract_id
        where
            o2i.sync_k3_id = #{contractId}
          and pci.contract_code = #{contractNum}
    </select>
</mapper>