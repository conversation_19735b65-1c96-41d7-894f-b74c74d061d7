<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.K3OrdersMapperExt">

  <select id="listK3Orders"
          resultType="com.chinamobile.iot.sc.pojo.mapper.K3OrdersDO">
    SELECT
      order_id orderId,
      DATE_FORMAT(order_time,'%Y-%m-%d %H:%i:%s') orderTime,
      DATE_FORMAT(order_finish_time,'%Y-%m-%d %H:%i:%s') orderFinishTime,
      spu_name spuName,
      spu_code spuCode,
      spu_type spuType,
      sku_name skuName,
      sku_code skuCode,
      atom_name atomName,
      atom_code atomCode,
      unit_price unitPrice,
      quatity quatity,
      total_price totalPrice,
      deduct_price deductPrice,
      receiver_phone receiverPhone,
      order_province orderProvince,
      order_city orderCity,
      create_oper_code createOperCode,
      employee_num employeeNum,
      material_num materialNum,
      material_name materialName,
      material_dept materialDept,
      contract_num contractNum,
      contract_name contractName,
      contract_dept contractDept,
      buyer_province buyerProvince,
      buyer_city buyerCity,
      contract_satis_type contractSatisType
  FROM
      k3_orders
  where 1= 1
    <if test="k3DataQuery.saleDate != null and k3DataQuery.saleDate != ''">
        and DATE_FORMAT(date_sub(create_time, interval 1 month),'%Y-%m') = #{k3DataQuery.saleDate}
    </if>
    <if test="k3DataQuery.spuName != null and k3DataQuery.spuName != ''">
        and spu_name like '%${k3DataQuery.spuName}%'
    </if>
    <if test="k3DataQuery.spuType != null and k3DataQuery.spuType != ''">
      and spu_type like '%${k3DataQuery.spuType}%'
    </if>
    <if test="k3DataQuery.orderProvince != null and k3DataQuery.orderProvince != ''">
      and order_province like '%${k3DataQuery.orderProvince}%'
    </if>
    <if test="k3DataQuery.orderCity != null and k3DataQuery.orderCity != ''">
      and order_city like '%${k3DataQuery.orderCity}%'
    </if>
    <if test="k3DataQuery.contractNum != null and k3DataQuery.contractNum != ''">
      and contract_num like '%${k3DataQuery.contractNum}%'
    </if>
    <if test="k3DataQuery.matetrialNum != null and k3DataQuery.matetrialNum != ''">
      and material_num like '%${k3DataQuery.matetrialNum}%'
    </if>
    order by order_finish_time desc
  </select>

  <select id="listSynOrder" resultType="com.chinamobile.iot.sc.pojo.mapper.K3SynOrderDO">
    select
        order_id orderId,
        contract_num contractNum,
        material_num materialNum,
        material_unit materialUnit,
        material_dept materialDept,
        quatity,
        contract_tax contractTax,
        unit_price unitPrice
    from
        k3_orders
    where
        1=1
    <if test="k3SynOrderQuery.saleDate != null and k3SynOrderQuery.saleDate != ''">
      and DATE_FORMAT(date_sub(create_time, interval 1 month),'%Y-%m') = #{k3SynOrderQuery.saleDate}
    </if>

    <if test="k3SynOrderQuery.orderIdList != null">
      and order_id in
      <foreach item ="item" index ="index" collection ="k3SynOrderQuery.orderIdList" open ="(" separator ="," close =")" >
        #{item}
      </foreach>
    </if>

    <if test="k3SynOrderQuery.toK3Date != null and k3SynOrderQuery.toK3Date != ''">
      and DATE_FORMAT(create_time,'%Y-%m') = #{k3SynOrderQuery.toK3Date}
    </if>
  </select>



  <select id="genOrderBills"
          resultType="com.chinamobile.iot.sc.pojo.entity.K3Orders">
    select
      o2i.order_id as orderId,
      DATE_FORMAT(o2i.create_time, '%Y-%m-%d %H:%i:%s') as orderTime,
      o2i.update_time as orderFinishTime,
      spu.offering_name as spuName,
      spu.offering_code as spuCode,
      case
          when ci.offering_class = 'A04' then 'DICT产品增值服务包'
          when ci.offering_class = 'A08' then 'OneNET独立服务'
          else ci.offering_class
          end as spuType,
      ci.offering_class as spuClass,
      sku.offering_name as skuName,
      sku.offering_code as skuCode,
      atom.offering_name as atomName,
      atom.offering_code as atomCode,
      o2ai.atom_settle_price as unitPrice,
      o2ai.atom_quantity*o2ai.sku_quantity as quatity,
      o2ai.atom_settle_price*o2ai.atom_quantity*o2ai.sku_quantity	as totalPrice,
--       o2ai.sku_price*o2ai.sku_quantity	as totalPrice,
      o2ai.deduct_price as deductPrice,
      o2i.contact_phone as receiverPhone,
      o2i.be_id as orderProvinceCode,
      o2i.location as orderCityCode,
      o2i.addr1 as orderProvince,
      o2i.addr2 as orderCity,
      o2i.create_oper_code as createOperCode,
      o2i.employee_num as employeeNum,
      cm.material_number as materialNum,
      cm.name as materialName,
      cm.material_unit as materialUnit,
      cm.owner_department as materialDept,
      ctr.number as contractNum,
      ctr.name as contractName,
      ctr.project as contractProject,
      ctr.sub_project as contractSubProject,
      ctr.create_dept_name as contractDept,
      ctr.vendor_code as customCode,
      ctr.province_mall_name as buyerProvince,
      ctr.province_mall_code as buyerProvinceMallCode,
      ctr.province_k3_code as buyerProvinceCode,
      ctr.city_mall_name as buyerCity,
      ctr.city_mall_code as buyerCityMallCode,
      ctr.city_k3_code as buyerCityCode,
      case
          when ctr.count_type = 0 then '按地市'
          when ctr.count_type = 1 then '按省份'
          when ctr.count_type = 2 then '按合同'
          else '无'
          end as contractSatisType,
      ctr.count_type as contractStatisEnum,
      ctr.currency as contractMoneyUnit,
      ctr.property as contractType,
      ctr.f_number as contractTax,
      ctr.settlement_mode as contractSettleMode,
      now() as createTime
    from
      order_2c_info o2i
        left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
        left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
        left join spu_offering_info spu on atom.spu_code = spu.offering_code
        left join sku_offering_info sku on atom.sku_code = sku.offering_code
        left join category_info ci on atom.spu_id=ci.spu_id
        right join k3_product_material kpm on atom.id = kpm.atom_id
        left join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number
        left join contract ctr on cm.contract_number = ctr.number
    where o2i.order_status = 7 and ci.offering_class in ('A04','A08') and o2i.update_time between #{from} and #{to}
  </select>



      <select id="genCityBill" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisCity">
          select
              kod.contract_num as contractNum,
              kod.contract_name as contractName,
              kod.contract_dept as contractDept,
              kod.contract_satis_type as contractStatisType,
              kod.contract_project as project,
              kod.contract_sub_project as subProject,
              case
                  when kod.spu_class = 'A04' then '1'
                  when kod.spu_class = 'A08' then '0'
                  else kod.spu_class
                  end as productType,
              kod.order_province as orderProvinceName,
              kod.order_city as orderCityName,
              kod.buyer_province as buyerProvince,
              kod.buyer_province_code as buyerProvinceCode,
              kod.buyer_city as buyerCity,
              kod.buyer_city_code as buyerCityCode,
              kod.contract_sell_unit  as sellUnit,
              kod.contract_money_unit  as moneyUnit,
              kod.contract_type as contractType,
              kod.custom_code as customCode,
              count(kod.id) as orderCount,
              sum(kod.total_price) as totalPrice,
              now() as createTime
          from
              k3_orders kod
          where kod.contract_statis_enum = 0 and kod.create_time between #{from} and #{to}
          group by
              kod.contract_num, kod.order_province, kod.order_city
      </select>

    <select id="genCityBillByOrder" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisCity">
        select
            kod.contract_num as contractNum,
            kod.contract_name as contractName,
            kod.contract_dept as contractDept,
            kod.contract_satis_type as contractStatisType,
            kod.contract_project as project,
            kod.contract_sub_project as subProject,
            case
                when kod.spu_class = 'A04' then '1'
                when kod.spu_class = 'A08' then '0'
                when kod.spu_class = 'A09' then '2'
                else kod.spu_class
                end as productType,
            kod.order_province as orderProvinceName,
            kod.order_city as orderCityName,
            kod.buyer_province as buyerProvince,
            kod.buyer_province_code as buyerProvinceCode,
            kod.buyer_city as buyerCity,
            kod.buyer_city_code as buyerCityCode,
            kod.contract_sell_unit  as sellUnit,
            kod.contract_money_unit  as moneyUnit,
            kod.contract_type as contractType,
            kod.custom_code as customCode,
            count(DISTINCT kod.order_id) as orderCount,
            sum(kod.total_price) as totalPrice,
            group_concat(DISTINCT kod.order_id separator ',') as relatedOrderIds,
            group_concat(kod.id separator ',') as relatedK3OrderIds,
            now() as createTime,
            kod.material_dept materialDept
        from
            k3_orders kod
        where kod.contract_statis_enum = 0 and kod.order_id in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
          kod.material_dept,kod.contract_num, kod.order_province, kod.order_city
    </select>


      <select id="genProvinceBill" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisProvince">
          select
              kod.contract_num as contractNum,
              kod.contract_name as contractName,
              kod.contract_dept as contractDept,
              kod.contract_satis_type as contractStatisType,
              kod.contract_project as project,
              kod.contract_sub_project as subProject,
              case
                  when kod.spu_class = 'A04' then '1'
                  when kod.spu_class = 'A08' then '0'
                  else kod.spu_class
                  end as productType,
              kod.order_province as orderProvinceName,
              kod.order_city as orderCityName,
              kod.buyer_province as buyerProvince,
              kod.buyer_province_code as buyerProvinceCode,
              kod.buyer_city as buyerCity,
              kod.buyer_city_code as buyerCityCode,
              kod.contract_sell_unit  as sellUnit,
              kod.contract_money_unit  as moneyUnit,
              kod.contract_type as contractType,
              kod.contract_settle_mode as contractSettleMode,
              kod.custom_code as customCode,
              count(kod.id) as orderCount,
              sum(kod.total_price) as totalPrice,
              now() as createTime
          from
              k3_orders kod
          where kod.contract_statis_enum = 1 and kod.contract_settle_mode = 1 and kod.create_time between #{from} and #{to}
          group by
              kod.contract_num, kod.order_province

      </select>


    <select id="genProvinceBillByOrder" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisProvince">
        select
            kod.contract_num as contractNum,
            kod.contract_name as contractName,
            kod.contract_dept as contractDept,
            kod.contract_satis_type as contractStatisType,
            kod.contract_project as project,
            kod.contract_sub_project as subProject,
            case
                when kod.spu_class = 'A04' then '1'
                when kod.spu_class = 'A08' then '0'
                when kod.spu_class = 'A09' then '2'
                else kod.spu_class
                end as productType,
            kod.order_province as orderProvinceName,
            kod.order_city as orderCityName,
            kod.buyer_province as buyerProvince,
            kod.buyer_province_code as buyerProvinceCode,
            kod.buyer_city as buyerCity,
            kod.buyer_city_code as buyerCityCode,
            kod.contract_sell_unit  as sellUnit,
            kod.contract_money_unit  as moneyUnit,
            kod.contract_type as contractType,
            kod.contract_settle_mode as contractSettleMode,
            kod.custom_code as customCode,
            count(DISTINCT kod.order_id) as orderCount,
            sum(kod.total_price) as totalPrice,
            group_concat(DISTINCT kod.order_id separator ',') as relatedOrderIds,
            group_concat(kod.id separator ',') as relatedK3OrderIds,
            now() as createTime,
            kod.material_dept materialDept
        from
            k3_orders kod
        where
            kod.contract_statis_enum = 1
        and kod.contract_settle_mode = 1
        and kod.order_id in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
          kod.material_dept,kod.contract_num, kod.order_province
    </select>


    <select id="genProCityBill" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisProcity">
        select
            kod.contract_num as contractNum,
            kod.contract_name as contractName,
            kod.contract_dept as contractDept,
            kod.contract_satis_type as contractStatisType,
            kod.contract_project as project,
            kod.contract_sub_project as subProject,
            case
                when kod.spu_class = 'A04' then '1'
                when kod.spu_class = 'A08' then '0'
                else kod.spu_class
                end as productType,
            kod.order_province as orderProvinceName,
            kod.order_city as orderCityName,
            kod.buyer_province as buyerProvince,
            kod.buyer_province_code as buyerProvinceCode,
            kod.buyer_city as buyerCity,
            kod.buyer_city_code as buyerCityCode,
            kod.contract_sell_unit  as sellUnit,
            kod.contract_money_unit  as moneyUnit,
            kod.contract_type as contractType,
            kod.contract_settle_mode as contractSettleMode,
            kod.custom_code as customCode,
            count(kod.id) as orderCount,
            sum(kod.total_price) as totalPrice,
            now() as createTime
        from
            k3_orders kod
        where kod.contract_statis_enum = 1 and kod.contract_settle_mode = 0 and kod.create_time between #{from} and #{to}
        group by
            kod.contract_num, kod.order_province, kod.order_city
    </select>

    <select id="genProCityBillByOrder" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisProcity">
        select
            kod.contract_num as contractNum,
            kod.contract_name as contractName,
            kod.contract_dept as contractDept,
            kod.contract_satis_type as contractStatisType,
            kod.contract_project as project,
            kod.contract_sub_project as subProject,
            case
                when kod.spu_class = 'A04' then '1'
                when kod.spu_class = 'A08' then '0'
                when kod.spu_class = 'A09' then '2'
                else kod.spu_class
                end as productType,
            kod.order_province as orderProvinceName,
            kod.order_city as orderCityName,
            kod.buyer_province as buyerProvince,
            kod.buyer_province_code as buyerProvinceCode,
            kod.buyer_city as buyerCity,
            kod.buyer_city_code as buyerCityCode,
            kod.contract_sell_unit  as sellUnit,
            kod.contract_money_unit  as moneyUnit,
            kod.contract_type as contractType,
            kod.contract_settle_mode as contractSettleMode,
            kod.custom_code as customCode,
            count(DISTINCT kod.order_id) as orderCount,
            sum(kod.total_price) as totalPrice,
            group_concat(DISTINCT kod.order_id separator ',') as relatedOrderIds,
            group_concat(kod.id separator ',') as relatedK3OrderIds,
            now() as createTime,
            kod.material_dept materialDept
        from
            k3_orders kod
        where kod.contract_statis_enum = 1 and kod.contract_settle_mode = 0 and kod.order_id in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
          kod.material_dept,kod.contract_num, kod.order_province, kod.order_city
    </select>

      <select id="genContractBill" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisContract">
          select
              kod.contract_num as contractNum,
              kod.contract_name as contractName,
              kod.contract_dept as contractDept,
              kod.contract_satis_type as contractStatisType,
              kod.contract_project as project,
              kod.contract_sub_project as subProject,
              case
                  when kod.spu_class = 'A04' then '1'
                  when kod.spu_class = 'A08' then '0'
                  else kod.spu_class
                  end as productType,
              kod.order_province as orderProvinceName,
              kod.order_city as orderCityName,
              kod.buyer_province as buyerProvince,
              kod.buyer_province_code as buyerProvinceCode,
              kod.buyer_city as buyerCity,
              kod.buyer_city_code as buyerCityCode,
              kod.contract_sell_unit  as sellUnit,
              kod.contract_money_unit  as moneyUnit,
              kod.contract_type as contractType,
              kod.custom_code as customCode,
              count(kod.id) as orderCount,
              sum(kod.total_price) as totalPrice,
              now() as createTime
          from
              k3_orders kod
          where kod.contract_statis_enum = 2 and kod.create_time between #{from} and #{to}
          group by
              kod.contract_num
      </select>

    <select id="genContractBillByOrder" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisContract">
        select
            kod.contract_num as contractNum,
            kod.contract_name as contractName,
            kod.contract_dept as contractDept,
            kod.contract_satis_type as contractStatisType,
            kod.contract_project as project,
            kod.contract_sub_project as subProject,
            case
                when kod.spu_class = 'A04' then '1'
                when kod.spu_class = 'A08' then '0'
                when kod.spu_class = 'A09' then '2'
                else kod.spu_class
                end as productType,
            kod.order_province as orderProvinceName,
            kod.order_city as orderCityName,
            kod.buyer_province as buyerProvince,
            kod.buyer_province_code as buyerProvinceCode,
            kod.buyer_city as buyerCity,
            kod.buyer_city_code as buyerCityCode,
            kod.contract_sell_unit  as sellUnit,
            kod.contract_money_unit  as moneyUnit,
            kod.contract_type as contractType,
            kod.custom_code as customCode,
            count(DISTINCT kod.order_id) as orderCount,
            sum(kod.total_price) as totalPrice,
            group_concat(DISTINCT kod.order_id separator ',') as relatedOrderIds,
            group_concat(kod.id separator ',') as relatedK3OrderIds,
            now() as createTime,
            kod.material_dept materialDept
        from
            k3_orders kod
        where kod.contract_statis_enum = 2 and kod.order_id in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
          kod.material_dept,kod.contract_num, kod.order_province, kod.order_city
    </select>

    <select id="genDepartmentBillByOrder" resultType="com.chinamobile.iot.sc.pojo.entity.K3syncStatisDepartment">
        select
            d.short_name departmentName,
            case
                when ko.spu_class = 'A08' then 0
                when ko.spu_class = 'A09' then 2
            else '-1'
            end productType,
            count(distinct ko.order_id) orderCount,
            sum(ko.total_price) totalPrice,
            group_concat(ko.id separator ',') relatedK3OrderIds
        from
            k3_orders ko,
            department d
        where
            ko.product_department_id = d.id
        and ko.spu_class in ('A08','A09')
        and ko.order_id in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by ko.product_department_id,ko.spu_class
    </select>


    <select id="unBindOrders" resultType="com.chinamobile.iot.sc.pojo.dto.UnBindOrderDTO">
        select
            o2i.order_id as orderId,
            o2ai.spu_offering_code as spuCode,
            o2ai.sku_offering_code as skuCode,
            o2ai.atom_offering_code as atomCode,
            case
                when o2ai.atom_offering_class = 'O' then 'OneNET独立服务'
                when o2ai.atom_offering_class = 'D' then 'DICT产品增值服务包'
                else o2ai.atom_offering_class
                end as atomClass,
            o2ai.atom_offering_class as atomClass,
            (select mall_name from contract_province_info cpi where o2i.be_id = cpi.mall_code) as provinceName,
            (select mall_name from contract_city_info cci where o2i.location = cci.mall_code) as cityName
        from
            order_2c_info o2i
                left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
                left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
--                 left join contract_province_info cpi on o2i.be_id = cpi.mall_code
--                 left join select mall_name from contract_city_info where contract_city_info cci on o2i.location = cci.mall_code
--                 left join spu_offering_info spu on atom.spu_code = spu.offering_code
--                 left join category_info ci on atom.spu_id=ci.spu_id
--                 right join k3_product_material kpm on atom.id = kpm.atom_id
                left join k3_product_material kpm on atom.id = kpm.atom_id
        where
            kpm.atom_id IS NULL and o2i.order_status = 7 and o2ai.atom_offering_class in ('O','D') and o2i.update_time between #{from} and #{to}

    </select>


    <select id="unBindOrdersById" resultType="com.chinamobile.iot.sc.pojo.dto.UnBindOrderDTO">
        select
            o2i.order_id as orderId,
            o2ai.spu_offering_code as spuCode,
            o2ai.sku_offering_code as skuCode,
            o2ai.atom_offering_code as atomCode,
            case
                when o2ai.atom_offering_class = 'O' then 'OneNET独立服务'
                when o2ai.atom_offering_class = 'D' then 'DICT产品增值服务包'
                when o2ai.atom_offering_class = 'P' then 'OnePark增值服务'
                else o2ai.atom_offering_class
                end as atomClass,
            o2ai.atom_offering_class as atomClass,
            (select mall_name from contract_province_info cpi where o2i.be_id = cpi.mall_code) as provinceName,
            (select mall_name from contract_city_info cci where o2i.location = cci.mall_code) as cityName
        from
            order_2c_info o2i
                left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
                left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
                left join k3_product_material kpm on atom.id = kpm.atom_id
        where
            kpm.atom_id IS NULL and o2i.order_status = 7 and o2ai.atom_offering_class in ('O','D','P')
            and o2i.order_id in
            <foreach item="item" collection="orderList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>


    <select id="checkMaterialContract" resultType="com.chinamobile.iot.sc.pojo.dto.MultiAtomContractDTO">
        select
            b.orderId,
            b.atomId
        from
            (select
                o2ai.order_id orderId,
                o2ai.id atomOrderId,
                kpm.atom_id atomId,
                kpm.material_num kmaterialNum,
                kpm.contract_num kcontractNum
             from
                order_2c_info o2i
                left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
                left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
                left join k3_product_material kpm on atom.id = kpm.atom_id
--         right join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number
        -- 				 contract_material cm
                where
                    kpm.atom_id IS NULL and o2i.order_status = 7 and o2ai.atom_offering_class in ('O','D','P')
                    and o2i.order_id in
                <foreach item="item" collection="orderList" open="(" separator="," close=")">
                    #{item}
                </foreach>
                group by
                    kpm.atom_id, kpm.material_num, kpm.contract_num
            ) b
        group by b.atomId having count(*) > 1



    </select>


    <select id="getK3OrdersList" parameterType="com.chinamobile.iot.sc.pojo.param.K3QueryListParam"
            resultType="com.chinamobile.iot.sc.pojo.vo.K3OrgOrderVO">
        SELECT
--             o2i.create_time createTime,
--             o2i.update_time finishedTime,
            DATE_FORMAT(o2i.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
            DATE_FORMAT(o2ai.update_time, '%Y-%m-%d %H:%i:%s') as finishedTime,
            o2i.order_id orderId,
            o2ai.sku_offering_name skuName,
            sum(o2ai.sku_quantity) skuQuantity,
--             o2ai.atom_offering_name atomName,
--             o2i.spu_offering_class spuOfferingClass,
            case
                when o2i.spu_offering_class = 'A04' then 'DICT增值服务'
                when o2i.spu_offering_class = 'A08' then 'OneNET独立服务'
                when o2i.spu_offering_class = 'A09' then 'OnePark增值服务'
                else o2i.spu_offering_class
            end as spuOfferingClass,
            o2i.be_id orderProvince,
            o2i.location orderCity,
--             o2i.total_price orderPrice,
--             o2ai.atom_price*o2ai.atom_quantity*o2ai.sku_quantity orderPrice
            sum(o2ai.atom_price*o2ai.atom_quantity*o2ai.sku_quantity) orderPrice,
            case
                when o2i.sync_k3_id = "" then 0
                when o2i.sync_k3_id != "" then 1
                else 0
                end as orderSelected
        FROM
            order_2c_atom_info o2ai force index (idx_time_id_price_name_quantity)
        INNER JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id AND
            o2i.spu_offering_class IN ('A04', 'A08', 'A09') AND
            o2i.order_status = '7' AND
            o2i.special_after_market_handle = 0 AND
            (o2i.sync_k3_id IS NULL OR o2i.sync_k3_id = '')
        WHERE o2ai.atom_settle_price != 0
        <if test="param.spuOfferingClass !=null and param.spuOfferingClass !=''">
            and o2i.spu_offering_class = #{param.spuOfferingClass}
        </if>
        <!--<if test="param.spuOfferingClass == null or param.spuOfferingClass == ''">
            and o2i.spu_offering_class in ('A04','A08','A09')
        </if>-->
        <if test="param.createTimeFrom != null and param.createTimeFrom !=''">
            and o2ai.create_time <![CDATA[ >= ]]> #{param.createTimeFrom}
        </if>
        <if test="param.createTimeTo != null and param.createTimeTo !=''">
            and o2ai.create_time <![CDATA[ <= ]]> #{param.createTimeTo}
        </if>
        <if test="param.dealTimeFromStr != null and param.dealTimeFromStr !=''">
            and o2ai.update_time <![CDATA[ >= ]]> #{param.dealTimeFromStr}
        </if>
        <if test="param.dealTimeToStr != null and param.dealTimeToStr !=''">
            and o2ai.update_time <![CDATA[ <= ]]> #{param.dealTimeToStr}
        </if>
        <if test="param.provinceCode != null and param.provinceCode !=''">
            and o2i.be_id = #{param.provinceCode}
        </if>
        <if test="param.cityCode !=null and param.cityCode !=''">
            and o2i.location = #{param.cityCode}
        </if>
        GROUP BY o2ai.update_time, o2ai.order_id
        ORDER BY o2ai.update_time DESC, o2ai.order_id DESC

    </select>

    <select id="genOrderBillsById"
            resultType="com.chinamobile.iot.sc.pojo.entity.K3Orders">
        select
            o2i.order_id as orderId,
            DATE_FORMAT(o2i.create_time, '%Y-%m-%d %H:%i:%s') as orderTime,
            o2i.update_time as orderFinishTime,
            spu.offering_name as spuName,
            spu.offering_code as spuCode,
            case
                when ci.offering_class = 'A04' then 'DICT产品增值服务包'
                when ci.offering_class = 'A08' then 'OneNET独立服务'
                when ci.offering_class = 'A09' then 'OnePark增值服务'
                else ci.offering_class
                end as spuType,
            ci.offering_class as spuClass,
            sku.offering_name as skuName,
            sku.offering_code as skuCode,
            atom.offering_name as atomName,
            atom.offering_code as atomCode,
            o2ai.atom_settle_price as unitPrice,
            o2ai.atom_quantity*o2ai.sku_quantity as quatity,
            o2ai.atom_settle_price*o2ai.atom_quantity*o2ai.sku_quantity	as totalPrice,
--       o2ai.sku_price*o2ai.sku_quantity	as totalPrice,
            o2ai.deduct_price as deductPrice,
            o2i.contact_phone as receiverPhone,
            o2i.be_id as orderProvinceCode,
            o2i.location as orderCityCode,
            o2i.addr1 as orderProvince,
            o2i.addr2 as orderCity,
            o2i.create_oper_code as createOperCode,
            o2i.employee_num as employeeNum,
            cm.material_number as materialNum,
            cm.name as materialName,
            cm.material_unit as materialUnit,
            cm.owner_department as materialDept,
            ctr.number as contractNum,
            ctr.name as contractName,
            ctr.project as contractProject,
            ctr.sub_project as contractSubProject,
            ctr.create_dept_name as contractDept,
            ctr.vendor_code as customCode,
            ctr.province_mall_name as buyerProvince,
            ctr.province_mall_code as buyerProvinceMallCode,
            ctr.province_k3_code as buyerProvinceCode,
            ctr.city_mall_name as buyerCity,
            ctr.city_mall_code as buyerCityMallCode,
            ctr.city_k3_code as buyerCityCode,
            case
                when ctr.count_type = 0 then '按地市'
                when ctr.count_type = 1 then '按省份'
                when ctr.count_type = 2 then '按合同'
                else '无'
                end as contractSatisType,
            ctr.count_type as contractStatisEnum,
            ctr.currency as contractMoneyUnit,
            ctr.property as contractType,
            ctr.f_number as contractTax,
            ctr.settlement_mode as contractSettleMode,
            now() as createTime,
            ss.product_department_id productDepartmentId,
            d.short_name productDepartmentName,
            ctr.vendor_code customCode
        from
            order_2c_info o2i
                left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
                left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
                left join spu_offering_info spu on atom.spu_code = spu.offering_code
                left join sku_offering_info sku on atom.sku_code = sku.offering_code
                left join category_info ci on atom.spu_id=ci.spu_id
                right join k3_product_material kpm on atom.id = kpm.atom_id
                left join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number
                  and kpm.material_dept = cm.owner_department
                left join contract ctr on cm.contract_number = ctr.number
                left join atom_std_service ass on ass.atom_id = atom.id
                left join standard_service ss on ass.std_service_id = ss.id
                left join department d on ss.product_department_id = d.id
        where
          o2i.order_status = 7
        and ci.offering_class in ('A04','A08','A09')
        and ctr.active = 1
       -- and ctr.end_date > now()
        and ctr.contract_type = 1
        and o2i.order_id in
            <foreach item="item" collection="orderList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>


    <select id="getRelatedOrdersByStatisId" resultType="java.lang.String">
        select
            GROUP_CONCAT(DISTINCT order_id)
        from
            k3_orders
        where
            k3_num = #{param}
--         group by
--             order_id
    </select>

    <select id="getRelatedOrderDetail" resultType="com.chinamobile.iot.sc.pojo.dto.OrderExportDTO">
        select
        o2i.order_id as orderId,
        DATE_FORMAT(o2i.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
        --             o2i.update_time as finishTime,
        case
        when o2i.status in (3,4) then o2i.order_status_time
        else null
        end as finishTime,
        spu.offering_name as spuName,
        spu.offering_code as spuCode,
        case
        when ci.offering_class = 'A04' then 'DICT产品增值服务包'
        when ci.offering_class = 'A08' then 'OneNET独立服务'
        when ci.offering_class = 'A09' then 'OnePark增值服务'
        else ci.offering_class
        end as spuClass,
        sku.offering_name as skuName,
        sku.offering_code as skuCode,
        atom.offering_name as atomName,
        atom.offering_class atomOfferingClass,
        atom.offering_code as atomCode,
        o2ai.atom_settle_price as atomSettleUnitPrice,
        o2ai.atom_quantity*o2ai.sku_quantity as atomQuantity,
        o2ai.atom_settle_price*o2ai.atom_quantity*o2ai.sku_quantity as atomPrice,
        o2ai.deduct_price as deductPrice,
        atom.settle_price settlePrice,
        cm.material_number as materialNum,
        cm.name as materialName,
        kpm.material_count as materialCount,
        cm.settle_price as materialUnitPrice,
        cm.settle_price*kpm.material_count*o2ai.sku_quantity as materialPrice,
        cm.owner_department as materialDept,
        cp.partner_name as partnerName,
        cp.user_name as partnerContactName,
        o2i.contact_person_name as enRvcName,
        o2i.addr1 as enAddr1,
        o2i.addr2 as enAddr2,
        o2i.addr3 as enAddr3,
        o2i.addr4 as enAddr4,
        o2i.contact_phone as enRvcPhone,
        o2i.be_id as mallProvinceCode,
        o2i.location as mallCityCode,
        o2i.region_ID as mallDistrictCode,
        o2i.create_oper_code as operCode,
        o2i.employee_num as operEmployNum,
        ctr.number as contractNum,
        ctr.name as contractName,
        ctr.property as ctrProperty,
        ctr.create_dept_name as contractDept,
        ctr.province_mall_name as contractBuyerProvince,
        ctr.city_mall_name as contractBuyerCity,
        ctr.count_type as ctrStatisType
        from
        order_2c_info o2i
        left join order_2c_atom_info o2ai on o2i.order_id = o2ai.order_id
        left join atom_offering_info atom on o2ai.atom_offering_code = atom.offering_code and o2ai.spu_offering_code = atom.spu_code and o2ai.sku_offering_code = atom.sku_code
        left join spu_offering_info spu on atom.spu_code = spu.offering_code
        left join sku_offering_info sku on atom.sku_code = sku.offering_code
        left join category_info ci on atom.spu_id=ci.spu_id
        right join k3_product_material kpm on atom.id = kpm.atom_id
        left join contract_material cm on kpm.material_num = cm.material_number and kpm.contract_num = cm.contract_number
        left join contract ctr on cm.contract_number = ctr.number
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_2c_atom_info o2aii
        inner join order_cooperator_relation ocr on o2aii.id = ocr.atom_order_id and o2aii.order_id = ocr.order_id
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by o2aii.id
        ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        where
        o2i.order_status = 7
        and ci.offering_class in ('A04','A08','A09')
        and ctr.active = 1
        -- and ctr.end_date > now()
            and o2i.order_id in
            <foreach item="item" collection="param" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>


    <select id="listK3ProBillOrder" resultType="com.chinamobile.iot.sc.pojo.dto.K3ProBillOrderDTO">
        select
            ko.order_province provinceName,
            ko.order_city cityName,
            o2i.be_id as mallProvinceCode,
            o2i.location as mallCityCode,
            ss.name standardServiceName,
            ko.material_name materialName,
            aoi.atom_sale_price atomSalePrice,
            aoi.quantity*kpm.material_count saleCount,
            aoi.atom_sale_price*aoi.quantity*kpm.material_count salePrice,
            cm.settle_price*0.06 taxPrice,
            cm.settle_price settlePrice,
            cm.settle_price*aoi.quantity*kpm.material_count settleTotalPrice,
            ko.order_finish_time orderFinishTime,
            ko.order_id orderId,
            pmi.tax_exclusive_univalence taxExclusiveUnivalence,
            pmi.tax_inclusive_univalence taxInclusiveUnivalence
        from
          k3_orders ko
        left join atom_offering_info aoi on ko.atom_code = aoi.offering_code and ko.spu_code = aoi.spu_code and ko.sku_code = aoi.sku_code
        left join atom_std_service ass on aoi.id = ass.atom_id
        left join standard_service ss on ass.std_service_id = ss.id
        left join k3_product_material kpm on ko.material_num = kpm.material_num and ko.material_dept = kpm.material_dept and aoi.id = kpm.atom_id
        left join contract_material cm on kpm.contract_num = cm.contract_number and kpm.material_num = cm.material_number
        left join order_2c_info o2i on ko.order_id = o2i.order_id
        left join province_material_info pmi on pmi.internet_material_code = kpm.material_num
        where
        1=1
        <if test="k3NumList != null">
            and ko.k3_num in
            <foreach item="item" collection="k3NumList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by o2i.be_id desc,o2i.location desc
    </select>

    <select id="listK3DepartmentBillOrder" resultType="com.chinamobile.iot.sc.pojo.dto.K3DepartmentBillOrderDTO">
        select
            ss.name standardServiceName,
            ko.material_name materialName,
            aoi.atom_sale_price atomSalePrice,
            aoi.quantity*kpm.material_count saleCount,
            aoi.atom_sale_price*aoi.quantity*kpm.material_count salePrice,
            cm.settle_price*0.06 taxPrice,
            cm.settle_price settlePrice,
            cm.settle_price*aoi.quantity*kpm.material_count settleTotalPrice,
            ko.order_finish_time orderFinishTime,
            ko.order_id orderId,
            pmi.tax_exclusive_univalence taxExclusiveUnivalence,
            pmi.tax_inclusive_univalence taxInclusiveUnivalence
        from
            k3_orders ko
        left join atom_offering_info aoi on ko.atom_code = aoi.offering_code and ko.spu_code = aoi.spu_code and ko.sku_code = aoi.sku_code
        left join atom_std_service ass on aoi.id = ass.atom_id
        left join standard_service ss on ass.std_service_id = ss.id
        left join k3_product_material kpm on ko.material_num = kpm.material_num and ko.material_dept = kpm.material_dept and aoi.id = kpm.atom_id
        left join contract_material cm on kpm.contract_num = cm.contract_number and kpm.material_num = cm.material_number
        left join province_material_info pmi on pmi.internet_material_code = kpm.material_num
        where
            ko.k3_sync_department_id = #{dataNum}
    </select>

</mapper>
