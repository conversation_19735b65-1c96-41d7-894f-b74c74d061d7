<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ContractMaterialMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ContractMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="material_number" jdbcType="VARCHAR" property="materialNumber" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="owner_department" jdbcType="VARCHAR" property="ownerDepartment" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="material_unit" jdbcType="VARCHAR" property="materialUnit" />
    <result column="plan_strategy" jdbcType="VARCHAR" property="planStrategy" />
    <result column="settle_price" jdbcType="BIGINT" property="settlePrice" />
    <result column="service_pack_id" jdbcType="VARCHAR" property="servicePackId" />
    <result column="service_quota_used" jdbcType="BIGINT" property="serviceQuotaUsed" />
    <result column="service_quota_remain" jdbcType="BIGINT" property="serviceQuotaRemain" />
    <result column="service_quota_reverse" jdbcType="BIGINT" property="serviceQuotaReverse" />
    <result column="material_quota_used" jdbcType="BIGINT" property="materialQuotaUsed" />
    <result column="material_quota_remain" jdbcType="BIGINT" property="materialQuotaRemain" />
    <result column="material_quota_reverse" jdbcType="BIGINT" property="materialQuotaReverse" />
    <result column="material_type" jdbcType="INTEGER" property="materialType" />
    <result column="label_tax" jdbcType="VARCHAR" property="labelTax" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, contract_number, material_number, name, owner_department, model, product_id, 
    material_unit, plan_strategy, settle_price, service_pack_id, service_quota_used, 
    service_quota_remain, service_quota_reverse, material_quota_used, material_quota_remain, 
    material_quota_reverse, material_type, label_tax
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterialExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from contract_material
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from contract_material
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterialExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from contract_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_material (id, contract_number, material_number, 
      name, owner_department, model, 
      product_id, material_unit, plan_strategy, 
      settle_price, service_pack_id, service_quota_used, 
      service_quota_remain, service_quota_reverse, 
      material_quota_used, material_quota_remain, material_quota_reverse, 
      material_type, label_tax)
    values (#{id,jdbcType=VARCHAR}, #{contractNumber,jdbcType=VARCHAR}, #{materialNumber,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{ownerDepartment,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{productId,jdbcType=VARCHAR}, #{materialUnit,jdbcType=VARCHAR}, #{planStrategy,jdbcType=VARCHAR}, 
      #{settlePrice,jdbcType=BIGINT}, #{servicePackId,jdbcType=VARCHAR}, #{serviceQuotaUsed,jdbcType=BIGINT}, 
      #{serviceQuotaRemain,jdbcType=BIGINT}, #{serviceQuotaReverse,jdbcType=BIGINT}, 
      #{materialQuotaUsed,jdbcType=BIGINT}, #{materialQuotaRemain,jdbcType=BIGINT}, #{materialQuotaReverse,jdbcType=BIGINT}, 
      #{materialType,jdbcType=INTEGER}, #{labelTax,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="materialNumber != null">
        material_number,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="ownerDepartment != null">
        owner_department,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="materialUnit != null">
        material_unit,
      </if>
      <if test="planStrategy != null">
        plan_strategy,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="servicePackId != null">
        service_pack_id,
      </if>
      <if test="serviceQuotaUsed != null">
        service_quota_used,
      </if>
      <if test="serviceQuotaRemain != null">
        service_quota_remain,
      </if>
      <if test="serviceQuotaReverse != null">
        service_quota_reverse,
      </if>
      <if test="materialQuotaUsed != null">
        material_quota_used,
      </if>
      <if test="materialQuotaRemain != null">
        material_quota_remain,
      </if>
      <if test="materialQuotaReverse != null">
        material_quota_reverse,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="labelTax != null">
        label_tax,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="materialNumber != null">
        #{materialNumber,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="materialUnit != null">
        #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="planStrategy != null">
        #{planStrategy,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackId != null">
        #{servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="serviceQuotaUsed != null">
        #{serviceQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="serviceQuotaRemain != null">
        #{serviceQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="serviceQuotaReverse != null">
        #{serviceQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaUsed != null">
        #{materialQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaRemain != null">
        #{materialQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaReverse != null">
        #{materialQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=INTEGER},
      </if>
      <if test="labelTax != null">
        #{labelTax,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterialExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from contract_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNumber != null">
        contract_number = #{record.contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.materialNumber != null">
        material_number = #{record.materialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerDepartment != null">
        owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.materialUnit != null">
        material_unit = #{record.materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.planStrategy != null">
        plan_strategy = #{record.planStrategy,jdbcType=VARCHAR},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.servicePackId != null">
        service_pack_id = #{record.servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceQuotaUsed != null">
        service_quota_used = #{record.serviceQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="record.serviceQuotaRemain != null">
        service_quota_remain = #{record.serviceQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="record.serviceQuotaReverse != null">
        service_quota_reverse = #{record.serviceQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="record.materialQuotaUsed != null">
        material_quota_used = #{record.materialQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="record.materialQuotaRemain != null">
        material_quota_remain = #{record.materialQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="record.materialQuotaReverse != null">
        material_quota_reverse = #{record.materialQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=INTEGER},
      </if>
      <if test="record.labelTax != null">
        label_tax = #{record.labelTax,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_material
    set id = #{record.id,jdbcType=VARCHAR},
      contract_number = #{record.contractNumber,jdbcType=VARCHAR},
      material_number = #{record.materialNumber,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=VARCHAR},
      material_unit = #{record.materialUnit,jdbcType=VARCHAR},
      plan_strategy = #{record.planStrategy,jdbcType=VARCHAR},
      settle_price = #{record.settlePrice,jdbcType=BIGINT},
      service_pack_id = #{record.servicePackId,jdbcType=VARCHAR},
      service_quota_used = #{record.serviceQuotaUsed,jdbcType=BIGINT},
      service_quota_remain = #{record.serviceQuotaRemain,jdbcType=BIGINT},
      service_quota_reverse = #{record.serviceQuotaReverse,jdbcType=BIGINT},
      material_quota_used = #{record.materialQuotaUsed,jdbcType=BIGINT},
      material_quota_remain = #{record.materialQuotaRemain,jdbcType=BIGINT},
      material_quota_reverse = #{record.materialQuotaReverse,jdbcType=BIGINT},
      material_type = #{record.materialType,jdbcType=INTEGER},
      label_tax = #{record.labelTax,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_material
    <set>
      <if test="contractNumber != null">
        contract_number = #{contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="materialNumber != null">
        material_number = #{materialNumber,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="materialUnit != null">
        material_unit = #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="planStrategy != null">
        plan_strategy = #{planStrategy,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackId != null">
        service_pack_id = #{servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="serviceQuotaUsed != null">
        service_quota_used = #{serviceQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="serviceQuotaRemain != null">
        service_quota_remain = #{serviceQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="serviceQuotaReverse != null">
        service_quota_reverse = #{serviceQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaUsed != null">
        material_quota_used = #{materialQuotaUsed,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaRemain != null">
        material_quota_remain = #{materialQuotaRemain,jdbcType=BIGINT},
      </if>
      <if test="materialQuotaReverse != null">
        material_quota_reverse = #{materialQuotaReverse,jdbcType=BIGINT},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=INTEGER},
      </if>
      <if test="labelTax != null">
        label_tax = #{labelTax,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ContractMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_material
    set contract_number = #{contractNumber,jdbcType=VARCHAR},
      material_number = #{materialNumber,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      material_unit = #{materialUnit,jdbcType=VARCHAR},
      plan_strategy = #{planStrategy,jdbcType=VARCHAR},
      settle_price = #{settlePrice,jdbcType=BIGINT},
      service_pack_id = #{servicePackId,jdbcType=VARCHAR},
      service_quota_used = #{serviceQuotaUsed,jdbcType=BIGINT},
      service_quota_remain = #{serviceQuotaRemain,jdbcType=BIGINT},
      service_quota_reverse = #{serviceQuotaReverse,jdbcType=BIGINT},
      material_quota_used = #{materialQuotaUsed,jdbcType=BIGINT},
      material_quota_remain = #{materialQuotaRemain,jdbcType=BIGINT},
      material_quota_reverse = #{materialQuotaReverse,jdbcType=BIGINT},
      material_type = #{materialType,jdbcType=INTEGER},
      label_tax = #{labelTax,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_material
    (id, contract_number, material_number, name, owner_department, model, product_id, 
      material_unit, plan_strategy, settle_price, service_pack_id, service_quota_used, 
      service_quota_remain, service_quota_reverse, material_quota_used, material_quota_remain, 
      material_quota_reverse, material_type, label_tax)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.contractNumber,jdbcType=VARCHAR}, #{item.materialNumber,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.ownerDepartment,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, 
        #{item.productId,jdbcType=VARCHAR}, #{item.materialUnit,jdbcType=VARCHAR}, #{item.planStrategy,jdbcType=VARCHAR}, 
        #{item.settlePrice,jdbcType=BIGINT}, #{item.servicePackId,jdbcType=VARCHAR}, #{item.serviceQuotaUsed,jdbcType=BIGINT}, 
        #{item.serviceQuotaRemain,jdbcType=BIGINT}, #{item.serviceQuotaReverse,jdbcType=BIGINT}, 
        #{item.materialQuotaUsed,jdbcType=BIGINT}, #{item.materialQuotaRemain,jdbcType=BIGINT}, 
        #{item.materialQuotaReverse,jdbcType=BIGINT}, #{item.materialType,jdbcType=INTEGER}, 
        #{item.labelTax,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 24 10:04:04 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_material (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'contract_number'.toString() == column.value">
          #{item.contractNumber,jdbcType=VARCHAR}
        </if>
        <if test="'material_number'.toString() == column.value">
          #{item.materialNumber,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'owner_department'.toString() == column.value">
          #{item.ownerDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=VARCHAR}
        </if>
        <if test="'material_unit'.toString() == column.value">
          #{item.materialUnit,jdbcType=VARCHAR}
        </if>
        <if test="'plan_strategy'.toString() == column.value">
          #{item.planStrategy,jdbcType=VARCHAR}
        </if>
        <if test="'settle_price'.toString() == column.value">
          #{item.settlePrice,jdbcType=BIGINT}
        </if>
        <if test="'service_pack_id'.toString() == column.value">
          #{item.servicePackId,jdbcType=VARCHAR}
        </if>
        <if test="'service_quota_used'.toString() == column.value">
          #{item.serviceQuotaUsed,jdbcType=BIGINT}
        </if>
        <if test="'service_quota_remain'.toString() == column.value">
          #{item.serviceQuotaRemain,jdbcType=BIGINT}
        </if>
        <if test="'service_quota_reverse'.toString() == column.value">
          #{item.serviceQuotaReverse,jdbcType=BIGINT}
        </if>
        <if test="'material_quota_used'.toString() == column.value">
          #{item.materialQuotaUsed,jdbcType=BIGINT}
        </if>
        <if test="'material_quota_remain'.toString() == column.value">
          #{item.materialQuotaRemain,jdbcType=BIGINT}
        </if>
        <if test="'material_quota_reverse'.toString() == column.value">
          #{item.materialQuotaReverse,jdbcType=BIGINT}
        </if>
        <if test="'material_type'.toString() == column.value">
          #{item.materialType,jdbcType=INTEGER}
        </if>
        <if test="'label_tax'.toString() == column.value">
          #{item.labelTax,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>