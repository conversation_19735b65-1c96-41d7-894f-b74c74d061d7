<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.StandardServiceMapperExt">
    <resultMap type="com.chinamobile.iot.sc.pojo.vo.StandardServiceVO"
               id="standardServiceResultMap">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="real_product_name" property="realProductName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="d_id" property="department.id" jdbcType="INTEGER"/>
        <result column="created_dt" property="department.createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_dt" property="department.updatedDt" jdbcType="TIMESTAMP"/>
        <result column="status" property="department.status" jdbcType="INTEGER"/>
        <result column="org_code" property="department.orgCode" jdbcType="VARCHAR"/>
        <result column="full_name" property="department.fullName" jdbcType="VARCHAR"/>
        <result column="short_name" property="department.shortName" jdbcType="VARCHAR"/>

        <result column="p_id" property="productProperty.id" jdbcType="VARCHAR"/>
        <result column="type" property="productProperty.type" jdbcType="INTEGER"/>
        <result column="p_name" property="productProperty.name" jdbcType="VARCHAR"/>

        <result column="remark1" property="remark1" jdbcType="VARCHAR"/>
        <result column="remark2" property="remark2" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="queryStandardServices"
            parameterType="com.chinamobile.iot.sc.pojo.param.QueryStandardServiceParam"
            resultMap="standardServiceResultMap">
        select
            s.id,
            s.name,
            s.real_product_name,
            s.create_time,
            s.update_time,
            d.id d_id,
            d.created_dt,
            d.updated_dt,
            d.status,
            d.org_code,
            d.full_name,
            d.short_name,
            p.id p_id,
            p.type,
            p.name as p_name,
            s.remark1,
            s.remark2
        from standard_service s
        inner join department d on s.product_department_id = d.id
        inner join product_property p on s.product_property_id = p.id
        <where>
            <if test="id != null and id != ''">
                s.id like '%${id}%'
            </if>
            <if test="name != null and name != ''">
                and s.name like '%${name}%'
            </if>
            <if test="productDepartmentId != null and productDepartmentId != ''">
                and s.product_department_id = #{productDepartmentId}
            </if>
            <if test="realProductName != null and realProductName != ''">
                and s.real_product_name like '%${realProductName}%'
            </if>
            <if test="productPropertyId != null and productPropertyId != ''">
                and s.product_property_id = #{productPropertyId}
            </if>
        </where>
        order by s.create_time desc
    </select>

    <insert id="batchInsertStandardService" parameterType="com.chinamobile.iot.sc.pojo.entity.StandardServiceDTO">
        insert into standard_service
        (`id`, `name`, `product_department_id`, `real_product_name`, `product_property_id`, `create_time`, `update_time`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.productDepartmentId,jdbcType=INTEGER},
            #{item.realProductName,jdbcType=VARCHAR}, #{item.productPropertyId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>