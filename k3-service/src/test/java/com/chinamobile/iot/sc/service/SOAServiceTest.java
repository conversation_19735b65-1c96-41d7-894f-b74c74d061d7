package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.pojo.dto.SupplyChainImportPoDraftDTO;
import com.chinamobile.iot.sc.service.soa.contract.OSB_CMS_CMS_HQ_PageInquiryContractBaseConfigSrvClient;
import com.chinamobile.iot.sc.service.soa.orderImport.*;
import com.chinamobile.iot.sc.service.soa.orderReturn.OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvClient;
import com.chinamobile.iot.sc.service.soa.orderStatusInfo.OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrvClient;
import com.chinamobile.iot.sc.service.soa.orderUpdate.OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvClient;
import com.chinamobile.iot.sc.service.soa.scmOrderQuery.OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @AUTHOR: HWF
 * @DATE: 2025/1/14
 */
@SpringBootTest
@Slf4j
public class SOAServiceTest {

    /**
     * OSB_SSCM_ZX_HQ_00002 导入订单草稿信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrvClient importOrderDraftClient;

    /**
     * OSB_SSCM_ZX_HQ_00003 导入采购订单草稿退回信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvClient importRetReqInfoClient;

    /**
     * OSB_SSCM_ZX_HQ_00004 导入采购订单修改或取消信息服务
     */
    @Resource
    private OSB_SSCM_ZX_HQ_ImportOrderUpdateOrCancelSrvClient updateOrCancelClient;

    /**
     * OSB_SSCM_ZX_HQ_00005 查询订单在哪个台信息服务（分页）
     */
     @Resource
     private OSB_SSCM_ZX_HQ_PageInquiryOrderStateInfoSrvClient inquiryOrderStateClient;


    /**
     * OSB_SSCM_ZX_HQ_00012 查询SCM采购订单信息服务（分页）
     */
    /*@Resource
    private OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrvClient scmOrderInquiryClient;*/

    /**
     * MFT_SSCM_ZX_HQ_00004 订单草稿附件传输服务（MFT）
     */
//    @Resource
//    private MFT_SSCM_ZX_HQ_MftXfePoOrderDraftFileSrvClient orderDraftFileSrvClient;

    /**
     * MFT_SSCM_ZX_HQ_00003 SCM采购订单信息传输服务（MFT）
     */
//    @Resource
//    private MFT_SSCM_ZX_HQ_MftXfePOInfoFileSrvClient scmOrderMftClient;

    @Resource
    private OSB_CMS_CMS_HQ_PageInquiryContractBaseConfigSrvClient contractClient;


    /**
     * 测试本地办公网合同接口
     */
    @Test
    public void testContractQuiry() throws Exception {
        contractClient.queryContract("CMIOT_9876543F2F1");
    }


    /**
     * 导入订单草稿信息服务
     */
    @Test
    public void testOrderDraftImport() throws Exception {
        InputParameters request = new InputParameters();
        MSGHEADER msgHeader = importOrderDraftClient.buildMSGHEADER();
        request.setMSGHEADER(msgHeader);


        INPUTCOLLECTION inputcollection = new INPUTCOLLECTION();
        INPUTCOLLECTIONITEM inputcollectionitem = new INPUTCOLLECTIONITEM();
//        inputcollectionitem.setPRIKEY("2025011000001");
        inputcollectionitem.setPRIKEY(BaseServiceUtils.getId());
        inputcollectionitem.setPROVINCECODE("CMIOT");
        inputcollectionitem.setDESCRIPTION("T100采购订单");
        inputcollectionitem.setSOURCEFROM("CMIOT-MS2");
        inputcollectionitem.setSOURCEFROMNO("XSKJ-1503-2501100001");
        inputcollectionitem.setMISBODY(new BigDecimal(7816));
        inputcollectionitem.setEXPTYPE("Opex");
        inputcollectionitem.setREIMBURSEMENTMODE("1");
        inputcollectionitem.setDEPTCODE("00760061000800130000");
        inputcollectionitem.setDEPTNAME("芯片运营组");
        inputcollectionitem.setCREATEDID("<EMAIL>");
        inputcollectionitem.setCREATEDNAME("刘*延");
        inputcollectionitem.setMTLTYPECODE("C");
        inputcollectionitem.setCONTRACTCODE("CMIOT-*********");
        inputcollectionitem.setVENDORCODE("MDM_100002377");
        inputcollectionitem.setREQTYPE("1");
        inputcollectionitem.setARRIVALTIMEMODEL("DATE");
        inputcollectionitem.setCURRENCYCODE("CNY");
        inputcollectionitem.setAMOUNT(new BigDecimal(55000));
        inputcollectionitem.setTAXSUM(new BigDecimal(3300));
        inputcollectionitem.setAMOUNTTAX(new BigDecimal(58300));
        inputcollectionitem.setISFULLPRESENT("N");

        Integer lineNum = 1;
        ORDERLINE orderline = new ORDERLINE();
//        for(SupplyChainIotLineInfoQuery item : queryParam.getOrderLine()){
            ORDERLINEITEM orderlineitem = new ORDERLINEITEM();
            orderlineitem.setPRIKEY(BaseServiceUtils.getId());
            orderlineitem.setLINENUM(new BigDecimal(1));
            orderlineitem.setMATERIALCODE("10468474");
            orderlineitem.setMATERIALNAME("CM6620芯片-before FT");
            orderlineitem.setUNIT("个");
            orderlineitem.setQUANTITY(new BigDecimal(110000));
            orderlineitem.setUNITPRICE(new BigDecimal(0.5));
            orderlineitem.setTAXRATE(new BigDecimal(0.06));
            orderlineitem.setTAXCODE("VAT6");
            orderlineitem.setLINEAMT(new BigDecimal(55000));
            orderlineitem.setLINETAX(new BigDecimal("3300"));
            orderlineitem.setLINEAMTTAX(new BigDecimal(58300));
            orderlineitem.setORGANIZATIONCODE("D71");
            orderlineitem.setITEMTYPE("EXPENSE");
            orderlineitem.setRCVUSERNUM("E1000024624");
            orderlineitem.setRCVUSER("金*");
            orderlineitem.setRCVCONTACTPHONE("13681479512");
            orderlineitem.setRCVSITEADDRESS("北京");

            orderline.getORDERLINEITEM().add(orderlineitem);
//            lineNum++;
//        }
        inputcollectionitem.setORDERLINE(orderline);

        inputcollection.getINPUTCOLLECTIONITEM().add(inputcollectionitem);

        log.info("importPoDraft request = {}",request);
        request.setINPUTCOLLECTION(inputcollection);
        // 设置服务客户端对象
        OSBSSCMZXHQImportPoOrderDraftInfoSrv port = importOrderDraftClient.buildServiceClient();
        // 调用服务接口
        OutputParameters response = port.process(request);

        log.info("importPoDraft response = {}",response);
        if (!"TRUE".equals(response.getESBFLAG())) {
            throw new Exception(response.getESBRETURNMESSAGE());
        }

        if (!"TRUE".equals(response.getBIZSERVICEFLAG())) {
            throw new Exception(response.getBIZRETURNMESSAGE());
        }
        // 输出服务调用结果
        System.out.println(response.getBIZSERVICEFLAG());
        System.out.println(response.getBIZRETURNMESSAGE());
        SupplyChainImportPoDraftDTO supplyChainImportPoDraftDTO = new SupplyChainImportPoDraftDTO();

        if (response.getRESPONSECOLLECTION() != null
                && CollectionUtils.isNotEmpty(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM())) {
            supplyChainImportPoDraftDTO.setScmPoNum(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getSCMPONUM());
            if(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM() != null
                    &&CollectionUtils.isNotEmpty(response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM().getLINENUMITEM())){
                List<String> scmlinenumList = response.getRESPONSECOLLECTION().getRESPONSECOLLECTIONITEM().get(0).getLINENUM().getLINENUMITEM().stream().map(item->item.getSCMLINENUM()).collect(Collectors.toList());
                supplyChainImportPoDraftDTO.setScmLineNum(String.join(",", scmlinenumList));
            }
        }


    }
}
