package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.dto.DepartmentDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/14 08:42
 * @description 标准服务Service测试类
 */
@SpringBootTest
public class DepartmentServiceTest {

    @Autowired
    private DepartmentService departmentService;

    @Test
    public void testQueryAllDepartments() {
        List<DepartmentDTO> departmentDTOS = departmentService.queryAllDepartments();
    }

}
