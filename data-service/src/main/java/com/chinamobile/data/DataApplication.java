package com.chinamobile.data;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Author: liuxiang
 */
@EnableDiscoveryClient
@EnableScheduling
@EnableApolloConfig
@EnableFeignClients(basePackages = "com.chinamobile")
//这里如果不写scanBasePackages，默认扫描的路径就是当前Application
// 所在的包路径（com.chinamobile.data）;
//所有非此路径的包都无法扫描到，所以引入模块的包名前缀
// 如果不是com.chinamobile.data，那就需要单独注明双方共同的包名前缀
@SpringBootApplication(scanBasePackages = {"com.chinamobile"})
@MapperScan(basePackages = "com.chinamobile.data.dao")
public class DataApplication {
    public static void main(String[] args) {
        SpringApplication.run(DataApplication.class,args);
    }
}
