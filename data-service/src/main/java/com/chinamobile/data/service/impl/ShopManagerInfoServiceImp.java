package com.chinamobile.data.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.data.dao.ext.ShopManagerInfoMapperExt;
import com.chinamobile.data.pojo.mapper.ShopManagerDO;
import com.chinamobile.data.pojo.param.PageShopManagerParam;
import com.chinamobile.data.service.ShopManagerInfoService;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OperationKanbanOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/24
 * @description 商城客户经理service实现类
 */
@Service
@Slf4j
public class ShopManagerInfoServiceImp implements ShopManagerInfoService {

    @Resource
    private ShopManagerInfoMapperExt shopManagerInfoMapperExt;

    @Resource
    private LogService logService;

    @Override
    public BaseAnswer<PageData<ShopManagerDO>> pageShopManager(PageShopManagerParam pageShopManagerParam) {
        BaseAnswer<PageData<ShopManagerDO>> baseAnswer = new BaseAnswer<>();
        try {
            DateUtils.validDate(pageShopManagerParam.getBeginRegisterDate(), pageShopManagerParam.getEndRegisterDate());
            DateUtils.validDate(pageShopManagerParam.getBeginRegisterLoginDate(), pageShopManagerParam.getEndRegisterLoginDate());
        } catch (Exception e) {
            throw new BusinessException(BaseErrorConstant.DATE_PARSE_ERROR);
        }
        Integer pageNum = pageShopManagerParam.getPageNum();
        Integer pageSize = pageShopManagerParam.getPageSize();
        Page<ShopManagerDO> page = new Page<>(pageNum, pageSize);
        List<ShopManagerDO> shopManagerDOList = shopManagerInfoMapperExt.listShopManager(page, pageShopManagerParam);
        PageData<ShopManagerDO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(shopManagerDOList);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    @Override
    public void exportShopManager(PageShopManagerParam pageShopManagerParam) throws IOException {
        String content = "【数据导出】";
        try {
            DateUtils.validDate(pageShopManagerParam.getBeginRegisterDate(), pageShopManagerParam.getEndRegisterDate());
            DateUtils.validDate(pageShopManagerParam.getBeginRegisterLoginDate(), pageShopManagerParam.getEndRegisterLoginDate());
        } catch (Exception e) {
            logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                    OperationKanbanOperateEnum.MALL_USER_INFO.code,
                    content, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.DATE_PARSE_ERROR.getMessage());
            throw new BusinessException(BaseErrorConstant.DATE_PARSE_ERROR);
        }

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<ShopManagerDO> shopManagerDOList = shopManagerInfoMapperExt.listShopManager(pageShopManagerParam);

        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("客户经理");
        exportParams.setCreateHeadRows(true);
        exportParams.setType(ExcelType.XSSF);

        //记录日志

        logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                OperationKanbanOperateEnum.MALL_USER_INFO.code,
                content, LogResultEnum.LOG_SUCESS.code,null);
        ExcelUtils.exportExcel(shopManagerDOList, ShopManagerDO.class, "客户经理", exportParams, response);
    }
}
