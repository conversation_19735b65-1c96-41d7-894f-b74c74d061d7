package com.chinamobile.data.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.chinamobile.data.config.AreaDataConfig;
import com.chinamobile.data.config.AreaTypeConstant;
import com.chinamobile.data.config.CommonConstant;
import com.chinamobile.data.config.IotConfig;
import com.chinamobile.data.dao.Order2cInfoMapper;
import com.chinamobile.data.dao.OrderAtomStatisticsProvinceMapper;
import com.chinamobile.data.dao.ProvinceMapper;
import com.chinamobile.data.dao.SpuOfferingInfoMapper;
import com.chinamobile.data.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.data.dao.ext.Order2cInfoMapperExt;
import com.chinamobile.data.dao.ext.OrderAtomStatisticsProvinceMapperExt;
import com.chinamobile.data.dao.ext.OrderAtomStatisticsSpuMapperExt;
import com.chinamobile.data.pojo.mapper.*;
import com.chinamobile.data.util.DateUtils;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.data.pojo.dto.DateDTO;
import com.chinamobile.data.config.ProvinceConfig;
import com.chinamobile.data.dao.*;
import com.chinamobile.data.dao.ext.*;
import com.chinamobile.data.pojo.entity.*;
import com.chinamobile.data.pojo.param.TimeParam;
import com.chinamobile.data.pojo.vo.*;
import com.chinamobile.data.enums.StatisticsTimeTypeEnum;
import com.chinamobile.data.service.DataQueryService;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.data.config.CommonConstant.*;

/**
 * created by liuxiang on 2022/4/13 10:25
 */
@Service
@Slf4j
public class DataQueryServiceImpl implements DataQueryService {

    @Resource
    OrderAtomStatisticsProvinceMapper orderAtomStatisticsProvinceMapper;

    @Resource
    OrderAtomStatisticsProvinceMapperExt orderAtomStatisticsProvinceMapperExt;

    @Resource
    Order2cAtomInfoMapperExt order2CAtomInfoMapperExt;

    @Resource
    SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    Order2cInfoMapper order2cInfoMapper;

    @Resource
    OrderAtomStatisticsSpuMapperExt orderAtomStatisticsSpuMapperExt;

    @Autowired
    private IotConfig iotConfig;

    @Resource
    AccountsImportMapperExt accountsImportMapperExt;

    @Resource
    private AccountsImportMapper accountsImportMapper;

    @Resource
    private ShopManagerInfoMapper shopManagerInfoMapper;

    @Resource
    ProvinceMapper provinceMapper;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Resource
    private Order2cInfoMapperExt order2cInfoMapperExt;

    @Resource
    private ProvinceConfig provinceConfig;

    @Autowired
    private AreaDataConfig areaDataConfig;

    @Resource
    private RedisTemplate redisTemplate;


    @Override
    public BaseAnswer<OrderStatisticsScreenVO> getTotalStatistics(String areaCode) {
        Date now = new Date();
        //历史数据表统计不准确，数据量目前较小，可以直接查询。如果数据量很大，再优化数据统计表插入和更新逻辑
        /*//查询今天之前的历史数据
        OrderStatisticsScreenDO dataObject = orderAtomStatisticsProvinceMapperExt.getSumData(null,null);
        //查询今天的数据,转换成数据库一致的时间格式
        String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
        OrderStatisticsScreenDO orderSumDataBetween = order2CAtomInfoMapperExt.getOrderSumData(dayBeginTime, null);*/
        OrderStatisticsScreenDO dataObject = order2CAtomInfoMapperExt.getOrderSumData(null, null,areaCode);

        //查询上架产品总数
        SpuOfferingInfoExample example = new SpuOfferingInfoExample().createCriteria().andDeleteTimeIsNull().andOfferingStatusEqualTo("1").example();
        Long spuCount = spuOfferingInfoMapper.countByExample(example);

        //统计表及今日数据拼装
        /*dataObject.setAtomOrderAmount(dataObject.getAtomOrderAmount() + orderSumDataBetween.getAtomOrderAmount());
        dataObject.setAtomOrderCount(dataObject.getAtomOrderCount() + orderSumDataBetween.getAtomOrderCount());
        dataObject.setAtomOrderQuantity(dataObject.getAtomOrderQuantity() + orderSumDataBetween.getAtomOrderQuantity());*/
        OrderStatisticsScreenVO vo = new OrderStatisticsScreenVO();
        BeanUtils.copyProperties(dataObject, vo);
        vo.setSpuCount(spuCount.intValue());
        return BaseAnswer.success(vo);
    }

    @Override
    public BaseAnswer<OrderStatisticsTodayVO> getTodayStatistics(String areaCode) {
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL)) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        Date now = new Date();
        //查询今天的数据,转换成数据库一致的时间格式
        String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
        //String dayBeginTime = "20220722000000";
        OrderStatisticsScreenDO orderSumDataBetween = order2CAtomInfoMapperExt.getOrderSumData(dayBeginTime, null,areaCode);

        OrderStatisticsTodayVO vo = new OrderStatisticsTodayVO();
        vo.setOrderAmount(orderSumDataBetween.getAtomOrderAmount() != null ? orderSumDataBetween.getAtomOrderAmount() : 0);
        vo.setOrderCount(orderSumDataBetween.getAtomOrderCount() != null ? orderSumDataBetween.getAtomOrderCount() : 0);
        vo.setAtomOrderQuantity(orderSumDataBetween.getAtomOrderQuantity() != null ? orderSumDataBetween.getAtomOrderQuantity() : 0);

        //获取今日创建订单
       /* Order2cInfoExample.Criteria criteria = new Order2cInfoExample().createCriteria().andStatusNotEqualTo(4);
        criteria.andCreateTimeGreaterThanOrEqualTo(dayBeginTime);
        if (StringUtils.isNotEmpty(areaCode)){
            criteria.andBeIdEqualTo(areaCode);
        }
        Order2cInfoExample order2cInfoExample = criteria.example();
        order2cInfoExample.setOrderByClause("create_time desc");
        order2cInfoExample.setOffset(0);
        order2cInfoExample.setLimit(5);
        List<Order2cInfo> order2cInfos = order2cInfoMapper.selectByExample(criteria.example());*/
        List<Order2cInfoTodayDataDO> orderTodayMessage = order2cInfoMapperExt.getOrderTodayMessage(dayBeginTime, areaCode);
        ArrayList<OrderStatisticsTodayOrderVO> orders = new ArrayList<>();
        for (Order2cInfoTodayDataDO order2cInfoTodayDataDO : orderTodayMessage) {
            OrderStatisticsTodayOrderVO order = new OrderStatisticsTodayOrderVO();
            try {
                String totalPrice = decryptIOTMessage(order2cInfoTodayDataDO.getTotalPrice(), encodeKey);
                order.setAmount(Long.valueOf(totalPrice));
            } catch (Exception exception) {
                order.setAmount(0L);
            }
            String areaName;
            if (StringUtils.isNotEmpty(areaCode)){
                areaName =(String) areaDataConfig.getLocationCodeNameMap().get(order2cInfoTodayDataDO.getLocation());
            }else {
                areaName =(String) areaDataConfig.getProvinceCodeNameMap().get(order2cInfoTodayDataDO.getBeId());
            }

            order.setProvince(areaName);
            if (StringUtils.isEmpty(order2cInfoTodayDataDO.getCreateOperCode())) {
                order.setOperCode("***");
            } else {
               /* AccountsImportExample accountsImportExample = new AccountsImportExample();
                accountsImportExample.createCriteria().andCustCodeEqualTo(order2cInfoTodayDataDO.getCreateOperCode()).andTypeEqualTo(0);
                List<AccountsImport> accountsImports = accountsImportMapper.selectByExample(accountsImportExample);*/
                ShopManagerInfoExample shopManagerInfoExample = new ShopManagerInfoExample();
                shopManagerInfoExample.createCriteria().andCreateOperCodeEqualTo(order2cInfoTodayDataDO.getCreateOperCode());
                List<ShopManagerInfo> shopManagerInfos = shopManagerInfoMapper.selectByExample(shopManagerInfoExample);

                if (CollectionUtil.isNotEmpty(shopManagerInfos)){
                    if (StringUtils.isNotEmpty(shopManagerInfos.get(0).getCustomerManagerName())){
                        order.setOperCode(custNameDesensitization(shopManagerInfos.get(0).getCustomerManagerName()));
                    }
                }else {
                    order.setOperCode("***");
                }
               // order.setOperCode(encryptOperCode(order2cInfo.getCreateOperCode()));
            }
            try {
                Date createTime = DateTimeUtil.getFormatDate(order2cInfoTodayDataDO.getCreateTime(), DateTimeUtil.DB_TIME_STR);
                order.setCreateTime(DateTimeUtil.formatDate(createTime, "HH:mm:ss"));
            } catch (Exception exception) {
                log.info("date {} convert error {}", order.getCreateTime(), exception.getMessage());
            }
            orders.add(order);
        }
        vo.setOrders(orders);
        return BaseAnswer.success(vo);
    }

    public static String encryptOperCode(String value) {
        if (value == null || value.length() <= 3) {
            return value;
        }
        char[] arr = value.toCharArray();
        Arrays.fill(arr, arr.length - 3, arr.length, 'x');
        return new String(arr);
    }

    @Override
    public BaseAnswer<OrderStatisticsScreenVO> getScreenStatisticsByTime(TimeParam param,String areaCode) {
        log.info("进入getScreenStatisticsByTime:{}",System.currentTimeMillis());
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL) && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_PRODUCT_ALL))) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        Date now = new Date();
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        Date startTime = dateDTO.getStartTime();
        Date endTime = dateDTO.getEndTime();
       /* //查询今天之前的数据
        OrderStatisticsScreenDO responseDO = orderAtomStatisticsProvinceMapperExt.getSumData(startTime,endTime);
        Date todayBegin = DateTimeUtil.getTodayBegin(now);
        if(endTime == null || endTime.after(todayBegin)){
            //查询今天的销售数据,转换成数据库一致的时间格式
            String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
            String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
            OrderStatisticsScreenDO orderSumDataBetween = order2CAtomInfoMapperExt.getOrderSumData(dayBeginTime, endTimeStr);
            //拼装今天的销售数据
            responseDO.setAtomOrderAmount(responseDO.getAtomOrderAmount() + orderSumDataBetween.getAtomOrderAmount());
            responseDO.setAtomOrderCount(responseDO.getAtomOrderCount() + orderSumDataBetween.getAtomOrderCount());
            responseDO.setAtomOrderQuantity(responseDO.getAtomOrderQuantity() + orderSumDataBetween.getAtomOrderQuantity());
        }*/
        String beginTimeStr = startTime == null ? null : DateTimeUtil.getDbTimeStr(startTime);
        String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
        OrderStatisticsScreenDO responseDO = order2CAtomInfoMapperExt.getOrderSumData(beginTimeStr, endTimeStr,areaCode);

        OrderStatisticsScreenVO responseVO = new OrderStatisticsScreenVO();
        BeanUtils.copyProperties(responseDO, responseVO);
        //查询上架产品数
        SpuOfferingInfoExample.Criteria criteria = new SpuOfferingInfoExample().createCriteria().andDeleteTimeIsNull().andOfferingStatusEqualTo("1");

        if (startTime != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(startTime);
        }
        if (endTime != null) {
            criteria.andCreateTimeLessThanOrEqualTo(endTime);
        }
        Long spuCount = spuOfferingInfoMapper.countByExample(criteria.example());
        responseVO.setSpuCount(spuCount.intValue());
        //计算客单价: 总销售额÷总成交顾客数（下单人数）. 4表示退款完成。
        int customerCount = order2cInfoMapperExt.getCustomerCount(beginTimeStr, endTimeStr,areaCode);
        if (customerCount != 0) {
            long customerPrice = responseDO.getAtomOrderAmount() / customerCount;
            responseVO.setCustomerPrice(customerPrice);
        }

        return BaseAnswer.success(responseVO);
    }

    @Override
    public BaseAnswer<OrderStatisticsDashboardResponseVO> getDashboardStatisticsByTime(TimeParam param, Integer timeType,String areaCode) {
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL)) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }


        if (!StatisticsTimeTypeEnum.contains(timeType)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "时间类型错误");
        }
        if (StringUtils.isEmpty(param.getStartTime())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "开始时间不能为空");
        }
        Date now = new Date();
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();
        OrderStatisticsDashboardResponseVO responseVO = new OrderStatisticsDashboardResponseVO();
        if (startTime != null && startTime.after(now)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无效时间，请重新选择");
        }
        //查询本阶段时间,属于今天之前的数据
       /* OrderStatisticsScreenDO dataObject = orderAtomStatisticsProvinceMapperExt.getSumData(startTime,endTime);
        Date todayBegin = DateTimeUtil.getTodayBegin(now);
        if(endTime == null || endTime.after(todayBegin)){
            //查询今天的销售数据,转换成数据库一致的时间格式
            String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
            String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
            OrderStatisticsScreenDO orderSumDataBetween = order2CAtomInfoMapperExt.getOrderSumData(dayBeginTime, endTimeStr);
            //拼装本阶段新增的销售额
            responseVO.setAtomOrderAmount(dataObject.getAtomOrderAmount() + orderSumDataBetween.getAtomOrderAmount());
        }*/
        String beginTimeStr = startTime == null ? null : DateTimeUtil.getDbTimeStr(startTime);
        String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
        OrderStatisticsScreenDO responseDO = order2CAtomInfoMapperExt.getOrderSumData(beginTimeStr, endTimeStr,areaCode);
        responseVO.setAtomOrderAmount(responseDO.getAtomOrderAmount() == null ? 0L : responseDO.getAtomOrderAmount());

        //查询上一时间周期的销售额
        DateDTO previousPeriod = getPreviousPeriod(startTime, timeType);
        Date previousStartTime = previousPeriod.getStartTime();
        Date previousEndTime = previousPeriod.getEndTime();
        String preveousBeginTimeStr = DateTimeUtil.getDbTimeStr(previousStartTime);
        String preveousEndTimeStr = DateTimeUtil.getDbTimeStr(previousEndTime);
        OrderStatisticsScreenDO previvousScreenDO = order2CAtomInfoMapperExt.getOrderSumData(preveousBeginTimeStr, preveousEndTimeStr,areaCode);
        Long previousAmount = previvousScreenDO.getAtomOrderAmount();
        if (previousAmount != null && previousAmount.longValue() != 0L) {
            double ratio = (double) (responseVO.getAtomOrderAmount() - previousAmount) / (double) previousAmount;
            responseVO.setIncreateRatio(ratio);
        }

        //查询累计销售数据（截止endTime）
        OrderStatisticsScreenDO totalData = order2CAtomInfoMapperExt.getOrderSumData(null, endTimeStr,areaCode);
        responseVO.setTotalAtomOrderAmount(totalData.getAtomOrderAmount());
        responseVO.setTotalAtomOrderCount(totalData.getAtomOrderCount());

        //计算本年的销售额,截止上周的最后一天
        Date yearStartDate = DateTimeUtil.getYearStartDate(startTime);
        Date yearEndDate = DateTimeUtil.getPreviousSaturdayEnd(now,1);
        OrderStatisticsScreenDO currentYearData = order2CAtomInfoMapperExt.getOrderSumData(DateTimeUtil.getDbTimeStr(yearStartDate), DateTimeUtil.getDbTimeStr(yearEndDate),areaCode);

        responseVO.setCurrentYearAmount(currentYearData.getAtomOrderAmount());
        double totalIncreaseRatio = (double)responseVO.getAtomOrderAmount() / responseVO.getTotalAtomOrderAmount();
        responseVO.setTotalIncreateRatio(totalIncreaseRatio);
        double currentYearIncreaseRatio = (double)responseVO.getAtomOrderAmount() / responseVO.getCurrentYearAmount();
        responseVO.setCurrentYearIncreateRatio(currentYearIncreaseRatio);

        //获取系统首条订单时间
        String firstDataTime = order2CAtomInfoMapperExt.getFirstOrderTime();
        responseVO.setFirstDataTime(firstDataTime);
        return BaseAnswer.success(responseVO);
    }

    @Override
    public BaseAnswer<List<OrderStatisticsVO>> getOrderStatisticsBySpu(TimeParam param, String areaCode) {
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL)) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        Date now = new Date();
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();
        List<OrderStatisticsVO> list = getAllStatisticsBySpu(now, startTime, endTime,areaCode,5);
        log.info("getAllStatisticsBySpu查询出的数量:{}",list.size());
        /*//取TOP5和其他
        List<OrderStatisticsBySpuVO> top5 = list.size() > 5 ? list.subList(0, 5) : list;
        if (list.size() > 5) {
            List<OrderStatisticsBySpuVO> other = list.subList(5, list.size());
            OrderStatisticsBySpuVO otherDto = new OrderStatisticsBySpuVO();
            LongSummaryStatistics amountCollect = other.stream().collect(Collectors.summarizingLong(s -> s.getAmount() == null ? 0L : s.getAmount()));
            IntSummaryStatistics countCollect = other.stream().collect(Collectors.summarizingInt(s -> s.getOrderCount()));
            otherDto.setName(CommonConstant.OTHER);
            otherDto.setAmount(amountCollect.getSum());
            otherDto.setOrderCount(Integer.valueOf(countCollect.getSum()+""));
            top5.add(otherDto);
        }*/

        return BaseAnswer.success(list);
    }

    @Override
    public BaseAnswer<List<OrderStatisticsVO>> getDashboardStatisticsBySpu(TimeParam param, String areaCode) {
        Date now = new Date();
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();
        List<OrderStatisticsVO> allSalesBySpu = getAllStatisticsBySpu(now, startTime, endTime,areaCode,null);
        log.info("getAllStatisticsBySpu查询出的数量:{}",allSalesBySpu.size());
        return BaseAnswer.success(allSalesBySpu);
    }

    @Override
    public BaseAnswer<List<OrderStatisticsByProvinceVO>> getOrderStatisticsByArea(TimeParam param, Integer areaType, String areaCode, Integer limit, Boolean getAreaTotalAmount) {

        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null) {
            throw new BusinessException(BaseErrorConstant.UN_LOGIN);
        }
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL)) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        Date now = new Date();
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();
       /* //查询今天之前的销售数据,这里查出的province是province表对应的code
        List<OrderStatisticsByProvinceDO> list = orderAtomStatisticsProvinceMapperExt.getProvinceData(startTime,endTime);
        Date todayBegin = DateTimeUtil.getTodayBegin(now);
        if(endTime == null || endTime.after(todayBegin)){
            //查询今天的销售数据
            String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
            String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
            List<OrderStatisticsByProvinceDO> todayList = order2CAtomInfoMapperExt.getOrderProvinceData(dayBeginTime, endTimeStr);
            if(!todayList.isEmpty()){
                Iterator<OrderStatisticsByProvinceDO> iterator = todayList.iterator();
                while (iterator.hasNext()){
                    OrderStatisticsByProvinceDO o = iterator.next();
                    //剔除物联网公司，政企公司，全网平台的数据(根据产品经理要求)
                    if(CommonConstant.EXCLUDE_PROVINCE_LIST.contains(o.getProvince())){
                        iterator.remove();
                    }
                }
                Map<String, OrderStatisticsByProvinceDO> map = new HashMap<>();
                list.addAll(todayList);
                for (OrderStatisticsByProvinceDO data : list) {
                    String province = data.getProvince();
                    if(map.containsKey(province)){
                        OrderStatisticsByProvinceDO existedData = map.get(province);
                        //省份存在于map，更新该省份销量
                        existedData.setAmount(existedData.getAmount() + data.getAmount());
                        existedData.setOrderCount(existedData.getOrderCount() + data.getOrderCount());
                    }else {
                        //省份不存在于map，添加省份
                        map.put(province,data);
                    }
                }
                list = new ArrayList<>(map.values());
            }
        }
        //数据排序,销售额降序
        Collections.sort(list,(o1,o2) -> {
            return Integer.parseInt(o2.getAmount()*10000+"") - Integer.parseInt(o1.getAmount()*10000+"");
        });*/
        String startTimeStr = startTime == null ? null : DateTimeUtil.getDbTimeStr(startTime);
        String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
        if (areaType == null) {
            areaType = 0;
        }
        if (!AreaTypeConstant.contains(areaType)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "区域类型错误");
        }
        if (areaType == AreaTypeConstant.LOCATION.ordinal() && StringUtils.isEmpty(areaCode)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "查询市区数据必选先选择省份");
        }
        if(areaType == AreaTypeConstant.REGION.ordinal() && StringUtils.isEmpty(areaCode)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"查询区县数据必选先选择省份或地市");
        }
        //省份选择直辖市的时候，其实需要查询的是对应区县的数据而非地市
        if(areaCode != null && PROVINCITAL_CITY_LIST.contains(areaCode)){
            //所以数据库传入的实际上应该是地市编码，直辖市对应的地市编码直接在后面加0
            areaCode = areaCode +"0";
        }
        List<OrderStatisticsByProvinceDO> list = order2CAtomInfoMapperExt.getOrderAreaData(startTimeStr, endTimeStr, areaType, areaCode,limit);
        final Integer finalAreaType = areaType;
        List<OrderStatisticsByProvinceVO> voList = list.stream().filter(d -> {
            return StringUtils.isNotEmpty(d.getAreaCode());
        }).map(d -> {
            OrderStatisticsByProvinceVO vo = new OrderStatisticsByProvinceVO();
            BeanUtils.copyProperties(d, vo);
            //解析区域名称
            if (finalAreaType == AreaTypeConstant.PROVINCE.ordinal()) {
                String provinceName = (String)areaDataConfig.getProvinceCodeNameMap().get(vo.getAreaCode());
                if (StringUtils.isNotEmpty(provinceName) && provinceName.equals(NEIMENG)){
                    vo.setAreaName(NEIMENGGU);
                }else {
                    vo.setAreaName(provinceName);
                }
                vo.setTotalAmount(vo.getAmount());
                if(getAreaTotalAmount != null && getAreaTotalAmount){
                    //查询的累计总金额，截止到所选日期
                    OrderStatisticsScreenDO dataObject = order2CAtomInfoMapperExt.getOrderSumData(null, endTimeStr,vo.getAreaCode());
                    vo.setTotalAmount(dataObject.getAtomOrderAmount());
                }
            }
            if (finalAreaType == AreaTypeConstant.LOCATION.ordinal()) {
                vo.setAreaName((String) areaDataConfig.getLocationCodeNameMap().get(vo.getAreaCode()));

            }
            if (finalAreaType == AreaTypeConstant.REGION.ordinal()) {
                vo.setAreaName((String) areaDataConfig.getRegionCodeNameMap().get(vo.getAreaCode()));
            }
            return vo;
        }).collect(Collectors.toList());

        List<ProvinceUserDO> provinceManagerList = accountsImportMapperExt.getAreaManagerCount(startTime, endTime, finalAreaType,areaCode);
        //组装有订单数或金额的订单的客户经理数
        voList.stream().forEach(data -> {
            for (ProvinceUserDO p : provinceManagerList) {
                if (data.getAreaCode().equals(p.getAreaCode())) {
                    data.setManagerCount(p.getManagerCount());
                    break;
                }
            }

        });
        List<String> areaCodeList = voList.stream().map(v -> {
            return v.getAreaCode();
        }).collect(Collectors.toList());
        //组装没有订单数据的客户经理数据
        provinceManagerList.forEach(p -> {
            if(!areaCodeList.contains(p.getAreaCode())){
                OrderStatisticsByProvinceVO vo = new OrderStatisticsByProvinceVO();
                vo.setAmount(0L);
                vo.setOrderCount(0);
                vo.setTotalAmount(0L);
                vo.setManagerCount(p.getManagerCount());
                vo.setAreaCode(p.getAreaCode());
                vo.setAreaName(p.getAreaName());
                //转换名称显示
                if(NEIMENG.equals(vo.getAreaName())){
                    vo.setAreaName(NEIMENGGU);
                }
                voList.add(vo);
            }
        });

        //省 封装没有数据的省(市区是由前端生成的，所以无数据的市区不用返回)
        if (finalAreaType == AreaTypeConstant.PROVINCE.ordinal()) {
            List<ProvinceConfig.ProvinceCode> provinceCodes = provinceConfig.getProvinceCodes();
            List<String> provinceName = provinceCodes.stream().map(ProvinceConfig.ProvinceCode::getProvinceName).collect(Collectors.toList());
            List<String> noneName = provinceName.parallelStream().filter(name -> voList.parallelStream().noneMatch
                    (voName -> name.contains(voName.getAreaName()) || voName.getAreaName().contains(name)))
                    .collect(Collectors.toList());
            List<OrderStatisticsByProvinceVO> collect = noneName.stream().map(name -> {
                OrderStatisticsByProvinceVO vo = new OrderStatisticsByProvinceVO();
                vo.setAreaName(name);
                if(NEIMENGGU.equals(name)){
                    name = NEIMENG;
                }
                vo.setAreaCode((String) areaDataConfig.getProvinceNameCodeMap().get(name));
               vo.setTotalAmount(0L);
                return vo;
            }).collect(Collectors.toList());
            voList.addAll(collect);
        }
        return BaseAnswer.success(voList);
    }

/*    @Override
    public BaseAnswer<List<OrderStatisticsListVO>> getStatisticsList(TimeParam param, Integer timeType) {
        if (!StatisticsTimeTypeEnum.contains(timeType)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "时间类型错误");
        }
        //需要开始时间和结束时间来确定趋势图的时间段
        if (param.getStartTime() == null || param.getEndTime() == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "时间不能为空");
        }
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();
        String starTimeStr = startTime == null ? null : DateTimeUtil.getDbTimeStr(startTime);
        String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
        OrderStatisticsParam orderStatisticsParam = new OrderStatisticsParam();
        orderStatisticsParam.setStartTime(starTimeStr);
        orderStatisticsParam.setEndTime(endTimeStr);
        orderStatisticsParam.setTimeType(timeType);
        List<OrderMarketMessageDO> orderDetailsList = order2CAtomInfoMapperExt.getOrderDetailsList(orderStatisticsParam);
        List<OrderStatisticsListVO> voList = new ArrayList<>();
        List<String> timeList = getTimeList(timeType, startTime, endTime);
        if (timeType.intValue() == StatisticsTimeTypeEnum.YEAR.ordinal()) {
            //年维度，每个数据点是月份的
            //封装数据
            voList = orderDetailsList.stream().map(orderMarketMessageDO -> {
                OrderStatisticsListVO vo = new OrderStatisticsListVO();
                String createTime = orderMarketMessageDO.getCreateTime();
                String yearStr = createTime.substring(0, 4);
                String monthStr = createTime.substring(4, 6);
                int month = Integer.parseInt(monthStr);
                vo.setTime(yearStr + CommonConstant.SLASH + (month < 10 ? "0" + month : month));
                vo.setAmount(orderMarketMessageDO.getAtomOrderAmount());
                vo.setOrderCount(orderMarketMessageDO.getAtomOrderCount());
                return vo;
            }).collect(Collectors.toList());


        } else if (timeType.intValue() == StatisticsTimeTypeEnum.MONTH.ordinal()
                || timeType.intValue() == StatisticsTimeTypeEnum.WEEK.ordinal()
                || timeType.intValue() == StatisticsTimeTypeEnum.DAY.ordinal()) {
            //月和周 天维度，每个数据点是某天的
            voList = orderDetailsList.stream().map(orderMarketMessageDO -> {
                OrderStatisticsListVO vo = new OrderStatisticsListVO();
                String createTime = orderMarketMessageDO.getCreateTime();
                String yearStr = createTime.substring(0, 4);
                String monthStr = createTime.substring(4, 6);
                String dayStr = createTime.substring(6, 8);
                int month = Integer.parseInt(monthStr);
                int day = Integer.parseInt(dayStr);
                vo.setTime(yearStr + CommonConstant.SLASH + (month < 10 ? "0" + month : month) + CommonConstant.SLASH + (day < 10 ? "0" + day : day));
                vo.setAmount(orderMarketMessageDO.getAtomOrderAmount());
                vo.setOrderCount(orderMarketMessageDO.getAtomOrderCount());
                return vo;
            }).collect(Collectors.toList());
        }

        Map<String, OrderStatisticsListVO> voMap = new LinkedHashMap<>();
        voList.stream().forEach(vo -> {
            voMap.put(vo.getTime(), vo);
        });
        voList = timeList.stream().map(t -> {
            OrderStatisticsListVO vo = new OrderStatisticsListVO();
            vo.setTime(t);
            OrderStatisticsListVO existedVo = voMap.get(t);
            if (existedVo != null) {
                vo.setOrderCount(existedVo.getOrderCount());
                vo.setAmount(existedVo.getAmount());
            }
            return vo;

        }).collect(Collectors.toList());
        return BaseAnswer.success(voList);
    }*/

      @Override
    public BaseAnswer<List<OrderStatisticsListVO>> getStatisticsList(TimeParam param, Integer timeType,String areaCode) {
          final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
          final HttpServletRequest request = requestAttr.getRequest();
          String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
          LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
          if (loginIfo4Redis == null) {
              throw new BusinessException(BaseErrorConstant.UN_LOGIN);
          }
          List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
          if (ObjectUtils.isEmpty(dataPermissionCodes) || !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_ROOT_ALL)) {
              throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
          }


        if(!StatisticsTimeTypeEnum.contains(timeType)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间类型错误");
        }
        //需要开始时间和结束时间来确定趋势图的时间段
        if(param.getStartTime() == null || param.getEndTime() == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间不能为空");
        }
        Date now = new Date();
        Date startTime = null;
        Date endTime = null;
        DateDTO dateDTO = DateUtils.checkTime(param.getStartTime(), param.getEndTime());
        startTime = dateDTO.getStartTime();
        endTime = dateDTO.getEndTime();

        //查询今天的销售数据
        Date todayBegin = DateTimeUtil.getTodayBegin(now);
        OrderStatisticsScreenDO todayData = null;
        if(endTime == null || endTime.after(todayBegin)){
            String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
            String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
            todayData = order2CAtomInfoMapperExt.getOrderSumData(dayBeginTime, endTimeStr,areaCode);
        }

        List<OrderStatisticsListVO> voList = new ArrayList<>();
        List<String> timeList = getTimeList(timeType,startTime,endTime);
        if(timeType.intValue() == StatisticsTimeTypeEnum.YEAR.ordinal()){
            //年维度，每个数据点是月份的
            //查询今天之前的销售数据
            List<OrderStatisticsListDO> doList = orderAtomStatisticsProvinceMapperExt.getMonthGroupData(startTime,endTime,areaCode);
            if(!doList.isEmpty()){
                voList = doList.stream().map(d -> {
                    OrderStatisticsListVO vo = new OrderStatisticsListVO();
                    BeanUtils.copyProperties(d, vo);
                    Integer month = d.getMonth();
                    vo.setTime(d.getYear() + CommonConstant.SLASH +  (month < 10 ? "0"+month : month));
                    return vo;
                }).collect(Collectors.toList());
                if(DateTimeUtil.isFirstMonthDay(now)) {
                    //对于某月的第一天，一定不属于历史表中的某个月数据
                    OrderStatisticsListVO vo = getTodayMonthData(now, todayData);
                    voList.add(vo);
                }else {
                    //拼装今日数据
                    OrderStatisticsListVO lastVO = voList.get(doList.size() - 1);
                    addTodayStastistics(todayData, lastVO);
                }
            }else if(todayData != null && todayData.getAtomOrderAmount() != null) {
                //今天之前没数据，仅有今日数据
                OrderStatisticsListVO vo = getTodayMonthData(now, todayData);
                voList.add(vo);
            }
        }else if(timeType.intValue() == StatisticsTimeTypeEnum.MONTH.ordinal() || timeType.intValue() == StatisticsTimeTypeEnum.WEEK.ordinal()){
            //月和周维度，每个数据点是某天的
            List<OrderStatisticsListDO> doList = orderAtomStatisticsProvinceMapperExt.getDayGroupData(startTime,endTime,areaCode);
            if(!doList.isEmpty()){
                voList = doList.stream().map(d -> {
                    OrderStatisticsListVO vo = new OrderStatisticsListVO();
                    BeanUtils.copyProperties(d, vo);
                    Integer month = d.getMonth();
                    Integer day = d.getDay();
                    vo.setTime(d.getYear() + CommonConstant.SLASH + (month < 10 ? "0"+month : month)+CommonConstant.SLASH+ (day < 10 ? "0"+day : day));
                    return vo;
                }).collect(Collectors.toList());
            }
            if(todayData != null && todayData.getAtomOrderAmount() != null){
                //拼装今日数据
                OrderStatisticsListVO vo = getTodayStatisticsVO(now, todayData);
                voList.add(vo);
            }
        }else if(timeType.intValue() == StatisticsTimeTypeEnum.DAY.ordinal()){
            //日维度，每个数据点是每个小时的
            for (String start : timeList) {
                try {
                    Date formatDate = DateTimeUtil.getFormatDate(start, DateTimeUtil.DAY_HOUR_FORMAT);
                    String dbStart = DateTimeUtil.formatDate(formatDate, DateTimeUtil.DB_TIME_STR);
                    String dbEnd = dbStart.substring(0,dbStart.lastIndexOf("0000")) + "5959";
                    OrderStatisticsScreenDO orderSumData = order2CAtomInfoMapperExt.getOrderSumData(dbStart, dbEnd, areaCode);
                    OrderStatisticsListVO vo = new OrderStatisticsListVO();
                    vo.setAmount(orderSumData.getAtomOrderAmount() == null ? 0L : orderSumData.getAtomOrderAmount());
                    vo.setOrderCount(orderSumData.getAtomOrderCount() == null? 0 : orderSumData.getAtomOrderCount());
                    vo.setTime(start);
                    voList.add(vo);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间类型错误");
        }
        //拼装无数据的月/日/小时
        Map<String,OrderStatisticsListVO> voMap = new LinkedHashMap<>();
        voList.stream().forEach(vo -> {
            voMap.put(vo.getTime(),vo);
        });
        voList = timeList.stream().map(t -> {
            OrderStatisticsListVO vo = new OrderStatisticsListVO();
            vo.setTime(t);
            OrderStatisticsListVO existedVo = voMap.get(t);
            if(existedVo != null){
                vo.setOrderCount(existedVo.getOrderCount());
                vo.setAmount(existedVo.getAmount());
            }
            return vo;

        }).collect(Collectors.toList());
        return BaseAnswer.success(voList);
    }

    @Override
    public BaseAnswer<TargetDataVO> getTargetData(Integer targetAmount) {
        TargetDataVO vo = new TargetDataVO();
        //转化为厘
        long target = targetAmount * 10000 * 1000L;
        Date now = new Date();
        Date yearStartDate = DateTimeUtil.getYearStartDate(now);
        String dbStartTime = DateTimeUtil.getDbTimeStr(yearStartDate);
        Date endDate = DateTimeUtil.getPreviousSaturdayEnd(now, 1);
        String dbEndTime = DateTimeUtil.getDbTimeStr(endDate);
        OrderStatisticsScreenDO orderSumData = order2CAtomInfoMapperExt.getOrderSumData(dbStartTime, dbEndTime, null);
        vo.setFinishRate(BigDecimal.valueOf(orderSumData.getAtomOrderAmount()) .divide(BigDecimal.valueOf(target),4,BigDecimal.ROUND_HALF_UP).doubleValue());
        //获取破零省份数量
        Integer totalSaleProvinceCount = order2CAtomInfoMapperExt.getOrderProvinceCount(null,null);
        Integer currentYearSaleProvinceCount = order2CAtomInfoMapperExt.getOrderProvinceCount(dbStartTime,dbEndTime);
        vo.setTotalSaleProvinceCount(totalSaleProvinceCount);
        vo.setCurrentYearSaleProvinceCount(currentYearSaleProvinceCount);
        //获取销售额top3省份
        List<OrderStatisticsByProvinceDO> totalOrderAreaData = order2CAtomInfoMapperExt.getOrderAreaData(null, null, 0, null,3);
        List<OrderStatisticsByProvinceDO> currentYearOrderAreaData = order2CAtomInfoMapperExt.getOrderAreaData(dbStartTime, dbEndTime, 0, null,3);
        List<String> totalTop3Province = totalOrderAreaData.stream().map(d -> {
            return (String)areaDataConfig.getProvinceCodeNameMap().get(d.getAreaCode());
        }).collect(Collectors.toList());
        List<String> cuurrentYearTop3Province = currentYearOrderAreaData.stream().map(d -> {
            return (String)areaDataConfig.getProvinceCodeNameMap().get(d.getAreaCode());
        }).collect(Collectors.toList());
        vo.setTotalTop3Province(totalTop3Province);
        vo.setCurrentYearTop3Province(cuurrentYearTop3Province);
        return BaseAnswer.success(vo);
    }




    @Override
    public BaseAnswer<OrderStatisticsListByWeekVO> getStatisticsListByWeek() {
        //周是从周日到周六为一个周期
        Date now = new Date();
        Date previousSaturdayEnd = DateTimeUtil.getPreviousSaturdayEnd(now,1);
        Date startTime = DateTimeUtil.getWeekStart(previousSaturdayEnd,-7);
        List<OrderTimeStatisticsDO> list = order2CAtomInfoMapperExt.getOrderTimeStatistics(DateTimeUtil.getDbTimeStr(startTime),DateTimeUtil.getDbTimeStr(previousSaturdayEnd));
        //获取周列表
        List<String> timeList = DateTimeUtil.getTimeNameList(StatisticsTimeTypeEnum.WEEK.ordinal(),startTime,previousSaturdayEnd);

        //根据周进行分组
        Map<String, List<OrderTimeStatisticsDO>> monthWeekData = list.stream().map(d -> {
            OrderTimeStatisticsDO orderTimeStatisticsDO = new OrderTimeStatisticsDO();
            orderTimeStatisticsDO.setAmount(d.getAmount());
            //转化成 X月X周 的格式
            orderTimeStatisticsDO.setCreateTime(DateTimeUtil.getMonthWeek(d.getCreateTime()));
            return orderTimeStatisticsDO;
        }).collect(Collectors.groupingBy(OrderTimeStatisticsDO::getCreateTime,LinkedHashMap::new,Collectors.toList()));
        List<TimeDataVO> data = new ArrayList<>();
        for (Map.Entry<String, List<OrderTimeStatisticsDO>> entry : monthWeekData.entrySet()) {
            TimeDataVO vo = new TimeDataVO();
            vo.setTime(entry.getKey());
            vo.setAmount(entry.getValue().stream().mapToLong(OrderTimeStatisticsDO::getAmount).sum());
            data.add(vo);
        }

        //为无数据的周填充空数据
        Map<String, TimeDataVO> weekMapData = new HashMap<>();
        data.forEach(d -> {
            weekMapData.put(d.getTime(),d);
        });
        List<TimeDataVO> data1 = new ArrayList<>();

        for (String week : timeList) {
            TimeDataVO timeDataVO = weekMapData.get(week);
            if(timeDataVO == null){
                timeDataVO = new TimeDataVO();
                timeDataVO.setTime(week);
                timeDataVO.setAmount(0L);
            }
            data1.add(timeDataVO);
        }

        OrderStatisticsListByWeekVO weekVO = new OrderStatisticsListByWeekVO();
        weekVO.setData(data1);
        return BaseAnswer.success(weekVO);
    }

    @Override
    public BaseAnswer<OrderStatisticsListByProvinceVO> getTop3StatisticsListByProvince() {
        Date now = new Date();
        Date previousSaturdayEnd = DateTimeUtil.getPreviousSaturdayEnd(now,1);
        Date monthEnd = DateTimeUtil.getMonthEnd(previousSaturdayEnd);
        Date startTime = DateTimeUtil.getMonthStart(previousSaturdayEnd,-7);
        //获取时间列表
        List<String> timeList = DateTimeUtil.getTimeNameList(StatisticsTimeTypeEnum.MONTH.ordinal(),startTime,monthEnd);

        List<OrderTimeStatisticsWithProvinceDO> list = order2CAtomInfoMapperExt.getOrderTimeStatisticsWithProvince(DateTimeUtil.getDbTimeStr(startTime),DateTimeUtil.getDbTimeStr(previousSaturdayEnd));
        //根据省将数据分组
        Map<String, List<OrderTimeStatisticsWithProvinceDO>> provinceDataMap = list.stream().map(d -> {
            OrderTimeStatisticsWithProvinceDO provinceDO = new OrderTimeStatisticsWithProvinceDO();
            provinceDO.setAmount(d.getAmount());
            provinceDO.setCreateTime(d.getCreateTime().substring(4,6)+"月");
            provinceDO.setProvince((String)areaDataConfig.getProvinceCodeNameMap().get(d.getProvince()));
            return provinceDO;
        }).collect(Collectors.groupingBy(OrderTimeStatisticsWithProvinceDO::getProvince));
        List<ListDataVO> resultData = new ArrayList<>();
        for (Map.Entry<String, List<OrderTimeStatisticsWithProvinceDO>> entry : provinceDataMap.entrySet()) {
            ListDataVO vo = new ListDataVO();
            String provinceName = entry.getKey();
            vo.setName(provinceName);
            List<OrderTimeStatisticsWithProvinceDO> data = entry.getValue();
            //根据时间分组
            Map<String, List<OrderTimeStatisticsWithProvinceDO>> timeDataMap = data.stream().collect(Collectors.groupingBy(OrderTimeStatisticsWithProvinceDO::getCreateTime,LinkedHashMap::new,Collectors.toList()));
            List<TimeDataVO> timeDataVOList = new ArrayList<>();
            Long totalAmount = 0L;
            for (Map.Entry<String, List<OrderTimeStatisticsWithProvinceDO>> entry1 : timeDataMap.entrySet()) {
                TimeDataVO timeDataVO = new TimeDataVO();
                timeDataVO.setTime(entry1.getKey());
                long sum = entry1.getValue().stream().mapToLong(OrderTimeStatisticsWithProvinceDO::getAmount).sum();
                timeDataVO.setAmount(sum);
                timeDataVOList.add(timeDataVO);
                totalAmount += sum;
            }

            //为无数据的月份填充数据
            Map<String,TimeDataVO> monthMapData = new HashMap<>();
            timeDataVOList.forEach(d -> {
                monthMapData.put(d.getTime(),d);
            });
            List<TimeDataVO> data1 = new ArrayList<>();
            for (String month : timeList) {
                TimeDataVO timeDataVO = monthMapData.get(month);
                if(timeDataVO == null) {
                    //对应月份无数据
                    timeDataVO = new TimeDataVO();
                    timeDataVO.setAmount(0L);
                    timeDataVO.setTime(month);
                }
                data1.add(timeDataVO);
            }

            vo.setList(data1);
            vo.setTotalAmount(totalAmount);
            resultData.add(vo);
        }
        //销售额倒序排序，截取前三
        resultData = resultData.stream().sorted((o1, o2) -> {
            return Long.compare(o2.getTotalAmount(),o1.getTotalAmount());
        }).collect(Collectors.toList()).subList(0, 3);
        return BaseAnswer.success(resultData);
    }

    @Override
    public BaseAnswer<OrderStatisticsBySpuTypeVO> getStatisticsBySpuType() {
        Date now = new Date();
        Date yearStartDate = DateTimeUtil.getYearStartDate(now);
        //截止日期是上周的最后一天
        Date previousSaturdayEnd = DateTimeUtil.getPreviousSaturdayEnd(now, 1);
        List<OrderStatisticsBySpuTypeDO> orderStatisticsBySpuTypeDO = order2CAtomInfoMapperExt.getOrderAmountBySpuType(DateTimeUtil.getDbTimeStr(yearStartDate),DateTimeUtil.getDbTimeStr(previousSaturdayEnd));
        OrderStatisticsBySpuTypeVO vo = new OrderStatisticsBySpuTypeVO();
        long totalAmount = orderStatisticsBySpuTypeDO.stream().mapToLong(OrderStatisticsBySpuTypeDO::getAmount).sum();
        List<SpuItemVO> data = orderStatisticsBySpuTypeDO.stream().map(d -> {
            String spuType = SPUOfferingClassEnum.getDisplay(d.getSpuType());
            SpuItemVO spuItemVO = new SpuItemVO();
            spuItemVO.setSpuName(spuType);
            spuItemVO.setAmount(d.getAmount());
            spuItemVO.setRate((BigDecimal.valueOf(spuItemVO.getAmount()).divide( BigDecimal.valueOf(totalAmount),2,BigDecimal.ROUND_HALF_UP).doubleValue()));
            return spuItemVO;
        }).collect(Collectors.toList());
        vo.setData(data);
        vo.setTotalAmount(totalAmount);
        return BaseAnswer.success(vo);
    }

    @Override
    public BaseAnswer<OrderStatisticsListBySpuTypeVO> getStatisticsListBySpuType() {
        Date now = new Date();
        Date previousSaturdayEnd = DateTimeUtil.getPreviousSaturdayEnd(now,1);
        Date monthEnd = DateTimeUtil.getMonthEnd(previousSaturdayEnd);
        Date startTime = DateTimeUtil.getMonthStart(previousSaturdayEnd,-7);
        //或者月份名称
        List<String> timeList = DateTimeUtil.getTimeNameList(StatisticsTimeTypeEnum.MONTH.ordinal(),startTime,monthEnd);
        List<OrderTimeStatisticsWithSpuDO> list = order2CAtomInfoMapperExt.getOrderTimeStatisticsWithSpu(DateTimeUtil.getDbTimeStr(startTime),DateTimeUtil.getDbTimeStr(previousSaturdayEnd));
        OrderStatisticsListBySpuTypeVO resultVo = new OrderStatisticsListBySpuTypeVO();
        List<ListDataVO> dataList = new ArrayList<>();
        Map<String, List<OrderTimeStatisticsWithSpuDO>> spuGroupData = list.stream().map(d -> {
            OrderTimeStatisticsWithSpuDO data = new OrderTimeStatisticsWithSpuDO();
            data.setAmount(d.getAmount());
            data.setCreateTime(d.getCreateTime().substring(4, 6) + "月");
            data.setSpuType(SPUOfferingClassEnum.getDisplay(d.getSpuType()));
            return data;
            //根据spu类型分组
        }).collect(Collectors.groupingBy(OrderTimeStatisticsWithSpuDO::getSpuType));
        for (Map.Entry<String, List<OrderTimeStatisticsWithSpuDO>> entry : spuGroupData.entrySet()) {
            ListDataVO vo = new ListDataVO();
            vo.setName(entry.getKey());
            List<OrderTimeStatisticsWithSpuDO> spuTimeData = entry.getValue();
            List<TimeDataVO> monthDataList = new ArrayList<>();
            //根据月份分组
            Map<String, List<OrderTimeStatisticsWithSpuDO>> collect = spuTimeData.stream().collect(Collectors.groupingBy(OrderTimeStatisticsWithSpuDO::getCreateTime,LinkedHashMap::new,Collectors.toList()));
            for (Map.Entry<String, List<OrderTimeStatisticsWithSpuDO>> e : collect.entrySet()) {
                TimeDataVO timeDataVO = new TimeDataVO();
                timeDataVO.setTime(e.getKey());
                long sum = e.getValue().stream().mapToLong(OrderTimeStatisticsWithSpuDO::getAmount).sum();
                timeDataVO.setAmount(sum);
                monthDataList.add(timeDataVO);
            }
            //为无数据的月份填充数据
            Map<String,TimeDataVO> monthMapData = new HashMap<>();
            monthDataList.forEach(d -> {
                monthMapData.put(d.getTime(),d);
            });
            List<TimeDataVO> data = new ArrayList<>();
            for (String month : timeList) {
                TimeDataVO timeDataVO = monthMapData.get(month);
                if(timeDataVO == null) {
                    //对应月份无数据
                    timeDataVO = new TimeDataVO();
                    timeDataVO.setAmount(0L);
                    timeDataVO.setTime(month);
                }
                data.add(timeDataVO);
            }
            vo.setList(data);
            dataList.add(vo);
        }
        resultVo.setData(dataList);
        return BaseAnswer.success(resultVo);
    }

    /**
     * 根据月或者周为单位，获取某个时间范围内的时间列表
     */
 /*   private List<String> getTimeNameList(Integer timeType, Date startTime, Date endTime) {
        List<String> list = new ArrayList<>();
        if (StatisticsTimeTypeEnum.MONTH.ordinal() == timeType.intValue()) {
            //日期为月份集合
            do {
                String month = null;
                try {
                    month = DateTimeUtil.getMonthStr(startTime.getTime()).substring(4, 6)+"月";
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                list.add(month);
                startTime = DateTimeUtil.addMonth(startTime, 1);
            } while (startTime.before(endTime));
        }
        if (StatisticsTimeTypeEnum.WEEK.ordinal() == timeType.intValue()) {
            //日期为周集合
            do {
                String monthWeek = DateTimeUtil.getMonthWeek(DateTimeUtil.getDbTimeStr(startTime));
                list.add(monthWeek);
                startTime = DateTimeUtil.addWeek(startTime,1);
            } while (startTime.before(endTime));
        }
        return list;
    }*/

    private OrderStatisticsListVO getTodayMonthData(Date now, OrderStatisticsScreenDO todayData) {
        String shortDate = DateTimeUtil.getShortDate(now.getTime());
        String[] split = shortDate.split(CommonConstant.SEPARATOR);
        String yearStr = split[0];
        String monthStr = split[1];
        OrderStatisticsListVO vo = new OrderStatisticsListVO();
        vo.setTime(yearStr+CommonConstant.SLASH+monthStr);
        vo.setOrderCount(todayData.getAtomOrderCount());
        vo.setAmount(todayData.getAtomOrderAmount());
        return vo;
    }

    private OrderStatisticsListVO getTodayStatisticsVO(Date now, OrderStatisticsScreenDO todayData) {
        String shortDate = DateTimeUtil.getShortDate(now.getTime());
        String[] split = shortDate.split(CommonConstant.SEPARATOR);
        String yearStr = split[0];
        String monthStr = split[1];
        String dayStr = split[2];
        OrderStatisticsListVO vo = new OrderStatisticsListVO();
        vo.setTime(yearStr + CommonConstant.SLASH + monthStr + CommonConstant.SLASH + dayStr);
        vo.setOrderCount(todayData.getAtomOrderCount());
        vo.setAmount(todayData.getAtomOrderAmount());
        return vo;
    }

    private List<String> getTimeList(Integer timeType, Date startTime, Date endTime) {
        List<String> list = new ArrayList<>();
        if (StatisticsTimeTypeEnum.YEAR.ordinal() == timeType.intValue()) {
            //日期为每月
            do {
                String yearMonth = DateTimeUtil.formatDate(startTime, DateTimeUtil.SLASH_YEAR_MONTH);
                list.add(yearMonth);
                startTime = DateTimeUtil.addMonth(startTime, 1);
            } while (startTime.before(endTime));

        } else if(StatisticsTimeTypeEnum.WEEK.ordinal() == timeType.intValue() || StatisticsTimeTypeEnum.MONTH.ordinal() == timeType.intValue()) {
            //日期为每日
            do {
                String yearMonthDay = DateTimeUtil.formatDate(startTime, DateTimeUtil.SLASH_YEAR_MONTH_DAY);
                list.add(yearMonthDay);
                startTime = DateTimeUtil.addDay(startTime, 1);
            } while (startTime.before(endTime));
        } else {
            //日期为每小时（如当前时间是10:40，即需要从当日的11:00往前展示到前一天的11:00之间的数据，共24个点;如果当前是10:00，展示昨天10：00到今天10：00的数据）
            startTime = DateTimeUtil.getCellHourDate(startTime);
            for(int i=0;i<24;i++){
                String dayHour = DateTimeUtil.formatDate(startTime, DateTimeUtil.DAY_HOUR_FORMAT);
                list.add(dayHour);
                startTime = DateTimeUtil.addHour(startTime,1);
            }
        }
        return list;
    }

    private void addTodayStastistics(OrderStatisticsScreenDO todayData, OrderStatisticsListVO lastVO) {
        lastVO.setAmount(todayData != null && todayData.getAtomOrderAmount() != null ? lastVO.getAmount() + todayData.getAtomOrderAmount() : lastVO.getAmount());
        lastVO.setOrderCount(todayData != null && todayData.getAtomOrderCount() != null ? lastVO.getOrderCount() + todayData.getAtomOrderCount() : lastVO.getOrderCount());
    }

    /**
     * 根据销售金额倒叙排列，每个SPU对应的销售额和原子订单数
     */
    private List<OrderStatisticsVO> getAllStatisticsBySpu(Date now, Date startTime, Date endTime, String areaCode, Integer limit) {
        //查询今天之前的数据
        /*List<OrderStatisticsBySpuDO> list = orderAtomStatisticsSpuMapperExt.getDataList(startTime,endTime);
        Date todayBegin = DateTimeUtil.getTodayBegin(now);
        if(endTime == null || endTime.after(todayBegin)){
            //查询今天的销售数据
            String dayBeginTime = DateTimeUtil.getDayBeginTime(now);
            String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);
            List<OrderStatisticsBySpuDO> todayList = order2CAtomInfoMapperExt.getSpuDataList(dayBeginTime,endTimeStr);
            if(!todayList.isEmpty()){
                //拼装今日数据
                list.addAll(todayList);
                //数据整合
                Map<String, OrderStatisticsBySpuDO> map = new HashMap<>();
                list.stream().forEach(salesBySpuDTO -> {
                    String name = salesBySpuDTO.getName();
                    if(map.containsKey(name)){
                        OrderStatisticsBySpuDO existedDo = map.get(name);
                        existedDo.setAmount(existedDo.getAmount() + salesBySpuDTO.getAmount());
                        existedDo.setOrderCount(existedDo.getOrderCount() + salesBySpuDTO.getOrderCount());
                    }else {
                        map.put(name,salesBySpuDTO);
                    }
                });
                list = new ArrayList<>(map.values());
            }
        }
        //数据排序
        Collections.sort((list), new Comparator<OrderStatisticsBySpuDO>() {
            @Override
            public int compare(OrderStatisticsBySpuDO o1, OrderStatisticsBySpuDO o2) {
                //要返回int，而这里的金额是double,所以乘以10000来去掉小数点，转化为int
                return Integer.parseInt(o2.getAmount()*10000 +"") - Integer.parseInt(o1.getAmount()*10000+"");
            }
        });*/
        String startTimeStr = startTime == null ? null : DateTimeUtil.getDbTimeStr(startTime);
        String endTimeStr = endTime == null ? null : DateTimeUtil.getDbTimeStr(endTime);

        List<OrderStatisticsBySpuDO> list = order2CAtomInfoMapperExt.getSpuDataList(startTimeStr, endTimeStr,areaCode,limit);
        List<OrderStatisticsVO> collect = list.stream().map(d -> {
            OrderStatisticsVO orderStatisticsVO = new OrderStatisticsVO();
            BeanUtils.copyProperties(d, orderStatisticsVO);
            return orderStatisticsVO;
        }).collect(Collectors.toList());
        return collect;
    }

    /**
     * startTime的时分秒都是0
     */
    private DateDTO getPreviousPeriod(Date startTime, Integer timeType) {
        Date previousStartTime = null;
        Date previousEndTime = null;
        Calendar startCalendar = Calendar.getInstance();
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(startTime);
        endCalendar.add(Calendar.SECOND, -1);
        previousEndTime = endCalendar.getTime();
        startCalendar.setTime(startTime);
        switch (timeType) {
            //年
            case 0:
                startCalendar.add(Calendar.YEAR, -1);
                break;
            //月
            case 1:
                startCalendar.add(Calendar.MONTH, -1);
                break;
            //周
            case 2:
                startCalendar.add(Calendar.WEEK_OF_YEAR, -1);
                break;
            //日
            case 3:
                startCalendar.add(Calendar.DAY_OF_YEAR, -1);
                break;
        }
        previousStartTime = startCalendar.getTime();
        DateDTO dateDTO = new DateDTO();
        dateDTO.setStartTime(previousStartTime);
        dateDTO.setEndTime(previousEndTime);
        return dateDTO;

    }




    public static String decryptIOTMessage(String secretContent, String stringKey) {
        if (org.apache.commons.lang.StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] hex = HexUtil.decodeHex(Base64.decodeStr(secretContent));

            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);

            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
//解密
            byte[] decrypt = des.decrypt(hex);
            return new String(decrypt, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }

    /**
     * 名称脱敏处理
     * @param custName
     * @return
     */
    public static String custNameDesensitization(String custName) {
        // 规则说明：
        // 姓名：字符长度小于5位；企业名称：字符长度大于等于5位。
        // 姓名规则
        // 规则一：1个字则不脱敏，如"张"-->"张"
        // 规则二：2个字则脱敏第二个字，如"张三"-->"张*"
        // 规则三：3个字则脱敏第二个字，如"张三丰"-->"张*丰"
        // 规则四：4个字则脱敏中间两个字，如"易烊千玺"-->"易**玺"
        // 企业名称规则：
        // 从第4位开始隐藏，最多隐藏6位。

        if (StringUtils.isNotEmpty(custName)) {
            char[] chars = custName.toCharArray();
            if (chars.length < 5) {// 表示姓名
                if (chars.length > 1) {
                    StringBuffer sb = new StringBuffer();
                    for (int i = 0; i < chars.length - 2; i++) {
                        sb.append("*");
                    }
                    custName = custName.replaceAll(custName.substring(1, chars.length - 1), sb.toString());
                }
            } else {// 企业名称
                int start = 4;
                // 第一部分
                String str1 = custName.substring(0, start);
                // 第二部分
                String str2 = "";
                if (chars.length == 5) {
                    str2 = "*";
                } else if (chars.length == 6) {
                    str2 = "**";
                } else if (chars.length == 7) {
                    str2 = "***";
                } else if (chars.length == 8) {
                    str2 = "****";
                } else if (chars.length == 9) {
                    str2 = "*****";
                } else {
                    str2 = "******";
                }
                // 通过计算得到第三部分需要从第几个字符截取
                int subIndex = start + str2.length();
                // 第三部分
                String str3 = custName.substring(subIndex);
                StringBuffer sb = new StringBuffer();
                sb.append(str1);
                sb.append(str2);
                sb.append(str3);
                custName = sb.toString();
            }
        }
        return custName;
    }

}
