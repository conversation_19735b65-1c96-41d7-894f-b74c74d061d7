package com.chinamobile.data.service.excel;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.RowTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinamobile.data.config.ProvinceCodeMapConfig;
import com.chinamobile.data.dao.AccountsImportMapper;
import com.chinamobile.data.exception.ServicePowerException;
import com.chinamobile.data.pojo.entity.AccountsImport;
import com.chinamobile.data.pojo.entity.AccountsImportExample;
import com.chinamobile.data.pojo.excel.ExcelAccountImport;
import com.chinamobile.data.util.DateUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.chinamobile.iot.sc.util.DateTimeUtil.*;

/**
 * <AUTHOR>
 * @date 2022/4/25 14:46
 */
@Slf4j
public class AccountImportExcelListener extends AnalysisEventListener<ExcelAccountImport> {

    private ProvinceCodeMapConfig provinceCodeMapConfig;

    @Resource
    private AccountsImportMapper accountsImportMapper;

    private Boolean stop = false;


    @Override
    public void invoke(ExcelAccountImport data, AnalysisContext analysisContext) {
        log.info("invoke Enter");
//        analysisContext.readWorkbookHolder().setIgnoreEmptyRow(false);

        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        log.info("解析到一条数据:{},当前行:{}", data, currentRow);

        // double count = data.getCount();
        //判断（通过时间维度来）当前数据是否是以存过数据
        String regDate = data.getRegTime();

        //获取当前时间上周的结束时间时间
          /*  Date now = new Date();
            Date lastWeekEnd = DateTimeUtil.getPreviousSaturdayEnd(now, 1);*/
        //之前导入数据注册时间为最近的时间(大于之前导入最新时间1秒钟)
        //String lastTime = data.getLastTime();

        Date formatDate;
        Date lastDate;
      /*  try {
            DateUtils.parase(lastTime, "yyyy/MM/dd HH:mm:ss");
        } catch (Exception e) {
            throw new ServicePowerException(500, "校验时间不符合规范，错误行：" + currentRow);
        }*/

    /*    try {
             String dayEndTime = DateTimeUtil.getDayEndTime(lastTime);
            lastDate = DateTimeUtil.getFormatDate(lastTime, SLASH_DATE);
            formatDate = DateTimeUtil.getFormatDate(regDate, SLASH_DATE);
        } catch (Exception e) {
            throw new ServicePowerException(500, "时间格式转换错误：" + currentRow);
        }*/

       /* if (lastDate.after(formatDate)) {
            stop = true;
        }*/

        if (StringUtils.isBlank(regDate)) {
            throw new ServicePowerException(500, "缺少注册日期，错误行：" + currentRow);
        }
        try {
            DateUtils.parase(regDate, "yyyy/MM/dd HH:mm:ss");
        } catch (Exception e) {
            throw new ServicePowerException(500, "注册日期不符合规范，错误行：" + currentRow);
        }
        String province = data.getProvince();
        if (StringUtils.isBlank(province)) {
            throw new ServicePowerException(500, "缺少省份信息，错误行：" + currentRow);
        }
//            if(province.substring(province.length()-1,province.length()).equals("省")){
//                province = province.substring(0,province.length()-1);+
//            }

        char c = province.charAt(province.length() - 1);
        if ("省".equals(String.valueOf(c))) {
            province = province.substring(0, province.length() - 1);
        }

        HashMap<String, String> codenameMap = provinceCodeMapConfig.getProvinceNameCodeMap();
        //province = convertCap(province);
        if (!codenameMap.containsKey(province)) {
            throw new ServicePowerException(500, "错误的省份信息，错误行：" + currentRow);
        }


        String type = data.getType();
        if (StringUtils.isBlank(type)) {
            throw new ServicePowerException(500, "缺少类型，错误行：" + currentRow);
        }


    }


    @Override
    public boolean hasNext(AnalysisContext context) {
        if(RowTypeEnum.EMPTY.equals(context.readRowHolder().getRowType())){
            doAfterAllAnalysed(context);
            return false;
        }
        return super.hasNext(context);
      /*  if (stop) {
            return false;
        } else {
            return super.hasNext(context);
        }*/
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }

    private String convertCap(String str) {
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    public AccountImportExcelListener(ProvinceCodeMapConfig mapconfig) {
        provinceCodeMapConfig = mapconfig;
    }

}
