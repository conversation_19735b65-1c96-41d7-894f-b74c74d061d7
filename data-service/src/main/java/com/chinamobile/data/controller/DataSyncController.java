package com.chinamobile.data.controller;

import com.chinamobile.data.service.DataSyncService;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * created by liuxia<PERSON> on 2022/4/13 10:24
 * 数据录入接口
 */
@RestController
@RequestMapping("/data/sync")
public class DataSyncController {

    @Autowired
    private DataSyncService dataSyncService;

//    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    @GetMapping("/province/count")
    public BaseAnswer<Object> countProvince(@RequestParam("day") String day){
        return BaseAnswer.success(dataSyncService.countOrder2cByProvince(day));
    }

//    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    @GetMapping("/spu/count")
    public BaseAnswer<Object> countSpu(@RequestParam("day") String day){
        return BaseAnswer.success(dataSyncService.countOrder2cBySpu(day));
    }

//    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    @GetMapping("/province/count/test")
    public BaseAnswer<Object> countProvince(@RequestParam("start") String start,@RequestParam("end") String end){
        return BaseAnswer.success(dataSyncService.countOrder2cByProvince(start,end));
    }

//    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    @GetMapping("/spu/count/test")
    public BaseAnswer<Object> countSpu(@RequestParam("start") String start,@RequestParam("end") String end){
        return BaseAnswer.success(dataSyncService.countOrder2cBySpu(start,end));
    }

}
