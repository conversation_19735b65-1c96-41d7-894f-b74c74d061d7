package com.chinamobile.data.config;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/4/19 10:46
 * 对于所有的查询方法，对查询结果进行缓存
 */
@Component
@Aspect
public class DataQueryAOP {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    //产品上下架统计，和订单无关且接口速度很快，不走缓存
    @Pointcut("execution (* com.chinamobile.data.controller.DataProductController.shelfStatistics(..)) ")
    public void shelfStatistics(){}

    //手动清理缓存的接口，和订单无关
    @Pointcut("execution (* com.chinamobile.data.controller.DataQueryController.clearRedisData(..)) ")
    public void clearRedis(){}

    //订单相关的查询，优先走缓存
    @Pointcut("execution (* com.chinamobile.data.controller.DataQueryController.*(..)) " +
            "|| execution (* com.chinamobile.data.controller.DataProductController.*(..)) " +
            "|| execution(* com.chinamobile.data.controller.DataInputController.getManagerSaleRanking(..))")
    public void dataQuery(){}

    /**
     * 查询接口，优先从缓存获取数据，缓存无数据再查接口并将结果存入缓存中
     */
    @Around("(dataQuery()&&!shelfStatistics()&&!clearRedis())")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String fullClassName = joinPoint.getTarget().getClass().getName();
        String controllerName = fullClassName.substring(fullClassName.lastIndexOf(".") + 1);
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        StringBuilder argsBuilder = new StringBuilder();
        for (Object arg : args) {
            if(arg != null){
                String s = String.valueOf(arg);
                if(StringUtils.isNotEmpty(s)){
                    argsBuilder.append(s).append("_");
                }
            }
        }
        String params = argsBuilder.toString();
        if(StringUtils.isNotEmpty(params)){
            params = params.substring(0, params.lastIndexOf("_"));
        }
        String redisKey = controllerName+"."+methodName+"#"+params;
        //首先尝试从redis中获取数据
        Object o = redisTemplate.opsForValue().get(redisKey);
        if(o != null){
            return o;
        }
        //redis没有数据，查询接口
        o = joinPoint.proceed();
        if(o != null){
            //缓存查询结果
            redisTemplate.opsForValue().set(redisKey,o);
            //保存缓存的key，便于收到新订单之后清除缓存数据
            stringRedisTemplate.opsForList().leftPush(CommonConstant.REDIS_DATA_KEY,redisKey);
        }
        return o;
    }

    /**
     * 清除缓存的数据
     */
    public void clearRedisData(){
        List<String> keyList = stringRedisTemplate.opsForList().range(CommonConstant.REDIS_DATA_KEY, 0, -1);
        if(!CollectionUtils.isEmpty(keyList)){
            keyList.parallelStream().forEach(k -> {stringRedisTemplate.delete(k);});
        }
        stringRedisTemplate.delete(CommonConstant.REDIS_DATA_KEY);
    }

}
