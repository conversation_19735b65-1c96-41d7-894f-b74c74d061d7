package com.chinamobile.data.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 原子订单历史记录表
 *
 * <AUTHOR>
public class Order2cAtomHistory implements Serializable {
    /**
     * 原子订单ID
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private String atomOrderId;

    /**
     * 订单ID
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private String orderId;

    /**
     * 退款物流单号
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private String refundOrderId;

    /**
     * 1 订单操作 2 退货退款操作
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private Integer operateType;

    /**
     * 操作审核人ID
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private String operatorId;

    /**
     * 内部状态，内部展示前端使用的状态
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private Integer innerStatus;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.atom_order_id
     *
     * @return the value of supply_chain..order_2c_atom_history.atom_order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..order_2c_atom_history.atom_order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.order_id
     *
     * @return the value of supply_chain..order_2c_atom_history.order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.order_id
     *
     * @param orderId the value for supply_chain..order_2c_atom_history.order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.refund_order_id
     *
     * @return the value of supply_chain..order_2c_atom_history.refund_order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public String getRefundOrderId() {
        return refundOrderId;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withRefundOrderId(String refundOrderId) {
        this.setRefundOrderId(refundOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.refund_order_id
     *
     * @param refundOrderId the value for supply_chain..order_2c_atom_history.refund_order_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.operate_type
     *
     * @return the value of supply_chain..order_2c_atom_history.operate_type
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Integer getOperateType() {
        return operateType;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withOperateType(Integer operateType) {
        this.setOperateType(operateType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.operate_type
     *
     * @param operateType the value for supply_chain..order_2c_atom_history.operate_type
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.operator_id
     *
     * @return the value of supply_chain..order_2c_atom_history.operator_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public String getOperatorId() {
        return operatorId;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withOperatorId(String operatorId) {
        this.setOperatorId(operatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.operator_id
     *
     * @param operatorId the value for supply_chain..order_2c_atom_history.operator_id
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.inner_status
     *
     * @return the value of supply_chain..order_2c_atom_history.inner_status
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Integer getInnerStatus() {
        return innerStatus;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withInnerStatus(Integer innerStatus) {
        this.setInnerStatus(innerStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.inner_status
     *
     * @param innerStatus the value for supply_chain..order_2c_atom_history.inner_status
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.create_time
     *
     * @return the value of supply_chain..order_2c_atom_history.create_time
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.create_time
     *
     * @param createTime the value for supply_chain..order_2c_atom_history.create_time
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_history.update_time
     *
     * @return the value of supply_chain..order_2c_atom_history.update_time
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public Order2cAtomHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_history.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_atom_history.update_time
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", refundOrderId=").append(refundOrderId);
        sb.append(", operateType=").append(operateType);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", innerStatus=").append(innerStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAtomHistory other = (Order2cAtomHistory) that;
        return (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getRefundOrderId() == null ? other.getRefundOrderId() == null : this.getRefundOrderId().equals(other.getRefundOrderId()))
            && (this.getOperateType() == null ? other.getOperateType() == null : this.getOperateType().equals(other.getOperateType()))
            && (this.getOperatorId() == null ? other.getOperatorId() == null : this.getOperatorId().equals(other.getOperatorId()))
            && (this.getInnerStatus() == null ? other.getInnerStatus() == null : this.getInnerStatus().equals(other.getInnerStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getRefundOrderId() == null) ? 0 : getRefundOrderId().hashCode());
        result = prime * result + ((getOperateType() == null) ? 0 : getOperateType().hashCode());
        result = prime * result + ((getOperatorId() == null) ? 0 : getOperatorId().hashCode());
        result = prime * result + ((getInnerStatus() == null) ? 0 : getInnerStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Apr 14 16:10:37 CST 2022
     */
    public enum Column {
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        refundOrderId("refund_order_id", "refundOrderId", "VARCHAR", false),
        operateType("operate_type", "operateType", "INTEGER", false),
        operatorId("operator_id", "operatorId", "VARCHAR", false),
        innerStatus("inner_status", "innerStatus", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Apr 14 16:10:37 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}