package com.chinamobile.data.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/19
 * @description 按省份纬度的范式销售额数据
 */
@Data
public class ProvinceOfferingClassTotalSellVO {

    /**
     * 省份名称
     */
    @Excel(name = "省份")
    private String provinceName;

    /**
     * 产品增值服务包销售额
     */
    @Excel(name = "产品增值服务包")
    private BigDecimal productAddedValueSell = new BigDecimal(0);

    /**
     * 合同履约销售额
     */
    @Excel(name = "合同履约")
    private BigDecimal contractComplianceSell = new BigDecimal(0);

    /**
     * onenet独立服务销售额
     */
    @Excel(name = "OneNET独立服务")
    private BigDecimal onenetServiceSell = new BigDecimal(0);

    /**
     * 联合销售额
     */
    @Excel(name = "联合销售(代销)")
    private BigDecimal unionSell = new BigDecimal(0);

    /**
     * onepark独立服务销售额
     */
    @Excel(name = "OnePark独立服务")
    private BigDecimal oneParkServiceSell = new BigDecimal(0);
    /**
     * 行车卫士标准产品销售额
     */
    @Excel(name = "行车卫士标准产品")
    private BigDecimal carTVStandardProductsSell = new BigDecimal(0);
    /**
     * 软件服务销售额
     */
    @Excel(name = "软件服务")
    private BigDecimal softwareService = new BigDecimal(0);
    /**
     * OneCyber标准产品销售额
     */
    @Excel(name = "OneCyber标准产品")
    private BigDecimal oneCyberSell = new BigDecimal(0);

    /**
     * 销售汇总
     */
    @Excel(name = "汇总数据")
    private BigDecimal provinceTotalSell = new BigDecimal(0);
}
