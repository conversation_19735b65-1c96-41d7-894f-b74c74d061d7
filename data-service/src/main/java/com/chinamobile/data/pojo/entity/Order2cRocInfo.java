package com.chinamobile.data.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * RefundApplyOrCancel
订单退货退款换货记录表
 *
 * <AUTHOR>
public class Order2cRocInfo implements Serializable {
    /**
     * 退款订单请求流水号
本次发起退货退款请求的流水号
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String refundOrderId;

    /**
     * 业务订单流水号
销售业务订单的唯一标识
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String orderId;

    /**
     * 原子订单ID
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String atomOrderId;

    /**
     * 合作伙伴ID
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String cooperatorId;

    /**
     * 退款类型
1：仅退款
2：退货退款
3：换货（暂不启用）
4：取消申请（以实时反馈为准）
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String refundsType;

    /**
     * 退款原因
1：不喜欢/不想要
2：商品错发
3：收到商品与描述不符
4：商品质量问题
5：快递/物流一直未送到
6：其他
7：七天无理由退换货
8：商品信息拍错
9：地址/电话信息填写错误
10：拍多了
11：协商一致退款
12：缺货
13：发货速度不满意
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String reason;

    /**
     * 退款说明
退货退款的补充说明
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String remark;

    /**
     * 图片文件名
参见退货退款申请文件命名规则
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String picture;

    /**
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String picturesInnerUrl;

    /**
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String picturesOuterUrl;

    /**
     * 订单原始状态
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private Integer originalStatus;

    /**
     * 内部订单状态
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private Integer innerStatus;

    /**
     * 审核人ID
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String auditId;

    /**
     * 审核结果
1：通过
2：不通过
3： 客户自己取消
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String auditResult;

    /**
     * 审核原因
当auditResult=2时必填
当auditResult=1时可为空
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String auditResultReason;

    /**
     * 退货信息地址
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String contactinfo;

    /**
     * 验收人ID
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String receiptId;

    /**
     * 验收结果 返回0：收货验货成功，1：收货验货失败
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String receiptResult;

    /**
     * 验收失败原因
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private String receiptResultReason;

    /**
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.refund_order_id
     *
     * @return the value of supply_chain..order_2c_roc_info.refund_order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getRefundOrderId() {
        return refundOrderId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withRefundOrderId(String refundOrderId) {
        this.setRefundOrderId(refundOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.refund_order_id
     *
     * @param refundOrderId the value for supply_chain..order_2c_roc_info.refund_order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.order_id
     *
     * @return the value of supply_chain..order_2c_roc_info.order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_roc_info.order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.atom_order_id
     *
     * @return the value of supply_chain..order_2c_roc_info.atom_order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..order_2c_roc_info.atom_order_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.cooperator_id
     *
     * @return the value of supply_chain..order_2c_roc_info.cooperator_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..order_2c_roc_info.cooperator_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.refunds_type
     *
     * @return the value of supply_chain..order_2c_roc_info.refunds_type
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getRefundsType() {
        return refundsType;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withRefundsType(String refundsType) {
        this.setRefundsType(refundsType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.refunds_type
     *
     * @param refundsType the value for supply_chain..order_2c_roc_info.refunds_type
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setRefundsType(String refundsType) {
        this.refundsType = refundsType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.reason
     *
     * @return the value of supply_chain..order_2c_roc_info.reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getReason() {
        return reason;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withReason(String reason) {
        this.setReason(reason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.reason
     *
     * @param reason the value for supply_chain..order_2c_roc_info.reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.remark
     *
     * @return the value of supply_chain..order_2c_roc_info.remark
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.remark
     *
     * @param remark the value for supply_chain..order_2c_roc_info.remark
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.picture
     *
     * @return the value of supply_chain..order_2c_roc_info.picture
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getPicture() {
        return picture;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withPicture(String picture) {
        this.setPicture(picture);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.picture
     *
     * @param picture the value for supply_chain..order_2c_roc_info.picture
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setPicture(String picture) {
        this.picture = picture;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.pictures_inner_url
     *
     * @return the value of supply_chain..order_2c_roc_info.pictures_inner_url
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getPicturesInnerUrl() {
        return picturesInnerUrl;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withPicturesInnerUrl(String picturesInnerUrl) {
        this.setPicturesInnerUrl(picturesInnerUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.pictures_inner_url
     *
     * @param picturesInnerUrl the value for supply_chain..order_2c_roc_info.pictures_inner_url
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setPicturesInnerUrl(String picturesInnerUrl) {
        this.picturesInnerUrl = picturesInnerUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.pictures_outer_url
     *
     * @return the value of supply_chain..order_2c_roc_info.pictures_outer_url
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getPicturesOuterUrl() {
        return picturesOuterUrl;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withPicturesOuterUrl(String picturesOuterUrl) {
        this.setPicturesOuterUrl(picturesOuterUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.pictures_outer_url
     *
     * @param picturesOuterUrl the value for supply_chain..order_2c_roc_info.pictures_outer_url
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setPicturesOuterUrl(String picturesOuterUrl) {
        this.picturesOuterUrl = picturesOuterUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.original_status
     *
     * @return the value of supply_chain..order_2c_roc_info.original_status
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Integer getOriginalStatus() {
        return originalStatus;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withOriginalStatus(Integer originalStatus) {
        this.setOriginalStatus(originalStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.original_status
     *
     * @param originalStatus the value for supply_chain..order_2c_roc_info.original_status
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setOriginalStatus(Integer originalStatus) {
        this.originalStatus = originalStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.inner_status
     *
     * @return the value of supply_chain..order_2c_roc_info.inner_status
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Integer getInnerStatus() {
        return innerStatus;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withInnerStatus(Integer innerStatus) {
        this.setInnerStatus(innerStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.inner_status
     *
     * @param innerStatus the value for supply_chain..order_2c_roc_info.inner_status
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.audit_id
     *
     * @return the value of supply_chain..order_2c_roc_info.audit_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getAuditId() {
        return auditId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withAuditId(String auditId) {
        this.setAuditId(auditId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.audit_id
     *
     * @param auditId the value for supply_chain..order_2c_roc_info.audit_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.audit_result
     *
     * @return the value of supply_chain..order_2c_roc_info.audit_result
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getAuditResult() {
        return auditResult;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withAuditResult(String auditResult) {
        this.setAuditResult(auditResult);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.audit_result
     *
     * @param auditResult the value for supply_chain..order_2c_roc_info.audit_result
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.audit_result_reason
     *
     * @return the value of supply_chain..order_2c_roc_info.audit_result_reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getAuditResultReason() {
        return auditResultReason;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withAuditResultReason(String auditResultReason) {
        this.setAuditResultReason(auditResultReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.audit_result_reason
     *
     * @param auditResultReason the value for supply_chain..order_2c_roc_info.audit_result_reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setAuditResultReason(String auditResultReason) {
        this.auditResultReason = auditResultReason;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.contactInfo
     *
     * @return the value of supply_chain..order_2c_roc_info.contactInfo
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getContactinfo() {
        return contactinfo;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withContactinfo(String contactinfo) {
        this.setContactinfo(contactinfo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.contactInfo
     *
     * @param contactinfo the value for supply_chain..order_2c_roc_info.contactInfo
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setContactinfo(String contactinfo) {
        this.contactinfo = contactinfo;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.receipt_id
     *
     * @return the value of supply_chain..order_2c_roc_info.receipt_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getReceiptId() {
        return receiptId;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withReceiptId(String receiptId) {
        this.setReceiptId(receiptId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.receipt_id
     *
     * @param receiptId the value for supply_chain..order_2c_roc_info.receipt_id
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.receipt_result
     *
     * @return the value of supply_chain..order_2c_roc_info.receipt_result
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getReceiptResult() {
        return receiptResult;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withReceiptResult(String receiptResult) {
        this.setReceiptResult(receiptResult);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.receipt_result
     *
     * @param receiptResult the value for supply_chain..order_2c_roc_info.receipt_result
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setReceiptResult(String receiptResult) {
        this.receiptResult = receiptResult;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.receipt_result_reason
     *
     * @return the value of supply_chain..order_2c_roc_info.receipt_result_reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public String getReceiptResultReason() {
        return receiptResultReason;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withReceiptResultReason(String receiptResultReason) {
        this.setReceiptResultReason(receiptResultReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.receipt_result_reason
     *
     * @param receiptResultReason the value for supply_chain..order_2c_roc_info.receipt_result_reason
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setReceiptResultReason(String receiptResultReason) {
        this.receiptResultReason = receiptResultReason;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.create_time
     *
     * @return the value of supply_chain..order_2c_roc_info.create_time
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_roc_info.create_time
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_roc_info.update_time
     *
     * @return the value of supply_chain..order_2c_roc_info.update_time
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public Order2cRocInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_roc_info.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_roc_info.update_time
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", refundOrderId=").append(refundOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", refundsType=").append(refundsType);
        sb.append(", reason=").append(reason);
        sb.append(", remark=").append(remark);
        sb.append(", picture=").append(picture);
        sb.append(", picturesInnerUrl=").append(picturesInnerUrl);
        sb.append(", picturesOuterUrl=").append(picturesOuterUrl);
        sb.append(", originalStatus=").append(originalStatus);
        sb.append(", innerStatus=").append(innerStatus);
        sb.append(", auditId=").append(auditId);
        sb.append(", auditResult=").append(auditResult);
        sb.append(", auditResultReason=").append(auditResultReason);
        sb.append(", contactinfo=").append(contactinfo);
        sb.append(", receiptId=").append(receiptId);
        sb.append(", receiptResult=").append(receiptResult);
        sb.append(", receiptResultReason=").append(receiptResultReason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cRocInfo other = (Order2cRocInfo) that;
        return (this.getRefundOrderId() == null ? other.getRefundOrderId() == null : this.getRefundOrderId().equals(other.getRefundOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getRefundsType() == null ? other.getRefundsType() == null : this.getRefundsType().equals(other.getRefundsType()))
            && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getPicture() == null ? other.getPicture() == null : this.getPicture().equals(other.getPicture()))
            && (this.getPicturesInnerUrl() == null ? other.getPicturesInnerUrl() == null : this.getPicturesInnerUrl().equals(other.getPicturesInnerUrl()))
            && (this.getPicturesOuterUrl() == null ? other.getPicturesOuterUrl() == null : this.getPicturesOuterUrl().equals(other.getPicturesOuterUrl()))
            && (this.getOriginalStatus() == null ? other.getOriginalStatus() == null : this.getOriginalStatus().equals(other.getOriginalStatus()))
            && (this.getInnerStatus() == null ? other.getInnerStatus() == null : this.getInnerStatus().equals(other.getInnerStatus()))
            && (this.getAuditId() == null ? other.getAuditId() == null : this.getAuditId().equals(other.getAuditId()))
            && (this.getAuditResult() == null ? other.getAuditResult() == null : this.getAuditResult().equals(other.getAuditResult()))
            && (this.getAuditResultReason() == null ? other.getAuditResultReason() == null : this.getAuditResultReason().equals(other.getAuditResultReason()))
            && (this.getContactinfo() == null ? other.getContactinfo() == null : this.getContactinfo().equals(other.getContactinfo()))
            && (this.getReceiptId() == null ? other.getReceiptId() == null : this.getReceiptId().equals(other.getReceiptId()))
            && (this.getReceiptResult() == null ? other.getReceiptResult() == null : this.getReceiptResult().equals(other.getReceiptResult()))
            && (this.getReceiptResultReason() == null ? other.getReceiptResultReason() == null : this.getReceiptResultReason().equals(other.getReceiptResultReason()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRefundOrderId() == null) ? 0 : getRefundOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getRefundsType() == null) ? 0 : getRefundsType().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getPicture() == null) ? 0 : getPicture().hashCode());
        result = prime * result + ((getPicturesInnerUrl() == null) ? 0 : getPicturesInnerUrl().hashCode());
        result = prime * result + ((getPicturesOuterUrl() == null) ? 0 : getPicturesOuterUrl().hashCode());
        result = prime * result + ((getOriginalStatus() == null) ? 0 : getOriginalStatus().hashCode());
        result = prime * result + ((getInnerStatus() == null) ? 0 : getInnerStatus().hashCode());
        result = prime * result + ((getAuditId() == null) ? 0 : getAuditId().hashCode());
        result = prime * result + ((getAuditResult() == null) ? 0 : getAuditResult().hashCode());
        result = prime * result + ((getAuditResultReason() == null) ? 0 : getAuditResultReason().hashCode());
        result = prime * result + ((getContactinfo() == null) ? 0 : getContactinfo().hashCode());
        result = prime * result + ((getReceiptId() == null) ? 0 : getReceiptId().hashCode());
        result = prime * result + ((getReceiptResult() == null) ? 0 : getReceiptResult().hashCode());
        result = prime * result + ((getReceiptResultReason() == null) ? 0 : getReceiptResultReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    public enum Column {
        refundOrderId("refund_order_id", "refundOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        refundsType("refunds_type", "refundsType", "VARCHAR", false),
        reason("reason", "reason", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        picture("picture", "picture", "VARCHAR", false),
        picturesInnerUrl("pictures_inner_url", "picturesInnerUrl", "VARCHAR", false),
        picturesOuterUrl("pictures_outer_url", "picturesOuterUrl", "VARCHAR", false),
        originalStatus("original_status", "originalStatus", "INTEGER", false),
        innerStatus("inner_status", "innerStatus", "INTEGER", false),
        auditId("audit_id", "auditId", "VARCHAR", false),
        auditResult("audit_result", "auditResult", "VARCHAR", false),
        auditResultReason("audit_result_reason", "auditResultReason", "VARCHAR", false),
        contactinfo("contactInfo", "contactinfo", "VARCHAR", false),
        receiptId("receipt_id", "receiptId", "VARCHAR", false),
        receiptResult("receipt_result", "receiptResult", "VARCHAR", false),
        receiptResultReason("receipt_result_reason", "receiptResultReason", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Apr 13 11:08:04 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}