package com.chinamobile.data.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/25 14:41
 */
@Data
public class ExcelAccountImport {

    @ExcelProperty("省份")
    private String province;

    @ExcelProperty("地市")
    private String cities;

    @ExcelProperty("区县")
    private String districts;

    @ExcelProperty("CODE")
    private String custCode;

    @ExcelProperty("姓名")
    private String registerName;

    @ExcelProperty("注册时间")
    private String regTime;

    @ExcelProperty("注册状态")
    private String registerState;

    @ExcelProperty("类型")
    private String type;

   /* @ExcelProperty("校验时间")
    private String lastTime;*/
}
