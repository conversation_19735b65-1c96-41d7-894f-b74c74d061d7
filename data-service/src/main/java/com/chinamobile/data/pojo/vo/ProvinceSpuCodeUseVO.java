package com.chinamobile.data.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/19
 * @description 按省份纬度的spu范式破零数据
 */
@Data
public class ProvinceSpuCodeUseVO {

    /**
     * 省份名称
     */
    @Excel(name = "省份")
    private String provinceName;

    /**
     * 产品增值服务包
     */
    @Excel(name = "产品增值服务包")
    private Integer productAddedValueUse = 0;

    /**
     * 合同履约
     */
    @Excel(name = "合同履约")
    private Integer contractComplianceUse = 0;

    /**
     * onenet独立服务
     */
    @Excel(name = "OneNET独立服务")
    private Integer onenetServiceUse = 0;

    /**
     * 联合销售
     */
    @Excel(name = "联合销售(代销)")
    private Integer unionUse = 0;

    /**
     * onepark独立服务
     */
    @Excel(name = "OnePark独立服务")
    private Integer oneParkServiceUse = 0;
    /**
     * 行车卫士标准产品
     */
    @Excel(name = "行车卫士标准产品")
    private Integer carTVStandardProductsUse = 0;
    /**
     * 行车卫士标准产品
     */
    @Excel(name = "软件服务")
    private Integer softwareService = 0;
    /**
     * OneCyber标准产品
     */
    @Excel(name = "OneCyber标准产品")
    private Integer oneCyberUse = 0;
    /**
     * 销售汇总
     */
    @Excel(name = "汇总数据")
    private Integer provinceTotalUse = 0;
}
