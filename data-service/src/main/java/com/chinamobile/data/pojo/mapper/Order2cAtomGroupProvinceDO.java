package com.chinamobile.data.pojo.mapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * mapper.Order2cInfoMapper.selectTotalPriceCountGroupByBeID的查询结果集，该代码由mybatis-plus-generator-ui自动生成
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Order2cAtomGroupProvinceDO {

    private String beId;

    private Long calculatePrice;

    private Integer orderCount;

    private Integer atomQuantityCount;

    private Long atomOrderAmount;

    private Integer skuQuantityCount;

}
