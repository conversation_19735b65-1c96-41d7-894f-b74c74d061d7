package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.OrderInfo;
import com.chinamobile.data.pojo.entity.OrderInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderInfoMapper {
    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    long countByExample(OrderInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int deleteByExample(OrderInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int insert(OrderInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int insertSelective(OrderInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    List<OrderInfo> selectByExample(OrderInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    OrderInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int updateByExampleSelective(@Param("record") OrderInfo record, @Param("example") OrderInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int updateByExample(@Param("record") OrderInfo record, @Param("example") OrderInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int updateByPrimaryKeySelective(OrderInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int updateByPrimaryKey(OrderInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int batchInsert(@Param("list") List<OrderInfo> list);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:09 CST 2022
     */
    int batchInsertSelective(@Param("list") List<OrderInfo> list, @Param("selective") OrderInfo.Column ... selective);
}