package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderAtomStatisticsProvinceMapper {
    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    long countByExample(OrderAtomStatisticsProvinceExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int deleteByExample(OrderAtomStatisticsProvinceExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int insert(OrderAtomStatisticsProvince record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int insertSelective(OrderAtomStatisticsProvince record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    List<OrderAtomStatisticsProvince> selectByExample(OrderAtomStatisticsProvinceExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    OrderAtomStatisticsProvince selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int updateByExampleSelective(@Param("record") OrderAtomStatisticsProvince record, @Param("example") OrderAtomStatisticsProvinceExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int updateByExample(@Param("record") OrderAtomStatisticsProvince record, @Param("example") OrderAtomStatisticsProvinceExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int updateByPrimaryKeySelective(OrderAtomStatisticsProvince record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int updateByPrimaryKey(OrderAtomStatisticsProvince record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int batchInsert(@Param("list") List<OrderAtomStatisticsProvince> list);

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    int batchInsertSelective(@Param("list") List<OrderAtomStatisticsProvince> list, @Param("selective") OrderAtomStatisticsProvince.Column ... selective);
}