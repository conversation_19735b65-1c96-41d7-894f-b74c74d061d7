package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.SpuOfferingInfo;
import com.chinamobile.data.pojo.entity.SpuOfferingInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SpuOfferingInfoMapper {
    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    long countByExample(SpuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int deleteByExample(SpuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int insert(SpuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int insertSelective(SpuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    List<SpuOfferingInfo> selectByExample(SpuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    SpuOfferingInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int updateByExampleSelective(@Param("record") SpuOfferingInfo record, @Param("example") SpuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int updateByExample(@Param("record") SpuOfferingInfo record, @Param("example") SpuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int updateByPrimaryKeySelective(SpuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int updateByPrimaryKey(SpuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int batchInsert(@Param("list") List<SpuOfferingInfo> list);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:06 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SpuOfferingInfo> list, @Param("selective") SpuOfferingInfo.Column ... selective);
}