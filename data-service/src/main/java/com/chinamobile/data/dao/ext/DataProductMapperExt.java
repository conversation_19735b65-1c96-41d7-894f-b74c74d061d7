package com.chinamobile.data.dao.ext;


import com.chinamobile.data.pojo.dto.DateDTO;
import com.chinamobile.data.pojo.mapper.DataProductSaleDO;
import com.chinamobile.data.pojo.mapper.DataProductUserOrderDO;
import com.chinamobile.data.pojo.mapper.DataProductUserTotalPriceDO;
import com.chinamobile.data.pojo.mapper.OrderStatisticsDO;
import com.chinamobile.data.pojo.mapper.ShelfStatisticsDO;
import com.chinamobile.data.pojo.param.TimeParam;
import com.chinamobile.data.pojo.vo.ProductOrderHotDataVO;
import com.chinamobile.data.pojo.vo.ProductOrderRealTimeVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7
 * @description 产品部门mapper类
 */
public interface DataProductMapperExt {


    /**
     * 获取用户情况的订单信息
     *
     * @param timeParam
     * @return
     */
    List<DataProductUserOrderDO> listDataProductUserOrder(@Param("timeParam") TimeParam timeParam);

    /**
     * 获取用户情况的销售额信息
     *
     * @param timeParam
     * @return
     */
    List<DataProductUserTotalPriceDO> listDataProductUserTotalPrice(@Param("timeParam") TimeParam timeParam);

    /**
     * 获取产品销售情况
     * @param timeParam
     * @return
     */
    List<DataProductSaleDO> listDataProductSale(@Param("timeParam") TimeParam timeParam);


    /**
     * 获取产品订单热销top15
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<ProductOrderHotDataVO> getProductOrderHotSell(String startTime, String endTime);

    /**
     * 查询产品订单实时今日数据
     *
     * @param startTime
     * @return
     */
    List<ProductOrderRealTimeVO> getTodayProductOrderData(String startTime);

    List<OrderStatisticsDO> departmentStatistics(String startTime, String endTime);

    OrderStatisticsDO totalStatistics(String startTime, String endTime);

    List<ShelfStatisticsDO> shelfStatistics(Date startTime, Date endTime);
}
