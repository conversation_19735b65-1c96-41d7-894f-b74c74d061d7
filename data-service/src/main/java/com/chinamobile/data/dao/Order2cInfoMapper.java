package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.Order2cInfo;
import com.chinamobile.data.pojo.entity.Order2cInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cInfoMapper {
    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    long countByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int deleteByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int deleteByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int insert(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int insertSelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    List<Order2cInfo> selectByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    Order2cInfo selectByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int updateByExampleSelective(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int updateByExample(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int updateByPrimaryKeySelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int updateByPrimaryKey(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int batchInsert(@Param("list") List<Order2cInfo> list);

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    int batchInsertSelective(@Param("list") List<Order2cInfo> list, @Param("selective") Order2cInfo.Column ... selective);
}