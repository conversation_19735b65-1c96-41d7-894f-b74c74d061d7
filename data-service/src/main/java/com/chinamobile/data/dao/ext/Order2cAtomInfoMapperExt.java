package com.chinamobile.data.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.data.pojo.mapper.*;
import com.chinamobile.data.pojo.param.OrderStatisticsParam;
import com.chinamobile.data.pojo.param.ProductRealNameSellParamProduct;
import com.chinamobile.data.pojo.param.ProductRunParam;
import com.chinamobile.data.pojo.vo.ProductOrderHotDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface Order2cAtomInfoMapperExt {
    OrderStatisticsScreenDO getOrderSumData(String startTime, String endTime, String areaCode);

    List<OrderStatisticsBySpuDO> getSpuDataList(String startTime, String endTime, String areaCode, Integer limit);

    List<OrderStatisticsByProvinceDO> getOrderAreaData(String startTime, String endTime, Integer areaType, String areaCode,Integer limit);

    List<Order2cAtomGroupProvinceDO> countOrderProvince(String startTime, String endTime);

    List<Order2cAtomGroupProvinceDO> countOrderProvinceFail(String startTime, String endTime);

    List<Order2cAtomGroupSpuDO> countOrderSpu(String startTime, String endTime);

    List<Order2cAtomGroupSpuDO> countOrderSpuFail(String startTime, String endTime);

    List<OrderStatisticsBySpuTypeDO> getOrderAmountBySpuType(String startTime, String endTime);

    List<OrderTimeStatisticsWithSpuDO> getOrderTimeStatisticsWithSpu(String startTime, String endTime);

    List<OrderTimeStatisticsWithProvinceDO> getOrderTimeStatisticsWithProvince(String startTime, String endTime);

    List<OrderTimeStatisticsDO> getOrderTimeStatistics(String startTime, String endTime);

    /**
     * 根据时间段及时间类型获取订单详情列表
     *
     * @param orderStatisticsParam
     * @return
     */
    List<OrderMarketMessageDO> getOrderDetailsList(OrderStatisticsParam orderStatisticsParam);

    /**
     * 按省份获取范式销售额
     *
     * @param productRunParam
     * @return
     */
    List<ProvinceOfferingClassTotalSellDO> listOfferingClassTotalSellByProvince(@Param("productRunParam") ProductRunParam productRunParam);

    /**
     * 按省份获取spu范式破零数据
     *
     * @param productRunParam
     * @return
     */
    List<ProvinceSpuCodeUseDO> listSpuCodeUseByProvince(@Param("productRunParam") ProductRunParam productRunParam);

    /**
     * 分页获取实际销售产品名称销售额
     *
     * @param page
     * @param productRealNameSellParam
     * @return
     */
    List<ProductRealNameSellDO> listProductRealNameSell(@Param("page") Page page, @Param("productRealNameSellParam") ProductRealNameSellParamProduct productRealNameSellParam);

    /**
     * 获取实际销售产品名称销售额
     *
     * @param productRealNameSellParam
     * @return
     */
    List<ProductRealNameSellDO> listProductRealNameSell(@Param("productRealNameSellParam") ProductRealNameSellParamProduct productRealNameSellParam);

    /**
     * 获取部门销售额
     * @param productRunParam
     * @return
     */
    List<DepartmentSellDO> listDepartmentSell(@Param("productRunParam") ProductRunParam productRunParam);

    String getFirstOrderTime();

    /**
     * 获取订单破零的省份数量
     */
    Integer getOrderProvinceCount(String startTime, String endTime);
}