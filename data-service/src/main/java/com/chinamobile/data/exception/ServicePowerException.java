package com.chinamobile.data.exception;

import lombok.Data;

/**
 * <AUTHOR> liang
 * @date : 2021/8/30 14:16
 * @description: 服务异常类
 **/
@Data
public class ServicePowerException extends RuntimeException{

    private Integer code;
    private String serviceMsg;

    public ServicePowerException(String msg) {
        super(msg);
    }

    public ServicePowerException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }
    public ServicePowerException(Integer code, String msg, String serviceMsg){
        super(msg);
        this.code = code;
        this.serviceMsg = serviceMsg;
    }
}
