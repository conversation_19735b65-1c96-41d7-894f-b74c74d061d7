package com.chinamobile.data.task;


import com.chinamobile.data.service.DataSyncService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;

/**
 * 定时任务统计
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
@Slf4j
public class DataStatisticsTask {
    @Resource
    private DataSyncService dataSyncService;

    // 凌晨一点执行
    @Scheduled(cron = "0 0 1 * * ?")
    private void countTask() {
        Date today = new Date();
        log.info("开始统计任务{}",today);
        Date yesterday = DateUtils.addDay(today,-1);
        String yesterdayStr = DateUtils.dateToStr(yesterday,DateUtils.DEFAULT_DATE_FORMAT);
        //为保证数据的准确性，避免任务遗漏执行（如上线导致服务暂时不可用等原因），每天对过去3天的数据进行覆盖同步。
        for(int i=0;i<3;i++){
            dataSyncService.countOrder2cByProvince(yesterdayStr);
            dataSyncService.countOrder2cBySpu(yesterdayStr);
            try {
                Date previousDate = DateTimeUtil.addDay(DateTimeUtil.getFormatDate(yesterdayStr, DateUtils.DEFAULT_DATE_FORMAT),-1);
                yesterdayStr = DateTimeUtil.formatDate(previousDate,DateUtils.DEFAULT_DATE_FORMAT) ;
            } catch (ParseException e) {
                e.printStackTrace();
            }

        }
    }

}
