<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.ext.ShopManagerInfoMapperExt">

  <select id="listShopManager" resultType="com.chinamobile.data.pojo.mapper.ShopManagerDO">
        select
          smi.province_name provinceName,
          smi.city_name cityName,
          smi.region_name regionName,
          smi.create_oper_code createOperCode,
          smi.customer_manager_name customerManagerName,
          smi.employee_num employeeNum,
          DATE_FORMAT(smi.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
          case
              when smi.mrg_status = 1 then '正常'
              when smi.mrg_status = 2 then '挂起'
              when smi.mrg_status = 3 then '禁用'
          else '错误状态'
          end mrgStatusName,
          sum(smih.mrg_invitation_register_successful_quantity) mrgInvitationRegisterSuccessfulQuantity,
          sum(smih.number_logins) numberLogins
        from
          shop_manager_info smi
        left join shop_manager_info_history smih on smi.create_oper_code = smih.create_oper_code
              and smi.be_id = smih.be_id
              and smi.location = smih.location
              and smi.region_id = smih.region_id
        where
          1=1
      <if test="pageShopManagerParam.beId != null and pageShopManagerParam.beId != ''">
          and smi.be_id = #{pageShopManagerParam.beId}
      </if>
      <if test="pageShopManagerParam.beginRegisterDate != null and pageShopManagerParam.beginRegisterDate != ''">
          and DATE_FORMAT(smi.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopManagerParam.beginRegisterDate}
      </if>
      <if test="pageShopManagerParam.endRegisterDate != null and pageShopManagerParam.endRegisterDate != ''">
          and DATE_FORMAT(smi.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopManagerParam.endRegisterDate}
      </if>
      <if test="pageShopManagerParam.mrgStatus != null and pageShopManagerParam.mrgStatus != ''">
          and smi.mrg_status = #{pageShopManagerParam.mrgStatus}
      </if>
      <if test="pageShopManagerParam.beginRegisterLoginDate != null and pageShopManagerParam.beginRegisterLoginDate != ''">
          and DATE_FORMAT(smih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopManagerParam.beginRegisterLoginDate}
      </if>
      <if test="pageShopManagerParam.endRegisterLoginDate != null and pageShopManagerParam.endRegisterLoginDate != ''">
          and DATE_FORMAT(smih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopManagerParam.endRegisterLoginDate}
      </if>
      group by smi.be_id,smi.location,smi.region_id,binary smi.create_oper_code
      <if test="pageShopManagerParam.beginRegisterLoginDate != null and pageShopManagerParam.beginRegisterLoginDate != ''">
          union
          select
          smi.province_name provinceName,
          smi.city_name cityName,
          smi.region_name regionName,
          smi.create_oper_code createOperCode,
          smi.customer_manager_name customerManagerName,
          smi.employee_num employeeNum,
          DATE_FORMAT(smi.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
          case
          when smi.mrg_status = 1 then '正常'
          when smi.mrg_status = 2 then '挂起'
          when smi.mrg_status = 3 then '禁用'
          else '错误状态'
          end mrgStatusName,
          0 mrgInvitationRegisterSuccessfulQuantity,
          0 numberLogins
          from
          shop_manager_info smi
          left join shop_manager_info_history smih on smi.create_oper_code = smih.create_oper_code
          and smi.be_id = smih.be_id
          and smi.location = smih.location
          and smi.region_id = smih.region_id
          where
          1=1
          <if test="pageShopManagerParam.beId != null and pageShopManagerParam.beId != ''">
              and smi.be_id = #{pageShopManagerParam.beId}
          </if>
          <if test="pageShopManagerParam.beginRegisterDate != null and pageShopManagerParam.beginRegisterDate != ''">
              and DATE_FORMAT(smi.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopManagerParam.beginRegisterDate}
          </if>
          <if test="pageShopManagerParam.endRegisterDate != null and pageShopManagerParam.endRegisterDate != ''">
              and DATE_FORMAT(smi.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopManagerParam.endRegisterDate}
          </if>
          <if test="pageShopManagerParam.mrgStatus != null and pageShopManagerParam.mrgStatus != ''">
              and smi.mrg_status = #{pageShopManagerParam.mrgStatus}
          </if>
          <if test="pageShopManagerParam.beginRegisterLoginDate != null and pageShopManagerParam.beginRegisterLoginDate != ''">
              and smi.create_oper_code not in
              (select
              smi.create_oper_code
              from
              shop_manager_info smi
              left join shop_manager_info_history smih on smi.create_oper_code = smih.create_oper_code
              and smi.be_id = smih.be_id
              and smi.location = smih.location
              and smi.region_id = smih.region_id
              where
              DATE_FORMAT(smih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopManagerParam.beginRegisterLoginDate}
              and DATE_FORMAT(smih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopManagerParam.endRegisterLoginDate}
              )
          </if>
          <if test="pageShopManagerParam.endRegisterLoginDate != null and pageShopManagerParam.endRegisterLoginDate != ''">
              and smi.create_oper_code in
              (select
              smi.create_oper_code
              from
              shop_manager_info smi
              where
              DATE_FORMAT(smi.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopManagerParam.endRegisterLoginDate}
              )
          </if>
          group by smi.be_id,smi.location,smi.region_id,binary smi.create_oper_code
      </if>
  </select>

    <select id="getManagerCount"  parameterType="java.util.Date"  resultType="java.lang.Long">
        select count(id)
        from shop_manager_info where 1=1
        <if test="startTime != null">
            and register_date <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and register_date <![CDATA[ <= ]]> #{endTime}
        </if>
    </select>
    <select id="getManagerCountByProvince"  resultType="java.lang.Long">
        select
        count(id)
        from shop_manager_info
        where
        1=1
        <if test="landName != null and landName !=''">
            and case when #{areaType}=0 then province_name =#{landName}
            when #{areaType}=1 then city_name =#{landName}
            when #{areaType}=2 then region_name =#{landName} end
        </if>
        <if test="startTime != null">
            and register_date <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and register_date <![CDATA[ <= ]]> #{endTime}
        </if>
    </select>
</mapper>