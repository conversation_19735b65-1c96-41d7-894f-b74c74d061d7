<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.OrderAtomStatisticsProvinceMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="atom_order_amount" jdbcType="BIGINT" property="atomOrderAmount" />
    <result column="atom_order_quantity" jdbcType="INTEGER" property="atomOrderQuantity" />
    <result column="sku_order_quantity" jdbcType="INTEGER" property="skuOrderQuantity" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, province, order_count, order_amount, atom_order_amount, atom_order_quantity, 
    sku_order_quantity, year, month, day, data_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_atom_statistics_province
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_atom_statistics_province
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_atom_statistics_province
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_atom_statistics_province
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_atom_statistics_province (id, province, order_count, 
      order_amount, atom_order_amount, atom_order_quantity, 
      sku_order_quantity, year, month, 
      day, data_time, create_time
      )
    values (#{id,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{orderCount,jdbcType=INTEGER}, 
      #{orderAmount,jdbcType=BIGINT}, #{atomOrderAmount,jdbcType=BIGINT}, #{atomOrderQuantity,jdbcType=INTEGER}, 
      #{skuOrderQuantity,jdbcType=INTEGER}, #{year,jdbcType=INTEGER}, #{month,jdbcType=INTEGER}, 
      #{day,jdbcType=INTEGER}, #{dataTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_atom_statistics_province
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="atomOrderAmount != null">
        atom_order_amount,
      </if>
      <if test="atomOrderQuantity != null">
        atom_order_quantity,
      </if>
      <if test="skuOrderQuantity != null">
        sku_order_quantity,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="month != null">
        month,
      </if>
      <if test="day != null">
        day,
      </if>
      <if test="dataTime != null">
        data_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="atomOrderAmount != null">
        #{atomOrderAmount,jdbcType=BIGINT},
      </if>
      <if test="atomOrderQuantity != null">
        #{atomOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="skuOrderQuantity != null">
        #{skuOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        #{month,jdbcType=INTEGER},
      </if>
      <if test="day != null">
        #{day,jdbcType=INTEGER},
      </if>
      <if test="dataTime != null">
        #{dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_atom_statistics_province
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_atom_statistics_province
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCount != null">
        order_count = #{record.orderCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.atomOrderAmount != null">
        atom_order_amount = #{record.atomOrderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.atomOrderQuantity != null">
        atom_order_quantity = #{record.atomOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.skuOrderQuantity != null">
        sku_order_quantity = #{record.skuOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.month != null">
        month = #{record.month,jdbcType=INTEGER},
      </if>
      <if test="record.day != null">
        day = #{record.day,jdbcType=INTEGER},
      </if>
      <if test="record.dataTime != null">
        data_time = #{record.dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_atom_statistics_province
    set id = #{record.id,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      order_count = #{record.orderCount,jdbcType=INTEGER},
      order_amount = #{record.orderAmount,jdbcType=BIGINT},
      atom_order_amount = #{record.atomOrderAmount,jdbcType=BIGINT},
      atom_order_quantity = #{record.atomOrderQuantity,jdbcType=INTEGER},
      sku_order_quantity = #{record.skuOrderQuantity,jdbcType=INTEGER},
      year = #{record.year,jdbcType=INTEGER},
      month = #{record.month,jdbcType=INTEGER},
      day = #{record.day,jdbcType=INTEGER},
      data_time = #{record.dataTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_atom_statistics_province
    <set>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="atomOrderAmount != null">
        atom_order_amount = #{atomOrderAmount,jdbcType=BIGINT},
      </if>
      <if test="atomOrderQuantity != null">
        atom_order_quantity = #{atomOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="skuOrderQuantity != null">
        sku_order_quantity = #{skuOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        month = #{month,jdbcType=INTEGER},
      </if>
      <if test="day != null">
        day = #{day,jdbcType=INTEGER},
      </if>
      <if test="dataTime != null">
        data_time = #{dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_atom_statistics_province
    set province = #{province,jdbcType=VARCHAR},
      order_count = #{orderCount,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      atom_order_amount = #{atomOrderAmount,jdbcType=BIGINT},
      atom_order_quantity = #{atomOrderQuantity,jdbcType=INTEGER},
      sku_order_quantity = #{skuOrderQuantity,jdbcType=INTEGER},
      year = #{year,jdbcType=INTEGER},
      month = #{month,jdbcType=INTEGER},
      day = #{day,jdbcType=INTEGER},
      data_time = #{dataTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_atom_statistics_province
    (id, province, order_count, order_amount, atom_order_amount, atom_order_quantity, 
      sku_order_quantity, year, month, day, data_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.orderCount,jdbcType=INTEGER}, 
        #{item.orderAmount,jdbcType=BIGINT}, #{item.atomOrderAmount,jdbcType=BIGINT}, #{item.atomOrderQuantity,jdbcType=INTEGER}, 
        #{item.skuOrderQuantity,jdbcType=INTEGER}, #{item.year,jdbcType=INTEGER}, #{item.month,jdbcType=INTEGER}, 
        #{item.day,jdbcType=INTEGER}, #{item.dataTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 22 15:26:30 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_atom_statistics_province (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'order_count'.toString() == column.value">
          #{item.orderCount,jdbcType=INTEGER}
        </if>
        <if test="'order_amount'.toString() == column.value">
          #{item.orderAmount,jdbcType=BIGINT}
        </if>
        <if test="'atom_order_amount'.toString() == column.value">
          #{item.atomOrderAmount,jdbcType=BIGINT}
        </if>
        <if test="'atom_order_quantity'.toString() == column.value">
          #{item.atomOrderQuantity,jdbcType=INTEGER}
        </if>
        <if test="'sku_order_quantity'.toString() == column.value">
          #{item.skuOrderQuantity,jdbcType=INTEGER}
        </if>
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=INTEGER}
        </if>
        <if test="'month'.toString() == column.value">
          #{item.month,jdbcType=INTEGER}
        </if>
        <if test="'day'.toString() == column.value">
          #{item.day,jdbcType=INTEGER}
        </if>
        <if test="'data_time'.toString() == column.value">
          #{item.dataTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>