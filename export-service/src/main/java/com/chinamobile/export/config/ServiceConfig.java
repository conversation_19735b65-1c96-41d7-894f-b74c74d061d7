package com.chinamobile.export.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/4/17 17:56
 */
@Component
@Data
public class ServiceConfig {

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${sms.newMessage:107024}")
    private String newMessageSms;

    private Integer orderExportExcelExpireDays = 3;

    @Value("${iot.sm4Key}")
    private String sm4Key;

    @Value("${iot.sm4Iv}")
    private String sm4Iv;

}
