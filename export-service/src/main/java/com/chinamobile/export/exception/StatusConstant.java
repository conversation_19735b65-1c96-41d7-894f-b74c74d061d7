package com.chinamobile.export.exception;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

/**
 * @Author: YSC
 * @Date: 2021/11/10 15:54
 * @Description: 异常返回映射
 */
public class StatusConstant extends BaseErrorConstant {
    private static final  String PREF = "80";
    public static final ExcepStatus ORDER_NOT_EXIST = createInstance(PREF+"001", "订单不存在");

    public static final ExcepStatus ORDER_PARAM_MUST = createInstance(PREF+"002", "订单地市参数必传");

    public static final ExcepStatus ORDER_PARAM_BID = createInstance(PREF+"003", "未找到默认的省份配置，请重新配置省管理员省份");

}
