package com.chinamobile.iot.cache.dao.ext;

import com.chinamobile.iot.cache.pojo.mapper.SpuQuantityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/15 14:11
 * @description TODO
 */
@Mapper
public interface SpuOfferingInfoMapperExt {

    /**
     * 商品管理-添加商品-查询商品列表
     */
    List<SpuQuantityDO> querySpuQuantity(@Param("spuCodeList") List<String> spuCodeList);

}
