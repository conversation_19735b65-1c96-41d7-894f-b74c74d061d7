package com.chinamobile.install.service;

import com.chinamobile.install.pojo.param.AfterMarketOrderImportParam;
import com.chinamobile.install.service.impl.AfterMarketOrderWebServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: dgj
 * @Date: 2024/12/26
 * @Description: 售后订单导入功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AfterMarketOrderImportTest {

    /**
     * 测试新增字段的设置
     */
    @Test
    public void testNewFieldsMapping() {
        AfterMarketOrderImportParam param = new AfterMarketOrderImportParam();
        
        // 设置基本字段
        param.setServicePackageCode("TEST_SERVICE_001");
        param.setOrderQuantity(1);
        param.setAppointmentName("测试用户");
        param.setAppointmentPhone("13800138000");
        param.setAppointmentProvince("北京市");
        param.setAppointmentCity("北京市");
        param.setAppointmentDistrict("朝阳区");
        param.setDetailAddress("测试详细地址");
        param.setAppointmentDate("2024-12-26 10:00");
        
        // 设置新增的版本字段
        param.setAfterMarketVersion("V1.0");
        param.setSkuOfferingCode("SKU_001");
        param.setSkuOfferingVersion("V1.0");
        param.setAtomOfferingCode("ATOM_001");
        param.setAtomOfferingVersion("V1.0");
        
        // 验证字段设置
        assert param.getAfterMarketVersion().equals("V1.0");
        assert param.getSkuOfferingCode().equals("SKU_001");
        assert param.getSkuOfferingVersion().equals("V1.0");
        assert param.getAtomOfferingCode().equals("ATOM_001");
        assert param.getAtomOfferingVersion().equals("V1.0");
        
        System.out.println("新增字段测试通过");
    }

    /**
     * 测试版本信息字符串生成
     */
    @Test
    public void testVersionInfoGeneration() {
        AfterMarketOrderImportParam param = new AfterMarketOrderImportParam();
        param.setAfterMarketVersion("V1.0");
        param.setSkuOfferingCode("SKU_001");
        param.setSkuOfferingVersion("V1.0");
        param.setAtomOfferingCode("ATOM_001");
        param.setAtomOfferingVersion("V1.0");
        
        // 这里可以测试版本信息的生成逻辑
        // 实际测试需要访问私有方法，这里只是示例
        System.out.println("版本信息生成测试通过");
    }

    /**
     * 测试空值处理
     */
    @Test
    public void testNullValueHandling() {
        AfterMarketOrderImportParam param = new AfterMarketOrderImportParam();
        
        // 设置基本字段
        param.setServicePackageCode("TEST_SERVICE_001");
        param.setOrderQuantity(1);
        param.setAppointmentName("测试用户");
        param.setAppointmentPhone("13800138000");
        param.setAppointmentProvince("北京市");
        param.setAppointmentCity("北京市");
        param.setAppointmentDistrict("朝阳区");
        param.setDetailAddress("测试详细地址");
        param.setAppointmentDate("2024-12-26 10:00");
        
        // 新增字段保持为null，测试空值处理
        assert param.getAfterMarketVersion() == null;
        assert param.getSkuOfferingCode() == null;
        assert param.getSkuOfferingVersion() == null;
        assert param.getAtomOfferingCode() == null;
        assert param.getAtomOfferingVersion() == null;
        
        System.out.println("空值处理测试通过");
    }
}
