package com.chinamobile.install.enums;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public enum SheetStateEnum {

    RUNNING("S01", "运行中"),
    ARCHIVED("S02", "已归档");

    private String code;
    private String name;

    SheetStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(String code) {
        for (SheetStateEnum sheetStateEnum : SheetStateEnum.values()) {
            if (sheetStateEnum.getCode().equals(code)) {
                return sheetStateEnum.getName();
            }
        }
        return null;
    }

    public static boolean isValid(String code) {
        for (SheetStateEnum sheetStateEnum : SheetStateEnum.values()) {
            if (sheetStateEnum.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
