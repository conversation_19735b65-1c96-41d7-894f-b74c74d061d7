package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.AfterMarketStdService;
import com.chinamobile.install.pojo.entity.AfterMarketStdServiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AfterMarketStdServiceMapper {
    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    long countByExample(AfterMarketStdServiceExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int deleteByExample(AfterMarketStdServiceExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int insert(AfterMarketStdService record);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int insertSelective(AfterMarketStdService record);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    List<AfterMarketStdService> selectByExample(AfterMarketStdServiceExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    AfterMarketStdService selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int updateByExampleSelective(@Param("record") AfterMarketStdService record, @Param("example") AfterMarketStdServiceExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int updateByExample(@Param("record") AfterMarketStdService record, @Param("example") AfterMarketStdServiceExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int updateByPrimaryKeySelective(AfterMarketStdService record);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int updateByPrimaryKey(AfterMarketStdService record);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int batchInsert(@Param("list") List<AfterMarketStdService> list);

    /**
     *
     * @mbg.generated Mon May 26 16:34:26 CST 2025
     */
    int batchInsertSelective(@Param("list") List<AfterMarketStdService> list, @Param("selective") AfterMarketStdService.Column ... selective);
}