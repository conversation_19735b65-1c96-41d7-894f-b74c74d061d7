package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.UserInstall;
import com.chinamobile.install.pojo.entity.UserInstallExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserInstallMapper {
    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    long countByExample(UserInstallExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int deleteByExample(UserInstallExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int insert(UserInstall record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int insertSelective(UserInstall record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    List<UserInstall> selectByExample(UserInstallExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    UserInstall selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int updateByExampleSelective(@Param("record") UserInstall record, @Param("example") UserInstallExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int updateByExample(@Param("record") UserInstall record, @Param("example") UserInstallExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int updateByPrimaryKeySelective(UserInstall record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int updateByPrimaryKey(UserInstall record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int batchInsert(@Param("list") List<UserInstall> list);

    /**
     *
     * @mbg.generated Mon May 26 16:36:43 CST 2025
     */
    int batchInsertSelective(@Param("list") List<UserInstall> list, @Param("selective") UserInstall.Column ... selective);
}