package com.chinamobile.install.pojo.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class AfterMarketProductSimpleInfoVO {

    private BaseInfo baseInfo;

    private StandardService standardService;

    private CooperatorInfo cooperatorInfo;

    //硬件名称
    private String hardwareName;

    //型号
    private String model;

    //颜色
    private String color;

    //备注1
    private String remark1;

    //备注2
    private String remark2;

    //备注3
    private String remark3;

    //接单方式 1--OS接单； 2--省内接单
    @Range(min = 1,max = 2,message = "接单方式错误")
    private Integer orderTakeType;

    //省侧装维平台名称
    private String provinceInstallPlatformName;

    //省侧装维平台编码
    private String provinceInstallPlatformCode;

    @Data
    //基本信息
    public static class BaseInfo{
        //spu主图链接
        private String spuImgUrl;

        //商品类型
        private String type = "售后服务商品";

        //售后服务包名称
        private String afterMarketInternalName;

        //售后服务包对外名称
        private String afterMarketExternalName;

        //售后服务包编码
        private String afterMarketCode;

        //售后服务类型 枚举值： 1：OneNET/OnePark属地化服务 2：铁通增值服务
        private String aftermarketType;

        //销售单价
        private String sellPrice;

        //结算单价
        private String settlePrice;

        //计量单位
        private String unit;

        //是否必选 枚举值 0:非必选 1:必选
        private String mandatory;

        //关联商品类型名称
        private String spuOfferingClassName;

        //关联规格商品编码
        private String skuOfferingCode;

        //关联原子商品编码
        private String atomOfferingCode;

        /**
         * 售后商品版本号
         */
        private String afterMarketVersion;

        /**
         * 销售商品版本号
         */
        private String spuOfferingVersion;

        /**
         * 规格商品版本号
         */
        private String skuOfferingVersion;

        /**
         * 原子商品版本号
         */
        private String atomOfferingVersion;

    }

    //标准服务信息
    @Data
    public static class StandardService {
        //标准服务编码
        private String code;

        //标准服务名称
        private String name;

        //产品部门
        private String department;

        //实质产品名称
        private String realProductName;

        //产品属性
        private String property;
    }

    @Data
    //合伙人信息
    public static class CooperatorInfo{
        //装维管理员用户id
        private String installManagerId;

        //装维管理员用户名称
        private String installManagerName;

        //装维主账号用户id
        private String adminCooperatorId;

        //合作伙伴名称
        private String partnerName;

        //合作伙伴联系人姓名
        private String cooperatorName;

        //电话
        private String phone;

        //邮箱
        private String email;

    }

}
