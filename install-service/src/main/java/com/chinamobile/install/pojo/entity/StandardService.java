package com.chinamobile.install.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 标准服务
 *
 * <AUTHOR>
public class StandardService implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String id;

    /**
     * 标准服务名称
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String name;

    /**
     * 产品部门ID
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private Integer productDepartmentId;

    /**
     * 实质产品名称
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String realProductName;

    /**
     * 产品属性ID
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String productPropertyId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private Date updateTime;

    /**
     * 备注1
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String remark1;

    /**
     * 备注2
     *
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private String remark2;

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..standard_service.id
     *
     * @return the value of supply_chain..standard_service.id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.id
     *
     * @param id the value for supply_chain..standard_service.id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.name
     *
     * @return the value of supply_chain..standard_service.name
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.name
     *
     * @param name the value for supply_chain..standard_service.name
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.product_department_id
     *
     * @return the value of supply_chain..standard_service.product_department_id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public Integer getProductDepartmentId() {
        return productDepartmentId;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withProductDepartmentId(Integer productDepartmentId) {
        this.setProductDepartmentId(productDepartmentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.product_department_id
     *
     * @param productDepartmentId the value for supply_chain..standard_service.product_department_id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setProductDepartmentId(Integer productDepartmentId) {
        this.productDepartmentId = productDepartmentId;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.real_product_name
     *
     * @return the value of supply_chain..standard_service.real_product_name
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getRealProductName() {
        return realProductName;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withRealProductName(String realProductName) {
        this.setRealProductName(realProductName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.real_product_name
     *
     * @param realProductName the value for supply_chain..standard_service.real_product_name
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setRealProductName(String realProductName) {
        this.realProductName = realProductName;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.product_property_id
     *
     * @return the value of supply_chain..standard_service.product_property_id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getProductPropertyId() {
        return productPropertyId;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withProductPropertyId(String productPropertyId) {
        this.setProductPropertyId(productPropertyId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.product_property_id
     *
     * @param productPropertyId the value for supply_chain..standard_service.product_property_id
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setProductPropertyId(String productPropertyId) {
        this.productPropertyId = productPropertyId;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.create_time
     *
     * @return the value of supply_chain..standard_service.create_time
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.create_time
     *
     * @param createTime the value for supply_chain..standard_service.create_time
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.update_time
     *
     * @return the value of supply_chain..standard_service.update_time
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.update_time
     *
     * @param updateTime the value for supply_chain..standard_service.update_time
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.remark1
     *
     * @return the value of supply_chain..standard_service.remark1
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getRemark1() {
        return remark1;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withRemark1(String remark1) {
        this.setRemark1(remark1);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.remark1
     *
     * @param remark1 the value for supply_chain..standard_service.remark1
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    /**
     * This method returns the value of the database column supply_chain..standard_service.remark2
     *
     * @return the value of supply_chain..standard_service.remark2
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public String getRemark2() {
        return remark2;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public StandardService withRemark2(String remark2) {
        this.setRemark2(remark2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..standard_service.remark2
     *
     * @param remark2 the value for supply_chain..standard_service.remark2
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", productDepartmentId=").append(productDepartmentId);
        sb.append(", realProductName=").append(realProductName);
        sb.append(", productPropertyId=").append(productPropertyId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", remark1=").append(remark1);
        sb.append(", remark2=").append(remark2);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StandardService other = (StandardService) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getProductDepartmentId() == null ? other.getProductDepartmentId() == null : this.getProductDepartmentId().equals(other.getProductDepartmentId()))
            && (this.getRealProductName() == null ? other.getRealProductName() == null : this.getRealProductName().equals(other.getRealProductName()))
            && (this.getProductPropertyId() == null ? other.getProductPropertyId() == null : this.getProductPropertyId().equals(other.getProductPropertyId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getRemark1() == null ? other.getRemark1() == null : this.getRemark1().equals(other.getRemark1()))
            && (this.getRemark2() == null ? other.getRemark2() == null : this.getRemark2().equals(other.getRemark2()));
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getProductDepartmentId() == null) ? 0 : getProductDepartmentId().hashCode());
        result = prime * result + ((getRealProductName() == null) ? 0 : getRealProductName().hashCode());
        result = prime * result + ((getProductPropertyId() == null) ? 0 : getProductPropertyId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getRemark1() == null) ? 0 : getRemark1().hashCode());
        result = prime * result + ((getRemark2() == null) ? 0 : getRemark2().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        productDepartmentId("product_department_id", "productDepartmentId", "INTEGER", false),
        realProductName("real_product_name", "realProductName", "VARCHAR", false),
        productPropertyId("product_property_id", "productPropertyId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        remark1("remark1", "remark1", "VARCHAR", false),
        remark2("remark2", "remark2", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue May 27 10:02:20 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}