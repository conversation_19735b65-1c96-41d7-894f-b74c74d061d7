package com.chinamobile.install.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AfterMarketOrder2cInfoExample {
    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        AfterMarketOrder2cInfoExample example = new AfterMarketOrder2cInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andServiceOrderIdIsNull() {
            addCriterion("service_order_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdIsNotNull() {
            addCriterion("service_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdEqualTo(String value) {
            addCriterion("service_order_id =", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotEqualTo(String value) {
            addCriterion("service_order_id <>", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThan(String value) {
            addCriterion("service_order_id >", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("service_order_id >=", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThan(String value) {
            addCriterion("service_order_id <", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanOrEqualTo(String value) {
            addCriterion("service_order_id <=", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLike(String value) {
            addCriterion("service_order_id like", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotLike(String value) {
            addCriterion("service_order_id not like", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdIn(List<String> values) {
            addCriterion("service_order_id in", values, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotIn(List<String> values) {
            addCriterion("service_order_id not in", values, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdBetween(String value1, String value2) {
            addCriterion("service_order_id between", value1, value2, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotBetween(String value1, String value2) {
            addCriterion("service_order_id not between", value1, value2, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdIsNull() {
            addCriterion("offering_order_id is null");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdIsNotNull() {
            addCriterion("offering_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdEqualTo(String value) {
            addCriterion("offering_order_id =", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdNotEqualTo(String value) {
            addCriterion("offering_order_id <>", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdGreaterThan(String value) {
            addCriterion("offering_order_id >", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("offering_order_id >=", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLessThan(String value) {
            addCriterion("offering_order_id <", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLessThanOrEqualTo(String value) {
            addCriterion("offering_order_id <=", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("offering_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLike(String value) {
            addCriterion("offering_order_id like", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdNotLike(String value) {
            addCriterion("offering_order_id not like", value, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdIn(List<String> values) {
            addCriterion("offering_order_id in", values, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdNotIn(List<String> values) {
            addCriterion("offering_order_id not in", values, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdBetween(String value1, String value2) {
            addCriterion("offering_order_id between", value1, value2, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdNotBetween(String value1, String value2) {
            addCriterion("offering_order_id not between", value1, value2, "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusTimeIsNull() {
            addCriterion("status_time is null");
            return (Criteria) this;
        }

        public Criteria andStatusTimeIsNotNull() {
            addCriterion("status_time is not null");
            return (Criteria) this;
        }

        public Criteria andStatusTimeEqualTo(Date value) {
            addCriterion("status_time =", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotEqualTo(Date value) {
            addCriterion("status_time <>", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThan(Date value) {
            addCriterion("status_time >", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("status_time >=", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThan(Date value) {
            addCriterion("status_time <", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("status_time <=", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusTimeIn(List<Date> values) {
            addCriterion("status_time in", values, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotIn(List<Date> values) {
            addCriterion("status_time not in", values, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeBetween(Date value1, Date value2) {
            addCriterion("status_time between", value1, value2, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("status_time not between", value1, value2, "statusTime");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(String value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(String value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(String value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(String value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(String value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(String value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLike(String value) {
            addCriterion("total_price like", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotLike(String value) {
            addCriterion("total_price not like", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<String> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<String> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(String value1, String value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(String value1, String value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeIsNull() {
            addCriterion("appointment_submit_type is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeIsNotNull() {
            addCriterion("appointment_submit_type is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeEqualTo(Integer value) {
            addCriterion("appointment_submit_type =", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeNotEqualTo(Integer value) {
            addCriterion("appointment_submit_type <>", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeGreaterThan(Integer value) {
            addCriterion("appointment_submit_type >", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("appointment_submit_type >=", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeLessThan(Integer value) {
            addCriterion("appointment_submit_type <", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("appointment_submit_type <=", value, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_submit_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeIn(List<Integer> values) {
            addCriterion("appointment_submit_type in", values, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeNotIn(List<Integer> values) {
            addCriterion("appointment_submit_type not in", values, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeBetween(Integer value1, Integer value2) {
            addCriterion("appointment_submit_type between", value1, value2, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentSubmitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("appointment_submit_type not between", value1, value2, "appointmentSubmitType");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameIsNull() {
            addCriterion("appointment_name is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameIsNotNull() {
            addCriterion("appointment_name is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameEqualTo(String value) {
            addCriterion("appointment_name =", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameNotEqualTo(String value) {
            addCriterion("appointment_name <>", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameGreaterThan(String value) {
            addCriterion("appointment_name >", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_name >=", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLessThan(String value) {
            addCriterion("appointment_name <", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLessThanOrEqualTo(String value) {
            addCriterion("appointment_name <=", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLike(String value) {
            addCriterion("appointment_name like", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameNotLike(String value) {
            addCriterion("appointment_name not like", value, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameIn(List<String> values) {
            addCriterion("appointment_name in", values, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameNotIn(List<String> values) {
            addCriterion("appointment_name not in", values, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameBetween(String value1, String value2) {
            addCriterion("appointment_name between", value1, value2, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameNotBetween(String value1, String value2) {
            addCriterion("appointment_name not between", value1, value2, "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneIsNull() {
            addCriterion("appointment_phone is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneIsNotNull() {
            addCriterion("appointment_phone is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneEqualTo(String value) {
            addCriterion("appointment_phone =", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneNotEqualTo(String value) {
            addCriterion("appointment_phone <>", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneGreaterThan(String value) {
            addCriterion("appointment_phone >", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_phone >=", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLessThan(String value) {
            addCriterion("appointment_phone <", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLessThanOrEqualTo(String value) {
            addCriterion("appointment_phone <=", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLike(String value) {
            addCriterion("appointment_phone like", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneNotLike(String value) {
            addCriterion("appointment_phone not like", value, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneIn(List<String> values) {
            addCriterion("appointment_phone in", values, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneNotIn(List<String> values) {
            addCriterion("appointment_phone not in", values, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneBetween(String value1, String value2) {
            addCriterion("appointment_phone between", value1, value2, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneNotBetween(String value1, String value2) {
            addCriterion("appointment_phone not between", value1, value2, "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressIsNull() {
            addCriterion("appointment_address is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressIsNotNull() {
            addCriterion("appointment_address is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressEqualTo(String value) {
            addCriterion("appointment_address =", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressNotEqualTo(String value) {
            addCriterion("appointment_address <>", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressGreaterThan(String value) {
            addCriterion("appointment_address >", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_address >=", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLessThan(String value) {
            addCriterion("appointment_address <", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLessThanOrEqualTo(String value) {
            addCriterion("appointment_address <=", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_address <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLike(String value) {
            addCriterion("appointment_address like", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressNotLike(String value) {
            addCriterion("appointment_address not like", value, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressIn(List<String> values) {
            addCriterion("appointment_address in", values, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressNotIn(List<String> values) {
            addCriterion("appointment_address not in", values, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressBetween(String value1, String value2) {
            addCriterion("appointment_address between", value1, value2, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressNotBetween(String value1, String value2) {
            addCriterion("appointment_address not between", value1, value2, "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeIsNull() {
            addCriterion("appointment_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeIsNotNull() {
            addCriterion("appointment_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeEqualTo(Date value) {
            addCriterion("appointment_time =", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeNotEqualTo(Date value) {
            addCriterion("appointment_time <>", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeGreaterThan(Date value) {
            addCriterion("appointment_time >", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("appointment_time >=", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeLessThan(Date value) {
            addCriterion("appointment_time <", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeLessThanOrEqualTo(Date value) {
            addCriterion("appointment_time <=", value, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("appointment_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeIn(List<Date> values) {
            addCriterion("appointment_time in", values, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeNotIn(List<Date> values) {
            addCriterion("appointment_time not in", values, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeBetween(Date value1, Date value2) {
            addCriterion("appointment_time between", value1, value2, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentTimeNotBetween(Date value1, Date value2) {
            addCriterion("appointment_time not between", value1, value2, "appointmentTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andAddr1IsNull() {
            addCriterion("addr1 is null");
            return (Criteria) this;
        }

        public Criteria andAddr1IsNotNull() {
            addCriterion("addr1 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr1EqualTo(String value) {
            addCriterion("addr1 =", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1EqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1NotEqualTo(String value) {
            addCriterion("addr1 <>", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThan(String value) {
            addCriterion("addr1 >", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanOrEqualTo(String value) {
            addCriterion("addr1 >=", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1LessThan(String value) {
            addCriterion("addr1 <", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanOrEqualTo(String value) {
            addCriterion("addr1 <=", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1Like(String value) {
            addCriterion("addr1 like", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotLike(String value) {
            addCriterion("addr1 not like", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1In(List<String> values) {
            addCriterion("addr1 in", values, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotIn(List<String> values) {
            addCriterion("addr1 not in", values, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1Between(String value1, String value2) {
            addCriterion("addr1 between", value1, value2, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotBetween(String value1, String value2) {
            addCriterion("addr1 not between", value1, value2, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr2IsNull() {
            addCriterion("addr2 is null");
            return (Criteria) this;
        }

        public Criteria andAddr2IsNotNull() {
            addCriterion("addr2 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr2EqualTo(String value) {
            addCriterion("addr2 =", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2EqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2NotEqualTo(String value) {
            addCriterion("addr2 <>", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThan(String value) {
            addCriterion("addr2 >", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanOrEqualTo(String value) {
            addCriterion("addr2 >=", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2LessThan(String value) {
            addCriterion("addr2 <", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanOrEqualTo(String value) {
            addCriterion("addr2 <=", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2Like(String value) {
            addCriterion("addr2 like", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotLike(String value) {
            addCriterion("addr2 not like", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2In(List<String> values) {
            addCriterion("addr2 in", values, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotIn(List<String> values) {
            addCriterion("addr2 not in", values, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2Between(String value1, String value2) {
            addCriterion("addr2 between", value1, value2, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotBetween(String value1, String value2) {
            addCriterion("addr2 not between", value1, value2, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr3IsNull() {
            addCriterion("addr3 is null");
            return (Criteria) this;
        }

        public Criteria andAddr3IsNotNull() {
            addCriterion("addr3 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr3EqualTo(String value) {
            addCriterion("addr3 =", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3EqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3NotEqualTo(String value) {
            addCriterion("addr3 <>", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThan(String value) {
            addCriterion("addr3 >", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanOrEqualTo(String value) {
            addCriterion("addr3 >=", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3LessThan(String value) {
            addCriterion("addr3 <", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanOrEqualTo(String value) {
            addCriterion("addr3 <=", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3Like(String value) {
            addCriterion("addr3 like", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotLike(String value) {
            addCriterion("addr3 not like", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3In(List<String> values) {
            addCriterion("addr3 in", values, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotIn(List<String> values) {
            addCriterion("addr3 not in", values, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3Between(String value1, String value2) {
            addCriterion("addr3 between", value1, value2, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotBetween(String value1, String value2) {
            addCriterion("addr3 not between", value1, value2, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr4IsNull() {
            addCriterion("addr4 is null");
            return (Criteria) this;
        }

        public Criteria andAddr4IsNotNull() {
            addCriterion("addr4 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr4EqualTo(String value) {
            addCriterion("addr4 =", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4EqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4NotEqualTo(String value) {
            addCriterion("addr4 <>", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThan(String value) {
            addCriterion("addr4 >", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanOrEqualTo(String value) {
            addCriterion("addr4 >=", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4LessThan(String value) {
            addCriterion("addr4 <", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanOrEqualTo(String value) {
            addCriterion("addr4 <=", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4Like(String value) {
            addCriterion("addr4 like", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotLike(String value) {
            addCriterion("addr4 not like", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4In(List<String> values) {
            addCriterion("addr4 in", values, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotIn(List<String> values) {
            addCriterion("addr4 not in", values, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4Between(String value1, String value2) {
            addCriterion("addr4 between", value1, value2, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotBetween(String value1, String value2) {
            addCriterion("addr4 not between", value1, value2, "addr4");
            return (Criteria) this;
        }

        public Criteria andUsaddrIsNull() {
            addCriterion("usaddr is null");
            return (Criteria) this;
        }

        public Criteria andUsaddrIsNotNull() {
            addCriterion("usaddr is not null");
            return (Criteria) this;
        }

        public Criteria andUsaddrEqualTo(String value) {
            addCriterion("usaddr =", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrNotEqualTo(String value) {
            addCriterion("usaddr <>", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThan(String value) {
            addCriterion("usaddr >", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanOrEqualTo(String value) {
            addCriterion("usaddr >=", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThan(String value) {
            addCriterion("usaddr <", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanOrEqualTo(String value) {
            addCriterion("usaddr <=", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLike(String value) {
            addCriterion("usaddr like", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotLike(String value) {
            addCriterion("usaddr not like", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrIn(List<String> values) {
            addCriterion("usaddr in", values, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotIn(List<String> values) {
            addCriterion("usaddr not in", values, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrBetween(String value1, String value2) {
            addCriterion("usaddr between", value1, value2, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotBetween(String value1, String value2) {
            addCriterion("usaddr not between", value1, value2, "usaddr");
            return (Criteria) this;
        }

        public Criteria andIsInnerIsNull() {
            addCriterion("is_inner is null");
            return (Criteria) this;
        }

        public Criteria andIsInnerIsNotNull() {
            addCriterion("is_inner is not null");
            return (Criteria) this;
        }

        public Criteria andIsInnerEqualTo(Boolean value) {
            addCriterion("is_inner =", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerNotEqualTo(Boolean value) {
            addCriterion("is_inner <>", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThan(Boolean value) {
            addCriterion("is_inner >", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_inner >=", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThan(Boolean value) {
            addCriterion("is_inner <", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThanColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThanOrEqualTo(Boolean value) {
            addCriterion("is_inner <=", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThanOrEqualToColumn(AfterMarketOrder2cInfo.Column column) {
            addCriterion(new StringBuilder("is_inner <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInnerIn(List<Boolean> values) {
            addCriterion("is_inner in", values, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotIn(List<Boolean> values) {
            addCriterion("is_inner not in", values, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inner between", value1, value2, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inner not between", value1, value2, "isInner");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLikeInsensitive(String value) {
            addCriterion("upper(service_order_id) like", value.toUpperCase(), "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andOfferingOrderIdLikeInsensitive(String value) {
            addCriterion("upper(offering_order_id) like", value.toUpperCase(), "offeringOrderId");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLikeInsensitive(String value) {
            addCriterion("upper(total_price) like", value.toUpperCase(), "totalPrice");
            return (Criteria) this;
        }

        public Criteria andAppointmentNameLikeInsensitive(String value) {
            addCriterion("upper(appointment_name) like", value.toUpperCase(), "appointmentName");
            return (Criteria) this;
        }

        public Criteria andAppointmentPhoneLikeInsensitive(String value) {
            addCriterion("upper(appointment_phone) like", value.toUpperCase(), "appointmentPhone");
            return (Criteria) this;
        }

        public Criteria andAppointmentAddressLikeInsensitive(String value) {
            addCriterion("upper(appointment_address) like", value.toUpperCase(), "appointmentAddress");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andAddr1LikeInsensitive(String value) {
            addCriterion("upper(addr1) like", value.toUpperCase(), "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr2LikeInsensitive(String value) {
            addCriterion("upper(addr2) like", value.toUpperCase(), "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr3LikeInsensitive(String value) {
            addCriterion("upper(addr3) like", value.toUpperCase(), "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr4LikeInsensitive(String value) {
            addCriterion("upper(addr4) like", value.toUpperCase(), "addr4");
            return (Criteria) this;
        }

        public Criteria andUsaddrLikeInsensitive(String value) {
            addCriterion("upper(usaddr) like", value.toUpperCase(), "usaddr");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon May 26 16:27:52 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private AfterMarketOrder2cInfoExample example;

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        protected Criteria(AfterMarketOrder2cInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public AfterMarketOrder2cInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon May 26 16:27:52 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        void example(com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfoExample example);
    }
}