package com.chinamobile.install.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 售后订单
 */
@Data
public class AfterMarketOrderStatusItemVO {
    /**
     * 售后订单ID
     */
    private String serviceOrderId;

    /**
     * 售后服务订单状态:
     * 1:待预约 -对应商城同步状态【1待预约（付款完成）】
     * 11:待分派-装维管理员接单时，用户提交预约申请后，售后服务订单状态由“待预约”变为“待分派”，装维管理员完成订单分派后，订单状态由“待分派”变为“待派单”;合作伙伴接单时无本状态，直接变为2
     * 2:待派单-对应商城同步状态【2派单中（预约完成）】
     * 3:已派单
     * 31：已签到
     * 4.已完结（成功）
     * 5.已完成（失败）
     * 7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
     * 8.交易失败-对应商城同步状态【4退款完成】
     * 9.同步省侧
     */
    private Integer status;

    /**
    * 派单时间
    */
    private Date sendOrderTime;

}
