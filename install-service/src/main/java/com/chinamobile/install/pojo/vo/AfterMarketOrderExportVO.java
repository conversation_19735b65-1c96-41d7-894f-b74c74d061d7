package com.chinamobile.install.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 订单的卡相关信息展示类
 */
@Data
public class AfterMarketOrderExportVO {

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;


    /**
     * 订单状态
     * */
    @Excel(name = "订单状态")
    private Integer orderStatus;

    /**
     * 下单时间
     * */
    @Excel(name = "下单时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date createOrderTime;

    @Excel(name = "预约发起时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date createAppointmentTime;

    @Excel(name = "填写的期望安装时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date appointmentTime;

    @Excel(name = "OS下发给铁通的时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date syncProvinceTime;

    @Excel(name = "铁通派遣安装人员时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date dispatchTime;

    @Excel(name = "安装人员姓名")
    private String installName;

    @Excel(name = "安装人员电话")
    private String installPhone;

    @Excel(name = "完成安装时间",exportFormat = "yyyy/MM/dd HH:mm:ss")
    private Date finishTime;

}
