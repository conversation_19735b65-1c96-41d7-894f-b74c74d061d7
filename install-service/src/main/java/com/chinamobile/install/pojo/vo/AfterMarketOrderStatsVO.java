package com.chinamobile.install.pojo.vo;

import lombok.Data;

/**
 * 售后工单统计信息VO
 */
@Data
public class AfterMarketOrderStatsVO {
    /**
     * 工单总数
     */
    private Integer totalCount;

    /**
     * 未分派工单数（只对装维管理员显示）
     */
    private Integer unassignedCount;

    /**
     * 未派单工单数
     */
    private Integer undispatchedCount;

    /**
     * 未施工工单数（已派单+已签到）
     */
    private Integer uncompletedCount;

    /**
     * 超时工单数（预约单派发至安装师傅后72小时内）
     */
    private Integer overtimeCount;

    /**
     * 已完结工单数（包括成功/失败）
     */
    private Integer finishedCount;

    /**
     * 交付成功率（交付成功数/订单总量）
     */
    private String deliverySuccessRate;

    /**
     * 订单退款率（退款成功数/工单总量）
     */
    private String refundRate;
}