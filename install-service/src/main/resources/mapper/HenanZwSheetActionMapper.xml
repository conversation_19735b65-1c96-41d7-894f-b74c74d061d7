<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.HenanZwSheetActionMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    <id column="record_uuid" jdbcType="VARCHAR" property="recordUuid" />
    <result column="sheet_no" jdbcType="VARCHAR" property="sheetNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="service_type" jdbcType="VARCHAR" property="serviceType" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="link_name" jdbcType="VARCHAR" property="linkName" />
    <result column="sheet_state" jdbcType="VARCHAR" property="sheetState" />
    <result column="op_person" jdbcType="VARCHAR" property="opPerson" />
    <result column="op_contact" jdbcType="VARCHAR" property="opContact" />
    <result column="ar_time" jdbcType="TIMESTAMP" property="arTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="deal_time" jdbcType="TIMESTAMP" property="dealTime" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="deal_result" jdbcType="VARCHAR" property="dealResult" />
    <result column="deal_action" jdbcType="VARCHAR" property="dealAction" />
    <result column="pre_ack_time" jdbcType="TIMESTAMP" property="preAckTime" />
    <result column="rescheduling_time" jdbcType="TIMESTAMP" property="reschedulingTime" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    <result column="all_link_name" jdbcType="LONGVARCHAR" property="allLinkName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    record_uuid, sheet_no, order_no, serial_no, service_type, product_name, flow_name, 
    link_name, sheet_state, op_person, op_contact, ar_time, end_time, deal_time, oper_time, 
    deal_result, deal_action, pre_ack_time, rescheduling_time, sign_time
  </sql>
  <sql id="Blob_Column_List">
    all_link_name
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetActionExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from henan_zw_sheet_action
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetActionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from henan_zw_sheet_action
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from henan_zw_sheet_action
    where record_uuid = #{recordUuid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from henan_zw_sheet_action
    where record_uuid = #{recordUuid,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetActionExample">
    delete from henan_zw_sheet_action
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    insert into henan_zw_sheet_action (record_uuid, sheet_no, order_no, 
      serial_no, service_type, product_name, 
      flow_name, link_name, sheet_state, 
      op_person, op_contact, ar_time, 
      end_time, deal_time, oper_time, 
      deal_result, deal_action, pre_ack_time, 
      rescheduling_time, sign_time, all_link_name
      )
    values (#{recordUuid,jdbcType=VARCHAR}, #{sheetNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{serialNo,jdbcType=VARCHAR}, #{serviceType,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{flowName,jdbcType=VARCHAR}, #{linkName,jdbcType=VARCHAR}, #{sheetState,jdbcType=VARCHAR}, 
      #{opPerson,jdbcType=VARCHAR}, #{opContact,jdbcType=VARCHAR}, #{arTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{dealTime,jdbcType=TIMESTAMP}, #{operTime,jdbcType=TIMESTAMP}, 
      #{dealResult,jdbcType=VARCHAR}, #{dealAction,jdbcType=VARCHAR}, #{preAckTime,jdbcType=TIMESTAMP}, 
      #{reschedulingTime,jdbcType=TIMESTAMP}, #{signTime,jdbcType=TIMESTAMP}, #{allLinkName,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    insert into henan_zw_sheet_action
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordUuid != null">
        record_uuid,
      </if>
      <if test="sheetNo != null">
        sheet_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="flowName != null">
        flow_name,
      </if>
      <if test="linkName != null">
        link_name,
      </if>
      <if test="sheetState != null">
        sheet_state,
      </if>
      <if test="opPerson != null">
        op_person,
      </if>
      <if test="opContact != null">
        op_contact,
      </if>
      <if test="arTime != null">
        ar_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="dealTime != null">
        deal_time,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="dealResult != null">
        deal_result,
      </if>
      <if test="dealAction != null">
        deal_action,
      </if>
      <if test="preAckTime != null">
        pre_ack_time,
      </if>
      <if test="reschedulingTime != null">
        rescheduling_time,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="allLinkName != null">
        all_link_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordUuid != null">
        #{recordUuid,jdbcType=VARCHAR},
      </if>
      <if test="sheetNo != null">
        #{sheetNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="flowName != null">
        #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="linkName != null">
        #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="sheetState != null">
        #{sheetState,jdbcType=VARCHAR},
      </if>
      <if test="opPerson != null">
        #{opPerson,jdbcType=VARCHAR},
      </if>
      <if test="opContact != null">
        #{opContact,jdbcType=VARCHAR},
      </if>
      <if test="arTime != null">
        #{arTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dealTime != null">
        #{dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dealResult != null">
        #{dealResult,jdbcType=VARCHAR},
      </if>
      <if test="dealAction != null">
        #{dealAction,jdbcType=VARCHAR},
      </if>
      <if test="preAckTime != null">
        #{preAckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reschedulingTime != null">
        #{reschedulingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allLinkName != null">
        #{allLinkName,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetActionExample" resultType="java.lang.Long">
    select count(*) from henan_zw_sheet_action
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update henan_zw_sheet_action
    <set>
      <if test="record.recordUuid != null">
        record_uuid = #{record.recordUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.sheetNo != null">
        sheet_no = #{record.sheetNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNo != null">
        serial_no = #{record.serialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.flowName != null">
        flow_name = #{record.flowName,jdbcType=VARCHAR},
      </if>
      <if test="record.linkName != null">
        link_name = #{record.linkName,jdbcType=VARCHAR},
      </if>
      <if test="record.sheetState != null">
        sheet_state = #{record.sheetState,jdbcType=VARCHAR},
      </if>
      <if test="record.opPerson != null">
        op_person = #{record.opPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.opContact != null">
        op_contact = #{record.opContact,jdbcType=VARCHAR},
      </if>
      <if test="record.arTime != null">
        ar_time = #{record.arTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dealTime != null">
        deal_time = #{record.dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operTime != null">
        oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dealResult != null">
        deal_result = #{record.dealResult,jdbcType=VARCHAR},
      </if>
      <if test="record.dealAction != null">
        deal_action = #{record.dealAction,jdbcType=VARCHAR},
      </if>
      <if test="record.preAckTime != null">
        pre_ack_time = #{record.preAckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reschedulingTime != null">
        rescheduling_time = #{record.reschedulingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.signTime != null">
        sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allLinkName != null">
        all_link_name = #{record.allLinkName,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update henan_zw_sheet_action
    set record_uuid = #{record.recordUuid,jdbcType=VARCHAR},
      sheet_no = #{record.sheetNo,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      service_type = #{record.serviceType,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      flow_name = #{record.flowName,jdbcType=VARCHAR},
      link_name = #{record.linkName,jdbcType=VARCHAR},
      sheet_state = #{record.sheetState,jdbcType=VARCHAR},
      op_person = #{record.opPerson,jdbcType=VARCHAR},
      op_contact = #{record.opContact,jdbcType=VARCHAR},
      ar_time = #{record.arTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      deal_time = #{record.dealTime,jdbcType=TIMESTAMP},
      oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      deal_result = #{record.dealResult,jdbcType=VARCHAR},
      deal_action = #{record.dealAction,jdbcType=VARCHAR},
      pre_ack_time = #{record.preAckTime,jdbcType=TIMESTAMP},
      rescheduling_time = #{record.reschedulingTime,jdbcType=TIMESTAMP},
      sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      all_link_name = #{record.allLinkName,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update henan_zw_sheet_action
    set record_uuid = #{record.recordUuid,jdbcType=VARCHAR},
      sheet_no = #{record.sheetNo,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      service_type = #{record.serviceType,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      flow_name = #{record.flowName,jdbcType=VARCHAR},
      link_name = #{record.linkName,jdbcType=VARCHAR},
      sheet_state = #{record.sheetState,jdbcType=VARCHAR},
      op_person = #{record.opPerson,jdbcType=VARCHAR},
      op_contact = #{record.opContact,jdbcType=VARCHAR},
      ar_time = #{record.arTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      deal_time = #{record.dealTime,jdbcType=TIMESTAMP},
      oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      deal_result = #{record.dealResult,jdbcType=VARCHAR},
      deal_action = #{record.dealAction,jdbcType=VARCHAR},
      pre_ack_time = #{record.preAckTime,jdbcType=TIMESTAMP},
      rescheduling_time = #{record.reschedulingTime,jdbcType=TIMESTAMP},
      sign_time = #{record.signTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    update henan_zw_sheet_action
    <set>
      <if test="sheetNo != null">
        sheet_no = #{sheetNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="flowName != null">
        flow_name = #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="linkName != null">
        link_name = #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="sheetState != null">
        sheet_state = #{sheetState,jdbcType=VARCHAR},
      </if>
      <if test="opPerson != null">
        op_person = #{opPerson,jdbcType=VARCHAR},
      </if>
      <if test="opContact != null">
        op_contact = #{opContact,jdbcType=VARCHAR},
      </if>
      <if test="arTime != null">
        ar_time = #{arTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dealTime != null">
        deal_time = #{dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dealResult != null">
        deal_result = #{dealResult,jdbcType=VARCHAR},
      </if>
      <if test="dealAction != null">
        deal_action = #{dealAction,jdbcType=VARCHAR},
      </if>
      <if test="preAckTime != null">
        pre_ack_time = #{preAckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reschedulingTime != null">
        rescheduling_time = #{reschedulingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allLinkName != null">
        all_link_name = #{allLinkName,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where record_uuid = #{recordUuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    update henan_zw_sheet_action
    set sheet_no = #{sheetNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      flow_name = #{flowName,jdbcType=VARCHAR},
      link_name = #{linkName,jdbcType=VARCHAR},
      sheet_state = #{sheetState,jdbcType=VARCHAR},
      op_person = #{opPerson,jdbcType=VARCHAR},
      op_contact = #{opContact,jdbcType=VARCHAR},
      ar_time = #{arTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      deal_time = #{dealTime,jdbcType=TIMESTAMP},
      oper_time = #{operTime,jdbcType=TIMESTAMP},
      deal_result = #{dealResult,jdbcType=VARCHAR},
      deal_action = #{dealAction,jdbcType=VARCHAR},
      pre_ack_time = #{preAckTime,jdbcType=TIMESTAMP},
      rescheduling_time = #{reschedulingTime,jdbcType=TIMESTAMP},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      all_link_name = #{allLinkName,jdbcType=LONGVARCHAR}
    where record_uuid = #{recordUuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.HenanZwSheetAction">
    update henan_zw_sheet_action
    set sheet_no = #{sheetNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      flow_name = #{flowName,jdbcType=VARCHAR},
      link_name = #{linkName,jdbcType=VARCHAR},
      sheet_state = #{sheetState,jdbcType=VARCHAR},
      op_person = #{opPerson,jdbcType=VARCHAR},
      op_contact = #{opContact,jdbcType=VARCHAR},
      ar_time = #{arTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      deal_time = #{dealTime,jdbcType=TIMESTAMP},
      oper_time = #{operTime,jdbcType=TIMESTAMP},
      deal_result = #{dealResult,jdbcType=VARCHAR},
      deal_action = #{dealAction,jdbcType=VARCHAR},
      pre_ack_time = #{preAckTime,jdbcType=TIMESTAMP},
      rescheduling_time = #{reschedulingTime,jdbcType=TIMESTAMP},
      sign_time = #{signTime,jdbcType=TIMESTAMP}
    where record_uuid = #{recordUuid,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into henan_zw_sheet_action
    (record_uuid, sheet_no, order_no, serial_no, service_type, product_name, flow_name, 
      link_name, sheet_state, op_person, op_contact, ar_time, end_time, deal_time, oper_time, 
      deal_result, deal_action, pre_ack_time, rescheduling_time, sign_time, all_link_name
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.recordUuid,jdbcType=VARCHAR}, #{item.sheetNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, 
        #{item.serialNo,jdbcType=VARCHAR}, #{item.serviceType,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.flowName,jdbcType=VARCHAR}, #{item.linkName,jdbcType=VARCHAR}, #{item.sheetState,jdbcType=VARCHAR}, 
        #{item.opPerson,jdbcType=VARCHAR}, #{item.opContact,jdbcType=VARCHAR}, #{item.arTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.dealTime,jdbcType=TIMESTAMP}, #{item.operTime,jdbcType=TIMESTAMP}, 
        #{item.dealResult,jdbcType=VARCHAR}, #{item.dealAction,jdbcType=VARCHAR}, #{item.preAckTime,jdbcType=TIMESTAMP}, 
        #{item.reschedulingTime,jdbcType=TIMESTAMP}, #{item.signTime,jdbcType=TIMESTAMP}, 
        #{item.allLinkName,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into henan_zw_sheet_action (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'record_uuid'.toString() == column.value">
          #{item.recordUuid,jdbcType=VARCHAR}
        </if>
        <if test="'sheet_no'.toString() == column.value">
          #{item.sheetNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'serial_no'.toString() == column.value">
          #{item.serialNo,jdbcType=VARCHAR}
        </if>
        <if test="'service_type'.toString() == column.value">
          #{item.serviceType,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'flow_name'.toString() == column.value">
          #{item.flowName,jdbcType=VARCHAR}
        </if>
        <if test="'link_name'.toString() == column.value">
          #{item.linkName,jdbcType=VARCHAR}
        </if>
        <if test="'sheet_state'.toString() == column.value">
          #{item.sheetState,jdbcType=VARCHAR}
        </if>
        <if test="'op_person'.toString() == column.value">
          #{item.opPerson,jdbcType=VARCHAR}
        </if>
        <if test="'op_contact'.toString() == column.value">
          #{item.opContact,jdbcType=VARCHAR}
        </if>
        <if test="'ar_time'.toString() == column.value">
          #{item.arTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'deal_time'.toString() == column.value">
          #{item.dealTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'oper_time'.toString() == column.value">
          #{item.operTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'deal_result'.toString() == column.value">
          #{item.dealResult,jdbcType=VARCHAR}
        </if>
        <if test="'deal_action'.toString() == column.value">
          #{item.dealAction,jdbcType=VARCHAR}
        </if>
        <if test="'pre_ack_time'.toString() == column.value">
          #{item.preAckTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'rescheduling_time'.toString() == column.value">
          #{item.reschedulingTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sign_time'.toString() == column.value">
          #{item.signTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'all_link_name'.toString() == column.value">
          #{item.allLinkName,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>