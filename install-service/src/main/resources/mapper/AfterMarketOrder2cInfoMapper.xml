<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.AfterMarketOrder2cInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="service_order_id" jdbcType="VARCHAR" property="serviceOrderId" />
    <result column="offering_order_id" jdbcType="VARCHAR" property="offeringOrderId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="status_time" jdbcType="TIMESTAMP" property="statusTime" />
    <result column="total_price" jdbcType="VARCHAR" property="totalPrice" />
    <result column="appointment_submit_type" jdbcType="INTEGER" property="appointmentSubmitType" />
    <result column="appointment_name" jdbcType="VARCHAR" property="appointmentName" />
    <result column="appointment_phone" jdbcType="VARCHAR" property="appointmentPhone" />
    <result column="appointment_address" jdbcType="VARCHAR" property="appointmentAddress" />
    <result column="appointment_time" jdbcType="TIMESTAMP" property="appointmentTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="addr1" jdbcType="VARCHAR" property="addr1" />
    <result column="addr2" jdbcType="VARCHAR" property="addr2" />
    <result column="addr3" jdbcType="VARCHAR" property="addr3" />
    <result column="addr4" jdbcType="VARCHAR" property="addr4" />
    <result column="usaddr" jdbcType="VARCHAR" property="usaddr" />
    <result column="is_inner" jdbcType="BIT" property="isInner" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    service_order_id, offering_order_id, status, status_time, total_price, appointment_submit_type, 
    appointment_name, appointment_phone, appointment_address, appointment_time, create_time, 
    update_time, be_id, region_id, addr1, addr2, addr3, addr4, usaddr, is_inner
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from after_market_order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from after_market_order_2c_info
    where service_order_id = #{serviceOrderId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from after_market_order_2c_info
    where service_order_id = #{serviceOrderId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from after_market_order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into after_market_order_2c_info (service_order_id, offering_order_id, 
      status, status_time, total_price, 
      appointment_submit_type, appointment_name, 
      appointment_phone, appointment_address, appointment_time, 
      create_time, update_time, be_id, 
      region_id, addr1, addr2, 
      addr3, addr4, usaddr, 
      is_inner)
    values (#{serviceOrderId,jdbcType=VARCHAR}, #{offeringOrderId,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{statusTime,jdbcType=TIMESTAMP}, #{totalPrice,jdbcType=VARCHAR}, 
      #{appointmentSubmitType,jdbcType=INTEGER}, #{appointmentName,jdbcType=VARCHAR}, 
      #{appointmentPhone,jdbcType=VARCHAR}, #{appointmentAddress,jdbcType=VARCHAR}, #{appointmentTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{beId,jdbcType=VARCHAR}, 
      #{regionId,jdbcType=VARCHAR}, #{addr1,jdbcType=VARCHAR}, #{addr2,jdbcType=VARCHAR}, 
      #{addr3,jdbcType=VARCHAR}, #{addr4,jdbcType=VARCHAR}, #{usaddr,jdbcType=VARCHAR}, 
      #{isInner,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into after_market_order_2c_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceOrderId != null">
        service_order_id,
      </if>
      <if test="offeringOrderId != null">
        offering_order_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="statusTime != null">
        status_time,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="appointmentSubmitType != null">
        appointment_submit_type,
      </if>
      <if test="appointmentName != null">
        appointment_name,
      </if>
      <if test="appointmentPhone != null">
        appointment_phone,
      </if>
      <if test="appointmentAddress != null">
        appointment_address,
      </if>
      <if test="appointmentTime != null">
        appointment_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="addr1 != null">
        addr1,
      </if>
      <if test="addr2 != null">
        addr2,
      </if>
      <if test="addr3 != null">
        addr3,
      </if>
      <if test="addr4 != null">
        addr4,
      </if>
      <if test="usaddr != null">
        usaddr,
      </if>
      <if test="isInner != null">
        is_inner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceOrderId != null">
        #{serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="offeringOrderId != null">
        #{offeringOrderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="statusTime != null">
        #{statusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="appointmentSubmitType != null">
        #{appointmentSubmitType,jdbcType=INTEGER},
      </if>
      <if test="appointmentName != null">
        #{appointmentName,jdbcType=VARCHAR},
      </if>
      <if test="appointmentPhone != null">
        #{appointmentPhone,jdbcType=VARCHAR},
      </if>
      <if test="appointmentAddress != null">
        #{appointmentAddress,jdbcType=VARCHAR},
      </if>
      <if test="appointmentTime != null">
        #{appointmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="isInner != null">
        #{isInner,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from after_market_order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    update after_market_order_2c_info
    <set>
      <if test="record.serviceOrderId != null">
        service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringOrderId != null">
        offering_order_id = #{record.offeringOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.statusTime != null">
        status_time = #{record.statusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentSubmitType != null">
        appointment_submit_type = #{record.appointmentSubmitType,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentName != null">
        appointment_name = #{record.appointmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentPhone != null">
        appointment_phone = #{record.appointmentPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentAddress != null">
        appointment_address = #{record.appointmentAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentTime != null">
        appointment_time = #{record.appointmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.addr1 != null">
        addr1 = #{record.addr1,jdbcType=VARCHAR},
      </if>
      <if test="record.addr2 != null">
        addr2 = #{record.addr2,jdbcType=VARCHAR},
      </if>
      <if test="record.addr3 != null">
        addr3 = #{record.addr3,jdbcType=VARCHAR},
      </if>
      <if test="record.addr4 != null">
        addr4 = #{record.addr4,jdbcType=VARCHAR},
      </if>
      <if test="record.usaddr != null">
        usaddr = #{record.usaddr,jdbcType=VARCHAR},
      </if>
      <if test="record.isInner != null">
        is_inner = #{record.isInner,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    update after_market_order_2c_info
    set service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      offering_order_id = #{record.offeringOrderId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      status_time = #{record.statusTime,jdbcType=TIMESTAMP},
      total_price = #{record.totalPrice,jdbcType=VARCHAR},
      appointment_submit_type = #{record.appointmentSubmitType,jdbcType=INTEGER},
      appointment_name = #{record.appointmentName,jdbcType=VARCHAR},
      appointment_phone = #{record.appointmentPhone,jdbcType=VARCHAR},
      appointment_address = #{record.appointmentAddress,jdbcType=VARCHAR},
      appointment_time = #{record.appointmentTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      be_id = #{record.beId,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      addr1 = #{record.addr1,jdbcType=VARCHAR},
      addr2 = #{record.addr2,jdbcType=VARCHAR},
      addr3 = #{record.addr3,jdbcType=VARCHAR},
      addr4 = #{record.addr4,jdbcType=VARCHAR},
      usaddr = #{record.usaddr,jdbcType=VARCHAR},
      is_inner = #{record.isInner,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    update after_market_order_2c_info
    <set>
      <if test="offeringOrderId != null">
        offering_order_id = #{offeringOrderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="statusTime != null">
        status_time = #{statusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="appointmentSubmitType != null">
        appointment_submit_type = #{appointmentSubmitType,jdbcType=INTEGER},
      </if>
      <if test="appointmentName != null">
        appointment_name = #{appointmentName,jdbcType=VARCHAR},
      </if>
      <if test="appointmentPhone != null">
        appointment_phone = #{appointmentPhone,jdbcType=VARCHAR},
      </if>
      <if test="appointmentAddress != null">
        appointment_address = #{appointmentAddress,jdbcType=VARCHAR},
      </if>
      <if test="appointmentTime != null">
        appointment_time = #{appointmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        addr1 = #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        addr2 = #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        addr3 = #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        addr4 = #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        usaddr = #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="isInner != null">
        is_inner = #{isInner,jdbcType=BIT},
      </if>
    </set>
    where service_order_id = #{serviceOrderId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    update after_market_order_2c_info
    set offering_order_id = #{offeringOrderId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      status_time = #{statusTime,jdbcType=TIMESTAMP},
      total_price = #{totalPrice,jdbcType=VARCHAR},
      appointment_submit_type = #{appointmentSubmitType,jdbcType=INTEGER},
      appointment_name = #{appointmentName,jdbcType=VARCHAR},
      appointment_phone = #{appointmentPhone,jdbcType=VARCHAR},
      appointment_address = #{appointmentAddress,jdbcType=VARCHAR},
      appointment_time = #{appointmentTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      be_id = #{beId,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      addr1 = #{addr1,jdbcType=VARCHAR},
      addr2 = #{addr2,jdbcType=VARCHAR},
      addr3 = #{addr3,jdbcType=VARCHAR},
      addr4 = #{addr4,jdbcType=VARCHAR},
      usaddr = #{usaddr,jdbcType=VARCHAR},
      is_inner = #{isInner,jdbcType=BIT}
    where service_order_id = #{serviceOrderId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into after_market_order_2c_info
    (service_order_id, offering_order_id, status, status_time, total_price, appointment_submit_type, 
      appointment_name, appointment_phone, appointment_address, appointment_time, create_time, 
      update_time, be_id, region_id, addr1, addr2, addr3, addr4, usaddr, is_inner)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.serviceOrderId,jdbcType=VARCHAR}, #{item.offeringOrderId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.statusTime,jdbcType=TIMESTAMP}, #{item.totalPrice,jdbcType=VARCHAR}, 
        #{item.appointmentSubmitType,jdbcType=INTEGER}, #{item.appointmentName,jdbcType=VARCHAR}, 
        #{item.appointmentPhone,jdbcType=VARCHAR}, #{item.appointmentAddress,jdbcType=VARCHAR}, 
        #{item.appointmentTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.beId,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, 
        #{item.addr1,jdbcType=VARCHAR}, #{item.addr2,jdbcType=VARCHAR}, #{item.addr3,jdbcType=VARCHAR}, 
        #{item.addr4,jdbcType=VARCHAR}, #{item.usaddr,jdbcType=VARCHAR}, #{item.isInner,jdbcType=BIT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:27:52 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into after_market_order_2c_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'service_order_id'.toString() == column.value">
          #{item.serviceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'offering_order_id'.toString() == column.value">
          #{item.offeringOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'status_time'.toString() == column.value">
          #{item.statusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'total_price'.toString() == column.value">
          #{item.totalPrice,jdbcType=VARCHAR}
        </if>
        <if test="'appointment_submit_type'.toString() == column.value">
          #{item.appointmentSubmitType,jdbcType=INTEGER}
        </if>
        <if test="'appointment_name'.toString() == column.value">
          #{item.appointmentName,jdbcType=VARCHAR}
        </if>
        <if test="'appointment_phone'.toString() == column.value">
          #{item.appointmentPhone,jdbcType=VARCHAR}
        </if>
        <if test="'appointment_address'.toString() == column.value">
          #{item.appointmentAddress,jdbcType=VARCHAR}
        </if>
        <if test="'appointment_time'.toString() == column.value">
          #{item.appointmentTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'addr1'.toString() == column.value">
          #{item.addr1,jdbcType=VARCHAR}
        </if>
        <if test="'addr2'.toString() == column.value">
          #{item.addr2,jdbcType=VARCHAR}
        </if>
        <if test="'addr3'.toString() == column.value">
          #{item.addr3,jdbcType=VARCHAR}
        </if>
        <if test="'addr4'.toString() == column.value">
          #{item.addr4,jdbcType=VARCHAR}
        </if>
        <if test="'usaddr'.toString() == column.value">
          #{item.usaddr,jdbcType=VARCHAR}
        </if>
        <if test="'is_inner'.toString() == column.value">
          #{item.isInner,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>