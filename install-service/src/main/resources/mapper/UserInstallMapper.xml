<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.UserInstallMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.UserInstall">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="install_name" jdbcType="VARCHAR" property="installName" />
    <result column="install_phone" jdbcType="VARCHAR" property="installPhone" />
    <result column="install_email" jdbcType="VARCHAR" property="installEmail" />
    <result column="install_partner_name" jdbcType="VARCHAR" property="installPartnerName" />
    <result column="primary_user_id" jdbcType="VARCHAR" property="primaryUserId" />
    <result column="is_cancel" jdbcType="BIT" property="isCancel" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, install_name, install_phone, install_email, install_partner_name, primary_user_id, 
    is_cancel, create_user_id, update_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.UserInstallExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_install
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_install
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from user_install
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.UserInstallExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from user_install
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.UserInstall">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_install (id, install_name, install_phone, 
      install_email, install_partner_name, primary_user_id, 
      is_cancel, create_user_id, update_time, 
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{installName,jdbcType=VARCHAR}, #{installPhone,jdbcType=VARCHAR}, 
      #{installEmail,jdbcType=VARCHAR}, #{installPartnerName,jdbcType=VARCHAR}, #{primaryUserId,jdbcType=VARCHAR}, 
      #{isCancel,jdbcType=BIT}, #{createUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.UserInstall">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_install
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="installName != null">
        install_name,
      </if>
      <if test="installPhone != null">
        install_phone,
      </if>
      <if test="installEmail != null">
        install_email,
      </if>
      <if test="installPartnerName != null">
        install_partner_name,
      </if>
      <if test="primaryUserId != null">
        primary_user_id,
      </if>
      <if test="isCancel != null">
        is_cancel,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="installName != null">
        #{installName,jdbcType=VARCHAR},
      </if>
      <if test="installPhone != null">
        #{installPhone,jdbcType=VARCHAR},
      </if>
      <if test="installEmail != null">
        #{installEmail,jdbcType=VARCHAR},
      </if>
      <if test="installPartnerName != null">
        #{installPartnerName,jdbcType=VARCHAR},
      </if>
      <if test="primaryUserId != null">
        #{primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="isCancel != null">
        #{isCancel,jdbcType=BIT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.UserInstallExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_install
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_install
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.installName != null">
        install_name = #{record.installName,jdbcType=VARCHAR},
      </if>
      <if test="record.installPhone != null">
        install_phone = #{record.installPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.installEmail != null">
        install_email = #{record.installEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.installPartnerName != null">
        install_partner_name = #{record.installPartnerName,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryUserId != null">
        primary_user_id = #{record.primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.isCancel != null">
        is_cancel = #{record.isCancel,jdbcType=BIT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_install
    set id = #{record.id,jdbcType=VARCHAR},
      install_name = #{record.installName,jdbcType=VARCHAR},
      install_phone = #{record.installPhone,jdbcType=VARCHAR},
      install_email = #{record.installEmail,jdbcType=VARCHAR},
      install_partner_name = #{record.installPartnerName,jdbcType=VARCHAR},
      primary_user_id = #{record.primaryUserId,jdbcType=VARCHAR},
      is_cancel = #{record.isCancel,jdbcType=BIT},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.UserInstall">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_install
    <set>
      <if test="installName != null">
        install_name = #{installName,jdbcType=VARCHAR},
      </if>
      <if test="installPhone != null">
        install_phone = #{installPhone,jdbcType=VARCHAR},
      </if>
      <if test="installEmail != null">
        install_email = #{installEmail,jdbcType=VARCHAR},
      </if>
      <if test="installPartnerName != null">
        install_partner_name = #{installPartnerName,jdbcType=VARCHAR},
      </if>
      <if test="primaryUserId != null">
        primary_user_id = #{primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="isCancel != null">
        is_cancel = #{isCancel,jdbcType=BIT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.UserInstall">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_install
    set install_name = #{installName,jdbcType=VARCHAR},
      install_phone = #{installPhone,jdbcType=VARCHAR},
      install_email = #{installEmail,jdbcType=VARCHAR},
      install_partner_name = #{installPartnerName,jdbcType=VARCHAR},
      primary_user_id = #{primaryUserId,jdbcType=VARCHAR},
      is_cancel = #{isCancel,jdbcType=BIT},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_install
    (id, install_name, install_phone, install_email, install_partner_name, primary_user_id, 
      is_cancel, create_user_id, update_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.installName,jdbcType=VARCHAR}, #{item.installPhone,jdbcType=VARCHAR}, 
        #{item.installEmail,jdbcType=VARCHAR}, #{item.installPartnerName,jdbcType=VARCHAR}, 
        #{item.primaryUserId,jdbcType=VARCHAR}, #{item.isCancel,jdbcType=BIT}, #{item.createUserId,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_install (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'install_name'.toString() == column.value">
          #{item.installName,jdbcType=VARCHAR}
        </if>
        <if test="'install_phone'.toString() == column.value">
          #{item.installPhone,jdbcType=VARCHAR}
        </if>
        <if test="'install_email'.toString() == column.value">
          #{item.installEmail,jdbcType=VARCHAR}
        </if>
        <if test="'install_partner_name'.toString() == column.value">
          #{item.installPartnerName,jdbcType=VARCHAR}
        </if>
        <if test="'primary_user_id'.toString() == column.value">
          #{item.primaryUserId,jdbcType=VARCHAR}
        </if>
        <if test="'is_cancel'.toString() == column.value">
          #{item.isCancel,jdbcType=BIT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>