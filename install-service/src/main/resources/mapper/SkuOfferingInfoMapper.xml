<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.SkuOfferingInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.SkuOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="offering_code" jdbcType="VARCHAR" property="offeringCode" />
    <result column="offering_name" jdbcType="VARCHAR" property="offeringName" />
    <result column="offering_status" jdbcType="VARCHAR" property="offeringStatus" />
    <result column="offering_status_time" jdbcType="TIMESTAMP" property="offeringStatusTime" />
    <result column="composition" jdbcType="VARCHAR" property="composition" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="quantity" jdbcType="BIGINT" property="quantity" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="recommend_price" jdbcType="BIGINT" property="recommendPrice" />
    <result column="sale_object" jdbcType="VARCHAR" property="saleObject" />
    <result column="price" jdbcType="BIGINT" property="price" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="market_name" jdbcType="VARCHAR" property="marketName" />
    <result column="market_code" jdbcType="VARCHAR" property="marketCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="receive_order" jdbcType="VARCHAR" property="receiveOrder" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="card_type" jdbcType="VARCHAR" property="cardType" />
    <result column="main_offering_code" jdbcType="VARCHAR" property="mainOfferingCode" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="sale_model" jdbcType="VARCHAR" property="saleModel" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="point_status" jdbcType="INTEGER" property="pointStatus" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="package_type" jdbcType="VARCHAR" property="packageType" />
    <result column="sku_abbreviation" jdbcType="VARCHAR" property="skuAbbreviation" />
    <result column="receive_order_name" jdbcType="VARCHAR" property="receiveOrderName" />
    <result column="receive_order_phone" jdbcType="VARCHAR" property="receiveOrderPhone" />
    <result column="deliver_name" jdbcType="VARCHAR" property="deliverName" />
    <result column="deliver_phone" jdbcType="VARCHAR" property="deliverPhone" />
    <result column="aftermarket_name" jdbcType="VARCHAR" property="aftermarketName" />
    <result column="aftermarket_phone" jdbcType="VARCHAR" property="aftermarketPhone" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_id, spu_code, offering_code, offering_name, offering_status, offering_status_time, 
    composition, model, quantity, size, oper_type, recommend_price, sale_object, price, 
    unit, market_name, market_code, supplier_name, product_type, receive_order, cust_code, 
    cust_name, card_type, main_offering_code, template_name, template_id, sale_model, 
    project, point_status, cooperator_id, create_time, update_time, delete_time, package_type, 
    sku_abbreviation, receive_order_name, receive_order_phone, deliver_name, deliver_phone, 
    aftermarket_name, aftermarket_phone
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sku_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sku_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from sku_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from sku_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_offering_info (id, spu_id, spu_code, 
      offering_code, offering_name, offering_status, 
      offering_status_time, composition, model, 
      quantity, size, oper_type, 
      recommend_price, sale_object, price, 
      unit, market_name, market_code, 
      supplier_name, product_type, receive_order, 
      cust_code, cust_name, card_type, 
      main_offering_code, template_name, template_id, 
      sale_model, project, point_status, 
      cooperator_id, create_time, update_time, 
      delete_time, package_type, sku_abbreviation, 
      receive_order_name, receive_order_phone, deliver_name, 
      deliver_phone, aftermarket_name, aftermarket_phone
      )
    values (#{id,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, 
      #{offeringCode,jdbcType=VARCHAR}, #{offeringName,jdbcType=VARCHAR}, #{offeringStatus,jdbcType=VARCHAR}, 
      #{offeringStatusTime,jdbcType=TIMESTAMP}, #{composition,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=BIGINT}, #{size,jdbcType=VARCHAR}, #{operType,jdbcType=VARCHAR}, 
      #{recommendPrice,jdbcType=BIGINT}, #{saleObject,jdbcType=VARCHAR}, #{price,jdbcType=BIGINT}, 
      #{unit,jdbcType=VARCHAR}, #{marketName,jdbcType=VARCHAR}, #{marketCode,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{receiveOrder,jdbcType=VARCHAR}, 
      #{custCode,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR}, 
      #{mainOfferingCode,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, 
      #{saleModel,jdbcType=VARCHAR}, #{project,jdbcType=VARCHAR}, #{pointStatus,jdbcType=INTEGER}, 
      #{cooperatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleteTime,jdbcType=TIMESTAMP}, #{packageType,jdbcType=VARCHAR}, #{skuAbbreviation,jdbcType=VARCHAR}, 
      #{receiveOrderName,jdbcType=VARCHAR}, #{receiveOrderPhone,jdbcType=VARCHAR}, #{deliverName,jdbcType=VARCHAR}, 
      #{deliverPhone,jdbcType=VARCHAR}, #{aftermarketName,jdbcType=VARCHAR}, #{aftermarketPhone,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_offering_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="offeringCode != null">
        offering_code,
      </if>
      <if test="offeringName != null">
        offering_name,
      </if>
      <if test="offeringStatus != null">
        offering_status,
      </if>
      <if test="offeringStatusTime != null">
        offering_status_time,
      </if>
      <if test="composition != null">
        composition,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="recommendPrice != null">
        recommend_price,
      </if>
      <if test="saleObject != null">
        sale_object,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="marketName != null">
        market_name,
      </if>
      <if test="marketCode != null">
        market_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="receiveOrder != null">
        receive_order,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="mainOfferingCode != null">
        main_offering_code,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="saleModel != null">
        sale_model,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="pointStatus != null">
        point_status,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="packageType != null">
        package_type,
      </if>
      <if test="skuAbbreviation != null">
        sku_abbreviation,
      </if>
      <if test="receiveOrderName != null">
        receive_order_name,
      </if>
      <if test="receiveOrderPhone != null">
        receive_order_phone,
      </if>
      <if test="deliverName != null">
        deliver_name,
      </if>
      <if test="deliverPhone != null">
        deliver_phone,
      </if>
      <if test="aftermarketName != null">
        aftermarket_name,
      </if>
      <if test="aftermarketPhone != null">
        aftermarket_phone,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatus != null">
        #{offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatusTime != null">
        #{offeringStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="composition != null">
        #{composition,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=BIGINT},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="recommendPrice != null">
        #{recommendPrice,jdbcType=BIGINT},
      </if>
      <if test="saleObject != null">
        #{saleObject,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=BIGINT},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="marketName != null">
        #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrder != null">
        #{receiveOrder,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="mainOfferingCode != null">
        #{mainOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="saleModel != null">
        #{saleModel,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="pointStatus != null">
        #{pointStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageType != null">
        #{packageType,jdbcType=VARCHAR},
      </if>
      <if test="skuAbbreviation != null">
        #{skuAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderName != null">
        #{receiveOrderName,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderPhone != null">
        #{receiveOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliverName != null">
        #{deliverName,jdbcType=VARCHAR},
      </if>
      <if test="deliverPhone != null">
        #{deliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketName != null">
        #{aftermarketName,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketPhone != null">
        #{aftermarketPhone,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from sku_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_offering_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringCode != null">
        offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringName != null">
        offering_name = #{record.offeringName,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringStatus != null">
        offering_status = #{record.offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringStatusTime != null">
        offering_status_time = #{record.offeringStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.composition != null">
        composition = #{record.composition,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=BIGINT},
      </if>
      <if test="record.size != null">
        size = #{record.size,jdbcType=VARCHAR},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendPrice != null">
        recommend_price = #{record.recommendPrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleObject != null">
        sale_object = #{record.saleObject,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=BIGINT},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.marketName != null">
        market_name = #{record.marketName,jdbcType=VARCHAR},
      </if>
      <if test="record.marketCode != null">
        market_code = #{record.marketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveOrder != null">
        receive_order = #{record.receiveOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=VARCHAR},
      </if>
      <if test="record.mainOfferingCode != null">
        main_offering_code = #{record.mainOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.saleModel != null">
        sale_model = #{record.saleModel,jdbcType=VARCHAR},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.pointStatus != null">
        point_status = #{record.pointStatus,jdbcType=INTEGER},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.packageType != null">
        package_type = #{record.packageType,jdbcType=VARCHAR},
      </if>
      <if test="record.skuAbbreviation != null">
        sku_abbreviation = #{record.skuAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveOrderName != null">
        receive_order_name = #{record.receiveOrderName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveOrderPhone != null">
        receive_order_phone = #{record.receiveOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverName != null">
        deliver_name = #{record.deliverName,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverPhone != null">
        deliver_phone = #{record.deliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.aftermarketName != null">
        aftermarket_name = #{record.aftermarketName,jdbcType=VARCHAR},
      </if>
      <if test="record.aftermarketPhone != null">
        aftermarket_phone = #{record.aftermarketPhone,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_offering_info
    set id = #{record.id,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      offering_name = #{record.offeringName,jdbcType=VARCHAR},
      offering_status = #{record.offeringStatus,jdbcType=VARCHAR},
      offering_status_time = #{record.offeringStatusTime,jdbcType=TIMESTAMP},
      composition = #{record.composition,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=BIGINT},
      size = #{record.size,jdbcType=VARCHAR},
      oper_type = #{record.operType,jdbcType=VARCHAR},
      recommend_price = #{record.recommendPrice,jdbcType=BIGINT},
      sale_object = #{record.saleObject,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=BIGINT},
      unit = #{record.unit,jdbcType=VARCHAR},
      market_name = #{record.marketName,jdbcType=VARCHAR},
      market_code = #{record.marketCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=VARCHAR},
      receive_order = #{record.receiveOrder,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      card_type = #{record.cardType,jdbcType=VARCHAR},
      main_offering_code = #{record.mainOfferingCode,jdbcType=VARCHAR},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      sale_model = #{record.saleModel,jdbcType=VARCHAR},
      project = #{record.project,jdbcType=VARCHAR},
      point_status = #{record.pointStatus,jdbcType=INTEGER},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      package_type = #{record.packageType,jdbcType=VARCHAR},
      sku_abbreviation = #{record.skuAbbreviation,jdbcType=VARCHAR},
      receive_order_name = #{record.receiveOrderName,jdbcType=VARCHAR},
      receive_order_phone = #{record.receiveOrderPhone,jdbcType=VARCHAR},
      deliver_name = #{record.deliverName,jdbcType=VARCHAR},
      deliver_phone = #{record.deliverPhone,jdbcType=VARCHAR},
      aftermarket_name = #{record.aftermarketName,jdbcType=VARCHAR},
      aftermarket_phone = #{record.aftermarketPhone,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_offering_info
    <set>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        offering_code = #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        offering_name = #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatus != null">
        offering_status = #{offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatusTime != null">
        offering_status_time = #{offeringStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="composition != null">
        composition = #{composition,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=BIGINT},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="recommendPrice != null">
        recommend_price = #{recommendPrice,jdbcType=BIGINT},
      </if>
      <if test="saleObject != null">
        sale_object = #{saleObject,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=BIGINT},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="marketName != null">
        market_name = #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        market_code = #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrder != null">
        receive_order = #{receiveOrder,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="mainOfferingCode != null">
        main_offering_code = #{mainOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="saleModel != null">
        sale_model = #{saleModel,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="pointStatus != null">
        point_status = #{pointStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageType != null">
        package_type = #{packageType,jdbcType=VARCHAR},
      </if>
      <if test="skuAbbreviation != null">
        sku_abbreviation = #{skuAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderName != null">
        receive_order_name = #{receiveOrderName,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderPhone != null">
        receive_order_phone = #{receiveOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliverName != null">
        deliver_name = #{deliverName,jdbcType=VARCHAR},
      </if>
      <if test="deliverPhone != null">
        deliver_phone = #{deliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketName != null">
        aftermarket_name = #{aftermarketName,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketPhone != null">
        aftermarket_phone = #{aftermarketPhone,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.SkuOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_offering_info
    set spu_id = #{spuId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      offering_code = #{offeringCode,jdbcType=VARCHAR},
      offering_name = #{offeringName,jdbcType=VARCHAR},
      offering_status = #{offeringStatus,jdbcType=VARCHAR},
      offering_status_time = #{offeringStatusTime,jdbcType=TIMESTAMP},
      composition = #{composition,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=BIGINT},
      size = #{size,jdbcType=VARCHAR},
      oper_type = #{operType,jdbcType=VARCHAR},
      recommend_price = #{recommendPrice,jdbcType=BIGINT},
      sale_object = #{saleObject,jdbcType=VARCHAR},
      price = #{price,jdbcType=BIGINT},
      unit = #{unit,jdbcType=VARCHAR},
      market_name = #{marketName,jdbcType=VARCHAR},
      market_code = #{marketCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=VARCHAR},
      receive_order = #{receiveOrder,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=VARCHAR},
      main_offering_code = #{mainOfferingCode,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      sale_model = #{saleModel,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      point_status = #{pointStatus,jdbcType=INTEGER},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      package_type = #{packageType,jdbcType=VARCHAR},
      sku_abbreviation = #{skuAbbreviation,jdbcType=VARCHAR},
      receive_order_name = #{receiveOrderName,jdbcType=VARCHAR},
      receive_order_phone = #{receiveOrderPhone,jdbcType=VARCHAR},
      deliver_name = #{deliverName,jdbcType=VARCHAR},
      deliver_phone = #{deliverPhone,jdbcType=VARCHAR},
      aftermarket_name = #{aftermarketName,jdbcType=VARCHAR},
      aftermarket_phone = #{aftermarketPhone,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_offering_info
    (id, spu_id, spu_code, offering_code, offering_name, offering_status, offering_status_time, 
      composition, model, quantity, size, oper_type, recommend_price, sale_object, price, 
      unit, market_name, market_code, supplier_name, product_type, receive_order, cust_code, 
      cust_name, card_type, main_offering_code, template_name, template_id, sale_model, 
      project, point_status, cooperator_id, create_time, update_time, delete_time, package_type, 
      sku_abbreviation, receive_order_name, receive_order_phone, deliver_name, deliver_phone, 
      aftermarket_name, aftermarket_phone)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuId,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, 
        #{item.offeringCode,jdbcType=VARCHAR}, #{item.offeringName,jdbcType=VARCHAR}, #{item.offeringStatus,jdbcType=VARCHAR}, 
        #{item.offeringStatusTime,jdbcType=TIMESTAMP}, #{item.composition,jdbcType=VARCHAR}, 
        #{item.model,jdbcType=VARCHAR}, #{item.quantity,jdbcType=BIGINT}, #{item.size,jdbcType=VARCHAR}, 
        #{item.operType,jdbcType=VARCHAR}, #{item.recommendPrice,jdbcType=BIGINT}, #{item.saleObject,jdbcType=VARCHAR}, 
        #{item.price,jdbcType=BIGINT}, #{item.unit,jdbcType=VARCHAR}, #{item.marketName,jdbcType=VARCHAR}, 
        #{item.marketCode,jdbcType=VARCHAR}, #{item.supplierName,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR}, 
        #{item.receiveOrder,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, 
        #{item.cardType,jdbcType=VARCHAR}, #{item.mainOfferingCode,jdbcType=VARCHAR}, #{item.templateName,jdbcType=VARCHAR}, 
        #{item.templateId,jdbcType=VARCHAR}, #{item.saleModel,jdbcType=VARCHAR}, #{item.project,jdbcType=VARCHAR}, 
        #{item.pointStatus,jdbcType=INTEGER}, #{item.cooperatorId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleteTime,jdbcType=TIMESTAMP}, #{item.packageType,jdbcType=VARCHAR}, 
        #{item.skuAbbreviation,jdbcType=VARCHAR}, #{item.receiveOrderName,jdbcType=VARCHAR}, 
        #{item.receiveOrderPhone,jdbcType=VARCHAR}, #{item.deliverName,jdbcType=VARCHAR}, 
        #{item.deliverPhone,jdbcType=VARCHAR}, #{item.aftermarketName,jdbcType=VARCHAR}, 
        #{item.aftermarketPhone,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 23 17:13:54 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_offering_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_code'.toString() == column.value">
          #{item.offeringCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_name'.toString() == column.value">
          #{item.offeringName,jdbcType=VARCHAR}
        </if>
        <if test="'offering_status'.toString() == column.value">
          #{item.offeringStatus,jdbcType=VARCHAR}
        </if>
        <if test="'offering_status_time'.toString() == column.value">
          #{item.offeringStatusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'composition'.toString() == column.value">
          #{item.composition,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'quantity'.toString() == column.value">
          #{item.quantity,jdbcType=BIGINT}
        </if>
        <if test="'size'.toString() == column.value">
          #{item.size,jdbcType=VARCHAR}
        </if>
        <if test="'oper_type'.toString() == column.value">
          #{item.operType,jdbcType=VARCHAR}
        </if>
        <if test="'recommend_price'.toString() == column.value">
          #{item.recommendPrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_object'.toString() == column.value">
          #{item.saleObject,jdbcType=VARCHAR}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=BIGINT}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'market_name'.toString() == column.value">
          #{item.marketName,jdbcType=VARCHAR}
        </if>
        <if test="'market_code'.toString() == column.value">
          #{item.marketCode,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_name'.toString() == column.value">
          #{item.supplierName,jdbcType=VARCHAR}
        </if>
        <if test="'product_type'.toString() == column.value">
          #{item.productType,jdbcType=VARCHAR}
        </if>
        <if test="'receive_order'.toString() == column.value">
          #{item.receiveOrder,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'card_type'.toString() == column.value">
          #{item.cardType,jdbcType=VARCHAR}
        </if>
        <if test="'main_offering_code'.toString() == column.value">
          #{item.mainOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'template_name'.toString() == column.value">
          #{item.templateName,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'sale_model'.toString() == column.value">
          #{item.saleModel,jdbcType=VARCHAR}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'point_status'.toString() == column.value">
          #{item.pointStatus,jdbcType=INTEGER}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'package_type'.toString() == column.value">
          #{item.packageType,jdbcType=VARCHAR}
        </if>
        <if test="'sku_abbreviation'.toString() == column.value">
          #{item.skuAbbreviation,jdbcType=VARCHAR}
        </if>
        <if test="'receive_order_name'.toString() == column.value">
          #{item.receiveOrderName,jdbcType=VARCHAR}
        </if>
        <if test="'receive_order_phone'.toString() == column.value">
          #{item.receiveOrderPhone,jdbcType=VARCHAR}
        </if>
        <if test="'deliver_name'.toString() == column.value">
          #{item.deliverName,jdbcType=VARCHAR}
        </if>
        <if test="'deliver_phone'.toString() == column.value">
          #{item.deliverPhone,jdbcType=VARCHAR}
        </if>
        <if test="'aftermarket_name'.toString() == column.value">
          #{item.aftermarketName,jdbcType=VARCHAR}
        </if>
        <if test="'aftermarket_phone'.toString() == column.value">
          #{item.aftermarketPhone,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>