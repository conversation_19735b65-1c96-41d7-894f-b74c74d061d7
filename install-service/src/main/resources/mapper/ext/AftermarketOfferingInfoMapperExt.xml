<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.ext.AftermarketOfferingInfoMapperExt">
  <select id="getList" parameterType="com.chinamobile.install.pojo.param.AfterMarketProductListParam" resultType="com.chinamobile.install.pojo.mapper.AfterMarketProductDO">
    SELECT
	afInfo.after_market_internal_name afterMarketInternalName,
	afInfo.after_market_external_name afterMarketExternalName,
	afInfo.after_market_code afterMarketCode,
	afInfo.aftermarket_type aftermarketType,
	afCode.id aftermarketOfferingCodeId,
	afCode.sku_offering_code skuOfferingCode,
	afCode.offering_code offeringCode,
	IF(afCode.config_time is NULL,0,1) configStatus,
	IF(afCode.config_time is NULL,null,DATE_FORMAT(afCode.config_time,'%Y-%m-%d %H:%i:%s')) configTime,
	u.partner_name partnerName,
	u.`name` cooperatorName,
    afCode.spu_offering_version spuOfferingVersion,
    afCode.sku_offering_version skuOfferingVersion,
    afCode.atom_offering_version atomOfferingVersion,
    afCode.after_market_version afterMarketVersion
  FROM
      aftermarket_offering_info afInfo
  LEFT JOIN aftermarket_offering_code afCode ON afInfo.after_market_code = afCode.after_market_code  and   (afCode.sku_offering_code  is not null or afCode.offering_code is not null)
  LEFT JOIN `user_partner` u on afCode.admin_cooperator_id = u.user_id
  WHERE afInfo.oper_type = 'A'
  <if test=" param.afterMarketInternalName != null and param.afterMarketInternalName !=''">
    and afInfo.after_market_internal_name like concat ('%',#{param.afterMarketInternalName},'%')
  </if>
  <if test=" param.afterMarketExternalName != null and param.afterMarketExternalName !=''">
    and afInfo.after_market_external_name like concat ('%',#{param.afterMarketExternalName},'%')
  </if>
  <if test=" param.afterMarketCode != null and param.afterMarketCode !=''">
    and afInfo.after_market_code like concat ('%',#{param.afterMarketCode},'%')
  </if>
  <if test=" param.aftermarketType != null and param.aftermarketType !=''">
    and afInfo.aftermarket_type = #{param.aftermarketType}
  </if>
  <if test=" param.skuOfferingCode != null and param.skuOfferingCode !=''">
    and afCode.sku_offering_code like concat ('%',#{param.skuOfferingCode},'%')
  </if>
  <if test=" param.offeringCode != null and param.offeringCode !=''">
    and afCode.offering_code like concat ('%',#{param.offeringCode},'%')
  </if>
  <if test=" param.configStatus != null and param.configStatus == 1">
    and afCode.config_time is not NULL
  </if>
  <if test=" param.configStatus != null and param.configStatus == 0">
    and afCode.config_time is NULL
  </if>
  <if test=" param.partnerName != null and param.partnerName !=''">
    and u.partner_name like concat ('%',#{param.partnerName},'%')
  </if>
  ORDER BY afCode.create_time DESC
  </select>

    <select id="selectAfterMarketProductExport" parameterType="com.chinamobile.install.pojo.param.AfterMarketProductExportParam" resultType="com.chinamobile.install.pojo.mapper.AfterMarkerProductExportDO">
        SELECT
        afInfo.after_market_internal_name afterMarketInternalName,
        afInfo.after_market_external_name afterMarketExternalName,
        afInfo.after_market_code afterMarketCode,
        IF(afInfo.oper_type = 'A','已生效','未生效') operType,
        afInfo.sell_price/1000 sellPrice,
        afInfo.settle_price/1000  settlePrice,
        IF(afInfo.mandatory = '1','必选','非必选') mandatory,
        afCode.sku_offering_code skuOfferingCode,
        afCode.offering_code atomOfferingCode,
        case
        when isnull(afInfo.aftermarket_type) then ''
        when afInfo.aftermarket_type = 1 then 'OneNET/OnePark属地化服务'
        when afInfo.aftermarket_type = 2 then '铁通增值服务'
        when afInfo.aftermarket_type = 3 then '铁通增值服务(卡+X专用)'
        else ''
        end as aftermarketType,
        case
        when isnull(afCode.order_take_type) then ''
        when afCode.order_take_type = 1 then 'OS接单'
        when afCode.order_take_type = 2 then '省内接单'
        else ''
        end as orderTakeType,
        u.partner_name partnerName,
        u.`name` cooperatorName,
        installManager.partner_name installManagerCompany,
        installManager.`name` installManagerName,
        ss.id  standardServiceCode,
        ss.`name`  standardServiceName,
        dept.full_name  department,
        ss.real_product_name realProductName,
        pp.`name` property,
        afCode.after_market_version afterMarketVersion,
        afCode.atom_offering_version atomOfferingVersion,
        afCode.sku_offering_version skuOfferingVersion,
        afCode.spu_offering_version spuOfferingVersion,
        afCode.province_install_platform provinceInstallPlatform
        FROM
        aftermarket_offering_info afInfo
        LEFT JOIN aftermarket_offering_code afCode ON afInfo.after_market_code = afCode.after_market_code and (afCode.sku_offering_code  is not null or afCode.offering_code is not null)
        LEFT JOIN `user_partner` u on afCode.admin_cooperator_id = u.user_id
        LEFT JOIN `user_partner` installManager on afCode.install_manager_id = installManager.user_id
        LEFT JOIN after_market_std_service amss on amss.after_market_code = afInfo.after_market_code and amss.after_market_version = afCode.after_market_version and amss.aftermarket_offering_code_id = afCode.id
        LEFT JOIN standard_service ss on ss.id = amss.std_service_id
        LEFT JOIN department dept on dept.id = ss.product_department_id
        LEFT JOIN product_property pp on ss.product_property_id = pp.id
        WHERE afInfo.oper_type = 'A'
        <if test=" param.afterMarketInternalName != null and param.afterMarketInternalName !=''">
            and afInfo.after_market_internal_name like concat ('%',#{param.afterMarketInternalName},'%')
        </if>
        <if test=" param.aftermarketType != null and param.aftermarketType !=''">
            and afInfo.aftermarket_type = #{param.aftermarketType}
        </if>
        <if test=" param.operType != null and param.operType !=''">
            and afInfo.oper_type = #{param.operType}
        </if>
        <if test=" param.orderTakeType != null and param.orderTakeType !=''">
            and afCode.order_take_type = #{param.orderTakeType}
        </if>
        <if test=" param.partnerName != null and param.partnerName !=''">
            and u.partner_name like concat ('%',#{param.partnerName},'%')
        </if>
        ORDER BY afCode.create_time DESC
    </select>
</mapper>