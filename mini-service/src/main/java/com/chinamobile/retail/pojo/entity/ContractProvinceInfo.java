package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class ContractProvinceInfo implements Serializable {
    /**
     * 商城省份编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String mallCode;

    /**
     * K3省份编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String k3Code;

    /**
     * 商城省份名称
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String mallName;

    /**
     * 商城省会城市编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String capitalMallCode;

    /**
     * 商城省会城市名称
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String capitalMallName;

    /**
     * K3省会城市编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String capitalK3Code;

    /**
     * 河南省采编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private String scmCode;

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.mall_code
     *
     * @return the value of supply_chain..contract_province_info.mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getMallCode() {
        return mallCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withMallCode(String mallCode) {
        this.setMallCode(mallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.mall_code
     *
     * @param mallCode the value for supply_chain..contract_province_info.mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setMallCode(String mallCode) {
        this.mallCode = mallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.k3_code
     *
     * @return the value of supply_chain..contract_province_info.k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getK3Code() {
        return k3Code;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withK3Code(String k3Code) {
        this.setK3Code(k3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.k3_code
     *
     * @param k3Code the value for supply_chain..contract_province_info.k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setK3Code(String k3Code) {
        this.k3Code = k3Code;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.mall_name
     *
     * @return the value of supply_chain..contract_province_info.mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getMallName() {
        return mallName;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withMallName(String mallName) {
        this.setMallName(mallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.mall_name
     *
     * @param mallName the value for supply_chain..contract_province_info.mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setMallName(String mallName) {
        this.mallName = mallName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.capital_mall_code
     *
     * @return the value of supply_chain..contract_province_info.capital_mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getCapitalMallCode() {
        return capitalMallCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withCapitalMallCode(String capitalMallCode) {
        this.setCapitalMallCode(capitalMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.capital_mall_code
     *
     * @param capitalMallCode the value for supply_chain..contract_province_info.capital_mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setCapitalMallCode(String capitalMallCode) {
        this.capitalMallCode = capitalMallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.capital_mall_name
     *
     * @return the value of supply_chain..contract_province_info.capital_mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getCapitalMallName() {
        return capitalMallName;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withCapitalMallName(String capitalMallName) {
        this.setCapitalMallName(capitalMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.capital_mall_name
     *
     * @param capitalMallName the value for supply_chain..contract_province_info.capital_mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setCapitalMallName(String capitalMallName) {
        this.capitalMallName = capitalMallName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.capital_k3_code
     *
     * @return the value of supply_chain..contract_province_info.capital_k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getCapitalK3Code() {
        return capitalK3Code;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withCapitalK3Code(String capitalK3Code) {
        this.setCapitalK3Code(capitalK3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.capital_k3_code
     *
     * @param capitalK3Code the value for supply_chain..contract_province_info.capital_k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setCapitalK3Code(String capitalK3Code) {
        this.capitalK3Code = capitalK3Code;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_province_info.scm_code
     *
     * @return the value of supply_chain..contract_province_info.scm_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public String getScmCode() {
        return scmCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public ContractProvinceInfo withScmCode(String scmCode) {
        this.setScmCode(scmCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_province_info.scm_code
     *
     * @param scmCode the value for supply_chain..contract_province_info.scm_code
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public void setScmCode(String scmCode) {
        this.scmCode = scmCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", mallCode=").append(mallCode);
        sb.append(", k3Code=").append(k3Code);
        sb.append(", mallName=").append(mallName);
        sb.append(", capitalMallCode=").append(capitalMallCode);
        sb.append(", capitalMallName=").append(capitalMallName);
        sb.append(", capitalK3Code=").append(capitalK3Code);
        sb.append(", scmCode=").append(scmCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContractProvinceInfo other = (ContractProvinceInfo) that;
        return (this.getMallCode() == null ? other.getMallCode() == null : this.getMallCode().equals(other.getMallCode()))
            && (this.getK3Code() == null ? other.getK3Code() == null : this.getK3Code().equals(other.getK3Code()))
            && (this.getMallName() == null ? other.getMallName() == null : this.getMallName().equals(other.getMallName()))
            && (this.getCapitalMallCode() == null ? other.getCapitalMallCode() == null : this.getCapitalMallCode().equals(other.getCapitalMallCode()))
            && (this.getCapitalMallName() == null ? other.getCapitalMallName() == null : this.getCapitalMallName().equals(other.getCapitalMallName()))
            && (this.getCapitalK3Code() == null ? other.getCapitalK3Code() == null : this.getCapitalK3Code().equals(other.getCapitalK3Code()))
            && (this.getScmCode() == null ? other.getScmCode() == null : this.getScmCode().equals(other.getScmCode()));
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getMallCode() == null) ? 0 : getMallCode().hashCode());
        result = prime * result + ((getK3Code() == null) ? 0 : getK3Code().hashCode());
        result = prime * result + ((getMallName() == null) ? 0 : getMallName().hashCode());
        result = prime * result + ((getCapitalMallCode() == null) ? 0 : getCapitalMallCode().hashCode());
        result = prime * result + ((getCapitalMallName() == null) ? 0 : getCapitalMallName().hashCode());
        result = prime * result + ((getCapitalK3Code() == null) ? 0 : getCapitalK3Code().hashCode());
        result = prime * result + ((getScmCode() == null) ? 0 : getScmCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:25 CST 2024
     */
    public enum Column {
        mallCode("mall_code", "mallCode", "VARCHAR", false),
        k3Code("k3_code", "k3Code", "VARCHAR", false),
        mallName("mall_name", "mallName", "VARCHAR", false),
        capitalMallCode("capital_mall_code", "capitalMallCode", "VARCHAR", false),
        capitalMallName("capital_mall_name", "capitalMallName", "VARCHAR", false),
        capitalK3Code("capital_k3_code", "capitalK3Code", "VARCHAR", false),
        scmCode("scm_code", "scmCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:25 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}