package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景需求
 *
 * <AUTHOR>
public class MiniProgramSceneRequirements implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String id;

    /**
     * 场景id
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String sceneId;

    /**
     * 一级目录id
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String firstDirectoryId;

    /**
     * 二级目录id
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String secondDirectoryId;

    /**
     * 附件url
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String attachmentFileUrl;

    /**
     * 省份名称
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String provinceName;

    /**
     * 省份编码
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String provinceCode;

    /**
     * 城市名称
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String cityName;

    /**
     * 城市编码
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String cityCode;

    /**
     * 联系人
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String contact;

    /**
     * 联系电话
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String phone;

    /**
     * 创建人用户id
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String createUid;

    /**
     * 商机经理用户id
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private String partnerBusinessId;

    /**
     * 审核状态，0-待审核、1-已拒绝、2-已派发
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private Integer auditState;

    /**
     * 是否已删除，0-否，1-是
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private Boolean deleted;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.id
     *
     * @return the value of supply_chain..mini_program_scene_requirements.id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.id
     *
     * @param id the value for supply_chain..mini_program_scene_requirements.id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.scene_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements.scene_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getSceneId() {
        return sceneId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withSceneId(String sceneId) {
        this.setSceneId(sceneId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.scene_id
     *
     * @param sceneId the value for supply_chain..mini_program_scene_requirements.scene_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setSceneId(String sceneId) {
        this.sceneId = sceneId == null ? null : sceneId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.first_directory_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements.first_directory_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getFirstDirectoryId() {
        return firstDirectoryId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withFirstDirectoryId(String firstDirectoryId) {
        this.setFirstDirectoryId(firstDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.first_directory_id
     *
     * @param firstDirectoryId the value for supply_chain..mini_program_scene_requirements.first_directory_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setFirstDirectoryId(String firstDirectoryId) {
        this.firstDirectoryId = firstDirectoryId == null ? null : firstDirectoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.second_directory_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements.second_directory_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getSecondDirectoryId() {
        return secondDirectoryId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withSecondDirectoryId(String secondDirectoryId) {
        this.setSecondDirectoryId(secondDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.second_directory_id
     *
     * @param secondDirectoryId the value for supply_chain..mini_program_scene_requirements.second_directory_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setSecondDirectoryId(String secondDirectoryId) {
        this.secondDirectoryId = secondDirectoryId == null ? null : secondDirectoryId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.attachment_file_url
     *
     * @return the value of supply_chain..mini_program_scene_requirements.attachment_file_url
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getAttachmentFileUrl() {
        return attachmentFileUrl;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withAttachmentFileUrl(String attachmentFileUrl) {
        this.setAttachmentFileUrl(attachmentFileUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.attachment_file_url
     *
     * @param attachmentFileUrl the value for supply_chain..mini_program_scene_requirements.attachment_file_url
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setAttachmentFileUrl(String attachmentFileUrl) {
        this.attachmentFileUrl = attachmentFileUrl == null ? null : attachmentFileUrl.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.province_name
     *
     * @return the value of supply_chain..mini_program_scene_requirements.province_name
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.province_name
     *
     * @param provinceName the value for supply_chain..mini_program_scene_requirements.province_name
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.province_code
     *
     * @return the value of supply_chain..mini_program_scene_requirements.province_code
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.province_code
     *
     * @param provinceCode the value for supply_chain..mini_program_scene_requirements.province_code
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.city_name
     *
     * @return the value of supply_chain..mini_program_scene_requirements.city_name
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.city_name
     *
     * @param cityName the value for supply_chain..mini_program_scene_requirements.city_name
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.city_code
     *
     * @return the value of supply_chain..mini_program_scene_requirements.city_code
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.city_code
     *
     * @param cityCode the value for supply_chain..mini_program_scene_requirements.city_code
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.contact
     *
     * @return the value of supply_chain..mini_program_scene_requirements.contact
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getContact() {
        return contact;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withContact(String contact) {
        this.setContact(contact);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.contact
     *
     * @param contact the value for supply_chain..mini_program_scene_requirements.contact
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setContact(String contact) {
        this.contact = contact == null ? null : contact.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.phone
     *
     * @return the value of supply_chain..mini_program_scene_requirements.phone
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.phone
     *
     * @param phone the value for supply_chain..mini_program_scene_requirements.phone
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.create_uid
     *
     * @return the value of supply_chain..mini_program_scene_requirements.create_uid
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getCreateUid() {
        return createUid;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withCreateUid(String createUid) {
        this.setCreateUid(createUid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.create_uid
     *
     * @param createUid the value for supply_chain..mini_program_scene_requirements.create_uid
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setCreateUid(String createUid) {
        this.createUid = createUid == null ? null : createUid.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.partner_business_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements.partner_business_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getPartnerBusinessId() {
        return partnerBusinessId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withPartnerBusinessId(String partnerBusinessId) {
        this.setPartnerBusinessId(partnerBusinessId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.partner_business_id
     *
     * @param partnerBusinessId the value for supply_chain..mini_program_scene_requirements.partner_business_id
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setPartnerBusinessId(String partnerBusinessId) {
        this.partnerBusinessId = partnerBusinessId == null ? null : partnerBusinessId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.audit_state
     *
     * @return the value of supply_chain..mini_program_scene_requirements.audit_state
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Integer getAuditState() {
        return auditState;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withAuditState(Integer auditState) {
        this.setAuditState(auditState);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.audit_state
     *
     * @param auditState the value for supply_chain..mini_program_scene_requirements.audit_state
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.deleted
     *
     * @return the value of supply_chain..mini_program_scene_requirements.deleted
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withDeleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.deleted
     *
     * @param deleted the value for supply_chain..mini_program_scene_requirements.deleted
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.create_time
     *
     * @return the value of supply_chain..mini_program_scene_requirements.create_time
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.create_time
     *
     * @param createTime the value for supply_chain..mini_program_scene_requirements.create_time
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements.update_time
     *
     * @return the value of supply_chain..mini_program_scene_requirements.update_time
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirements withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_scene_requirements.update_time
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sceneId=").append(sceneId);
        sb.append(", firstDirectoryId=").append(firstDirectoryId);
        sb.append(", secondDirectoryId=").append(secondDirectoryId);
        sb.append(", attachmentFileUrl=").append(attachmentFileUrl);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", contact=").append(contact);
        sb.append(", phone=").append(phone);
        sb.append(", createUid=").append(createUid);
        sb.append(", partnerBusinessId=").append(partnerBusinessId);
        sb.append(", auditState=").append(auditState);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSceneRequirements other = (MiniProgramSceneRequirements) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSceneId() == null ? other.getSceneId() == null : this.getSceneId().equals(other.getSceneId()))
            && (this.getFirstDirectoryId() == null ? other.getFirstDirectoryId() == null : this.getFirstDirectoryId().equals(other.getFirstDirectoryId()))
            && (this.getSecondDirectoryId() == null ? other.getSecondDirectoryId() == null : this.getSecondDirectoryId().equals(other.getSecondDirectoryId()))
            && (this.getAttachmentFileUrl() == null ? other.getAttachmentFileUrl() == null : this.getAttachmentFileUrl().equals(other.getAttachmentFileUrl()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getContact() == null ? other.getContact() == null : this.getContact().equals(other.getContact()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getCreateUid() == null ? other.getCreateUid() == null : this.getCreateUid().equals(other.getCreateUid()))
            && (this.getPartnerBusinessId() == null ? other.getPartnerBusinessId() == null : this.getPartnerBusinessId().equals(other.getPartnerBusinessId()))
            && (this.getAuditState() == null ? other.getAuditState() == null : this.getAuditState().equals(other.getAuditState()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSceneId() == null) ? 0 : getSceneId().hashCode());
        result = prime * result + ((getFirstDirectoryId() == null) ? 0 : getFirstDirectoryId().hashCode());
        result = prime * result + ((getSecondDirectoryId() == null) ? 0 : getSecondDirectoryId().hashCode());
        result = prime * result + ((getAttachmentFileUrl() == null) ? 0 : getAttachmentFileUrl().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getContact() == null) ? 0 : getContact().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getCreateUid() == null) ? 0 : getCreateUid().hashCode());
        result = prime * result + ((getPartnerBusinessId() == null) ? 0 : getPartnerBusinessId().hashCode());
        result = prime * result + ((getAuditState() == null) ? 0 : getAuditState().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        sceneId("scene_id", "sceneId", "VARCHAR", false),
        firstDirectoryId("first_directory_id", "firstDirectoryId", "VARCHAR", false),
        secondDirectoryId("second_directory_id", "secondDirectoryId", "VARCHAR", false),
        attachmentFileUrl("attachment_file_url", "attachmentFileUrl", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        contact("contact", "contact", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        createUid("create_uid", "createUid", "VARCHAR", false),
        partnerBusinessId("partner_business_id", "partnerBusinessId", "VARCHAR", false),
        auditState("audit_state", "auditState", "INTEGER", false),
        deleted("deleted", "deleted", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}