package com.chinamobile.retail.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.chinamobile.retail.pojo.vo.OrderBackListVO;
import lombok.Data;

/**
 * created by l<PERSON><PERSON>ng on 2022/9/6 10:06
 */
@Data
public class OrderExportListDTO {

    @Excel(name = "下单时间")
    private String createTime;

    @Excel(name = "订单编号")
    private String orderId;

    @Excel(name = "产品名称(SPU)")
    private String spuOfferingName;

    @Excel(name = "规格名称(SKU)")
    private String skuOfferingName;

    @Excel(name = "单价")
    private Double price;

    @Excel(name = "数量")
    private Integer count;

    @Excel(name = "订单金额")
    private Double amount;

    @Excel(name = "订单状态")
    private String orderStatus;

    @Excel(name = "订单积分")
    private Double point;

    @Excel(name = "积分是否可兑换")
    private String available;

    @Excel(name = "合伙人名称")
    private String partnerName;

    @Excel(name = "合伙人电话")
    private String phone;

    @Excel(name = "合伙人省份")
    private String partnerProvince;

    @Excel(name = "合伙人地市")
    private String partnerCity;

    @Excel(name = "积分供应商")
    private String pointSupplier;

}
