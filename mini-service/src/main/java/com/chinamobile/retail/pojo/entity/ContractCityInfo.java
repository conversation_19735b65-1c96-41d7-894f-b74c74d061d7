package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class ContractCityInfo implements Serializable {
    /**
     * 商城城市编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String mallCode;

    /**
     * 商城城市名称
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String mallName;

    /**
     * 商城省份编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String provinceMallCode;

    /**
     * 商城省份名称
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String provinceMallName;

    /**
     * K3省份编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String provinceK3Code;

    /**
     * K3城市编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String k3Code;

    /**
     * 河南省采编码
     *
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private String scmCode;

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.mall_code
     *
     * @return the value of supply_chain..contract_city_info.mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getMallCode() {
        return mallCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withMallCode(String mallCode) {
        this.setMallCode(mallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.mall_code
     *
     * @param mallCode the value for supply_chain..contract_city_info.mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setMallCode(String mallCode) {
        this.mallCode = mallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.mall_name
     *
     * @return the value of supply_chain..contract_city_info.mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getMallName() {
        return mallName;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withMallName(String mallName) {
        this.setMallName(mallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.mall_name
     *
     * @param mallName the value for supply_chain..contract_city_info.mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setMallName(String mallName) {
        this.mallName = mallName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.province_mall_code
     *
     * @return the value of supply_chain..contract_city_info.province_mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getProvinceMallCode() {
        return provinceMallCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withProvinceMallCode(String provinceMallCode) {
        this.setProvinceMallCode(provinceMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.province_mall_code
     *
     * @param provinceMallCode the value for supply_chain..contract_city_info.province_mall_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setProvinceMallCode(String provinceMallCode) {
        this.provinceMallCode = provinceMallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.province_mall_name
     *
     * @return the value of supply_chain..contract_city_info.province_mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getProvinceMallName() {
        return provinceMallName;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withProvinceMallName(String provinceMallName) {
        this.setProvinceMallName(provinceMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.province_mall_name
     *
     * @param provinceMallName the value for supply_chain..contract_city_info.province_mall_name
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setProvinceMallName(String provinceMallName) {
        this.provinceMallName = provinceMallName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.province_k3_code
     *
     * @return the value of supply_chain..contract_city_info.province_k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getProvinceK3Code() {
        return provinceK3Code;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withProvinceK3Code(String provinceK3Code) {
        this.setProvinceK3Code(provinceK3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.province_k3_code
     *
     * @param provinceK3Code the value for supply_chain..contract_city_info.province_k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setProvinceK3Code(String provinceK3Code) {
        this.provinceK3Code = provinceK3Code;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.k3_code
     *
     * @return the value of supply_chain..contract_city_info.k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getK3Code() {
        return k3Code;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withK3Code(String k3Code) {
        this.setK3Code(k3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.k3_code
     *
     * @param k3Code the value for supply_chain..contract_city_info.k3_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setK3Code(String k3Code) {
        this.k3Code = k3Code;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_city_info.scm_code
     *
     * @return the value of supply_chain..contract_city_info.scm_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public String getScmCode() {
        return scmCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public ContractCityInfo withScmCode(String scmCode) {
        this.setScmCode(scmCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_city_info.scm_code
     *
     * @param scmCode the value for supply_chain..contract_city_info.scm_code
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public void setScmCode(String scmCode) {
        this.scmCode = scmCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", mallCode=").append(mallCode);
        sb.append(", mallName=").append(mallName);
        sb.append(", provinceMallCode=").append(provinceMallCode);
        sb.append(", provinceMallName=").append(provinceMallName);
        sb.append(", provinceK3Code=").append(provinceK3Code);
        sb.append(", k3Code=").append(k3Code);
        sb.append(", scmCode=").append(scmCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContractCityInfo other = (ContractCityInfo) that;
        return (this.getMallCode() == null ? other.getMallCode() == null : this.getMallCode().equals(other.getMallCode()))
            && (this.getMallName() == null ? other.getMallName() == null : this.getMallName().equals(other.getMallName()))
            && (this.getProvinceMallCode() == null ? other.getProvinceMallCode() == null : this.getProvinceMallCode().equals(other.getProvinceMallCode()))
            && (this.getProvinceMallName() == null ? other.getProvinceMallName() == null : this.getProvinceMallName().equals(other.getProvinceMallName()))
            && (this.getProvinceK3Code() == null ? other.getProvinceK3Code() == null : this.getProvinceK3Code().equals(other.getProvinceK3Code()))
            && (this.getK3Code() == null ? other.getK3Code() == null : this.getK3Code().equals(other.getK3Code()))
            && (this.getScmCode() == null ? other.getScmCode() == null : this.getScmCode().equals(other.getScmCode()));
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getMallCode() == null) ? 0 : getMallCode().hashCode());
        result = prime * result + ((getMallName() == null) ? 0 : getMallName().hashCode());
        result = prime * result + ((getProvinceMallCode() == null) ? 0 : getProvinceMallCode().hashCode());
        result = prime * result + ((getProvinceMallName() == null) ? 0 : getProvinceMallName().hashCode());
        result = prime * result + ((getProvinceK3Code() == null) ? 0 : getProvinceK3Code().hashCode());
        result = prime * result + ((getK3Code() == null) ? 0 : getK3Code().hashCode());
        result = prime * result + ((getScmCode() == null) ? 0 : getScmCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 11:15:31 CST 2024
     */
    public enum Column {
        mallCode("mall_code", "mallCode", "VARCHAR", false),
        mallName("mall_name", "mallName", "VARCHAR", false),
        provinceMallCode("province_mall_code", "provinceMallCode", "VARCHAR", false),
        provinceMallName("province_mall_name", "provinceMallName", "VARCHAR", false),
        provinceK3Code("province_k3_code", "provinceK3Code", "VARCHAR", false),
        k3Code("k3_code", "k3Code", "VARCHAR", false),
        scmCode("scm_code", "scmCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Aug 07 11:15:31 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}