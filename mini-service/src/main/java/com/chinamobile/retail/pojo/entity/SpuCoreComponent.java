package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * spu核心部件配置
 *
 * <AUTHOR>
public class SpuCoreComponent implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private String id;

    /**
     * spu编码
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private String spuCode;

    /**
     * spu核心部件名称
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private String coreComponentName;

    /**
     * spu核心部件头图
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private String coreComponentImg;

    /**
     * 状态,0:已上传、1:已发布、2:已下线、3:审核中,4:已驳回
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private Integer status;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private Integer auditStatus;

    /**
     * 是否删除 0-未删除，1-已删除
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private Boolean isDelete;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private String createUid;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.id
     *
     * @return the value of supply_chain..spu_core_component.id
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.id
     *
     * @param id the value for supply_chain..spu_core_component.id
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.spu_code
     *
     * @return the value of supply_chain..spu_core_component.spu_code
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.spu_code
     *
     * @param spuCode the value for supply_chain..spu_core_component.spu_code
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.core_component_name
     *
     * @return the value of supply_chain..spu_core_component.core_component_name
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public String getCoreComponentName() {
        return coreComponentName;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withCoreComponentName(String coreComponentName) {
        this.setCoreComponentName(coreComponentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.core_component_name
     *
     * @param coreComponentName the value for supply_chain..spu_core_component.core_component_name
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setCoreComponentName(String coreComponentName) {
        this.coreComponentName = coreComponentName;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.core_component_img
     *
     * @return the value of supply_chain..spu_core_component.core_component_img
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public String getCoreComponentImg() {
        return coreComponentImg;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withCoreComponentImg(String coreComponentImg) {
        this.setCoreComponentImg(coreComponentImg);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.core_component_img
     *
     * @param coreComponentImg the value for supply_chain..spu_core_component.core_component_img
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setCoreComponentImg(String coreComponentImg) {
        this.coreComponentImg = coreComponentImg;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.status
     *
     * @return the value of supply_chain..spu_core_component.status
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.status
     *
     * @param status the value for supply_chain..spu_core_component.status
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.audit_status
     *
     * @return the value of supply_chain..spu_core_component.audit_status
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.audit_status
     *
     * @param auditStatus the value for supply_chain..spu_core_component.audit_status
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.is_delete
     *
     * @return the value of supply_chain..spu_core_component.is_delete
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withIsDelete(Boolean isDelete) {
        this.setIsDelete(isDelete);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.is_delete
     *
     * @param isDelete the value for supply_chain..spu_core_component.is_delete
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.create_uid
     *
     * @return the value of supply_chain..spu_core_component.create_uid
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public String getCreateUid() {
        return createUid;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withCreateUid(String createUid) {
        this.setCreateUid(createUid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.create_uid
     *
     * @param createUid the value for supply_chain..spu_core_component.create_uid
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setCreateUid(String createUid) {
        this.createUid = createUid;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.create_time
     *
     * @return the value of supply_chain..spu_core_component.create_time
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.create_time
     *
     * @param createTime the value for supply_chain..spu_core_component.create_time
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_core_component.update_time
     *
     * @return the value of supply_chain..spu_core_component.update_time
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public SpuCoreComponent withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_core_component.update_time
     *
     * @param updateTime the value for supply_chain..spu_core_component.update_time
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", coreComponentName=").append(coreComponentName);
        sb.append(", coreComponentImg=").append(coreComponentImg);
        sb.append(", status=").append(status);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", createUid=").append(createUid);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SpuCoreComponent other = (SpuCoreComponent) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getCoreComponentName() == null ? other.getCoreComponentName() == null : this.getCoreComponentName().equals(other.getCoreComponentName()))
            && (this.getCoreComponentImg() == null ? other.getCoreComponentImg() == null : this.getCoreComponentImg().equals(other.getCoreComponentImg()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getCreateUid() == null ? other.getCreateUid() == null : this.getCreateUid().equals(other.getCreateUid()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getCoreComponentName() == null) ? 0 : getCoreComponentName().hashCode());
        result = prime * result + ((getCoreComponentImg() == null) ? 0 : getCoreComponentImg().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getCreateUid() == null) ? 0 : getCreateUid().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:58:23 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        coreComponentName("core_component_name", "coreComponentName", "VARCHAR", false),
        coreComponentImg("core_component_img", "coreComponentImg", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        isDelete("is_delete", "isDelete", "BIT", false),
        createUid("create_uid", "createUid", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:58:23 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}