package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商城客户信息表
 *
 * <AUTHOR>
public class ShopCustomerInfo implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String id;

    /**
     * 客户编码
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String custCode;

    /**
     * 客户ID
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String custId;

    /**
     * 用户标识
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String userId;

    /**
     * 客户姓名
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String custName;

    /**
     * 注册时间
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private Date registerDate;

    /**
     * 枚举值：0：普通用户  1：一级分销员  2：二级分销员 3:渠道商
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String roleType;

    /**
     * 客户省份
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String beId;

    /**
     * 客户省份
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String provinceName;

    /**
     * 客户地市
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String location;

    /**
     * 客户地市
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String cityName;

    /**
     * 客户区县
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String regionId;

    /**
     * 客户区县
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String regionName;

    /**
     * 用户状态0：待激活（暂不启用） 1：激活  2：暂停（暂不启用） 3：失效
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String clientStatus;

    /**
     * 渠道ID
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorChannelId;

    /**
     * 渠道名称
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorChannelName;

    /**
     * 推荐码
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorReferralCode;

    /**
     * 绑定客户经理姓名
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorMrgInf;

    /**
     * 绑定客户经理工号
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorMrgCode;

    /**
     * 分销员名称
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String distributorName;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private Date updateTime;

    /**
     * 渠道类型：1：分销渠道 2：社会渠道
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String channelType;

    /**
     * 清洗时间
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private Date rinseTime;

    /**
     * 是否已经清洗：0：没有  1：是
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private Boolean isRinse;

    /**
     * 渠道商编码
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String agentNumber;

    /**
     * 渠道商全称
     *
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private String agentName;

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.id
     *
     * @return the value of supply_chain..shop_customer_info.id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.id
     *
     * @param id the value for supply_chain..shop_customer_info.id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.cust_code
     *
     * @return the value of supply_chain..shop_customer_info.cust_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.cust_code
     *
     * @param custCode the value for supply_chain..shop_customer_info.cust_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.cust_id
     *
     * @return the value of supply_chain..shop_customer_info.cust_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getCustId() {
        return custId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withCustId(String custId) {
        this.setCustId(custId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.cust_id
     *
     * @param custId the value for supply_chain..shop_customer_info.cust_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setCustId(String custId) {
        this.custId = custId;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.user_id
     *
     * @return the value of supply_chain..shop_customer_info.user_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.user_id
     *
     * @param userId the value for supply_chain..shop_customer_info.user_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.cust_name
     *
     * @return the value of supply_chain..shop_customer_info.cust_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.cust_name
     *
     * @param custName the value for supply_chain..shop_customer_info.cust_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.register_date
     *
     * @return the value of supply_chain..shop_customer_info.register_date
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Date getRegisterDate() {
        return registerDate;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withRegisterDate(Date registerDate) {
        this.setRegisterDate(registerDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.register_date
     *
     * @param registerDate the value for supply_chain..shop_customer_info.register_date
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.role_type
     *
     * @return the value of supply_chain..shop_customer_info.role_type
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getRoleType() {
        return roleType;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withRoleType(String roleType) {
        this.setRoleType(roleType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.role_type
     *
     * @param roleType the value for supply_chain..shop_customer_info.role_type
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.be_id
     *
     * @return the value of supply_chain..shop_customer_info.be_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.be_id
     *
     * @param beId the value for supply_chain..shop_customer_info.be_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.province_name
     *
     * @return the value of supply_chain..shop_customer_info.province_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.province_name
     *
     * @param provinceName the value for supply_chain..shop_customer_info.province_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.location
     *
     * @return the value of supply_chain..shop_customer_info.location
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.location
     *
     * @param location the value for supply_chain..shop_customer_info.location
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.city_name
     *
     * @return the value of supply_chain..shop_customer_info.city_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.city_name
     *
     * @param cityName the value for supply_chain..shop_customer_info.city_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.region_id
     *
     * @return the value of supply_chain..shop_customer_info.region_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.region_id
     *
     * @param regionId the value for supply_chain..shop_customer_info.region_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.region_name
     *
     * @return the value of supply_chain..shop_customer_info.region_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.region_name
     *
     * @param regionName the value for supply_chain..shop_customer_info.region_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.client_status
     *
     * @return the value of supply_chain..shop_customer_info.client_status
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getClientStatus() {
        return clientStatus;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withClientStatus(String clientStatus) {
        this.setClientStatus(clientStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.client_status
     *
     * @param clientStatus the value for supply_chain..shop_customer_info.client_status
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setClientStatus(String clientStatus) {
        this.clientStatus = clientStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_channel_id
     *
     * @return the value of supply_chain..shop_customer_info.distributor_channel_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorChannelId() {
        return distributorChannelId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorChannelId(String distributorChannelId) {
        this.setDistributorChannelId(distributorChannelId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_channel_id
     *
     * @param distributorChannelId the value for supply_chain..shop_customer_info.distributor_channel_id
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorChannelId(String distributorChannelId) {
        this.distributorChannelId = distributorChannelId;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_channel_name
     *
     * @return the value of supply_chain..shop_customer_info.distributor_channel_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorChannelName() {
        return distributorChannelName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorChannelName(String distributorChannelName) {
        this.setDistributorChannelName(distributorChannelName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_channel_name
     *
     * @param distributorChannelName the value for supply_chain..shop_customer_info.distributor_channel_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorChannelName(String distributorChannelName) {
        this.distributorChannelName = distributorChannelName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_referral_code
     *
     * @return the value of supply_chain..shop_customer_info.distributor_referral_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorReferralCode() {
        return distributorReferralCode;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorReferralCode(String distributorReferralCode) {
        this.setDistributorReferralCode(distributorReferralCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_referral_code
     *
     * @param distributorReferralCode the value for supply_chain..shop_customer_info.distributor_referral_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorReferralCode(String distributorReferralCode) {
        this.distributorReferralCode = distributorReferralCode;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_mrg_inf
     *
     * @return the value of supply_chain..shop_customer_info.distributor_mrg_inf
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorMrgInf() {
        return distributorMrgInf;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorMrgInf(String distributorMrgInf) {
        this.setDistributorMrgInf(distributorMrgInf);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_mrg_inf
     *
     * @param distributorMrgInf the value for supply_chain..shop_customer_info.distributor_mrg_inf
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorMrgInf(String distributorMrgInf) {
        this.distributorMrgInf = distributorMrgInf;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_mrg_code
     *
     * @return the value of supply_chain..shop_customer_info.distributor_mrg_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorMrgCode() {
        return distributorMrgCode;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorMrgCode(String distributorMrgCode) {
        this.setDistributorMrgCode(distributorMrgCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_mrg_code
     *
     * @param distributorMrgCode the value for supply_chain..shop_customer_info.distributor_mrg_code
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorMrgCode(String distributorMrgCode) {
        this.distributorMrgCode = distributorMrgCode;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.distributor_name
     *
     * @return the value of supply_chain..shop_customer_info.distributor_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getDistributorName() {
        return distributorName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withDistributorName(String distributorName) {
        this.setDistributorName(distributorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.distributor_name
     *
     * @param distributorName the value for supply_chain..shop_customer_info.distributor_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.create_time
     *
     * @return the value of supply_chain..shop_customer_info.create_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.create_time
     *
     * @param createTime the value for supply_chain..shop_customer_info.create_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.update_time
     *
     * @return the value of supply_chain..shop_customer_info.update_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.update_time
     *
     * @param updateTime the value for supply_chain..shop_customer_info.update_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.channel_type
     *
     * @return the value of supply_chain..shop_customer_info.channel_type
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getChannelType() {
        return channelType;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withChannelType(String channelType) {
        this.setChannelType(channelType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.channel_type
     *
     * @param channelType the value for supply_chain..shop_customer_info.channel_type
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.rinse_time
     *
     * @return the value of supply_chain..shop_customer_info.rinse_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Date getRinseTime() {
        return rinseTime;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withRinseTime(Date rinseTime) {
        this.setRinseTime(rinseTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.rinse_time
     *
     * @param rinseTime the value for supply_chain..shop_customer_info.rinse_time
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setRinseTime(Date rinseTime) {
        this.rinseTime = rinseTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.is_rinse
     *
     * @return the value of supply_chain..shop_customer_info.is_rinse
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Boolean getIsRinse() {
        return isRinse;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withIsRinse(Boolean isRinse) {
        this.setIsRinse(isRinse);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.is_rinse
     *
     * @param isRinse the value for supply_chain..shop_customer_info.is_rinse
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setIsRinse(Boolean isRinse) {
        this.isRinse = isRinse;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.agent_number
     *
     * @return the value of supply_chain..shop_customer_info.agent_number
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getAgentNumber() {
        return agentNumber;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withAgentNumber(String agentNumber) {
        this.setAgentNumber(agentNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.agent_number
     *
     * @param agentNumber the value for supply_chain..shop_customer_info.agent_number
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setAgentNumber(String agentNumber) {
        this.agentNumber = agentNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_customer_info.agent_name
     *
     * @return the value of supply_chain..shop_customer_info.agent_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getAgentName() {
        return agentName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfo withAgentName(String agentName) {
        this.setAgentName(agentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_customer_info.agent_name
     *
     * @param agentName the value for supply_chain..shop_customer_info.agent_name
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", custCode=").append(custCode);
        sb.append(", custId=").append(custId);
        sb.append(", userId=").append(userId);
        sb.append(", custName=").append(custName);
        sb.append(", registerDate=").append(registerDate);
        sb.append(", roleType=").append(roleType);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", location=").append(location);
        sb.append(", cityName=").append(cityName);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", clientStatus=").append(clientStatus);
        sb.append(", distributorChannelId=").append(distributorChannelId);
        sb.append(", distributorChannelName=").append(distributorChannelName);
        sb.append(", distributorReferralCode=").append(distributorReferralCode);
        sb.append(", distributorMrgInf=").append(distributorMrgInf);
        sb.append(", distributorMrgCode=").append(distributorMrgCode);
        sb.append(", distributorName=").append(distributorName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", channelType=").append(channelType);
        sb.append(", rinseTime=").append(rinseTime);
        sb.append(", isRinse=").append(isRinse);
        sb.append(", agentNumber=").append(agentNumber);
        sb.append(", agentName=").append(agentName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShopCustomerInfo other = (ShopCustomerInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustId() == null ? other.getCustId() == null : this.getCustId().equals(other.getCustId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getRegisterDate() == null ? other.getRegisterDate() == null : this.getRegisterDate().equals(other.getRegisterDate()))
            && (this.getRoleType() == null ? other.getRoleType() == null : this.getRoleType().equals(other.getRoleType()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getClientStatus() == null ? other.getClientStatus() == null : this.getClientStatus().equals(other.getClientStatus()))
            && (this.getDistributorChannelId() == null ? other.getDistributorChannelId() == null : this.getDistributorChannelId().equals(other.getDistributorChannelId()))
            && (this.getDistributorChannelName() == null ? other.getDistributorChannelName() == null : this.getDistributorChannelName().equals(other.getDistributorChannelName()))
            && (this.getDistributorReferralCode() == null ? other.getDistributorReferralCode() == null : this.getDistributorReferralCode().equals(other.getDistributorReferralCode()))
            && (this.getDistributorMrgInf() == null ? other.getDistributorMrgInf() == null : this.getDistributorMrgInf().equals(other.getDistributorMrgInf()))
            && (this.getDistributorMrgCode() == null ? other.getDistributorMrgCode() == null : this.getDistributorMrgCode().equals(other.getDistributorMrgCode()))
            && (this.getDistributorName() == null ? other.getDistributorName() == null : this.getDistributorName().equals(other.getDistributorName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getChannelType() == null ? other.getChannelType() == null : this.getChannelType().equals(other.getChannelType()))
            && (this.getRinseTime() == null ? other.getRinseTime() == null : this.getRinseTime().equals(other.getRinseTime()))
            && (this.getIsRinse() == null ? other.getIsRinse() == null : this.getIsRinse().equals(other.getIsRinse()))
            && (this.getAgentNumber() == null ? other.getAgentNumber() == null : this.getAgentNumber().equals(other.getAgentNumber()))
            && (this.getAgentName() == null ? other.getAgentName() == null : this.getAgentName().equals(other.getAgentName()));
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustId() == null) ? 0 : getCustId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getRegisterDate() == null) ? 0 : getRegisterDate().hashCode());
        result = prime * result + ((getRoleType() == null) ? 0 : getRoleType().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getClientStatus() == null) ? 0 : getClientStatus().hashCode());
        result = prime * result + ((getDistributorChannelId() == null) ? 0 : getDistributorChannelId().hashCode());
        result = prime * result + ((getDistributorChannelName() == null) ? 0 : getDistributorChannelName().hashCode());
        result = prime * result + ((getDistributorReferralCode() == null) ? 0 : getDistributorReferralCode().hashCode());
        result = prime * result + ((getDistributorMrgInf() == null) ? 0 : getDistributorMrgInf().hashCode());
        result = prime * result + ((getDistributorMrgCode() == null) ? 0 : getDistributorMrgCode().hashCode());
        result = prime * result + ((getDistributorName() == null) ? 0 : getDistributorName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getChannelType() == null) ? 0 : getChannelType().hashCode());
        result = prime * result + ((getRinseTime() == null) ? 0 : getRinseTime().hashCode());
        result = prime * result + ((getIsRinse() == null) ? 0 : getIsRinse().hashCode());
        result = prime * result + ((getAgentNumber() == null) ? 0 : getAgentNumber().hashCode());
        result = prime * result + ((getAgentName() == null) ? 0 : getAgentName().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custId("cust_id", "custId", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        registerDate("register_date", "registerDate", "TIMESTAMP", false),
        roleType("role_type", "roleType", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        clientStatus("client_status", "clientStatus", "VARCHAR", false),
        distributorChannelId("distributor_channel_id", "distributorChannelId", "VARCHAR", false),
        distributorChannelName("distributor_channel_name", "distributorChannelName", "VARCHAR", false),
        distributorReferralCode("distributor_referral_code", "distributorReferralCode", "VARCHAR", false),
        distributorMrgInf("distributor_mrg_inf", "distributorMrgInf", "VARCHAR", false),
        distributorMrgCode("distributor_mrg_code", "distributorMrgCode", "VARCHAR", false),
        distributorName("distributor_name", "distributorName", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        channelType("channel_type", "channelType", "VARCHAR", false),
        rinseTime("rinse_time", "rinseTime", "TIMESTAMP", false),
        isRinse("is_rinse", "isRinse", "BIT", false),
        agentNumber("agent_number", "agentNumber", "VARCHAR", false),
        agentName("agent_name", "agentName", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}