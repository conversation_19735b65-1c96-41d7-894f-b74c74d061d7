package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 用户表
 *
 * <AUTHOR>
public class User implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String userId;

    /**
     * 姓名
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String name;

    /**
     * 密码
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String pwd;

    /**
     * 电话
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String phone;

    /**
     * 备注
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String remark;

    /**
     * 是否是超级管理员
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Boolean isAdmin;

    /**
     * 角色id
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String roleId;

    /**
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Date updateTime;

    /**
     * 是否无效
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Boolean isCancel;

    /**
     * 邮箱
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String email;

    /**
     * 公司
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String company;

    /**
     * 合作伙伴名
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String partnerName;

    /**
     * 创建人
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private String creator;

    /**
     * 是否是主合作伙伴
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Boolean isPrimary;

    /**
     * 是否发送短信
     *
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private Boolean isSend;

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user.user_id
     *
     * @return the value of supply_chain..user.user_id
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.user_id
     *
     * @param userId the value for supply_chain..user.user_id
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.name
     *
     * @return the value of supply_chain..user.name
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.name
     *
     * @param name the value for supply_chain..user.name
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..user.pwd
     *
     * @return the value of supply_chain..user.pwd
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getPwd() {
        return pwd;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withPwd(String pwd) {
        this.setPwd(pwd);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.pwd
     *
     * @param pwd the value for supply_chain..user.pwd
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    /**
     * This method returns the value of the database column supply_chain..user.phone
     *
     * @return the value of supply_chain..user.phone
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.phone
     *
     * @param phone the value for supply_chain..user.phone
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..user.remark
     *
     * @return the value of supply_chain..user.remark
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.remark
     *
     * @param remark the value for supply_chain..user.remark
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_admin
     *
     * @return the value of supply_chain..user.is_admin
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Boolean getIsAdmin() {
        return isAdmin;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withIsAdmin(Boolean isAdmin) {
        this.setIsAdmin(isAdmin);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_admin
     *
     * @param isAdmin the value for supply_chain..user.is_admin
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setIsAdmin(Boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    /**
     * This method returns the value of the database column supply_chain..user.role_id
     *
     * @return the value of supply_chain..user.role_id
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.role_id
     *
     * @param roleId the value for supply_chain..user.role_id
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.create_time
     *
     * @return the value of supply_chain..user.create_time
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.create_time
     *
     * @param createTime the value for supply_chain..user.create_time
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user.update_time
     *
     * @return the value of supply_chain..user.update_time
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.update_time
     *
     * @param updateTime the value for supply_chain..user.update_time
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_cancel
     *
     * @return the value of supply_chain..user.is_cancel
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Boolean getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withIsCancel(Boolean isCancel) {
        this.setIsCancel(isCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_cancel
     *
     * @param isCancel the value for supply_chain..user.is_cancel
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setIsCancel(Boolean isCancel) {
        this.isCancel = isCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..user.email
     *
     * @return the value of supply_chain..user.email
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withEmail(String email) {
        this.setEmail(email);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.email
     *
     * @param email the value for supply_chain..user.email
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method returns the value of the database column supply_chain..user.company
     *
     * @return the value of supply_chain..user.company
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getCompany() {
        return company;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withCompany(String company) {
        this.setCompany(company);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.company
     *
     * @param company the value for supply_chain..user.company
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setCompany(String company) {
        this.company = company;
    }

    /**
     * This method returns the value of the database column supply_chain..user.partner_name
     *
     * @return the value of supply_chain..user.partner_name
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withPartnerName(String partnerName) {
        this.setPartnerName(partnerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.partner_name
     *
     * @param partnerName the value for supply_chain..user.partner_name
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * This method returns the value of the database column supply_chain..user.creator
     *
     * @return the value of supply_chain..user.creator
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public String getCreator() {
        return creator;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withCreator(String creator) {
        this.setCreator(creator);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.creator
     *
     * @param creator the value for supply_chain..user.creator
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_primary
     *
     * @return the value of supply_chain..user.is_primary
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Boolean getIsPrimary() {
        return isPrimary;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withIsPrimary(Boolean isPrimary) {
        this.setIsPrimary(isPrimary);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_primary
     *
     * @param isPrimary the value for supply_chain..user.is_primary
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setIsPrimary(Boolean isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_send
     *
     * @return the value of supply_chain..user.is_send
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public Boolean getIsSend() {
        return isSend;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public User withIsSend(Boolean isSend) {
        this.setIsSend(isSend);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_send
     *
     * @param isSend the value for supply_chain..user.is_send
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public void setIsSend(Boolean isSend) {
        this.isSend = isSend;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userId=").append(userId);
        sb.append(", name=").append(name);
        sb.append(", pwd=").append(pwd);
        sb.append(", phone=").append(phone);
        sb.append(", remark=").append(remark);
        sb.append(", isAdmin=").append(isAdmin);
        sb.append(", roleId=").append(roleId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isCancel=").append(isCancel);
        sb.append(", email=").append(email);
        sb.append(", company=").append(company);
        sb.append(", partnerName=").append(partnerName);
        sb.append(", creator=").append(creator);
        sb.append(", isPrimary=").append(isPrimary);
        sb.append(", isSend=").append(isSend);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        User other = (User) that;
        return (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPwd() == null ? other.getPwd() == null : this.getPwd().equals(other.getPwd()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getIsAdmin() == null ? other.getIsAdmin() == null : this.getIsAdmin().equals(other.getIsAdmin()))
            && (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsCancel() == null ? other.getIsCancel() == null : this.getIsCancel().equals(other.getIsCancel()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getCompany() == null ? other.getCompany() == null : this.getCompany().equals(other.getCompany()))
            && (this.getPartnerName() == null ? other.getPartnerName() == null : this.getPartnerName().equals(other.getPartnerName()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getIsPrimary() == null ? other.getIsPrimary() == null : this.getIsPrimary().equals(other.getIsPrimary()))
            && (this.getIsSend() == null ? other.getIsSend() == null : this.getIsSend().equals(other.getIsSend()));
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPwd() == null) ? 0 : getPwd().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getIsAdmin() == null) ? 0 : getIsAdmin().hashCode());
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsCancel() == null) ? 0 : getIsCancel().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getCompany() == null) ? 0 : getCompany().hashCode());
        result = prime * result + ((getPartnerName() == null) ? 0 : getPartnerName().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getIsPrimary() == null) ? 0 : getIsPrimary().hashCode());
        result = prime * result + ((getIsSend() == null) ? 0 : getIsSend().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Aug 31 10:57:36 CST 2022
     */
    public enum Column {
        userId("user_id", "userId", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        pwd("pwd", "pwd", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        isAdmin("is_admin", "isAdmin", "BIT", false),
        roleId("role_id", "roleId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        isCancel("is_cancel", "isCancel", "BIT", false),
        email("email", "email", "VARCHAR", false),
        company("company", "company", "VARCHAR", false),
        partnerName("partner_name", "partnerName", "VARCHAR", false),
        creator("creator", "creator", "VARCHAR", false),
        isPrimary("is_primary", "isPrimary", "BIT", false),
        isSend("is_send", "isSend", "BIT", false);

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Aug 31 10:57:36 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}