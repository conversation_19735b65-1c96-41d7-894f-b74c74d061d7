package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class JkbanQuestionInfo implements Serializable {
    /**
     * 故障业务
     *
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    private String questionType;

    /**
     * 故障类型
     *
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    private String questionDesc;

    /**
     * 故障类型，编码
     *
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    private String question;

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..jkban_question_info.question_type
     *
     * @return the value of supply_chain..jkban_question_info.question_type
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public String getQuestionType() {
        return questionType;
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public JkbanQuestionInfo withQuestionType(String questionType) {
        this.setQuestionType(questionType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_question_info.question_type
     *
     * @param questionType the value for supply_chain..jkban_question_info.question_type
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_question_info.question_desc
     *
     * @return the value of supply_chain..jkban_question_info.question_desc
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public String getQuestionDesc() {
        return questionDesc;
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public JkbanQuestionInfo withQuestionDesc(String questionDesc) {
        this.setQuestionDesc(questionDesc);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_question_info.question_desc
     *
     * @param questionDesc the value for supply_chain..jkban_question_info.question_desc
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public void setQuestionDesc(String questionDesc) {
        this.questionDesc = questionDesc;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_question_info.question
     *
     * @return the value of supply_chain..jkban_question_info.question
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public String getQuestion() {
        return question;
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public JkbanQuestionInfo withQuestion(String question) {
        this.setQuestion(question);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_question_info.question
     *
     * @param question the value for supply_chain..jkban_question_info.question
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public void setQuestion(String question) {
        this.question = question;
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", questionType=").append(questionType);
        sb.append(", questionDesc=").append(questionDesc);
        sb.append(", question=").append(question);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JkbanQuestionInfo other = (JkbanQuestionInfo) that;
        return (this.getQuestionType() == null ? other.getQuestionType() == null : this.getQuestionType().equals(other.getQuestionType()))
            && (this.getQuestionDesc() == null ? other.getQuestionDesc() == null : this.getQuestionDesc().equals(other.getQuestionDesc()))
            && (this.getQuestion() == null ? other.getQuestion() == null : this.getQuestion().equals(other.getQuestion()));
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getQuestionType() == null) ? 0 : getQuestionType().hashCode());
        result = prime * result + ((getQuestionDesc() == null) ? 0 : getQuestionDesc().hashCode());
        result = prime * result + ((getQuestion() == null) ? 0 : getQuestion().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jul 24 09:44:09 CST 2025
     */
    public enum Column {
        questionType("question_type", "questionType", "VARCHAR", false),
        questionDesc("question_desc", "questionDesc", "VARCHAR", false),
        question("question", "question", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jul 24 09:44:09 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}