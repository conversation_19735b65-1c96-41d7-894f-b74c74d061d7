package com.chinamobile.retail.pojo.vo.miniprogram;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityDataRankVO {
    /**
     * 中奖记录id
     */
    private String id;
    /**
     * 活动类型
     */
    private Integer activityType;
    /**
     * 排名
     */
    private Integer ranking;
    /**
     * 名称
     */
    private String name;
    /**
     * 电话
     */
    private String phone;
    /**
     * 所属地市
     */
    private String location;
    /**
     * 奖项
     */
    private String awardName;
    /**
     * 奖品Id
     */
    private String awardId;
    /**
     * 奖品类型
     */
    private Integer awardType;
    /**
     * 奖品名称
     */
    private String product;
    /**
     * 积分
     */
    private Long points;
    /**
     * 积分供应商
     */
    private String supplierId;
    /**
     * 获奖时间
     */
    private Date createTime;
    /**
     * 收货地址
     */
    private String deliveryAddress;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 订单总额
     */
    private Long orderAmount;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 单号
     */
    private String logisticsCode;
    /**
     * 是否兑换
     */
    private Integer isExchange;
    /**
     * 是否超过订单确认时间
     */
    private Boolean isOverOrderTime;

}
