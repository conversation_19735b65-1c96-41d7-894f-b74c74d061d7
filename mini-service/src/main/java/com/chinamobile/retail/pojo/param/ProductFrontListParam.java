package com.chinamobile.retail.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * created by l<PERSON><PERSON>ng on 2022/9/1 10:46
 */
@Data
public class ProductFrontListParam extends BasePageQuery {

    //排序类型, 1 – 销量； 2– 佣金； 3–价格。默认1
    @Range(min = 1,max = 3)
    private Integer orderType = 1;

    //排序 1– 降序； 2– 升序。默认1
    @Range(min = 1,max = 2)
    private Integer orderSort = 1;

    //sku或spu名称模糊搜索
    private String queryParam;
}


