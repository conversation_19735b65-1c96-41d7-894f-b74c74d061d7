package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 积分兑换计划表
 *
 * <AUTHOR>
public class PointExchange implements Serializable {
    /**
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private String id;

    /**
     * 兑换批次编号
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private String batchNo;

    /**
     * 兑换发起人id
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private String operateUserId;

    /**
     * 积分供应商id
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private String supplierId;

    /**
     * 兑换人数
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Integer partnerCount;

    /**
     * 兑换成功人数
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Integer successPartnerCount;

    /**
     * 兑换积分总数
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Long point;

    /**
     * 兑换成功积分总数
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Long successPoint;

    /**
     * 兑换任务状态 1--兑换中  2--已兑换 3--兑换失败
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Integer status;

    /**
     * 失败原因
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private String failReason;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..point_exchange.id
     *
     * @return the value of supply_chain..point_exchange.id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.id
     *
     * @param id the value for supply_chain..point_exchange.id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.batch_no
     *
     * @return the value of supply_chain..point_exchange.batch_no
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public String getBatchNo() {
        return batchNo;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withBatchNo(String batchNo) {
        this.setBatchNo(batchNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.batch_no
     *
     * @param batchNo the value for supply_chain..point_exchange.batch_no
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.operate_user_id
     *
     * @return the value of supply_chain..point_exchange.operate_user_id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public String getOperateUserId() {
        return operateUserId;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withOperateUserId(String operateUserId) {
        this.setOperateUserId(operateUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.operate_user_id
     *
     * @param operateUserId the value for supply_chain..point_exchange.operate_user_id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.supplier_id
     *
     * @return the value of supply_chain..point_exchange.supplier_id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.supplier_id
     *
     * @param supplierId the value for supply_chain..point_exchange.supplier_id
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.partner_count
     *
     * @return the value of supply_chain..point_exchange.partner_count
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Integer getPartnerCount() {
        return partnerCount;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withPartnerCount(Integer partnerCount) {
        this.setPartnerCount(partnerCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.partner_count
     *
     * @param partnerCount the value for supply_chain..point_exchange.partner_count
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setPartnerCount(Integer partnerCount) {
        this.partnerCount = partnerCount;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.success_partner_count
     *
     * @return the value of supply_chain..point_exchange.success_partner_count
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Integer getSuccessPartnerCount() {
        return successPartnerCount;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withSuccessPartnerCount(Integer successPartnerCount) {
        this.setSuccessPartnerCount(successPartnerCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.success_partner_count
     *
     * @param successPartnerCount the value for supply_chain..point_exchange.success_partner_count
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setSuccessPartnerCount(Integer successPartnerCount) {
        this.successPartnerCount = successPartnerCount;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.point
     *
     * @return the value of supply_chain..point_exchange.point
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Long getPoint() {
        return point;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withPoint(Long point) {
        this.setPoint(point);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.point
     *
     * @param point the value for supply_chain..point_exchange.point
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setPoint(Long point) {
        this.point = point;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.success_point
     *
     * @return the value of supply_chain..point_exchange.success_point
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Long getSuccessPoint() {
        return successPoint;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withSuccessPoint(Long successPoint) {
        this.setSuccessPoint(successPoint);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.success_point
     *
     * @param successPoint the value for supply_chain..point_exchange.success_point
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setSuccessPoint(Long successPoint) {
        this.successPoint = successPoint;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.status
     *
     * @return the value of supply_chain..point_exchange.status
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.status
     *
     * @param status the value for supply_chain..point_exchange.status
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.fail_reason
     *
     * @return the value of supply_chain..point_exchange.fail_reason
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public String getFailReason() {
        return failReason;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withFailReason(String failReason) {
        this.setFailReason(failReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.fail_reason
     *
     * @param failReason the value for supply_chain..point_exchange.fail_reason
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.create_time
     *
     * @return the value of supply_chain..point_exchange.create_time
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.create_time
     *
     * @param createTime the value for supply_chain..point_exchange.create_time
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..point_exchange.update_time
     *
     * @return the value of supply_chain..point_exchange.update_time
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public PointExchange withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_exchange.update_time
     *
     * @param updateTime the value for supply_chain..point_exchange.update_time
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", operateUserId=").append(operateUserId);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", partnerCount=").append(partnerCount);
        sb.append(", successPartnerCount=").append(successPartnerCount);
        sb.append(", point=").append(point);
        sb.append(", successPoint=").append(successPoint);
        sb.append(", status=").append(status);
        sb.append(", failReason=").append(failReason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PointExchange other = (PointExchange) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getOperateUserId() == null ? other.getOperateUserId() == null : this.getOperateUserId().equals(other.getOperateUserId()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getPartnerCount() == null ? other.getPartnerCount() == null : this.getPartnerCount().equals(other.getPartnerCount()))
            && (this.getSuccessPartnerCount() == null ? other.getSuccessPartnerCount() == null : this.getSuccessPartnerCount().equals(other.getSuccessPartnerCount()))
            && (this.getPoint() == null ? other.getPoint() == null : this.getPoint().equals(other.getPoint()))
            && (this.getSuccessPoint() == null ? other.getSuccessPoint() == null : this.getSuccessPoint().equals(other.getSuccessPoint()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getFailReason() == null ? other.getFailReason() == null : this.getFailReason().equals(other.getFailReason()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getOperateUserId() == null) ? 0 : getOperateUserId().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getPartnerCount() == null) ? 0 : getPartnerCount().hashCode());
        result = prime * result + ((getSuccessPartnerCount() == null) ? 0 : getSuccessPartnerCount().hashCode());
        result = prime * result + ((getPoint() == null) ? 0 : getPoint().hashCode());
        result = prime * result + ((getSuccessPoint() == null) ? 0 : getSuccessPoint().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getFailReason() == null) ? 0 : getFailReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        batchNo("batch_no", "batchNo", "VARCHAR", false),
        operateUserId("operate_user_id", "operateUserId", "VARCHAR", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        partnerCount("partner_count", "partnerCount", "INTEGER", false),
        successPartnerCount("success_partner_count", "successPartnerCount", "INTEGER", false),
        point("point", "point", "BIGINT", false),
        successPoint("success_point", "successPoint", "BIGINT", false),
        status("status", "status", "INTEGER", false),
        failReason("fail_reason", "failReason", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Apr 18 17:42:11 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}