package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 销售端小程序活动访问人数统计
 *
 * <AUTHOR>
public class MiniProgramActivityAccess {
    /**
     * 主键id
     *
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    private Integer id;

    /**
     * 用户id
     *
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    private String userId;

    /**
     * 小程序活动id
     *
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    private String activityId;

    /**
     * 访问时间
     *
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    private Date accessTime;

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_access.id
     *
     * @return the value of supply_chain..mini_program_activity_access.id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public MiniProgramActivityAccess withId(Integer id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_access.id
     *
     * @param id the value for supply_chain..mini_program_activity_access.id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_access.user_id
     *
     * @return the value of supply_chain..mini_program_activity_access.user_id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public MiniProgramActivityAccess withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_access.user_id
     *
     * @param userId the value for supply_chain..mini_program_activity_access.user_id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_access.activity_id
     *
     * @return the value of supply_chain..mini_program_activity_access.activity_id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public MiniProgramActivityAccess withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_access.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_activity_access.activity_id
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId == null ? null : activityId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_access.access_time
     *
     * @return the value of supply_chain..mini_program_activity_access.access_time
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public Date getAccessTime() {
        return accessTime;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public MiniProgramActivityAccess withAccessTime(Date accessTime) {
        this.setAccessTime(accessTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_access.access_time
     *
     * @param accessTime the value for supply_chain..mini_program_activity_access.access_time
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public void setAccessTime(Date accessTime) {
        this.accessTime = accessTime;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", activityId=").append(activityId);
        sb.append(", accessTime=").append(accessTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramActivityAccess other = (MiniProgramActivityAccess) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getAccessTime() == null ? other.getAccessTime() == null : this.getAccessTime().equals(other.getAccessTime()));
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getAccessTime() == null) ? 0 : getAccessTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jul 18 17:48:56 CST 2024
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        userId("user_id", "userId", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        accessTime("access_time", "accessTime", "DATE", false);

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jul 18 17:48:56 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}