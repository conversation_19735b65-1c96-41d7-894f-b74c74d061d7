package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Data
public class HomePublishParam {

    /**
     * 小程序自选id
     */
    @NotEmpty(message = "id不能为空")
    private List<String> ids;

//    /**
//     * 下线/上线
//     */
//    @NotNull(message = "下线状态不能为空")
//    private Boolean offline;
}
