package com.chinamobile.retail.pojo.dto.lakala;

import lombok.Data;

@Data
public class ThirdUserSignInfoResponseDTO {

    /**
     * 接口响应结果（000000:请求成功，其余为异常情况，是否符合请参考verify_status字段）
     */
    private String resCode;

    /**
     * 状态，
     1：用户已签约+已填写收款信息
     2：用户未签约+未填写收款信息
     3：用户已签约+未填写收款信息
     */
    private String verifyStatus;

    /**
     返回链接
     verifyStatus=1：返回用户修改收款信息URL
     verifyStatus=2：返回用户签约URL
     verifyStatus=3：返回用户填写收款信息UR
     */
    private String verifyUrl;

    private String failMsg;
}
