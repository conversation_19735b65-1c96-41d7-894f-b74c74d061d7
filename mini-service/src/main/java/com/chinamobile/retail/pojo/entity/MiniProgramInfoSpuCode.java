package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序资讯SPU编码
 *
 * <AUTHOR>
public class MiniProgramInfoSpuCode implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    private String id;

    /**
     * 小程序资讯id
     *
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    private String infoId;

    /**
     * SPU编码
     *
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    private String spuOfferingCode;

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_spu_code.id
     *
     * @return the value of supply_chain..mini_program_info_spu_code.id
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCode withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_spu_code.id
     *
     * @param id the value for supply_chain..mini_program_info_spu_code.id
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_spu_code.info_id
     *
     * @return the value of supply_chain..mini_program_info_spu_code.info_id
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public String getInfoId() {
        return infoId;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCode withInfoId(String infoId) {
        this.setInfoId(infoId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_spu_code.info_id
     *
     * @param infoId the value for supply_chain..mini_program_info_spu_code.info_id
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_spu_code.spu_offering_code
     *
     * @return the value of supply_chain..mini_program_info_spu_code.spu_offering_code
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCode withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_spu_code.spu_offering_code
     *
     * @param spuOfferingCode the value for supply_chain..mini_program_info_spu_code.spu_offering_code
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", infoId=").append(infoId);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramInfoSpuCode other = (MiniProgramInfoSpuCode) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInfoId() == null ? other.getInfoId() == null : this.getInfoId().equals(other.getInfoId()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()));
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInfoId() == null) ? 0 : getInfoId().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        infoId("info_id", "infoId", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}