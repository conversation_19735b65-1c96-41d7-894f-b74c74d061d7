package com.chinamobile.retail.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3
 * @description 极客用户个人中心信息
 */
@Data
public class UserCenterVO {

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 头像url地址
     */
    private String headerImgUrl;

    /**
     * 审批状态 1--未审批 2--审批通过  3--审批未通过
     */
    private Integer auditStatus;

    /**
     * 审批原因
     */
    private String auditReason;

    /**
     * 头像审批后是否提示过 1--未提示  2--已提示
     */
    private Integer auditHeaderNotice;
    /**
     * 奖品未配置地址的数量
     */
    private Integer prizeWaitingCollectedNum;

    /**
     * 角色
     */
    private String roleType;

    /**
     * 客户经理操作码
     */
    private String code;

    /**
     * 角色
     */
    private String roleName;

    /**
     * 组织机构名称
     */
    private String organizationName;

    /**
     * 省份
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String cityCode;

    /**
     * 城市
     */
    private String regionId;


}
