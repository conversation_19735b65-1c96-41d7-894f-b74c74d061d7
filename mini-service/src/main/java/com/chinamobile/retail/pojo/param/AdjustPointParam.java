package com.chinamobile.retail.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 合伙人积分查询参数
 * <AUTHOR>
 */
@Data
public class AdjustPointParam {

    /**合伙人*/
    @NotEmpty(message = "合伙人ID不能为空")
    private String partnerId;

    /**供应商*/
    @NotEmpty(message = "供应商ID不能为空")
    private String supplierId;

    /**调整类型*/
    @NotNull(message = "调整类型不能为空")
    private Integer type;

    /**调整积分数量*/
    @NotNull(message = "调整积分数量不能为空")
    private Long point;

    /**调整原因*/
    private String desc;
    /**调整渠道*/
    private Integer channel;

    private String orderId;
}
