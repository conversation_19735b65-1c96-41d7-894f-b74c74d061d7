package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.retail.pojo.dto.SceneProductDTO;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售端小程序小场景信息
 */
@Data
public class SceneParam implements Serializable {
    /**
     * 主键
     *
     */
    private String id;

    @NotNull(message = "操作类型不能为空")
    private Integer oprType;


    /**
     * 场景解决方案名称
     */
    @NotBlank(message = "场景解决方案名称不能为空")
    private String name;

    /**
     * 一级目录id
     */
    @NotBlank(message = "一级目录id不能为空")
    private String firstDirectoryId;


    /**
     * 二级目录id
     *
     */
    @NotBlank(message = "二级目录id不能为空")
    private String secondDirectoryId;

    /**
     * 解决方案产品编码
     *
     */
    @NotBlank(message = "解决方案产品编码不能为空")
    private String spuCode;

    /**
     * 场景图片url
     *
     */
    @NotBlank(message = "场景头图url不能为空")
    private String headImageUrl;

    /**
     * 场景图片url
     *
     */
    @NotBlank(message = "场景图片url不能为空")
    private String imageUrl;

    /**
     * 需求模板ID
     *
     */
    private String templateId;

    /**产品列表*/
    @NotEmpty(message = "关联方案产品不能为空")
    private List<SceneProductDTO> products;




}