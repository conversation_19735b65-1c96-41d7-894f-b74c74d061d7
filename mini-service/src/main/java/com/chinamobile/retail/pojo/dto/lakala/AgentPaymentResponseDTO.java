package com.chinamobile.retail.pojo.dto.lakala;

import lombok.Data;

/**
 * created by liuxiang on 2022/10/25 15:15
 * 状态处理逻辑：
 * 1.超时、无返回情况：
 *    无需更改订单状态，依赖提现订单状态查询接口（getTradeInfo）
 * 2.查询有返回：
 *    1).公共参数 res 为 true 且 res_code 为 000000 时：data 中存在 trade_status 【PROCESS 处理中】 交易状态字段，则代表提现下单成功；其余情况依赖提现订单状态查询接口（getTradeInfo）查询，不可更改提现订单状态。
 *    2).公共参数 res 为 true 且 res_code 非 000000 时：当res_code 为 999999 可认为提现失败；其余情况依赖提现订单状态查询接口（getTradeInfo）查询，不可更改提现订单状态。
 *    3).公共参数 res 为 fale 时 请联系业务人员或依赖提现订单状态查询接口（getTradeInfo）查询，不可更改提现订单状态。
 */
@Data
public class AgentPaymentResponseDTO {

    //接口响应结果（000000:成功，其余为异常情况，发佣结果以 trade_status 为准）
    private String resCode;

    //失败原因
    private String failMsg;

    //商户唯一订单号
    private String outTradeNo;

    //灵工平台交易订单号
    private String tradeNo;

    //发佣金额,单位元
    private String amount;

    //交易状态【PROCESS 处理中】
    private String tradeStatus;

    //交易创建时间
    private String tradeCreateTime;

    //交易付款时间
    private String tradePaymentTime;
}
