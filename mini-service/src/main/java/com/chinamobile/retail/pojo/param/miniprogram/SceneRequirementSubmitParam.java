package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class SceneRequirementSubmitParam {

    @NotBlank(message = "场景id不能为空")
    private String sceneId;
    @NotBlank(message = "一级目录id不能为空")
    private String firstDirectoryId;
    @NotBlank(message = "二级目录id不能为空")
    private String secondDirectoryId;
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;
    @NotBlank(message = "省份编码不能为空")
    private String provinceCode;
    @NotBlank(message = "城市名称不能为空")
    private String cityName;
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;
    @NotBlank(message = "联系人不能为空")
    private String contact;
    @NotBlank(message = "联系电话不能为空")
    private String phone;
    @NotEmpty(message = "需求字段列表不能为空")
    private List<Fields> fields;

    @Data
    public static class Fields {
        @NotBlank(message = "需求字段id不能为空")
        private String fieldsId;
        @NotBlank(message = "需求字段值不能为空")
        private String fieldsValue;
    }
}
