package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InventoryCutInfoExample {
    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        InventoryCutInfoExample example = new InventoryCutInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameIsNull() {
            addCriterion("inventory_now_name is null");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameIsNotNull() {
            addCriterion("inventory_now_name is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameEqualTo(String value) {
            addCriterion("inventory_now_name =", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameNotEqualTo(String value) {
            addCriterion("inventory_now_name <>", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameGreaterThan(String value) {
            addCriterion("inventory_now_name >", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_now_name >=", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLessThan(String value) {
            addCriterion("inventory_now_name <", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLessThanOrEqualTo(String value) {
            addCriterion("inventory_now_name <=", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_now_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLike(String value) {
            addCriterion("inventory_now_name like", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameNotLike(String value) {
            addCriterion("inventory_now_name not like", value, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameIn(List<String> values) {
            addCriterion("inventory_now_name in", values, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameNotIn(List<String> values) {
            addCriterion("inventory_now_name not in", values, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameBetween(String value1, String value2) {
            addCriterion("inventory_now_name between", value1, value2, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameNotBetween(String value1, String value2) {
            addCriterion("inventory_now_name not between", value1, value2, "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameIsNull() {
            addCriterion("inventory_pattern_name is null");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameIsNotNull() {
            addCriterion("inventory_pattern_name is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameEqualTo(String value) {
            addCriterion("inventory_pattern_name =", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameNotEqualTo(String value) {
            addCriterion("inventory_pattern_name <>", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameGreaterThan(String value) {
            addCriterion("inventory_pattern_name >", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_pattern_name >=", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLessThan(String value) {
            addCriterion("inventory_pattern_name <", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLessThanOrEqualTo(String value) {
            addCriterion("inventory_pattern_name <=", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("inventory_pattern_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLike(String value) {
            addCriterion("inventory_pattern_name like", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameNotLike(String value) {
            addCriterion("inventory_pattern_name not like", value, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameIn(List<String> values) {
            addCriterion("inventory_pattern_name in", values, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameNotIn(List<String> values) {
            addCriterion("inventory_pattern_name not in", values, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameBetween(String value1, String value2) {
            addCriterion("inventory_pattern_name between", value1, value2, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameNotBetween(String value1, String value2) {
            addCriterion("inventory_pattern_name not between", value1, value2, "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andCutTimeIsNull() {
            addCriterion("cut_time is null");
            return (Criteria) this;
        }

        public Criteria andCutTimeIsNotNull() {
            addCriterion("cut_time is not null");
            return (Criteria) this;
        }

        public Criteria andCutTimeEqualTo(Date value) {
            addCriterion("cut_time =", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeNotEqualTo(Date value) {
            addCriterion("cut_time <>", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeGreaterThan(Date value) {
            addCriterion("cut_time >", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("cut_time >=", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeLessThan(Date value) {
            addCriterion("cut_time <", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeLessThanOrEqualTo(Date value) {
            addCriterion("cut_time <=", value, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("cut_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCutTimeIn(List<Date> values) {
            addCriterion("cut_time in", values, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeNotIn(List<Date> values) {
            addCriterion("cut_time not in", values, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeBetween(Date value1, Date value2) {
            addCriterion("cut_time between", value1, value2, "cutTime");
            return (Criteria) this;
        }

        public Criteria andCutTimeNotBetween(Date value1, Date value2) {
            addCriterion("cut_time not between", value1, value2, "cutTime");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("operator <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andIsCancelIsNull() {
            addCriterion("is_cancel is null");
            return (Criteria) this;
        }

        public Criteria andIsCancelIsNotNull() {
            addCriterion("is_cancel is not null");
            return (Criteria) this;
        }

        public Criteria andIsCancelEqualTo(Boolean value) {
            addCriterion("is_cancel =", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelNotEqualTo(Boolean value) {
            addCriterion("is_cancel <>", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThan(Boolean value) {
            addCriterion("is_cancel >", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_cancel >=", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThan(Boolean value) {
            addCriterion("is_cancel <", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanOrEqualTo(Boolean value) {
            addCriterion("is_cancel <=", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("is_cancel <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelIn(List<Boolean> values) {
            addCriterion("is_cancel in", values, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotIn(List<Boolean> values) {
            addCriterion("is_cancel not in", values, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelBetween(Boolean value1, Boolean value2) {
            addCriterion("is_cancel between", value1, value2, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_cancel not between", value1, value2, "isCancel");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(InventoryCutInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andInventoryNowNameLikeInsensitive(String value) {
            addCriterion("upper(inventory_now_name) like", value.toUpperCase(), "inventoryNowName");
            return (Criteria) this;
        }

        public Criteria andInventoryPatternNameLikeInsensitive(String value) {
            addCriterion("upper(inventory_pattern_name) like", value.toUpperCase(), "inventoryPatternName");
            return (Criteria) this;
        }

        public Criteria andOperatorLikeInsensitive(String value) {
            addCriterion("upper(operator) like", value.toUpperCase(), "operator");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Sep 09 10:25:53 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private InventoryCutInfoExample example;

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        protected Criteria(InventoryCutInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public InventoryCutInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Sep 09 10:25:53 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.InventoryCutInfoExample example);
    }
}