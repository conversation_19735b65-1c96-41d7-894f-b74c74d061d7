package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeAd;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeBanner;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售端小程序小场景信息
 */
@Data
public class SceneVO implements Serializable {
    /**
     * 主键
     *
     */
    private String id;


    /**
     * 状态,0:已上传、1:已发布、2:已下线、3:审核中,4:已驳回
     */
    private Integer status;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     */
    private Integer auditStatus;

    /**
     * 场景解决方案名称
     */
    private String name;

    /**
     * 一级目录id
     */
    private String firstDirectoryId;

    /**
     * 一级目录
     */
    private String firstDirectoryName;

    /**
     * 二级目录id
     *
     */
    private String secondDirectoryId;

    /**
     * 二级目录
     *
     */
    private String secondDirectoryName;

    /**
     * 解决方案产品编码
     *
     */
    private String spuCode;

    /**
     * 解决方案产品编码
     *
     */
    private String spuName;

    /**
     * 场景头图url
     *
     */
    private String headImageUrl;

    /**
     * 场景图片url
     *
     */
    private String imageUrl;

    /**
     * 需求模板ID
     *
     */
    private String templateId;

    /**
     * 需求模板名称
     *
     */
    private String templateName;


    /**
     * 创建人id
     *
     */
    private String createUid;

    /**
     * 创建人
     *
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**产品列表*/
    private List<MiniProgramProductListVO> products;




}