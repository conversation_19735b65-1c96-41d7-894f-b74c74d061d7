package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/4/7 09:46
 */
@Data
public class HomeSearchParam extends BasePageQuery {

    //搜索关键字
    private String keyWord;

    //类型 1-产品、2-活动、3-资讯、4-素材、5-知识
    @NotNull(message = "搜索类型不能为空")
    @Range(min = 1,max = 5,message = "搜索类型错误")
    private Integer type;

    private String userId;

    private String provinceCode;

    private String cityCode;

    private String roleType;
}
