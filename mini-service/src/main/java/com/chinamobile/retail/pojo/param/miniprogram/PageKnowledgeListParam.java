package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/09/23 11:04
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageKnowledgeListParam extends BasePageQuery {

    /**
     * 问答主题
     */
    private String topics;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 是否为热门问答,0-否，1-是
     */
    private Integer isPopular;
    /**
     * 审批状态
     */
    private List<Integer> auditStatusList;
    /**
     * 发布状态
     */
    private Integer releaseStatus;
    /**
     * 小程序搜索
     */
    private String searchkey;


}
