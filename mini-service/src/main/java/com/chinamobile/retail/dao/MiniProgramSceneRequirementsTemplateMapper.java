package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplate;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MiniProgramSceneRequirementsTemplateMapper {
    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    long countByExample(MiniProgramSceneRequirementsTemplateExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int deleteByExample(MiniProgramSceneRequirementsTemplateExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int insert(MiniProgramSceneRequirementsTemplate record);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int insertSelective(MiniProgramSceneRequirementsTemplate record);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    List<MiniProgramSceneRequirementsTemplate> selectByExample(MiniProgramSceneRequirementsTemplateExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    MiniProgramSceneRequirementsTemplate selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramSceneRequirementsTemplate record, @Param("example") MiniProgramSceneRequirementsTemplateExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramSceneRequirementsTemplate record, @Param("example") MiniProgramSceneRequirementsTemplateExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramSceneRequirementsTemplate record);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int updateByPrimaryKey(MiniProgramSceneRequirementsTemplate record);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramSceneRequirementsTemplate> list);

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramSceneRequirementsTemplate> list, @Param("selective") MiniProgramSceneRequirementsTemplate.Column ... selective);
}