package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.Order2cDistributorInfo;
import com.chinamobile.retail.pojo.entity.Order2cDistributorInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cDistributorInfoMapper {
    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    long countByExample(Order2cDistributorInfoExample example);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int deleteByExample(Order2cDistributorInfoExample example);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int insert(Order2cDistributorInfo record);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int insertSelective(Order2cDistributorInfo record);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    List<Order2cDistributorInfo> selectByExample(Order2cDistributorInfoExample example);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    Order2cDistributorInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int updateByExampleSelective(@Param("record") Order2cDistributorInfo record, @Param("example") Order2cDistributorInfoExample example);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int updateByExample(@Param("record") Order2cDistributorInfo record, @Param("example") Order2cDistributorInfoExample example);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int updateByPrimaryKeySelective(Order2cDistributorInfo record);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int updateByPrimaryKey(Order2cDistributorInfo record);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int batchInsert(@Param("list") List<Order2cDistributorInfo> list);

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    int batchInsertSelective(@Param("list") List<Order2cDistributorInfo> list, @Param("selective") Order2cDistributorInfo.Column ... selective);
}