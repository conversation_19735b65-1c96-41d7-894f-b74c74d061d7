package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportViewTag;
import com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportViewTagExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramSaleYearReportViewTagMapper {
    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    long countByExample(MiniProgramSaleYearReportViewTagExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int deleteByExample(MiniProgramSaleYearReportViewTagExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int insert(MiniProgramSaleYearReportViewTag record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int insertSelective(MiniProgramSaleYearReportViewTag record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    List<MiniProgramSaleYearReportViewTag> selectByExample(MiniProgramSaleYearReportViewTagExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    MiniProgramSaleYearReportViewTag selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int updateByExampleSelective(@Param("record") MiniProgramSaleYearReportViewTag record, @Param("example") MiniProgramSaleYearReportViewTagExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int updateByExample(@Param("record") MiniProgramSaleYearReportViewTag record, @Param("example") MiniProgramSaleYearReportViewTagExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int updateByPrimaryKeySelective(MiniProgramSaleYearReportViewTag record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int updateByPrimaryKey(MiniProgramSaleYearReportViewTag record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int batchInsert(@Param("list") List<MiniProgramSaleYearReportViewTag> list);

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    int batchInsertSelective(@Param("list") List<MiniProgramSaleYearReportViewTag> list, @Param("selective") MiniProgramSaleYearReportViewTag.Column ... selective);
}