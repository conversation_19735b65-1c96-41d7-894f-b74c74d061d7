package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.JkbanProvinceInfo;
import com.chinamobile.retail.pojo.entity.JkbanProvinceInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface JkbanProvinceInfoMapper {
    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    long countByExample(JkbanProvinceInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int deleteByExample(JkbanProvinceInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int insert(JkbanProvinceInfo record);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int insertSelective(JkbanProvinceInfo record);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    List<JkbanProvinceInfo> selectByExample(JkbanProvinceInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int updateByExampleSelective(@Param("record") JkbanProvinceInfo record, @Param("example") JkbanProvinceInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int updateByExample(@Param("record") JkbanProvinceInfo record, @Param("example") JkbanProvinceInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int batchInsert(@Param("list") List<JkbanProvinceInfo> list);

    /**
     *
     * @mbg.generated Wed Jul 02 15:22:11 CST 2025
     */
    int batchInsertSelective(@Param("list") List<JkbanProvinceInfo> list, @Param("selective") JkbanProvinceInfo.Column ... selective);
}