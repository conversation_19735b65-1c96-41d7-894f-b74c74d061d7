package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramActivity;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramActivityMapper {
    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    long countByExample(MiniProgramActivityExample example);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int deleteByExample(MiniProgramActivityExample example);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int insert(MiniProgramActivity record);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int insertSelective(MiniProgramActivity record);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    List<MiniProgramActivity> selectByExample(MiniProgramActivityExample example);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    MiniProgramActivity selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int updateByExampleSelective(@Param("record") MiniProgramActivity record, @Param("example") MiniProgramActivityExample example);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int updateByExample(@Param("record") MiniProgramActivity record, @Param("example") MiniProgramActivityExample example);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int updateByPrimaryKeySelective(MiniProgramActivity record);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int updateByPrimaryKey(MiniProgramActivity record);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int batchInsert(@Param("list") List<MiniProgramActivity> list);

    /**
     *
     * @mbg.generated Wed Apr 09 15:09:20 CST 2025
     */
    int batchInsertSelective(@Param("list") List<MiniProgramActivity> list, @Param("selective") MiniProgramActivity.Column ... selective);
}