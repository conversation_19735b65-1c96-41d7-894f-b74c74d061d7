package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramActivityRegion;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityRegionExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramActivityRegionMapper {
    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    long countByExample(MiniProgramActivityRegionExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int deleteByExample(MiniProgramActivityRegionExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int insert(MiniProgramActivityRegion record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int insertSelective(MiniProgramActivityRegion record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    List<MiniProgramActivityRegion> selectByExample(MiniProgramActivityRegionExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    MiniProgramActivityRegion selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramActivityRegion record, @Param("example") MiniProgramActivityRegionExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramActivityRegion record, @Param("example") MiniProgramActivityRegionExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramActivityRegion record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int updateByPrimaryKey(MiniProgramActivityRegion record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramActivityRegion> list);

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramActivityRegion> list, @Param("selective") MiniProgramActivityRegion.Column ... selective);
}