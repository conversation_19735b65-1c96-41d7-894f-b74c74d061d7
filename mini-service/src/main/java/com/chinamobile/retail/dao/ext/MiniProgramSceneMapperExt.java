package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.dto.RequirementsQuestionAndAnswerDTO;
import com.chinamobile.retail.pojo.param.miniprogram.PageRequirementListParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageSceneFrontendParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageSceneListParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageTemplateListParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Mapper
public interface MiniProgramSceneMapperExt {

    List<SceneFrontendItemVO> pageSceneFrontend(PageSceneFrontendParam param);

    Long countSceneFrontend(PageSceneFrontendParam param);

    List<SceneFrontendItemVO.RelatedSpuVO> selectRelatedSpu(@Param("sceneId") String sceneId);

    SceneDetailFrontendVO getSceneDetailFrontend(@Param("sceneId") String sceneId);

    List<SceneVO> pageSceneList(PageSceneListParam param);

    Long countSceneList(PageSceneListParam param);

    List<MiniProgramProductListVO> getSceneProduct(@Param("sceneId") String homeId);

    List<SceneRequirementVO> pageRequirementList(PageRequirementListParam param);

    Long countRequirementList(PageRequirementListParam param);

    List<TemplateVO> pageTemplateList(PageTemplateListParam param);

    Long countTemplateList(PageTemplateListParam param);

    List<TemplateVO> searchTemplateList(PageTemplateListParam param);

    List<RequirementsQuestionAndAnswerDTO> getRequirementAnswers(@Param("requirementId") String requirementId);

    String getPartnerBusinessName(@Param("partnerBusinessId")String partnerBusinessId);
}
