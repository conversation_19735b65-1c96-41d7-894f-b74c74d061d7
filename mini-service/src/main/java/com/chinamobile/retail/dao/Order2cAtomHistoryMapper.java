package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.Order2cAtomHistory;
import com.chinamobile.retail.pojo.entity.Order2cAtomHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAtomHistoryMapper {
    long countByExample(Order2cAtomHistoryExample example);

    int deleteByExample(Order2cAtomHistoryExample example);

    int insert(Order2cAtomHistory record);

    int insertSelective(Order2cAtomHistory record);

    List<Order2cAtomHistory> selectByExample(Order2cAtomHistoryExample example);

    int updateByExampleSelective(@Param("record") Order2cAtomHistory record, @Param("example") Order2cAtomHistoryExample example);

    int updateByExample(@Param("record") Order2cAtomHistory record, @Param("example") Order2cAtomHistoryExample example);

    int batchInsert(@Param("list") List<Order2cAtomHistory> list);

    int batchInsertSelective(@Param("list") List<Order2cAtomHistory> list, @Param("selective") Order2cAtomHistory.Column ... selective);
}