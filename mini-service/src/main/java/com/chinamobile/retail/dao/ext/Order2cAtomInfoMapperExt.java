package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.mapper.ExportDistributeOrderDO;
import com.chinamobile.retail.pojo.mapper.OrderBackListDO;
import com.chinamobile.retail.pojo.mapper.OrderExportListDO;
import com.chinamobile.retail.pojo.vo.miniprogram.OrderThisYearVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface Order2cAtomInfoMapperExt {

    List<OrderBackListDO> pageQueryOrderBackList(String orderId, String startTimeStr,String endTimeStr, Integer orderStatus, String partnerName, String phone, String skuOfferingName, String spuOfferingName, int start, Integer pageSize);

    Integer pageCountOrderBackList(String orderId, String startTimeStr,String endTimeStr, Integer orderStatus, String partnerName, String phone, String skuOfferingName, String spuOfferingName);

    List<OrderExportListDO> getOrderExportList(String orderId, Integer orderStatus, String startTimeStr, String endTimeStr, String partnerName, String phone, String skuOfferingName, String spuOfferingName);

    List<ExportDistributeOrderDO> getExportDistributeOrderList(String startTimeStr, String endTimeStr);

    List<OrderThisYearVO> selectOrderSince2024();
}
