package com.chinamobile.retail.config;

import com.chinamobile.retail.schedule.ActivityRankingTask;
import com.chinamobile.retail.schedule.WeixinAccessTokenRefreshTask;
import com.chinamobile.retail.schedule.WeixinJsapiTicketRefreshTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/12/28 11:24
 */
@Component
public class WeixinApplicationRunner implements ApplicationRunner {

    @Autowired
    WeixinAccessTokenRefreshTask weixinAccessTokenRefreshTask;

    @Autowired
    WeixinJsapiTicketRefreshTask weixinJsapiTicketRefreshTask;
    @Autowired
    ActivityRankingTask activityRankingTask;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        weixinAccessTokenRefreshTask.work();
        weixinJsapiTicketRefreshTask.work();
    }
}
