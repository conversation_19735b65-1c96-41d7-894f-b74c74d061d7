package com.chinamobile.retail.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * RSAUtils （RSA工具类）
 */
public class RSAUtils {



    // MAX_DECRYPT_BLOCK应等于密钥长度/8（1byte=8bit），所以当密钥位数为2048时，最大解密长度应为256.
    // 128 对应 1024，256对应2048
    private static final int KEYSIZE = 2048;

    // RSA最大加密明文大小
    private static final int MAX_ENCRYPT_BLOCK = 117;

    // RSA最大解密密文大小
    private static final int MAX_DECRYPT_BLOCK = 256;

    // 不仅可以使用DSA算法，同样也可以使用RSA算法做数字签名
    private static final String KEY_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    public static final String DEFAULT_SEED = "$%^*%^()(ED47d784sde78"; // 默认种子

    public static final String PUBLIC_KEY = "PublicKey";
    public static final String PRIVATE_KEY = "PrivateKey";

    /**
     *
     * 生成密钥
     *
     * @param seed 种子
     *
     * @return 密钥对象
     * @throws Exception
     *
     */

    public static Map<String, Key> initKey(String seed) throws Exception {
        System.out.println("生成密钥");
        KeyPairGenerator keygen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        SecureRandom secureRandom = new SecureRandom();
        // 如果指定seed，那么secureRandom结果是一样的，所以生成的公私钥也永远不会变
        // secureRandom.setSeed(seed.getBytes());
        // Modulus size must range from 512 to 1024 and be a multiple of 64
        keygen.initialize(KEYSIZE, secureRandom);
        KeyPair keys = keygen.genKeyPair();
        PrivateKey privateKey = keys.getPrivate();
        PublicKey publicKey = keys.getPublic();
        Map<String, Key> map = new HashMap<>(2);
        map.put(PUBLIC_KEY, publicKey);
        map.put(PRIVATE_KEY, privateKey);
        return map;
    }

    /**
     *
     * 生成默认密钥
     *
     *
     * @return 密钥对象
     * @throws Exception
     *
     */

    public static Map<String, Key> initKey() throws Exception {
        return initKey(DEFAULT_SEED);
    }

    /**
     *
     * 取得私钥
     *
     * @param keyMap
     *
     * @return
     * @throws Exception
     *
     */
    public static String getPrivateKey(Map<String, Key> keyMap) throws Exception {
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        return encryptBASE64(key.getEncoded()); // base64加密私钥
    }

    /**
     *
     * 取得公钥
     *
     * @param keyMap
     *
     * @return
     * @throws Exception
     *
     */
    public static String getPublicKey(Map<String, Key> keyMap) throws Exception {
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        return encryptBASE64(key.getEncoded()); // base64加密公钥
    }


    /**
     * 用私钥对map对象进行排序并签名
     *
     * @param
     */
    public static void signMapByPrivateKey(Map<String, Object> resp,String privateKey) throws Exception {
        //排序
        sortMapByKey(resp);
        StringBuilder paramBuilder = new StringBuilder();
        resp.forEach((k, v) -> {
            if (v != null && !k.equals("sign")) {
                paramBuilder.append(k).append("=").append(v).append("&");
            }
        });
        //获取签名
        String sign = signByPrivateKey(paramBuilder.toString().getBytes(), new String(privateKey.getBytes()));
        System.out.println("4.加签结果：" + sign);
        resp.put("sign",sign);

    }

    /**
     * 验签（公钥验签）
     *
     * @param req 请求参数
     * @param sign 签名
     */
    public static boolean verifySign(TreeMap<String, Object> req, String sign, String publicKey) throws Exception {
        //排序
        sortMapByKey(req);
        StringBuilder paramBuilder = new StringBuilder();
        req.forEach((k, v) -> {
            if (v != null && !k.equals("sign")) {
                paramBuilder.append(k).append("=").append(v).append("&");
            }
        });
        //验签
        String newDataJson = paramBuilder.toString();
//        String sign_c = RSAUtils.signByPrivateKey(newDataJson.getBytes(),config.getPrivateKey());
//        System.out.println("参数加签：" + sign_c);
        //获取签名
        boolean res =
                RSAUtils.verifyByPublicKey(newDataJson.getBytes(),publicKey,sign);
        System.out.println("4.验签结果：" + res);
       return res;

    }

    /**
     *
     * 用私钥对信息进行数字签名
     *
     * @param data       加密数据
     *
     * @param privateKey 私钥-base64加密的
     *
     * @return
     *
     * @throws Exception
     *
     */
    public static String signByPrivateKey(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = decryptBASE64(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey priKey = factory.generatePrivate(keySpec);// 生成私钥
        // 用私钥对信息进行数字签名
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data);
        return encryptBASE64(signature.sign());

    }

    /**
     *
     * BASE64Encoder 加密
     *
     * @param data 要加密的数据
     *
     * @return 加密后的字符串
     *
     */
    private static String encryptBASE64(byte[] data) {
//		BASE64Encoder encoder = new BASE64Encoder();
//		String encode = encoder.encode(data);
//		return encode;
        return new String(Base64.encodeBase64(data));
    }

    private static byte[] decryptBASE64(String data) {
        // BASE64Decoder 每76个字符换行
        // BASE64Decoder decoder = new BASE64Decoder();
        // byte[] buffer = decoder.decodeBuffer(data);
        // return buffer;
        // codec 的 Base64 不换行
        return Base64.decodeBase64(data);
    }

    public static boolean verifyByPublicKey(byte[] data, String publicKey, String sign) throws Exception {
        byte[] keyBytes = decryptBASE64(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data);
        return signature.verify(decryptBASE64(sign)); // 验证签名
    }

    /**
     * RSA公钥加密
     *
     * @param str       加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws UnsupportedEncodingException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws Exception                    加密过程中的异常信息
     */
    public static String encryptByPublicKey(String str, String publicKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        // base64编码的公钥
        byte[] keyBytes = decryptBASE64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(KEY_ALGORITHM)
                .generatePublic(new X509EncodedKeySpec(keyBytes));
        // RSA加密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);

        byte[] data = str.getBytes("UTF-8");
        // 加密时超过117字节就报错。为此采用分段加密的办法来加密
        byte[] enBytes = null;
        for (int i = 0; i < data.length; i += MAX_ENCRYPT_BLOCK) {
            // 注意要使用2的倍数，否则会出现加密后的内容再解密时为乱码
            byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(data, i, i + MAX_ENCRYPT_BLOCK));
            enBytes = ArrayUtils.addAll(enBytes, doFinal);
        }
        String outStr = encryptBASE64(enBytes);
        return outStr;
    }

    /**
     * RSA私钥加密Map对象
     *
     * @param param       加密map
     * @param privateKey 私钥
     * @return 密文
     */
    public static String encryptMapByPrivateKey(Map<String,Object> param, String privateKey)
            throws NoSuchPaddingException, IllegalBlockSizeException, UnsupportedEncodingException,
            InvalidKeySpecException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String dataStr = JSON.toJSONString(param);
        byte[] data = dataStr.getBytes();
       //加密
        return encryptByPrivateKey(new String(data), new String(privateKey.getBytes()));
    }

    /**
     * RSA私钥加密
     *
     * @param str       加密字符串
     * @param privateKey 公钥
     * @return 密文
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws UnsupportedEncodingException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws Exception                    加密过程中的异常信息
     */
    public static String encryptByPrivateKey(String str, String privateKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        // base64编码的公钥
        byte[] keyBytes = decryptBASE64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(KEY_ALGORITHM)
                .generatePrivate(new PKCS8EncodedKeySpec(keyBytes));
        // RSA加密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, priKey);

        byte[] data = str.getBytes("UTF-8");
        // 加密时超过117字节就报错。为此采用分段加密的办法来加密
        byte[] enBytes = null;
        for (int i = 0; i < data.length; i += MAX_ENCRYPT_BLOCK) {
            // 注意要使用2的倍数，否则会出现加密后的内容再解密时为乱码
            byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(data, i, i + MAX_ENCRYPT_BLOCK));
            enBytes = ArrayUtils.addAll(enBytes, doFinal);
        }
        String outStr = encryptBASE64(enBytes);
        return outStr;
    }

    /**
     * 读取公钥
     *
     * @param publicKeyPath
     * @return
     */
    public static PublicKey readPublic(String publicKeyPath) {
        if (publicKeyPath != null) {
            try (FileInputStream bais = new FileInputStream(publicKeyPath)) {
                CertificateFactory certificatefactory = CertificateFactory.getInstance("X.509");
                X509Certificate cert = (X509Certificate) certificatefactory.generateCertificate(bais);
                return cert.getPublicKey();
            } catch (CertificateException e) {
                e.printStackTrace();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 读取私钥
     *
     * @param
     * @return
     */
    public static PrivateKey readPrivate(String privateKeyPath, String privateKeyPwd) {
        if (privateKeyPath == null || privateKeyPwd == null) {
            return null;
        }
        try (InputStream stream = new FileInputStream(new File(privateKeyPath));) {
            // 获取JKS 服务器私有证书的私钥，取得标准的JKS的 KeyStore实例
            KeyStore store = KeyStore.getInstance("JKS");// JKS，二进制格式，同时包含证书和私钥，一般有密码保护；PKCS12，二进制格式，同时包含证书和私钥，一般有密码保护。
            // jks文件密码，根据实际情况修改
            store.load(stream, privateKeyPwd.toCharArray());
            // 获取jks证书别名
            Enumeration<String> en = store.aliases();
            String pName = null;
            while (en.hasMoreElements()) {
                String n = (String) en.nextElement();
                if (store.isKeyEntry(n)) {
                    pName = n;
                }
            }
            // 获取证书的私钥
            PrivateKey key = (PrivateKey) store.getKey(pName, privateKeyPwd.toCharArray());
            return key;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * RSA私钥解密
     *
     * @param encryStr   加密字符串
     * @param privateKey 私钥
     * @return 铭文
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws NoSuchPaddingException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws InvalidKeyException
     * @throws Exception                 解密过程中的异常信息
     */
    public static String decryptByPrivateKey(String encryStr, String privateKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, IllegalBlockSizeException,
            BadPaddingException, InvalidKeyException {
        // base64编码的私钥
        byte[] decoded = decryptBASE64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(KEY_ALGORITHM)
                .generatePrivate(new PKCS8EncodedKeySpec(decoded));
        // RSA解密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, priKey);

        // 64位解码加密后的字符串
        byte[] data = decryptBASE64(encryStr);
        // 解密时超过128字节报错。为此采用分段解密的办法来解密
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i += MAX_DECRYPT_BLOCK) {
            byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(data, i, i + MAX_DECRYPT_BLOCK));
            sb.append(new String(doFinal));
        }

        return sb.toString();
    }

    /**
     * RSA公钥解密
     *
     * @param encryStr   加密字符串
     * @param
     * @return 铭文
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws NoSuchPaddingException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws InvalidKeyException
     * @throws Exception                 解密过程中的异常信息
     */
    public static String decryptByPublicKey(String encryStr, String publicKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, IllegalBlockSizeException,
            BadPaddingException, InvalidKeyException {
        // base64编码的私钥
        byte[] decoded = decryptBASE64(publicKey);
        RSAPublicKey priKey = (RSAPublicKey) KeyFactory.getInstance(KEY_ALGORITHM)
                .generatePublic(new X509EncodedKeySpec(decoded));
        // RSA解密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, priKey);

        // 64位解码加密后的字符串
        byte[] data = decryptBASE64(encryStr);
        // 解密时超过128字节报错。为此采用分段解密的办法来解密
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i += MAX_DECRYPT_BLOCK) {
            byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(data, i, i + MAX_DECRYPT_BLOCK));
            sb.append(new String(doFinal));
        }

        return sb.toString();
    }


    /**
     * 对Map对象key升序排序
     * @param map
     * @return
     */
    public static Map<String, Object> sortMapByKey(Map<String, Object> map) {
        /**
         * 对Map对象的key升序（a->z）排列
         */
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, Object> sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(map);
        return sortMap;
    }

    /**
     * 同 PHP http_build_query方法,并过滤签名key
     *
     * @param sortedParams 需要处理的参数map
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getEncodeSignContent(Map<String, String> sortedParams) throws
            UnsupportedEncodingException {
        StringBuffer content = new StringBuffer();
        sortedParams.remove("sign");
        List<String> keys = new ArrayList(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); ++i) {
            String publicKey = keys.get(i);
            String value = sortedParams.get(publicKey);
            if (value != null && !publicKey.equals("sign")) {
                content.append(publicKey).append("=").append(value).append("&");
            }
        }
        return content.toString();
    }

    /**
     * 拉卡拉回调方法专用
     * @param sortedParams
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getBackEncodeSignContent(Map<String, String> sortedParams) throws UnsupportedEncodingException {
        StringBuilder content = new StringBuilder();
        sortedParams.remove("sign");
        List<String> keys = new ArrayList<>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;

        for (String publicKey : keys) {
            String value = sortedParams.get(publicKey);
            if (areNotEmpty(publicKey, value)) {
                value = URLEncoder.encode(value, "utf-8");
                content.append(index == 0 ? "" : "&").append(publicKey).append("=").append(value);
                ++index;
            }
        }
        return content.toString();
    }


    /**
     * 检查指定的字符串列表是否不为空。
     */
    public static boolean areNotEmpty(String... values) {
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            for (String value : values) {
                result &= !isEmpty(value);
            }
        }
        return result;
    }

    /**
     * 检查指定的字符串是否为空。
     *
     * @param value 待检查的字符串
     * @return true/false
     */
    public static boolean isEmpty(String value) {
        int strLen;
        if (value == null || (strLen = value.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((Character.isWhitespace(value.charAt(i)) == false)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 对 字符串进行hash算法
     * @param message
     * @return
     */
    public static String hashMessage(String message,String key) {
        try {
            String hashMessage = message + key;
            // 创建SHA-256消息摘要对象
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 将姓名转换为字节数组
            byte[] messageBytes = hashMessage.getBytes();

            // 计算哈希值
            byte[] hashBytes = digest.digest(messageBytes);

            // 将哈希值转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static void main(String[] args) throws NoSuchPaddingException, IllegalBlockSizeException, UnsupportedEncodingException, InvalidKeySpecException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String privateKeyBase64 = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDTyrPZuAjILMsqTPxkkh3Rjp4/Cl94mw+8HP2lyN71m4GvOiAQQiT2JgsT/G//6XfcDqwPnc7VTIY7+3NN2dTR/D40dE9IHhLTBjMyuBAU4vJ9iwzBHv0dPfY17DTBNvd/MP7Rsp+j+YGaOhWaGlsgm6TVtlQey7QsmvWAV0YXGlyAf07ssmh1/M2bfxE95YrnpyUhjDZQzA+rq51kS7EAuPE/UNSOgDklwOKLi5guAhvI1Q6rLjtufdC+JqYrIPmxjRhjW1RFE2UNCw7LqBfByb9n8mxgP4LKBjiCLYAfEMfzieIQUNpLSj+a3xo1S4rpMGiR7TYxFLBlzdcxif6NAgMBAAECggEAWfSyxzq/oCZGdMUWPrFBMQzecfA59MQHvuKhaZUT/kX6oy6RB9bQVCx8cBS8jXngivtAYbGpdDd4nGmE5AAtwLLeyPDN69e6Lx9nB5feXMC4NlKlLDG5WH5E4UpebeKm5MMuuGqiG8eSIKujGT0wj30MWimDOUFUWc3HkKeBAbZOo7xRs28cSHk81G7mpiYmCQUJVyOCbdDOwGMkE51t07iVGNswXRqYaavVI85eEB4QRuPAdCGOZff/ll6rVrJPEBj8ch03oDEszdQb+0B39pUi3sfex2AGvLISSJTSy9cab43MCcGYZdL4leX7Eq+x3exZO3fmVPTm3dtJyhiPEQKBgQD0VWXMofRAHPl8cxf/lq1PAcalffBUo24Fv1DfsD9yNxagM8s5PzChsrTpi4BxEUdMZeeQrUtlrHgxWF9sBp21LczObkkqKQ9/b0M3rXIS91bqsQsFWS2OsGFzLo+voxtGip5KK9ORI34cwSaXxuJ5kwC5hAXnrG8I9Ef7IyWLewKBgQDd54gdKsR80HIuRLsirQZP/cKbHgcjQY1OATtk8q6b/rtvO9t0wSOeCCRMuv/EuKGk7POtqyuvwutqg1WKNxMy1bY9ty4bRXd44ii4aCYGDOAl8NFtJCSMWr++t6XzNHbwClbLSbY/tOGGIVumg1p+udu+btOb8lpHg7/WyHBblwKBgQC+3D6rPQ/JTVjuCBIFC7TR9Lcx61DjLM7zGmGYetr042d/OTZUv7HDfg+oJ9rrd+3UFf5vm488Gx/AnCHeBsUHFIHZ93ibwHtktosxYQGtIxz4M9hCE0ltHwbgrMx9DNJvpjTEB7w6shj/aTo2cZvUECsOv7zFHoOV4QyhdvELJQKBgBkluvwrM5c9fCMYMOjuGNAJ3vr7PS3WqO/VHekDw5v0E0O40db6aFHpdEupyYB+t/rby4W75ziE627nsVL3iNpy87MsxjHa/n4ZiynSy7RV0zUZhHJM7UNmqWIwp8LXCD+NvGZPVTMFCaXMs/k7246O+4MqRhrfLTH7kUsC7hDDAoGBAMs+rMVstr9udOIsKLmADBXPGK+UuWbtJAVCSyX61EuKz8gJL8KyXOlc3Dnokf4Qhh5ZMtslM6VPeSwGkHCH0CA66t/Aq56TCkbQi1IrJbc7gCL/CuX1jly8pOQs8widY/U7UrVxsJ87POVVXsf3I35FD198uYrqXzklLNGX7X6l";
        String publicKeyBase64 = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA08qz2bgIyCzLKkz8ZJId0Y6ePwpfeJsPvBz9pcje9ZuBrzogEEIk9iYLE/xv/+l33A6sD53O1UyGO/tzTdnU0fw+NHRPSB4S0wYzMrgQFOLyfYsMwR79HT32New0wTb3fzD+0bKfo/mBmjoVmhpbIJuk1bZUHsu0LJr1gFdGFxpcgH9O7LJodfzNm38RPeWK56clIYw2UMwPq6udZEuxALjxP1DUjoA5JcDii4uYLgIbyNUOqy47bn3QviamKyD5sY0YY1tURRNlDQsOy6gXwcm/Z/JsYD+CygY4gi2AHxDH84niEFDaS0o/mt8aNUuK6TBoke02MRSwZc3XMYn+jQIDAQAB";
        String phone = encryptByPublicKey("13883129561", publicKeyBase64);
        String code = encryptByPublicKey("809029", publicKeyBase64);

        System.out.println("phone=" + phone);
        System.out.println("code=" + code);
        String result = decryptByPrivateKey(phone, privateKeyBase64);
        System.out.println("phone=" + result);
    }
}
