package com.chinamobile.retail.request.order2c;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/3 17:00
 * @Description:
 */
@Data
public class OrderInfoDTO {
    /**
     * 业务编码	业务编码名称
     * SyncGrpOrderInfo	集团订单同步
     * SyncIndividualOrderInfo	个人客户订单同步
     */
    private String businessCode;
    /**
     * 客户类型
     * ordertype=01时必传；
     * 0：个人客户
     * 1：集团客户
     */

    private String customerType;

    /**
     * 提单人类型
     * 代客下单订单必传；
     * 1：客户经理代客下单
     * 2：营业员代客下单
     */
    private String billLadderType;

    /**
     * 操作员编码
     * 取个人客户所属的客户经理的操作员编码;
     * 分享订购场景，取分享链接中的客户经理编码。（本场景暂不支持）
     * 自主注册个人客户，本字段为空。
     */
    private String createOperCode;

    /**
     * 操作员UserID
     * 与操作员编码（ createOperCode）搭配使用，传操作员的UserID信息；
     * 当ordertype=00且 billLadderType=2：营业员代客下单且offeringClass=A11:卡+X硬件时，以省侧回填的客户经理信息进行传值
     */
    private String createOperUserID;
    /**
     * 操作员省工号
     */
    private String employeeNum;
    /**
     * 操作员姓名
     */
    private String customerManagerName;
    /**
     * 操作员手机号
     */
    private String customerManagerPhone;
    /**
     * 操作员电话
     */
    private Date orderStatusTime;
    /**
     * 客户信息
     */
    private CustInfoDTO custInfo;
    /**
     * 备注
     * 个人客户非标准化的要求在备注中说明。
     */
    private String remarks;
    /**
     * 业务订单流水号
     */
    private String orderId;

    /**
     * 订购渠道来源
     * 商城自有渠道时，默认传019030-移动物联网商城；
     * 4A单点登录渠道时，默认传000000-其他；
     * 其他非4A单点登录渠道时，传运营管理员在渠道来源管理菜单中配置的渠道来源编码。
     */
    private String orderingChannelSource;

    /**
     * 订购渠道名称
     * 商城自有渠道时，默认传019030对应的“移动物联网商城”；
     * 4A单点登录渠道时，默认传000000对应的“其他”；
     * 其他非4A单点登录渠道时，传运营管理员在渠道来源管理菜单中配置的渠道来源编码对应的渠道来源名称。
     */
    private String orderingChannelName;

    /**
     * 4A单点登录访问类型
     * 枚举值：
     * 1：PC
     * 2：H5
     * 仅当个人/集团客户访问带有4A单点登录应用（如网格通/ESOP等）
     * 生成的链接下单或客户经理通过4A单点登录应用（如网格通/ESOP等）
     * 进行代客下单订单下单时传对应的枚举值（H5/PC），其他情况下该值不传。
     */
    private String ssoTerminalType;

    /**
     * 预占流水号
     */
    private String bookId;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 订单总金额
     */
    private String totalPrice;
    /**
     * 抵扣金额
     */
    private String deductPrice;
    /**
     * 关联领货码
     */
    private List<CouponInfoDTO> couponInfo;
    /**
     * 订单创建时间
     */
    private String createTime;
    /**
     * 收货人信息
     * 订单创建场景必填，其他同步场景非必填。
     */
    private ContactInfoDTO contactInfo;
    /**
     * 规格商品信息
     */
    private SpuOfferingInfoDTO spuOfferingInfo;

    /**
     * 订单组织信息
     */
    private OrderOrgBizInfoDTO orderOrgBizInfo;

    /**
     * 分销员信息
     */
    private List<DistributorInfoDTO> distributorInfo;


    /**
     * 订单类型 00：代客下单 01：自主下单
     */
    private String orderType;

    /**
     * 分销员信息
     */
    private List<ServiceNumberInfoDTO> serviceNumberInfo;

    /**
     * 渠道商信息
     */
    private List<AgentInfoDTO> agentInfo;

    /**
     * 卡增值服务包信息
     */
    private List<ValueAddedDTO> valueAdded;

    /**
     * 附近文件名，当订单状态=0:订单创建时传输
     */
    private String filename;
}
