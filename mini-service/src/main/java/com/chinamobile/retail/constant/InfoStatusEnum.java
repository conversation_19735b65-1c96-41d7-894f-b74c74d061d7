package com.chinamobile.retail.constant;

public enum InfoStatusEnum {

    DRAFT(0, "已上传"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "已下线"),
    AUDITING(3, "审核中"),
    REJECTED(4, "已驳回");

    private Integer status;

    private String name;

    InfoStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
