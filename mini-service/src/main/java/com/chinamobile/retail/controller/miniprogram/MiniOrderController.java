package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.enums.OrderTypeEnum;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.LogisticsVO;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.OrderInfoMiniProgramParam;
import com.chinamobile.retail.pojo.vo.MiniProgramOrderDetailVO;
import com.chinamobile.retail.pojo.vo.OrderInfoMiniProgramVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeDashboardVO;
import com.chinamobile.retail.service.OrderInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.chinamobile.iot.sc.feign.IotFeignClient;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/4
 * @description 极客小程序订单controller类
 */
@RestController
@RequestMapping("/miniprogram/order")
public class MiniOrderController {

    @Resource
    private IotFeignClient iotFeignClient;
    @Resource
    private OrderInfoService orderInfoService;

    /**
     * 分页查询订单信息
     * @param orderInfoMiniProgramParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageMiniProgramOrderInfo")
    public BaseAnswer<PageData<OrderInfoMiniProgramVO>> pageMiniProgramOrderInfo(OrderInfoMiniProgramParam orderInfoMiniProgramParam,
                                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        PageData<OrderInfoMiniProgramVO> pageData = orderInfoService.pageMiniProgramOrderInfo(orderInfoMiniProgramParam, loginIfo4Redis);
        return new BaseAnswer<PageData<OrderInfoMiniProgramVO>>().setData(pageData);
    }

    /**
     * 获取订单详情
     * @param orderId
     * @return
     */
    @GetMapping(value = "/getMiniProgramOrderDetail")
    public BaseAnswer<MiniProgramOrderDetailVO> getMiniProgramOrderDetail(@RequestParam(value = "orderId") String orderId,
                                                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        MiniProgramOrderDetailVO miniProgramOrderDetail = orderInfoService.getMiniProgramOrderDetail(orderId,loginIfo4Redis);
        miniProgramOrderDetail.setOrderTypeDesc(OrderTypeEnum.getDescByCode(miniProgramOrderDetail.getOrderType()));
        return new BaseAnswer<MiniProgramOrderDetailVO>().setData(miniProgramOrderDetail);
    }

    @GetMapping("/migrateUserOrderThisYear")
    public BaseAnswer migrateUserOrderThisYear() {
        orderInfoService.migrateUserOrderSince2024();
        return new BaseAnswer();
    }

    @GetMapping("/homeDashboard")
    public BaseAnswer<HomeDashboardVO> getHomeDashboard(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(orderInfoService.getHomeDashboard(loginIfo4Redis));
    }


    /**
     * 查询订单物流详情信息
     *
     * @param logisticsCode
     * @param supplierName
     * @param contactPhone
     * @return
     */
    @GetMapping("/logistics")
    public BaseAnswer<LogisticsVO> getOrderLogisticsMessage(@RequestParam("logisticsCode") String logisticsCode,
                                                            @RequestParam("supplierName") String supplierName,
                                                            @RequestParam(value = "contactPhone",required = false) String contactPhone) {
        return iotFeignClient.getOrderLogisticsMessage(logisticsCode, supplierName,contactPhone);
    }
}
