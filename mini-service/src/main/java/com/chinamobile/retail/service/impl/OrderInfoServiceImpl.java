package com.chinamobile.retail.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.InvoiceStatusEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.retail.config.SupplierInfoConfig;
import com.chinamobile.retail.constant.MiniRoleEnum;
import com.chinamobile.retail.constant.RedisLockConstant;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.retail.dao.ext.OrderInfoMapperExt;
import com.chinamobile.retail.enums.OrderStatusInnerEnum;
import com.chinamobile.retail.pojo.dto.LogisticsInfoDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.OrderInfoMiniProgramParam;
import com.chinamobile.retail.pojo.vo.MiniProgramOrderDetailVO;
import com.chinamobile.retail.pojo.vo.OrderInfoMiniProgramVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeDashboardVO;
import com.chinamobile.retail.pojo.vo.miniprogram.OrderThisYearVO;
import com.chinamobile.retail.request.Order2CInfoAtomRequest;
import com.chinamobile.retail.request.Order2CInfoAtomRequest.Order2cKfkAtomInfo;
import com.chinamobile.retail.request.order2c.AgentInfoDTO;
import com.chinamobile.retail.request.order2c.DistributorInfoDTO;
import com.chinamobile.retail.request.order2c.OrderInfoDTO;
import com.chinamobile.retail.service.IMiniProgramUserService;
import com.chinamobile.retail.service.OrderInfoService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/4
 * @description 订单service实现类
 */
@Service
@Slf4j
public class OrderInfoServiceImpl implements OrderInfoService {

    @Resource
    private OrderInfoMapperExt orderInfoMapperExt;

    @Resource
    private IMiniProgramUserService miniProgramUserService;

    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    @Resource
    private SupplierInfoConfig supplierInfo;
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private MiniProgramUserOrderInfoMapper miniProgramUserOrderInfoMapper;

    @Resource
    private SupplierAssociateProductMapper supplierAssociateProductMapper;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    @DS("query")
    public PageData<OrderInfoMiniProgramVO> pageMiniProgramOrderInfo(
            OrderInfoMiniProgramParam orderInfoMiniProgramParam,
            LoginIfo4Redis loginIfo4Redis) {
        PageData<OrderInfoMiniProgramVO> pageData = new PageData<>();
        Integer pageNum = orderInfoMiniProgramParam.getPageNum();
        Integer pageSize = orderInfoMiniProgramParam.getPageSize();

        String userId = loginIfo4Redis.getUserId();
        UserMiniProgram userMiniProgram = miniProgramUserService.getUserMiniProgramById(userId);
        if (!Optional.ofNullable(userMiniProgram).isPresent()
                || !"1".equals(userMiniProgram.getStatus())) {
            throw new BusinessException("10004", "用户信息错误");
        }
        String roleType = userMiniProgram.getRoleType();
        orderInfoMiniProgramParam.setRoleType(roleType);
        orderInfoMiniProgramParam.setUserId(userMiniProgram.getUserId());
        if (!"1".equals(roleType) && !"2".equals(roleType) && !"3".equals(roleType) && !"4".equals(roleType)) {
            throw new BusinessException("10004", "该用户类型不允许数据查询");
        }

        Page<OrderInfoMiniProgramVO> page = new Page<>(pageNum, pageSize);
        List<OrderInfoMiniProgramVO> orderInfoMiniProgramVOList = orderInfoMapperExt.listMiniProgramOrderInfo(page,
                orderInfoMiniProgramParam);
        if (CollectionUtils.isNotEmpty(orderInfoMiniProgramVOList)) {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月d日");
            orderInfoMiniProgramVOList.stream().forEach(orderInfoMiniProgramVO -> {
                orderInfoMiniProgramVO.setPrice(Integer
                        .valueOf(IOTEncodeUtils.decryptSM4(orderInfoMiniProgramVO.getPriceStr(), iotSm4Key, iotSm4Iv)));
                String createTime = orderInfoMiniProgramVO.getCreateTime();
                createTime = DateUtils.toStringDate(createTime, DateUtils.DATETIME_FORMAT_NO_SYMBOL,
                        DateUtils.DEFAULT_DATETIME_FORMAT);
                orderInfoMiniProgramVO.setCreateTime(createTime);
                Integer status = orderInfoMiniProgramVO.getStatus();
                if (0 == status
                        || 9 == status
                        || 10 == status) {
                    // 已付款，订单创建，确认收货后7天到账
                    orderInfoMiniProgramVO.setStatusStr("待发货");
                    orderInfoMiniProgramVO.setDesc("待发货，客户确认收货7天后积分到账");
                } else if (1 == status) {
                    // 确认收货
                    Date confirmTime = orderInfoMiniProgramVO.getOrderStatusTime() != null
                            ? orderInfoMiniProgramVO.getOrderStatusTime()
                            : orderInfoMiniProgramVO.getUpdateTime();
                    Calendar doneCalendar = Calendar.getInstance();
                    doneCalendar.setTime(confirmTime);
                    // 确认收货+7天，积分到账
                    doneCalendar.add(Calendar.DAY_OF_YEAR, 7);
                    orderInfoMiniProgramVO.setStatusStr("已收货");
                    orderInfoMiniProgramVO.setDesc("已收货，预计" + sdf.format(doneCalendar.getTime()) + "积分到账");

                } else if (3 == status || 11 == status) {
                    // 交易完成，积分到账
                    Date date = orderInfoMiniProgramVO.getOrderStatusTime() != null
                            ? orderInfoMiniProgramVO.getOrderStatusTime()
                            : orderInfoMiniProgramVO.getUpdateTime();
                    orderInfoMiniProgramVO.setStatusStr("交易成功");
                    orderInfoMiniProgramVO.setDesc("交易成功，" + sdf.format(date) + "积分已到账");

                } else if (4 == status
                        || 5 == status
                        || 6 == status) {
                    // 已退款,对于退款这类交易失败的订单，不计算积分
                    orderInfoMiniProgramVO.setStatusStr("交易关闭");
                    orderInfoMiniProgramVO.setDesc("客户已退款，无法获得积分");
                }

                orderInfoMiniProgramVO.setAfterMarketInfoList(orderInfoMapperExt.getMiniProgramAfterMarketOrderDetail(orderInfoMiniProgramVO.getOrderId()));

            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(orderInfoMiniProgramVOList);

        return pageData;
    }

    @Override
    @DS("query")
    public MiniProgramOrderDetailVO getMiniProgramOrderDetail(String orderId, LoginIfo4Redis loginIfo4Redis) {
        String userId = loginIfo4Redis.getUserId();
        UserMiniProgram userMiniProgram = miniProgramUserService.getUserMiniProgramById(userId);
        if (!Optional.ofNullable(userMiniProgram).isPresent()
                || !"1".equals(userMiniProgram.getStatus())) {
            throw new BusinessException("10004", "用户信息错误");
        }
        String roleType = userMiniProgram.getRoleType();
        OrderInfoMiniProgramParam orderInfoMiniProgramParam = new OrderInfoMiniProgramParam();
        orderInfoMiniProgramParam.setRoleType(roleType);
        orderInfoMiniProgramParam.setUserId(userMiniProgram.getUserId());
        if (!"1".equals(roleType) && !"2".equals(roleType) && !"3".equals(roleType) && !"4".equals(roleType)) {
            throw new BusinessException("10004", "该用户类型不允许数据查询");
        }
        orderInfoMiniProgramParam.setOrderId(orderId);

        Page<OrderInfoMiniProgramVO> page = new Page<>(1, 10);
        List<OrderInfoMiniProgramVO> orderInfoMiniProgramVOList = orderInfoMapperExt.listMiniProgramOrderInfo(page,
                orderInfoMiniProgramParam);
        if (CollectionUtils.isEmpty(orderInfoMiniProgramVOList)) {
            throw new BusinessException("10004", "当前用户不能查阅该订单详情");
        }

        MiniProgramOrderDetailVO miniProgramOrderDetail = orderInfoMapperExt.getMiniProgramOrderDetail(orderId);
        List<MiniProgramOrderDetailVO.LogisticsMsg> logisticsMsgs = new ArrayList<>();
        miniProgramOrderDetail.setLogisticsMsgs(logisticsMsgs);
        if (!Optional.ofNullable(miniProgramOrderDetail).isPresent()) {
            throw new BusinessException("10004", "订单信息错误");
        }
        miniProgramOrderDetail.setContactPhone(
                IOTEncodeUtils.decryptSM4(miniProgramOrderDetail.getContactPhone(), iotSm4Key, iotSm4Iv));
        miniProgramOrderDetail.setContactPersonName(
                IOTEncodeUtils.decryptSM4(miniProgramOrderDetail.getContactPersonName(), iotSm4Key, iotSm4Iv));

        String totalAddr = "";
        String addr1 = miniProgramOrderDetail.getAddr1();
        String addr2 = miniProgramOrderDetail.getAddr2();
        String addr3 = miniProgramOrderDetail.getAddr3();
        String addr4 = miniProgramOrderDetail.getAddr4();
        String usaddr = miniProgramOrderDetail.getUsaddr();
        if (StringUtils.isNotEmpty(addr1)) {
            addr1 = IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv);
            miniProgramOrderDetail.setAddr1(addr1);
            totalAddr = totalAddr.concat(addr1);
        }
        if (StringUtils.isNotEmpty(addr2)) {
            addr2 = IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv);
            miniProgramOrderDetail.setAddr2(addr2);
            totalAddr = totalAddr.concat(addr2);
        }
        if (StringUtils.isNotEmpty(addr3)) {
            addr3 = IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv);
            miniProgramOrderDetail.setAddr3(addr3);
            totalAddr = totalAddr.concat(addr3);
        }
        if (StringUtils.isNotEmpty(addr4)) {
            addr4 = IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv);
            miniProgramOrderDetail.setAddr4(addr4);
            totalAddr = totalAddr.concat(addr4);
        }
        if (StringUtils.isNotEmpty(usaddr)) {
            usaddr = IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
            miniProgramOrderDetail.setUsaddr(usaddr);
            totalAddr = totalAddr.concat(usaddr);
        }
        miniProgramOrderDetail.setTotalAddr(totalAddr);
        miniProgramOrderDetail.setCreateTime(DateUtils.toStringDate(miniProgramOrderDetail.getCreateTime(),
                DateUtils.DATETIME_FORMAT_NO_SYMBOL, DateUtils.DEFAULT_DATETIME_FORMAT));
        String spuOfferingClass = miniProgramOrderDetail.getSpuOfferingClass();
        miniProgramOrderDetail.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(spuOfferingClass));

        miniProgramOrderDetail
                .setOrderStatusName(OrderStatusInnerEnum.getDescribe(miniProgramOrderDetail.getOrderStatus()));
        miniProgramOrderDetail
                .setInvoiceStatusName(InvoiceStatusEnum.getDescribe(miniProgramOrderDetail.getInvoiceStatus()));

        Order2cAtomHistoryExample order2cAtomHistoryExample = new Order2cAtomHistoryExample();
        Order2cAtomHistoryExample.Criteria criteria = order2cAtomHistoryExample.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andOperateTypeEqualTo(1);
        if (SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A14.getSpuOfferingClass().equals(spuOfferingClass)) {
            criteria.andInnerStatusEqualTo(OrderStatusInnerEnum.WAIT_RECEIVE.getStatus());
        } else if (SPUOfferingClassEnum.A04.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A08.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A09.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A10.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A12.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A15.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A16.getSpuOfferingClass().equals(spuOfferingClass)
                || SPUOfferingClassEnum.A17.getSpuOfferingClass().equals(spuOfferingClass)) {
            criteria.andInnerStatusEqualTo(OrderStatusInnerEnum.COMPLETE.getStatus());
        } else if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClass)) {
            criteria.andInnerStatusEqualTo(OrderStatusInnerEnum.ORDER_SUCCESS.getStatus());
        } else {
            throw new BusinessException("10004", "暂时未配置此商品类型订单的查询");
        }

        List<Order2cAtomHistory> order2cAtomHistories = order2cAtomHistoryMapper
                .selectByExample(order2cAtomHistoryExample);
        if (CollectionUtils.isNotEmpty(order2cAtomHistories)) {
            Order2cAtomHistory order2cAtomHistory = order2cAtomHistories.get(0);
            miniProgramOrderDetail.setSendGoodsTime(
                    DateUtils.dateToStr(order2cAtomHistory.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
        }

        List<LogisticsInfoDTO> supplierInfoList = supplierInfo.getList();
        // 有该状态说明有物流信息，需要手动查询
        List<LogisticsInfo> logisticsInfos = logisticsInfoMapper
                .selectByExample(new LogisticsInfoExample().createCriteria().andOrderIdEqualTo(orderId)
                        .andLogisticsTypeEqualTo(0).example());
        logisticsInfos.forEach(logisticsInfo -> {
            MiniProgramOrderDetailVO.LogisticsMsg logisticsMsg = new MiniProgramOrderDetailVO.LogisticsMsg();
            logisticsMsg.setLogisCode(logisticsInfo.getLogisCode());
            logisticsMsg.setDescription(logisticsInfo.getDescription());
            LogisticsInfoDTO logisticsInfoDTO = supplierInfoList.stream()
                    .filter(b -> b.getSupplierName().equals(logisticsInfo.getSupplierName())).findFirst()
                    .orElse(new LogisticsInfoDTO());
            logisticsMsg.setSupplierName(logisticsInfoDTO == null ? "" : logisticsInfoDTO.getSupplierName());
            logisticsMsg.setSupplierCnName(logisticsInfoDTO == null ? "" : logisticsInfoDTO.getName());
            logisticsMsgs.add(logisticsMsg);
        });

        return miniProgramOrderDetail;
    }

    /**
     * 将2024年的订单导入到小程序用户订单表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void migrateUserOrderSince2024() {
        List<OrderThisYearVO> orderInfos = order2cAtomInfoMapperExt.selectOrderSince2024();
        if (CollectionUtils.isEmpty(orderInfos)) {
            return;
        }
        List<MiniProgramUserOrderInfo> miniProgramUserOrderInfos = new ArrayList<>();
        for (OrderThisYearVO orderThisYearVO : orderInfos) {
            if (StringUtils.isEmpty(orderThisYearVO.getUserId())) {
                continue;
            }
            MiniProgramUserOrderInfo miniProgramUserOrderInfo = new MiniProgramUserOrderInfo();
            BeanUtils.copyProperties(orderThisYearVO, miniProgramUserOrderInfo);
            miniProgramUserOrderInfo.setUserId(orderThisYearVO.getUserId());
            miniProgramUserOrderInfo.setId(BaseServiceUtils.getId());
            miniProgramUserOrderInfo.setCreateTime(new Date());
            miniProgramUserOrderInfo.setUpdateTime(new Date());
            miniProgramUserOrderInfos.add(miniProgramUserOrderInfo);
        }
        miniProgramUserOrderInfoMapper.deleteByExample(new MiniProgramUserOrderInfoExample());

        if (CollectionUtils.isNotEmpty(miniProgramUserOrderInfos)) {
            final int reportsSize = miniProgramUserOrderInfos.size();
            for (int i = 0; i < reportsSize; i += 1000) {
                int end = Math.min(i + 1000, reportsSize);
                miniProgramUserOrderInfoMapper.batchInsert(miniProgramUserOrderInfos.subList(i, end));
            }
        }
    }

    @KafkaListener(topics = "mini_program_user_order")
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void syncMiniProgramUserOrder(ConsumerRecord<String, byte[]> record) {
        log.info("同步小程序用户订单开始");
        try {

            Order2CInfoAtomRequest request = new ObjectMapper().readValue(record.value(), Order2CInfoAtomRequest.class);
            OrderInfoDTO orderInfo = request.getOrderInfo();
            log.info("订单信息：{}", orderInfo);
            String userId = null;
            // 客户经理编码
            String createOperCode = orderInfo.getCreateOperCode();
            // 分销员信息
            List<DistributorInfoDTO> distributorInfoDTOS = orderInfo.getDistributorInfo();
            // 渠道商信息
            List<AgentInfoDTO> agentInfoDTOS = orderInfo.getAgentInfo();
            String msgPref = "";
            if (CollectionUtils.isNotEmpty(agentInfoDTOS)) { // 渠道商
                userId = agentInfoDTOS.get(0).getAgentUserID();
                msgPref = "渠道商";
            } else if (CollectionUtils.isNotEmpty(distributorInfoDTOS)) { // 分销员
                // 获取最低级分销员计算积分
                DistributorInfoDTO distributorInfoDTO = distributorInfoDTOS.stream().sorted((o1, o2) -> {
                    return Integer.parseInt(o2.getDistributorLevel()) - Integer.parseInt(o1.getDistributorLevel());
                }).findFirst().get();
                userId = distributorInfoDTO.getDistributorUserID();
                msgPref = "分销员";
            } else if (org.apache.commons.lang.StringUtils.isNotEmpty(createOperCode)) { // 客户经理
                userId = orderInfo.getCreateOperUserID();
                msgPref = "客户经理";
            } else {
                // 客户下单直接返回
                return;
            }

            if (StringUtils.isBlank(userId)) {
                log.info("orderId:" + orderInfo.getOrderId() + "," + msgPref + "没有userId,不处理小程序订单保存");
                return;
            }

            log.info("订单归属对象：{}，userId：{}", msgPref, userId);

            List<Order2cAtomInfo> atomInfos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(request.getOrder2cAtomInfos())) {
                log.info("kafak原子订单信息:{}", request.getOrder2cAtomInfos());
                for (Order2cKfkAtomInfo order2cKfkAtomInfo : request.getOrder2cAtomInfos()) {
                    Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                    BeanUtils.copyProperties(order2cKfkAtomInfo, order2cAtomInfo);
                    atomInfos.add(order2cAtomInfo);
                }

            } else {
                atomInfos = order2cAtomInfoMapper.selectByExample(
                        new Order2cAtomInfoExample().createCriteria()
                                .andOrderIdEqualTo(orderInfo.getOrderId())
                                .example());
            }

            log.info("同步小程序用户订单原子订单信息：{}", atomInfos);
            if (CollectionUtils.isNotEmpty(atomInfos)) {
                final String miniProgramUserId = userId;
                atomInfos.forEach(order2cAtomInfo -> {
                    MiniProgramUserOrderInfo miniProgramUserOrderInfo = new MiniProgramUserOrderInfo();
                    miniProgramUserOrderInfo
                            .withId(BaseServiceUtils.getId())
                            .withUserId(miniProgramUserId)
                            .withOrderId(order2cAtomInfo.getOrderId())
                            .withAtomOrderId(order2cAtomInfo.getId())
                            .withCreateTime(new Date())
                            .withUpdateTime(new Date())
                            .withAmount(order2cAtomInfo.getSkuPrice() * order2cAtomInfo.getAtomQuantity());

                    SupplierAssociateProductExample example = new SupplierAssociateProductExample().createCriteria()
                            .andProductIdEqualTo(order2cAtomInfo.getSkuOfferingCode()).andDeleteTimeIsNull().example();
                    List<SupplierAssociateProduct> supplierAssociateProducts = supplierAssociateProductMapper
                            .selectByExample(example);
                    String supplierId = CollectionUtils.isEmpty(supplierAssociateProducts) ? null
                            : supplierAssociateProducts.get(0).getSupplierId();
                    if (!org.apache.commons.lang.StringUtils.isEmpty(supplierId)) {
                        miniProgramUserOrderInfo.setSupplierId(supplierId);
                    }

                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                        miniProgramUserOrderInfo.setOrderCreateTime(sdf.parse(orderInfo.getCreateTime()));
                    } catch (ParseException e) {
                        log.error("订单日期格式错误", e);
                    }

                    miniProgramUserOrderInfoMapper.deleteByExample(
                            new MiniProgramUserOrderInfoExample().createCriteria()
                                    .andOrderIdEqualTo(order2cAtomInfo.getOrderId())
                                    .andAtomOrderIdEqualTo(order2cAtomInfo.getId())
                                    .example());
                    miniProgramUserOrderInfoMapper.insert(miniProgramUserOrderInfo);
                });
                log.info("同步小程序用户订单成功");
            }
        } catch (IOException e) {
            log.info("同步小程序用户订单数据解析失败【record】:{}", new String(record.value()), e);
        }

    }


    @Override
    @DS("doris")
    public HomeDashboardVO getHomeDashboard(LoginIfo4Redis loginIfo4Redis) {
        UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_USER + loginIfo4Redis.getUserId(),
                RedisLockConstant.LOCK_MINI_USER + loginIfo4Redis.getUserId(),
                1,
                TimeUnit.DAYS,
                () -> userMiniProgramMapper.selectByPrimaryKey(loginIfo4Redis.getUserId())
        );
        String yearStartStr = DateUtils.getDateStartYearTime();
        String monthStartStr = DateUtils.getDateStartMonthTime();
        String todayStartStr =  DateUtils.getDateStartDayTime();
        String endTimeStr = DateUtils.getDateEndDayTime();
        String provinceName = null;
        String cityName = null;
        String organizationCode = null;

        HomeDashboardVO homeDashboardVO = new HomeDashboardVO();

        if (StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.IOT.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.PROVINCE.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.CITY.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.REGION.getType()) ) {
            //业管查询
            if (StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.PROVINCE.getType())) {
                provinceName = userMiniProgram.getOrgProvinceName();
                homeDashboardVO.setOrganizationName(userMiniProgram.getOrgProvinceName());
            } else if (StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.CITY.getType())) {
                provinceName = userMiniProgram.getOrgProvinceName();
                cityName = userMiniProgram.getOrgCityName();
                homeDashboardVO.setOrganizationName(userMiniProgram.getOrgCityName());
            } else if (StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.REGION.getType())) {
                provinceName = userMiniProgram.getOrgProvinceName();
                cityName = userMiniProgram.getOrgCityName();
                organizationCode = userMiniProgram.getOrganizationId();
                homeDashboardVO.setOrganizationName(userMiniProgram.getRegionName());
            } else {
                homeDashboardVO.setOrganizationName("全国");
            }
            log.info("{}查询看板数据，yearStartStr:{},monthStartStr:{},todayStartStr:{},endTimeStr:{},provinceName:{},cityName:{},organizationCode:{}",
                    MiniRoleEnum.getName(userMiniProgram.getRoleType(),userMiniProgram.getProvinceName(),userMiniProgram.getCityName(),userMiniProgram.getOrgRegionName()),
                    yearStartStr,monthStartStr,todayStartStr,endTimeStr,provinceName,cityName,organizationCode);
            homeDashboardVO.setYearStatistics(orderInfoMapperExt.getOrderAreaCount(yearStartStr, endTimeStr, provinceName, cityName, organizationCode));
            homeDashboardVO.setMonthStatistics(orderInfoMapperExt.getOrderAreaCount(monthStartStr, endTimeStr, provinceName, cityName, organizationCode));
            homeDashboardVO.setDayStatistics(orderInfoMapperExt.getOrderAreaCount(todayStartStr, endTimeStr, provinceName, cityName, organizationCode));
        } else if (StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.DISTRIBUTOR_FIRST.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.DISTRIBUTOR_SECOND.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.MANAGER.getType())
                || StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.CHANNEL.getType())) {
            //销售人员查询
            log.info("{}查询看板数据，yearStartStr:{},monthStartStr:{},todayStartStr:{},endTimeStr:{},provinceName:{},cityName:{},organizationCode:{}",
                    MiniRoleEnum.getName(userMiniProgram.getRoleType()),
                    yearStartStr,monthStartStr,todayStartStr,endTimeStr,provinceName,cityName,organizationCode);
            homeDashboardVO.setYearStatistics(orderInfoMapperExt.getOrderUserCount(yearStartStr, endTimeStr, userMiniProgram.getRoleType(), userMiniProgram.getUserId()));
            homeDashboardVO.setMonthStatistics(orderInfoMapperExt.getOrderUserCount(monthStartStr, endTimeStr, userMiniProgram.getRoleType(), userMiniProgram.getUserId()));
            homeDashboardVO.setDayStatistics(orderInfoMapperExt.getOrderUserCount(todayStartStr, endTimeStr, userMiniProgram.getRoleType(), userMiniProgram.getUserId()));
        } else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户角色错误");
        }




        return homeDashboardVO;
    }
}
