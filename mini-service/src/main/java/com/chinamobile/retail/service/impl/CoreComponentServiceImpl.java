package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.TransactionUtil;
import com.chinamobile.retail.constant.InfoAuditStatusEnum;
import com.chinamobile.retail.constant.InfoStatusEnum;
import com.chinamobile.retail.dao.MiniProgramSpuDataConfigMapper;
import com.chinamobile.retail.dao.SkuCoreComponentMapper;
import com.chinamobile.retail.dao.SpuCoreComponentMapper;
import com.chinamobile.retail.dao.SpuSkuAttachmentMapper;
import com.chinamobile.retail.dao.ext.SpuCoreComponentMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SkuReleaseTargetVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO;
import com.chinamobile.retail.service.ICoreComponentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Slf4j
@Service
public class CoreComponentServiceImpl implements ICoreComponentService {

    @Resource
    private LogService logService;

    @Resource
    private SpuCoreComponentMapperExt spuCoreComponentMapperExt;

    @Resource
    private SpuCoreComponentMapper spuCoreComponentMapper;

    @Resource
    private SpuSkuAttachmentMapper spuSkuAttachmentMapper;

    @Resource
    private SkuCoreComponentMapper skuCoreComponentMapper;

    @Resource
    private MiniProgramSpuDataConfigMapper miniProgramSpuDataConfigMapper;

    @Resource
    private RedisTemplate redisTemplate;


    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    @Override
    @DS("query")
    public SpuCoreComponentVO getCoreComponentDetail(String spuCode, LoginIfo4Redis loginIfo4Redis) {
        SpuCoreComponentVO vo = spuCoreComponentMapperExt.getCoreComponentDetail(spuCode);
        if (ObjectUtils.isEmpty(vo) || (ObjectUtils.isNotEmpty(vo.getIsDelete()) && vo.getIsDelete())) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }
        
        vo.setSpuDataConfigList(getSpuDataConfigList(spuCode));
        vo.setSkuList(spuCoreComponentMapperExt.getSkuCoreComponentList(vo.getSpuCode()));
        if (CollectionUtils.isNotEmpty(vo.getSkuList())) {
            SpuSkuAttachmentExample spuSkuAttachmentExample = new SpuSkuAttachmentExample();
            spuSkuAttachmentExample.createCriteria().andSpuCodeEqualTo(vo.getSpuCode()).andTypeIn(Arrays.asList(7, 8));
            List<SpuSkuAttachment> spuSkuAttachments = spuSkuAttachmentMapper.selectByExample(spuSkuAttachmentExample);
            Map<String, List<SpuSkuAttachment>> map = spuSkuAttachments.stream().collect(Collectors.groupingBy(SpuSkuAttachment::getSkuCode));
            List<SkuReleaseTargetVO> skuReleaseTargetVOS = spuCoreComponentMapperExt.getSkuReleaseTargetList(vo.getSkuList().stream()
                    .map(SkuCoreComponentVO::getSkuCode).collect(Collectors.toList()));
            Map<String, List<SkuReleaseTargetVO>> skuReleaseTargetVOMap = skuReleaseTargetVOS.stream()
                    .collect(Collectors.groupingBy(SkuReleaseTargetVO::getSkuCode));
            vo.getSkuList().forEach(skuCoreComponentVO -> {
                List<SpuSkuAttachment> list = map.get(skuCoreComponentVO.getSkuCode());
                if (CollectionUtils.isNotEmpty(list)) {
                    skuCoreComponentVO.setCoreBannerList(list.stream().filter(skuSkuAttachment -> skuSkuAttachment.getType() == 7)
                            .map(SpuSkuAttachment::getFileUrl).collect(Collectors.toList()));
                    skuCoreComponentVO.setCoreVideoList(list.stream().filter(skuSkuAttachment -> skuSkuAttachment.getType() == 8)
                            .map(SpuSkuAttachment::getFileUrl).collect(Collectors.toList()));
                }

                List<SkuReleaseTargetVO> targetVOS = skuReleaseTargetVOMap.get(skuCoreComponentVO.getSkuCode());
                if (CollectionUtils.isNotEmpty(targetVOS)) {
                    skuCoreComponentVO.setReleaseProvinceName(targetVOS.stream().map(SkuReleaseTargetVO::getProvinceName).collect(Collectors.toList()));
                    skuCoreComponentVO.setReleaseCityName(targetVOS.stream().map(SkuReleaseTargetVO::getCityName)
                            .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                }
            });
        }
        String content = "【查看配置详情】\n" + "SPU编码" + vo.getSpuCode();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, content);
        return vo;
    }

    @Override
    @DS("query")
    public PageData<SpuCoreComponentVO> pageCoreComponentList(PageCoreComponentListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<SpuCoreComponentVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = spuCoreComponentMapperExt.countCoreComponentList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<SpuCoreComponentVO> list = spuCoreComponentMapperExt.pageCoreComponentList(param);
            pageData.setData(list);
        }
        return pageData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void create(SpuCoreComponentParam param, String userId) {
        List<SpuCoreComponent> spuCoreComponents = spuCoreComponentMapper.selectByExample(new SpuCoreComponentExample().createCriteria()
                .andSpuCodeEqualTo(param.getSpuCode()).andStatusNotEqualTo(InfoStatusEnum.OFFLINE.getStatus()).andIsDeleteEqualTo(false).example());

        if (CollectionUtils.isNotEmpty(spuCoreComponents)) {
            throw new BusinessException(PARAM_ERROR, "SPU已配置核心部件");
        }
        Integer status = param.getOprType() == 1 ? InfoStatusEnum.DRAFT.getStatus() : InfoStatusEnum.AUDITING.getStatus();
        Integer auditStatus = param.getOprType() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.AUDITING.getStatus();
        Date now = new Date();
        SpuCoreComponent spuCoreComponent = new SpuCoreComponent();
        BeanUtils.copyProperties(param, spuCoreComponent);
        spuCoreComponent.setId(BaseServiceUtils.getId());
        spuCoreComponent.setCreateUid(userId);
        spuCoreComponent.setCreateTime(now);
        spuCoreComponent.setUpdateTime(now);
        spuCoreComponent.setIsDelete(false);
        spuCoreComponent.setStatus(status);
        spuCoreComponent.setAuditStatus(auditStatus);

        spuCoreComponentMapper.insertSelective(spuCoreComponent);
        List<SkuCoreComponent> skuCoreComponents = new ArrayList<>();
        List<SpuSkuAttachment> spuSkuAttachments = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSkuList())) {
            param.getSkuList().forEach(x -> {
                SkuCoreComponent skuCoreComponent = new SkuCoreComponent();
                BeanUtils.copyProperties(x, skuCoreComponent);
                skuCoreComponent.setId(BaseServiceUtils.getId());
                skuCoreComponent.setIsDelete(false);
                skuCoreComponents.add(skuCoreComponent);
                if (CollectionUtils.isNotEmpty(x.getCoreBannerList())) {
                    x.getCoreBannerList().forEach(y -> {
                        SpuSkuAttachment spuSkuAttachment = new SpuSkuAttachment();
                        spuSkuAttachment.setId(BaseServiceUtils.getId());
                        spuSkuAttachment.setSpuCode(param.getSpuCode());
                        spuSkuAttachment.setSkuCode(x.getSkuCode());
                        spuSkuAttachment.setType(7);
                        spuSkuAttachment.setFileUrl(y);
                        spuSkuAttachment.setCreateTime(now);
                        spuSkuAttachment.setUpdateTime(now);
                        spuSkuAttachments.add(spuSkuAttachment);
                    });
                }

                if (CollectionUtils.isNotEmpty(x.getCoreVideoList())) {
                    x.getCoreVideoList().forEach(y -> {
                        SpuSkuAttachment spuSkuAttachment = new SpuSkuAttachment();
                        spuSkuAttachment.setId(BaseServiceUtils.getId());
                        spuSkuAttachment.setSpuCode(param.getSpuCode());
                        spuSkuAttachment.setSkuCode(x.getSkuCode());
                        spuSkuAttachment.setType(8);
                        spuSkuAttachment.setFileUrl(y);
                        spuSkuAttachment.setCreateTime(now);
                        spuSkuAttachment.setUpdateTime(now);
                        spuSkuAttachments.add(spuSkuAttachment);
                    });
                }
            });

            if (CollectionUtils.isNotEmpty(skuCoreComponents)) {
                skuCoreComponentMapper.batchInsert(skuCoreComponents);
            }

            if (CollectionUtils.isNotEmpty(spuSkuAttachments)) {
                spuSkuAttachmentMapper.batchInsert(spuSkuAttachments);
            }
        }

        if(CollectionUtils.isNotEmpty(param.getSpuDataConfigList())){
            for(DataConfigParam dataConfigParam : param.getSpuDataConfigList()){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig = new MiniProgramSpuDataConfig();
                String moduleId = BaseServiceUtils.getId();
                miniProgramSpuDataConfig.withId(moduleId)
                        .withSpuCode(param.getSpuCode())
                        .withType(dataConfigParam.getType())
                        .withName(dataConfigParam.getName())
                        .withCreateTime(now)
                        .withUpdateTime(now)
                        .withAuditStatus(auditStatus)
                        .withStatus(status);
                for(DataConfigParam dataConfigParamFile : dataConfigParam.getFileList()){
                    MiniProgramSpuDataConfig miniProgramSpuDataConfigFile = new MiniProgramSpuDataConfig();
                    String fileId = BaseServiceUtils.getId();
                    miniProgramSpuDataConfigFile.withId(fileId)
                            .withParentId(moduleId)
                            .withSpuCode(param.getSpuCode())
                            .withType(dataConfigParamFile.getType())
                            .withName(dataConfigParamFile.getName())
                            .withFileKey(dataConfigParamFile.getFileKey())
                            .withFileUrl(dataConfigParamFile.getFileUrl())
                            .withFileSize(dataConfigParamFile.getFileSize())
                            .withCreateTime(now)
                            .withUpdateTime(now)
                            .withAuditStatus(auditStatus)
                            .withStatus(status);
                    miniProgramSpuDataConfigMapper.insertSelective(miniProgramSpuDataConfigFile);
                }
                miniProgramSpuDataConfigMapper.insertSelective(miniProgramSpuDataConfig);
            }

        }

        TransactionUtil.afterCommit(() -> invalidProduct2Redis(spuCoreComponent.getSpuCode()));

        String log = getEditContent(param, new SpuCoreComponent(), spuCoreComponent, new ArrayList<>(), new ArrayList<>(), skuCoreComponents, spuSkuAttachments);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, log);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void edit(SpuCoreComponentParam param, String userId) {
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "产品核心部件配置ID不能为空");
        }
        Date now = new Date();
        SpuCoreComponent spuCoreComponent = spuCoreComponentMapper.selectByPrimaryKey(param.getId());
        if (ObjectUtils.isEmpty(spuCoreComponent) || spuCoreComponent.getIsDelete()) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_CORE_NOT_EXIST);
        }

        if (!InfoStatusEnum.DRAFT.getStatus().equals(spuCoreComponent.getStatus()) &&
                !InfoStatusEnum.REJECTED.getStatus().equals(spuCoreComponent.getStatus())
                && !InfoStatusEnum.OFFLINE.getStatus().equals(spuCoreComponent.getStatus())) {
            // 只有已上传,已驳回,已下线状态的素材才可以编辑
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_CANNOT_EDIT);
        }

        Integer status = param.getOprType() == 1 ? InfoStatusEnum.DRAFT.getStatus() : InfoStatusEnum.AUDITING.getStatus();
        Integer auditStatus = param.getOprType() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.AUDITING.getStatus();
        SpuCoreComponent old = new SpuCoreComponent();
        BeanUtils.copyProperties(spuCoreComponent, old);
        BeanUtils.copyProperties(param, spuCoreComponent);
        spuCoreComponent.setUpdateTime(now);
        // 充值素材状态和审核状态
        spuCoreComponent.setStatus(status);
        spuCoreComponent.setAuditStatus(auditStatus);
        spuCoreComponentMapper.updateByPrimaryKey(spuCoreComponent);

        List<SkuCoreComponent> oldSkuConfig = skuCoreComponentMapper.selectByExample(new SkuCoreComponentExample().createCriteria()
                .andSpuCodeEqualTo(spuCoreComponent.getSpuCode()).example());
        List<SpuSkuAttachment> oldAttachments = spuSkuAttachmentMapper.selectByExample(new SpuSkuAttachmentExample().createCriteria()
                .andSpuCodeEqualTo(spuCoreComponent.getSpuCode()).andTypeIn(Arrays.asList(7, 8)).example());


        skuCoreComponentMapper.deleteByExample(new SkuCoreComponentExample().createCriteria()
                .andSpuCodeEqualTo(spuCoreComponent.getSpuCode()).example());
        spuSkuAttachmentMapper.deleteByExample(new SpuSkuAttachmentExample().createCriteria()
                .andSpuCodeEqualTo(spuCoreComponent.getSpuCode()).andTypeIn(Arrays.asList(7, 8)).example());
        miniProgramSpuDataConfigMapper.deleteByExample(new MiniProgramSpuDataConfigExample().createCriteria()
                .andSpuCodeEqualTo(spuCoreComponent.getSpuCode()).example());
        List<SkuCoreComponent> skuCoreComponents = new ArrayList<>();
        List<SpuSkuAttachment> spuSkuAttachments = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSkuList())) {
            param.getSkuList().forEach(x -> {
                SkuCoreComponent skuCoreComponent = new SkuCoreComponent();
                BeanUtils.copyProperties(x, skuCoreComponent);
                skuCoreComponent.setId(BaseServiceUtils.getId());
                skuCoreComponent.setIsDelete(false);
                skuCoreComponents.add(skuCoreComponent);
                if (CollectionUtils.isNotEmpty(x.getCoreBannerList())) {
                    x.getCoreBannerList().forEach(y -> {
                        SpuSkuAttachment spuSkuAttachment = new SpuSkuAttachment();
                        spuSkuAttachment.setId(BaseServiceUtils.getId());
                        spuSkuAttachment.setSpuCode(param.getSpuCode());
                        spuSkuAttachment.setSkuCode(x.getSkuCode());
                        spuSkuAttachment.setType(7);
                        spuSkuAttachment.setFileUrl(y);
                        spuSkuAttachment.setCreateTime(now);
                        spuSkuAttachment.setUpdateTime(now);
                        spuSkuAttachments.add(spuSkuAttachment);
                    });
                }

                if (CollectionUtils.isNotEmpty(x.getCoreVideoList())) {
                    x.getCoreVideoList().forEach(y -> {
                        SpuSkuAttachment spuSkuAttachment = new SpuSkuAttachment();
                        spuSkuAttachment.setId(BaseServiceUtils.getId());
                        spuSkuAttachment.setSpuCode(param.getSpuCode());
                        spuSkuAttachment.setSkuCode(x.getSkuCode());
                        spuSkuAttachment.setType(8);
                        spuSkuAttachment.setFileUrl(y);
                        spuSkuAttachment.setCreateTime(now);
                        spuSkuAttachment.setUpdateTime(now);
                        spuSkuAttachments.add(spuSkuAttachment);
                    });
                }
            });

            if (CollectionUtils.isNotEmpty(skuCoreComponents)) {
                skuCoreComponentMapper.batchInsert(skuCoreComponents);
            }

            if (CollectionUtils.isNotEmpty(spuSkuAttachments)) {
                spuSkuAttachmentMapper.batchInsert(spuSkuAttachments);
            }
        }

        // 全部重建
        if(CollectionUtils.isNotEmpty(param.getSpuDataConfigList())){
            for(DataConfigParam dataConfigParam : param.getSpuDataConfigList()){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig = new MiniProgramSpuDataConfig();
                String moduleId = BaseServiceUtils.getId();
                miniProgramSpuDataConfig.withId(moduleId)
                        .withSpuCode(param.getSpuCode())
                        .withType(dataConfigParam.getType())
                        .withName(dataConfigParam.getName())
                        .withCreateTime(now)
                        .withUpdateTime(now)
                        .withAuditStatus(auditStatus)
                        .withStatus(status);
                for(DataConfigParam dataConfigParamFile : dataConfigParam.getFileList()){
                    MiniProgramSpuDataConfig miniProgramSpuDataConfigFile = new MiniProgramSpuDataConfig();
                    String fileId = BaseServiceUtils.getId();
                    miniProgramSpuDataConfigFile.withId(fileId)
                            .withParentId(moduleId)
                            .withSpuCode(param.getSpuCode())
                            .withType(dataConfigParamFile.getType())
                            .withName(dataConfigParamFile.getName())
                            .withFileKey(dataConfigParamFile.getFileKey())
                            .withFileUrl(dataConfigParamFile.getFileUrl())
                            .withFileSize(dataConfigParamFile.getFileSize())
                            .withCreateTime(now)
                            .withUpdateTime(now)
                            .withAuditStatus(auditStatus)
                            .withStatus(status);
                    miniProgramSpuDataConfigMapper.insertSelective(miniProgramSpuDataConfigFile);
                }
                miniProgramSpuDataConfigMapper.insertSelective(miniProgramSpuDataConfig);
            }

        }
        TransactionUtil.afterCommit(() -> invalidProduct2Redis(spuCoreComponent.getSpuCode()));
        String log = getEditContent(param, old, spuCoreComponent, oldSkuConfig, oldAttachments, skuCoreComponents, spuSkuAttachments);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, log);
        }
    }

    private String getEditContent(SpuCoreComponentParam param, SpuCoreComponent old, SpuCoreComponent spuCoreComponent, List<SkuCoreComponent> oldSkuConfig,
                                  List<SpuSkuAttachment> oldAttachments, List<SkuCoreComponent> skuCoreComponents,
                                  List<SpuSkuAttachment> spuSkuAttachments) {
        boolean writeLog = false;
        StringBuilder content = new StringBuilder();

        //spu核心部件名称
        if (!StringUtils.equals(old.getCoreComponentName(), spuCoreComponent.getCoreComponentName())) {
            writeLog = true;
            content.append("\n").append("核心部件名称").append("由").append(StringUtils.defaultIfBlank(old.getCoreComponentName(),"空"))
                    .append("修改为").append(StringUtils.defaultIfBlank(spuCoreComponent.getCoreComponentName(),"空"));
        }

        //spu核心部件头图
        if (!StringUtils.equals(old.getCoreComponentImg(), spuCoreComponent.getCoreComponentImg())) {
            writeLog = true;
            content.append("\n").append("核心部件头图").append("由").append(StringUtils.defaultIfBlank(old.getCoreComponentImg(),"空"))
                    .append("修改为").append(StringUtils.defaultIfBlank(spuCoreComponent.getCoreComponentImg(),"空"));
        }
        //sku核心部件
        for (SkuCoreComponentVO skuCoreComponent : spuCoreComponentMapperExt.getSkuCoreComponentList(spuCoreComponent.getSpuCode())) {
            SkuCoreComponent oldSku = oldSkuConfig.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())).findFirst().orElse(null);
            SkuCoreComponent newSku = skuCoreComponents.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(oldSku) && ObjectUtils.isNotEmpty(newSku)) {
                if (!StringUtils.equals(oldSku.getCoreComponentName(), newSku.getCoreComponentName())) {
                    writeLog = true;
                    content.append("\n").append("sku").append(skuCoreComponent.getSkuName()).append("核心部件名称")
                            .append("由").append(StringUtils.defaultIfBlank(oldSku.getCoreComponentName(),"空"))
                            .append("修改为").append(StringUtils.defaultIfBlank(newSku.getCoreComponentName(),"空"));
                }
            } else if (ObjectUtils.isEmpty(oldSku) && ObjectUtils.isNotEmpty(newSku)) {
                //增加
                if (StringUtils.isNotBlank(newSku.getCoreComponentName())) {
                    writeLog = true;
                    content.append("\n").append("sku").append(skuCoreComponent.getSkuName()).append("核心部件名称")
                            .append("由空").append("修改为").append(newSku.getCoreComponentName());
                }
            } else if (ObjectUtils.isNotEmpty(oldSku) && ObjectUtils.isEmpty(newSku)) {
                //删除
                if (StringUtils.isNotBlank(oldSku.getCoreComponentName())) {
                    writeLog = true;
                    content.append("\n").append("sku").append(skuCoreComponent.getSkuName()).append("核心部件名称")
                            .append("由").append(oldSku.getCoreComponentName()).append("修改为空");
                }
            }

            List<SpuSkuAttachment> oldSkuBanners = oldAttachments.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())
                    && x.getType() == 7).collect(Collectors.toList());
            List<SpuSkuAttachment> newSkuBanners = spuSkuAttachments.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())
                    && x.getType() == 7).collect(Collectors.toList());
            oldSkuBanners.sort(Comparator.comparing(SpuSkuAttachment::getFileUrl));
            newSkuBanners.sort(Comparator.comparing(SpuSkuAttachment::getFileUrl));
            String oldBanners = oldSkuBanners.stream().map(SpuSkuAttachment::getFileUrl).collect(Collectors.joining(","));
            String newBanners = newSkuBanners.stream().map(SpuSkuAttachment::getFileUrl).collect(Collectors.joining(","));
            if (!StringUtils.equals(oldBanners, newBanners)) {
                writeLog = true;
                content.append("\n").append("sku").append(skuCoreComponent.getSkuName()).append("核心部件轮播图")
                        .append("由").append(StringUtils.defaultIfBlank(oldBanners,"空"))
                        .append("修改为").append(StringUtils.defaultIfBlank(newBanners,"空"));
            }

            List<SpuSkuAttachment> oldSkuVideo = oldAttachments.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())
                    && x.getType() == 8).collect(Collectors.toList());
            List<SpuSkuAttachment> newSkuVideo = spuSkuAttachments.stream().filter(x -> x.getSkuCode().equals(skuCoreComponent.getSkuCode())
                    && x.getType() == 8).collect(Collectors.toList());
            oldSkuVideo.sort(Comparator.comparing(SpuSkuAttachment::getFileUrl));
            newSkuVideo.sort(Comparator.comparing(SpuSkuAttachment::getFileUrl));
            String oldVideo = oldSkuVideo.stream().map(SpuSkuAttachment::getFileUrl).collect(Collectors.joining(","));
            String newVideo = newSkuVideo.stream().map(SpuSkuAttachment::getFileUrl).collect(Collectors.joining(","));
            if (!StringUtils.equals(oldVideo, newVideo)) {
                writeLog = true;
                content.append("\n").append("sku").append(skuCoreComponent.getSkuName()).append("核心部件视频")
                        .append("由").append(StringUtils.defaultIfBlank(oldVideo,"空"))
                        .append("修改为").append(StringUtils.defaultIfBlank(newVideo,"空"));
            }
        }


        if (writeLog) {
            content.insert(0, new StringBuilder().append("\n").append("SPU编码").append(param.getSpuCode()));
            content.insert(0, "【编辑】");
        }
        return content.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis) {
        List<SpuCoreComponent> spuCoreComponents = spuCoreComponentMapper.selectByExample(new SpuCoreComponentExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(false).example());
        if (spuCoreComponents.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所审核的核心部件"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    spuCoreComponents.stream().map(SpuCoreComponent::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<SpuCoreComponent> notInProcess = spuCoreComponents.stream().filter(x -> !x.getAuditStatus().equals(InfoAuditStatusEnum.AUDITING.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInProcess)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所审核的核心部件"
                    + JSON.toJSONString(notInProcess.stream().map(SpuCoreComponent::getId).collect(Collectors.toList()))
                    + "不可以被审核");
        }

        Integer auditStatus = param.getApprove() ? InfoAuditStatusEnum.PASSED.getStatus() : InfoAuditStatusEnum.DENIED.getStatus();
        Integer status = param.getApprove() ? InfoStatusEnum.PUBLISHED.getStatus() : InfoStatusEnum.REJECTED.getStatus();
        Date now = new Date();
        spuCoreComponents.forEach(x -> {
            x.setUpdateTime(now);
            x.setAuditStatus(auditStatus);
            x.setStatus(status);

            spuCoreComponentMapper.updateByPrimaryKeySelective(x);

            if(StringUtils.isNotEmpty(x.getSpuCode())){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig =  new MiniProgramSpuDataConfig();
                miniProgramSpuDataConfig.setSpuCode(x.getSpuCode());
                miniProgramSpuDataConfig.setAuditStatus(auditStatus);
                miniProgramSpuDataConfig.setUpdateTime(now);
                miniProgramSpuDataConfig.setStatus(status);
                miniProgramSpuDataConfigMapper.updateByExampleSelective(miniProgramSpuDataConfig,
                        new MiniProgramSpuDataConfigExample().createCriteria()
                                .andSpuCodeEqualTo(x.getSpuCode())
                                .example());
            }
        });

        String header = null;
        if (param.getApprove()) {
            header = param.getIds().size() > 1 ? "【批量同意】" : "【同意】";
        } else {
            header = param.getIds().size() > 1 ? "【批量驳回】" : "【驳回】";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(header);
        spuCoreComponents.forEach(x -> sb.append("\n").append("SPU编码").append(x.getSpuCode()));
        TransactionUtil.afterCommit(() -> invalidProduct2Redis(spuCoreComponents.stream().map(SpuCoreComponent::getSpuCode).collect(Collectors.toList())));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, sb.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        List<SpuCoreComponent> spuCoreComponents = spuCoreComponentMapper.selectByExample(new SpuCoreComponentExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(false).example());
        if (spuCoreComponents.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所下线的核心部件"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    spuCoreComponents.stream().map(SpuCoreComponent::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<SpuCoreComponent> notPublished = spuCoreComponents.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.PUBLISHED.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notPublished)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所下线的核心部件"
                    + JSON.toJSONString(notPublished.stream().map(SpuCoreComponent::getId).collect(Collectors.toList()))
                    + "不可以被下线");
        }

        Integer status = InfoStatusEnum.OFFLINE.getStatus();
        Date now = new Date();
        spuCoreComponents.forEach(x -> {
            x.setUpdateTime(now);
            x.setStatus(status);
            spuCoreComponentMapper.updateByPrimaryKeySelective(x);

            if(StringUtils.isNotEmpty(x.getSpuCode())){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig =  new MiniProgramSpuDataConfig();
                miniProgramSpuDataConfig.setSpuCode(x.getSpuCode());
                miniProgramSpuDataConfig.setUpdateTime(now);
                miniProgramSpuDataConfig.setStatus(status);
                miniProgramSpuDataConfigMapper.updateByExampleSelective(miniProgramSpuDataConfig,
                        new MiniProgramSpuDataConfigExample().createCriteria()
                                .andSpuCodeEqualTo(x.getSpuCode())
                                .example());
            }
        });
        TransactionUtil.afterCommit(() -> {
            invalidProduct2Redis(spuCoreComponents.stream().map(SpuCoreComponent::getSpuCode).collect(Collectors.toList()));
        });

        StringBuilder sb = new StringBuilder("【下线】");
        spuCoreComponents.forEach(x -> sb.append("\n").append("SPU编码").append(x.getSpuCode()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, sb.toString());
    }

    @Transactional
    @Override
    @DS("save")
    public void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis) {
        List<SpuCoreComponent> spuCoreComponents = spuCoreComponentMapper.selectByExample(new SpuCoreComponentExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(false).example());
        if (spuCoreComponents.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所发布的核心部件"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    spuCoreComponents.stream().map(SpuCoreComponent::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<SpuCoreComponent> notDraft = spuCoreComponents.stream().filter(x -> InfoAuditStatusEnum.AUDITING.getStatus().equals(x.getAuditStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notDraft)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所发布的核心部件"
                    + JSON.toJSONString(notDraft.stream().map(SpuCoreComponent::getId).collect(Collectors.toList()))
                    + "不可以被发布");
        }
        Date now = new Date();
        Integer status = InfoStatusEnum.AUDITING.getStatus();
        Integer auditStatus = InfoAuditStatusEnum.AUDITING.getStatus();
        spuCoreComponents.forEach(x -> {
            x.setUpdateTime(now);
            x.setStatus(status);
            x.setAuditStatus(auditStatus);
            spuCoreComponentMapper.updateByPrimaryKeySelective(x);

            if(StringUtils.isNotEmpty(x.getSpuCode())){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig =  new MiniProgramSpuDataConfig();
                miniProgramSpuDataConfig.setSpuCode(x.getSpuCode());
                miniProgramSpuDataConfig.setAuditStatus(auditStatus);
                miniProgramSpuDataConfig.setUpdateTime(now);
                miniProgramSpuDataConfig.setStatus(status);

                miniProgramSpuDataConfigMapper.updateByExampleSelective(miniProgramSpuDataConfig,
                        new MiniProgramSpuDataConfigExample().createCriteria()
                                .andSpuCodeEqualTo(x.getSpuCode())
                                .example());
            }
        });
        TransactionUtil.afterCommit(() -> invalidProduct2Redis(spuCoreComponents.stream().map(SpuCoreComponent::getSpuCode).collect(Collectors.toList())));
        StringBuilder sb = new StringBuilder("【发布】");
        spuCoreComponents.forEach(x -> sb.append("\n").append("SPU编码").append(x.getSpuCode()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, sb.toString());
    }

    /**
     * 添加活动进缓存
     */
    private void invalidProduct2Redis(List<String> spuCodes) {
        //异步加载首页进缓存
        executorService.execute(() -> {
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_LIST);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_COUNT);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_SCENE_RELATED_SPU);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_HOME_ID);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_INFO_ID);
            spuCodes.forEach(spuCode -> {
                deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_DETAIL + spuCode);
                deleteAllRedisCache(Constant.REDIS_KEY_SPU_SKU_ATTACHMENT + spuCode);

            });
        });
    }

    public <K> void deleteAllRedisCache(K key) {
        Set<String> keys = redisTemplate.keys(key + "*");
        List<String> batchDeleteKeys = new ArrayList<>(keys);
        if (!batchDeleteKeys.isEmpty()) {
            redisTemplate.delete(batchDeleteKeys);
        }
    }

    private void invalidProduct2Redis(String spuCode) {
        //异步加载首页进缓存
        List<String> spuCodes = new ArrayList<>();
        spuCodes.add(spuCode);
        invalidProduct2Redis(spuCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的核心部件ID不能为空");
        }
        List<SpuCoreComponent> spuCoreComponents = spuCoreComponentMapper.selectByExample(new SpuCoreComponentExample().createCriteria()
                .andIdIn(param.getIds()).example());

        List<SpuCoreComponent> cannotDelete = spuCoreComponents.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.DRAFT.getStatus())
                && !x.getStatus().equals(InfoStatusEnum.OFFLINE.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cannotDelete)) {
            throw new BusinessException(PARAM_ERROR, "所删除的核心部件"
                    + JSON.toJSONString(cannotDelete.stream().map(SpuCoreComponent::getCoreComponentName).collect(Collectors.toList()))
                    + "不可以被删除");
        }
        Date now = new Date();
        spuCoreComponents.forEach(x -> {
            x.setUpdateTime(now);
            x.setIsDelete(true);

            spuCoreComponentMapper.updateByPrimaryKeySelective(x);

            if(StringUtils.isNotEmpty(x.getSpuCode())){
                MiniProgramSpuDataConfig miniProgramSpuDataConfig =  new MiniProgramSpuDataConfig();
                miniProgramSpuDataConfig.setSpuCode(x.getSpuCode());
                miniProgramSpuDataConfig.setIsDelete(true);
                miniProgramSpuDataConfig.setUpdateTime(now);
                miniProgramSpuDataConfigMapper.updateByExampleSelective(miniProgramSpuDataConfig,
                        new MiniProgramSpuDataConfigExample().createCriteria()
                                .andSpuCodeEqualTo(x.getSpuCode())
                                .example());
            }
        });

        TransactionUtil.afterCommit(() -> invalidProduct2Redis(spuCoreComponents.stream().map(SpuCoreComponent::getSpuCode)
                .collect(Collectors.toList())));

        String header = param.getIds().size() > 1 ? "【批量删除】" : "【删除】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        spuCoreComponents.forEach(x -> sb.append("\n").append("SPU编码").append(x.getSpuCode()));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.CORE_COMPONENT.code, sb.toString());
    }

    @Override
    public List<SpuCoreComponentVO> searchSpu(String keyword, LoginIfo4Redis loginIfo4Redis) {
        return spuCoreComponentMapperExt.searchSpu(keyword);
    }

    @Override
    public List<SkuCoreComponentVO> searchSku(String keyword, LoginIfo4Redis loginIfo4Redis) {
        List<SkuCoreComponentVO> skuCoreComponentVOS = spuCoreComponentMapperExt.searchSku(keyword);
        if (CollectionUtils.isNotEmpty(skuCoreComponentVOS)) {
            List<SpuSkuAttachment> attachments = spuSkuAttachmentMapper.selectByExample(new SpuSkuAttachmentExample().createCriteria()
                    .andSkuCodeIn(skuCoreComponentVOS.stream().map(SkuCoreComponentVO::getSkuCode).collect(Collectors.toList()))
                    .andTypeIn(Arrays.asList(7, 8)).example());
            Map<String, List<SpuSkuAttachment>> attachmentMap = attachments.stream().collect(Collectors.groupingBy(SpuSkuAttachment::getSkuCode));
            skuCoreComponentVOS.forEach(x -> {
                List<SpuSkuAttachment> skuAttachments = attachmentMap.get(x.getSkuCode());
                if (CollectionUtils.isNotEmpty(skuAttachments)) {
                    List<String> banners = skuAttachments.stream().filter(y -> y.getType() == 7).map(SpuSkuAttachment::getFileUrl)
                            .collect(Collectors.toList());
                    x.setCoreBannerList(banners);
                    List<String> videos = skuAttachments.stream().filter(y -> y.getType() == 8).map(SpuSkuAttachment::getFileUrl)
                            .collect(Collectors.toList());
                    x.setCoreVideoList(videos);
                }
            });
        }
        return skuCoreComponentVOS;
    }

    public List<DataConfigParam> getSpuDataConfigList(String spuCode) {
        List<MiniProgramSpuDataConfig> miniProgramSpuDataConfigList = miniProgramSpuDataConfigMapper.selectByExample(new MiniProgramSpuDataConfigExample()
                .createCriteria()
                .andSpuCodeEqualTo(spuCode)
                .andIsDeleteEqualTo(false)
                .example());

        // 按parentId分组
        Map<String, List<MiniProgramSpuDataConfig>> configMap = miniProgramSpuDataConfigList.stream()
                .collect(Collectors.groupingBy(config -> StringUtils.defaultIfBlank(config.getParentId(), "root")));

        // 获取一级模块数据
        List<DataConfigParam> spuDataConfigList = configMap.getOrDefault("root", Collections.emptyList())
                .stream()
                .map(module -> {
                    DataConfigParam moduleParam = new DataConfigParam();
                    moduleParam.setType(module.getType());
                    moduleParam.setName(module.getName());

                    // 获取二级文件数据
                    List<DataConfigParam> fileParams = configMap.getOrDefault(module.getId(), Collections.emptyList())
                            .stream()
                            .map(file -> {
                                DataConfigParam fileParam = new DataConfigParam();
                                fileParam.setType(file.getType());
                                fileParam.setName(file.getName());
                                fileParam.setFileKey(file.getFileKey());
                                fileParam.setFileUrl(file.getFileUrl());
                                fileParam.setFileSize(file.getFileSize());
                                return fileParam;
                            })
                            .collect(Collectors.toList());

                    moduleParam.setFileList(fileParams);
                    return moduleParam;
                })
                .collect(Collectors.toList());

        return spuDataConfigList;
    }
}
