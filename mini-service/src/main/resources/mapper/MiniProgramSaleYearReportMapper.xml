<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramSaleYearReportMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="register_index" jdbcType="BIGINT" property="registerIndex" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="first_order_time" jdbcType="TIMESTAMP" property="firstOrderTime" />
    <result column="year_last_order_time" jdbcType="TIMESTAMP" property="yearLastOrderTime" />
    <result column="first_order_spu_code" jdbcType="VARCHAR" property="firstOrderSpuCode" />
    <result column="first_order_spu_name" jdbcType="VARCHAR" property="firstOrderSpuName" />
    <result column="order_total_price" jdbcType="BIGINT" property="orderTotalPrice" />
    <result column="order_total_count" jdbcType="BIGINT" property="orderTotalCount" />
    <result column="order_total_count_prepay" jdbcType="BIGINT" property="orderTotalCountPrepay" />
    <result column="order_total_count_afterpay" jdbcType="BIGINT" property="orderTotalCountAfterpay" />
    <result column="order_total_count_mix" jdbcType="BIGINT" property="orderTotalCountMix" />
    <result column="order_total_point" jdbcType="BIGINT" property="orderTotalPoint" />
    <result column="spu_total_count" jdbcType="BIGINT" property="spuTotalCount" />
    <result column="sale_ranking" jdbcType="BIGINT" property="saleRanking" />
    <result column="best_sale_spu_code" jdbcType="VARCHAR" property="bestSaleSpuCode" />
    <result column="best_sale_spu_name" jdbcType="VARCHAR" property="bestSaleSpuName" />
    <result column="best_sale_spu_order_count" jdbcType="BIGINT" property="bestSaleSpuOrderCount" />
    <result column="best_sale_spu_code_group" jdbcType="VARCHAR" property="bestSaleSpuCodeGroup" />
    <result column="best_sale_spu_name_group" jdbcType="VARCHAR" property="bestSaleSpuNameGroup" />
    <result column="best_sale_spu_count_group" jdbcType="BIGINT" property="bestSaleSpuCountGroup" />
    <result column="best_sale_spu_code_individual" jdbcType="VARCHAR" property="bestSaleSpuCodeIndividual" />
    <result column="best_sale_spu_name_individual" jdbcType="VARCHAR" property="bestSaleSpuNameIndividual" />
    <result column="best_sale_spu_count_individual" jdbcType="BIGINT" property="bestSaleSpuCountIndividual" />
    <result column="best_sale_spu_prompt" jdbcType="VARCHAR" property="bestSaleSpuPrompt" />
    <result column="year_latest_order_time" jdbcType="TIMESTAMP" property="yearLatestOrderTime" />
    <result column="join_mall_activity_count" jdbcType="INTEGER" property="joinMallActivityCount" />
    <result column="join_mall_activity_reward_count" jdbcType="INTEGER" property="joinMallActivityRewardCount" />
    <result column="join_mall_best_activity_name" jdbcType="VARCHAR" property="joinMallBestActivityName" />
    <result column="join_mall_best_activity_ranking" jdbcType="BIGINT" property="joinMallBestActivityRanking" />
    <result column="customer_count" jdbcType="BIGINT" property="customerCount" />
    <result column="customer_count_group" jdbcType="BIGINT" property="customerCountGroup" />
    <result column="customer_count_individual" jdbcType="BIGINT" property="customerCountIndividual" />
    <result column="customer_total_count" jdbcType="BIGINT" property="customerTotalCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    id, user_id, `name`, `year`, register_index, register_time, role_type, province_code, 
    province_name, city_code, city_name, first_order_time, year_last_order_time, first_order_spu_code, 
    first_order_spu_name, order_total_price, order_total_count, order_total_count_prepay, 
    order_total_count_afterpay, order_total_count_mix, order_total_point, spu_total_count, 
    sale_ranking, best_sale_spu_code, best_sale_spu_name, best_sale_spu_order_count, 
    best_sale_spu_code_group, best_sale_spu_name_group, best_sale_spu_count_group, best_sale_spu_code_individual, 
    best_sale_spu_name_individual, best_sale_spu_count_individual, best_sale_spu_prompt, 
    year_latest_order_time, join_mall_activity_count, join_mall_activity_reward_count, 
    join_mall_best_activity_name, join_mall_best_activity_ranking, customer_count, customer_count_group, 
    customer_count_individual, customer_total_count
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from `mini_program_sale_year_report`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from `mini_program_sale_year_report`
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    delete from `mini_program_sale_year_report`
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    delete from `mini_program_sale_year_report`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_sale_year_report` (id, user_id, `name`, 
      `year`, register_index, register_time, 
      role_type, province_code, province_name, 
      city_code, city_name, first_order_time, 
      year_last_order_time, first_order_spu_code, 
      first_order_spu_name, order_total_price, order_total_count, 
      order_total_count_prepay, order_total_count_afterpay, 
      order_total_count_mix, order_total_point, spu_total_count, 
      sale_ranking, best_sale_spu_code, best_sale_spu_name, 
      best_sale_spu_order_count, best_sale_spu_code_group, 
      best_sale_spu_name_group, best_sale_spu_count_group, 
      best_sale_spu_code_individual, best_sale_spu_name_individual, 
      best_sale_spu_count_individual, best_sale_spu_prompt, 
      year_latest_order_time, join_mall_activity_count, 
      join_mall_activity_reward_count, join_mall_best_activity_name, 
      join_mall_best_activity_ranking, customer_count, 
      customer_count_group, customer_count_individual, 
      customer_total_count)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{year,jdbcType=INTEGER}, #{registerIndex,jdbcType=BIGINT}, #{registerTime,jdbcType=TIMESTAMP}, 
      #{roleType,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{firstOrderTime,jdbcType=TIMESTAMP}, 
      #{yearLastOrderTime,jdbcType=TIMESTAMP}, #{firstOrderSpuCode,jdbcType=VARCHAR}, 
      #{firstOrderSpuName,jdbcType=VARCHAR}, #{orderTotalPrice,jdbcType=BIGINT}, #{orderTotalCount,jdbcType=BIGINT}, 
      #{orderTotalCountPrepay,jdbcType=BIGINT}, #{orderTotalCountAfterpay,jdbcType=BIGINT}, 
      #{orderTotalCountMix,jdbcType=BIGINT}, #{orderTotalPoint,jdbcType=BIGINT}, #{spuTotalCount,jdbcType=BIGINT}, 
      #{saleRanking,jdbcType=BIGINT}, #{bestSaleSpuCode,jdbcType=VARCHAR}, #{bestSaleSpuName,jdbcType=VARCHAR}, 
      #{bestSaleSpuOrderCount,jdbcType=BIGINT}, #{bestSaleSpuCodeGroup,jdbcType=VARCHAR}, 
      #{bestSaleSpuNameGroup,jdbcType=VARCHAR}, #{bestSaleSpuCountGroup,jdbcType=BIGINT}, 
      #{bestSaleSpuCodeIndividual,jdbcType=VARCHAR}, #{bestSaleSpuNameIndividual,jdbcType=VARCHAR}, 
      #{bestSaleSpuCountIndividual,jdbcType=BIGINT}, #{bestSaleSpuPrompt,jdbcType=VARCHAR}, 
      #{yearLatestOrderTime,jdbcType=TIMESTAMP}, #{joinMallActivityCount,jdbcType=INTEGER}, 
      #{joinMallActivityRewardCount,jdbcType=INTEGER}, #{joinMallBestActivityName,jdbcType=VARCHAR}, 
      #{joinMallBestActivityRanking,jdbcType=BIGINT}, #{customerCount,jdbcType=BIGINT}, 
      #{customerCountGroup,jdbcType=BIGINT}, #{customerCountIndividual,jdbcType=BIGINT}, 
      #{customerTotalCount,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_sale_year_report`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="year != null">
        `year`,
      </if>
      <if test="registerIndex != null">
        register_index,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="firstOrderTime != null">
        first_order_time,
      </if>
      <if test="yearLastOrderTime != null">
        year_last_order_time,
      </if>
      <if test="firstOrderSpuCode != null">
        first_order_spu_code,
      </if>
      <if test="firstOrderSpuName != null">
        first_order_spu_name,
      </if>
      <if test="orderTotalPrice != null">
        order_total_price,
      </if>
      <if test="orderTotalCount != null">
        order_total_count,
      </if>
      <if test="orderTotalCountPrepay != null">
        order_total_count_prepay,
      </if>
      <if test="orderTotalCountAfterpay != null">
        order_total_count_afterpay,
      </if>
      <if test="orderTotalCountMix != null">
        order_total_count_mix,
      </if>
      <if test="orderTotalPoint != null">
        order_total_point,
      </if>
      <if test="spuTotalCount != null">
        spu_total_count,
      </if>
      <if test="saleRanking != null">
        sale_ranking,
      </if>
      <if test="bestSaleSpuCode != null">
        best_sale_spu_code,
      </if>
      <if test="bestSaleSpuName != null">
        best_sale_spu_name,
      </if>
      <if test="bestSaleSpuOrderCount != null">
        best_sale_spu_order_count,
      </if>
      <if test="bestSaleSpuCodeGroup != null">
        best_sale_spu_code_group,
      </if>
      <if test="bestSaleSpuNameGroup != null">
        best_sale_spu_name_group,
      </if>
      <if test="bestSaleSpuCountGroup != null">
        best_sale_spu_count_group,
      </if>
      <if test="bestSaleSpuCodeIndividual != null">
        best_sale_spu_code_individual,
      </if>
      <if test="bestSaleSpuNameIndividual != null">
        best_sale_spu_name_individual,
      </if>
      <if test="bestSaleSpuCountIndividual != null">
        best_sale_spu_count_individual,
      </if>
      <if test="bestSaleSpuPrompt != null">
        best_sale_spu_prompt,
      </if>
      <if test="yearLatestOrderTime != null">
        year_latest_order_time,
      </if>
      <if test="joinMallActivityCount != null">
        join_mall_activity_count,
      </if>
      <if test="joinMallActivityRewardCount != null">
        join_mall_activity_reward_count,
      </if>
      <if test="joinMallBestActivityName != null">
        join_mall_best_activity_name,
      </if>
      <if test="joinMallBestActivityRanking != null">
        join_mall_best_activity_ranking,
      </if>
      <if test="customerCount != null">
        customer_count,
      </if>
      <if test="customerCountGroup != null">
        customer_count_group,
      </if>
      <if test="customerCountIndividual != null">
        customer_count_individual,
      </if>
      <if test="customerTotalCount != null">
        customer_total_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="registerIndex != null">
        #{registerIndex,jdbcType=BIGINT},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="firstOrderTime != null">
        #{firstOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yearLastOrderTime != null">
        #{yearLastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOrderSpuCode != null">
        #{firstOrderSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="firstOrderSpuName != null">
        #{firstOrderSpuName,jdbcType=VARCHAR},
      </if>
      <if test="orderTotalPrice != null">
        #{orderTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCount != null">
        #{orderTotalCount,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountPrepay != null">
        #{orderTotalCountPrepay,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountAfterpay != null">
        #{orderTotalCountAfterpay,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountMix != null">
        #{orderTotalCountMix,jdbcType=BIGINT},
      </if>
      <if test="orderTotalPoint != null">
        #{orderTotalPoint,jdbcType=BIGINT},
      </if>
      <if test="spuTotalCount != null">
        #{spuTotalCount,jdbcType=BIGINT},
      </if>
      <if test="saleRanking != null">
        #{saleRanking,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCode != null">
        #{bestSaleSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuName != null">
        #{bestSaleSpuName,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuOrderCount != null">
        #{bestSaleSpuOrderCount,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCodeGroup != null">
        #{bestSaleSpuCodeGroup,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuNameGroup != null">
        #{bestSaleSpuNameGroup,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuCountGroup != null">
        #{bestSaleSpuCountGroup,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCodeIndividual != null">
        #{bestSaleSpuCodeIndividual,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuNameIndividual != null">
        #{bestSaleSpuNameIndividual,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuCountIndividual != null">
        #{bestSaleSpuCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuPrompt != null">
        #{bestSaleSpuPrompt,jdbcType=VARCHAR},
      </if>
      <if test="yearLatestOrderTime != null">
        #{yearLatestOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinMallActivityCount != null">
        #{joinMallActivityCount,jdbcType=INTEGER},
      </if>
      <if test="joinMallActivityRewardCount != null">
        #{joinMallActivityRewardCount,jdbcType=INTEGER},
      </if>
      <if test="joinMallBestActivityName != null">
        #{joinMallBestActivityName,jdbcType=VARCHAR},
      </if>
      <if test="joinMallBestActivityRanking != null">
        #{joinMallBestActivityRanking,jdbcType=BIGINT},
      </if>
      <if test="customerCount != null">
        #{customerCount,jdbcType=BIGINT},
      </if>
      <if test="customerCountGroup != null">
        #{customerCountGroup,jdbcType=BIGINT},
      </if>
      <if test="customerCountIndividual != null">
        #{customerCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="customerTotalCount != null">
        #{customerTotalCount,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from `mini_program_sale_year_report`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `mini_program_sale_year_report`
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.year != null">
        `year` = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.registerIndex != null">
        register_index = #{record.registerIndex,jdbcType=BIGINT},
      </if>
      <if test="record.registerTime != null">
        register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.firstOrderTime != null">
        first_order_time = #{record.firstOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.yearLastOrderTime != null">
        year_last_order_time = #{record.yearLastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstOrderSpuCode != null">
        first_order_spu_code = #{record.firstOrderSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.firstOrderSpuName != null">
        first_order_spu_name = #{record.firstOrderSpuName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTotalPrice != null">
        order_total_price = #{record.orderTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.orderTotalCount != null">
        order_total_count = #{record.orderTotalCount,jdbcType=BIGINT},
      </if>
      <if test="record.orderTotalCountPrepay != null">
        order_total_count_prepay = #{record.orderTotalCountPrepay,jdbcType=BIGINT},
      </if>
      <if test="record.orderTotalCountAfterpay != null">
        order_total_count_afterpay = #{record.orderTotalCountAfterpay,jdbcType=BIGINT},
      </if>
      <if test="record.orderTotalCountMix != null">
        order_total_count_mix = #{record.orderTotalCountMix,jdbcType=BIGINT},
      </if>
      <if test="record.orderTotalPoint != null">
        order_total_point = #{record.orderTotalPoint,jdbcType=BIGINT},
      </if>
      <if test="record.spuTotalCount != null">
        spu_total_count = #{record.spuTotalCount,jdbcType=BIGINT},
      </if>
      <if test="record.saleRanking != null">
        sale_ranking = #{record.saleRanking,jdbcType=BIGINT},
      </if>
      <if test="record.bestSaleSpuCode != null">
        best_sale_spu_code = #{record.bestSaleSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuName != null">
        best_sale_spu_name = #{record.bestSaleSpuName,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuOrderCount != null">
        best_sale_spu_order_count = #{record.bestSaleSpuOrderCount,jdbcType=BIGINT},
      </if>
      <if test="record.bestSaleSpuCodeGroup != null">
        best_sale_spu_code_group = #{record.bestSaleSpuCodeGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuNameGroup != null">
        best_sale_spu_name_group = #{record.bestSaleSpuNameGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuCountGroup != null">
        best_sale_spu_count_group = #{record.bestSaleSpuCountGroup,jdbcType=BIGINT},
      </if>
      <if test="record.bestSaleSpuCodeIndividual != null">
        best_sale_spu_code_individual = #{record.bestSaleSpuCodeIndividual,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuNameIndividual != null">
        best_sale_spu_name_individual = #{record.bestSaleSpuNameIndividual,jdbcType=VARCHAR},
      </if>
      <if test="record.bestSaleSpuCountIndividual != null">
        best_sale_spu_count_individual = #{record.bestSaleSpuCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="record.bestSaleSpuPrompt != null">
        best_sale_spu_prompt = #{record.bestSaleSpuPrompt,jdbcType=VARCHAR},
      </if>
      <if test="record.yearLatestOrderTime != null">
        year_latest_order_time = #{record.yearLatestOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.joinMallActivityCount != null">
        join_mall_activity_count = #{record.joinMallActivityCount,jdbcType=INTEGER},
      </if>
      <if test="record.joinMallActivityRewardCount != null">
        join_mall_activity_reward_count = #{record.joinMallActivityRewardCount,jdbcType=INTEGER},
      </if>
      <if test="record.joinMallBestActivityName != null">
        join_mall_best_activity_name = #{record.joinMallBestActivityName,jdbcType=VARCHAR},
      </if>
      <if test="record.joinMallBestActivityRanking != null">
        join_mall_best_activity_ranking = #{record.joinMallBestActivityRanking,jdbcType=BIGINT},
      </if>
      <if test="record.customerCount != null">
        customer_count = #{record.customerCount,jdbcType=BIGINT},
      </if>
      <if test="record.customerCountGroup != null">
        customer_count_group = #{record.customerCountGroup,jdbcType=BIGINT},
      </if>
      <if test="record.customerCountIndividual != null">
        customer_count_individual = #{record.customerCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="record.customerTotalCount != null">
        customer_total_count = #{record.customerTotalCount,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `mini_program_sale_year_report`
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      `year` = #{record.year,jdbcType=INTEGER},
      register_index = #{record.registerIndex,jdbcType=BIGINT},
      register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      role_type = #{record.roleType,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      first_order_time = #{record.firstOrderTime,jdbcType=TIMESTAMP},
      year_last_order_time = #{record.yearLastOrderTime,jdbcType=TIMESTAMP},
      first_order_spu_code = #{record.firstOrderSpuCode,jdbcType=VARCHAR},
      first_order_spu_name = #{record.firstOrderSpuName,jdbcType=VARCHAR},
      order_total_price = #{record.orderTotalPrice,jdbcType=BIGINT},
      order_total_count = #{record.orderTotalCount,jdbcType=BIGINT},
      order_total_count_prepay = #{record.orderTotalCountPrepay,jdbcType=BIGINT},
      order_total_count_afterpay = #{record.orderTotalCountAfterpay,jdbcType=BIGINT},
      order_total_count_mix = #{record.orderTotalCountMix,jdbcType=BIGINT},
      order_total_point = #{record.orderTotalPoint,jdbcType=BIGINT},
      spu_total_count = #{record.spuTotalCount,jdbcType=BIGINT},
      sale_ranking = #{record.saleRanking,jdbcType=BIGINT},
      best_sale_spu_code = #{record.bestSaleSpuCode,jdbcType=VARCHAR},
      best_sale_spu_name = #{record.bestSaleSpuName,jdbcType=VARCHAR},
      best_sale_spu_order_count = #{record.bestSaleSpuOrderCount,jdbcType=BIGINT},
      best_sale_spu_code_group = #{record.bestSaleSpuCodeGroup,jdbcType=VARCHAR},
      best_sale_spu_name_group = #{record.bestSaleSpuNameGroup,jdbcType=VARCHAR},
      best_sale_spu_count_group = #{record.bestSaleSpuCountGroup,jdbcType=BIGINT},
      best_sale_spu_code_individual = #{record.bestSaleSpuCodeIndividual,jdbcType=VARCHAR},
      best_sale_spu_name_individual = #{record.bestSaleSpuNameIndividual,jdbcType=VARCHAR},
      best_sale_spu_count_individual = #{record.bestSaleSpuCountIndividual,jdbcType=BIGINT},
      best_sale_spu_prompt = #{record.bestSaleSpuPrompt,jdbcType=VARCHAR},
      year_latest_order_time = #{record.yearLatestOrderTime,jdbcType=TIMESTAMP},
      join_mall_activity_count = #{record.joinMallActivityCount,jdbcType=INTEGER},
      join_mall_activity_reward_count = #{record.joinMallActivityRewardCount,jdbcType=INTEGER},
      join_mall_best_activity_name = #{record.joinMallBestActivityName,jdbcType=VARCHAR},
      join_mall_best_activity_ranking = #{record.joinMallBestActivityRanking,jdbcType=BIGINT},
      customer_count = #{record.customerCount,jdbcType=BIGINT},
      customer_count_group = #{record.customerCountGroup,jdbcType=BIGINT},
      customer_count_individual = #{record.customerCountIndividual,jdbcType=BIGINT},
      customer_total_count = #{record.customerTotalCount,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `mini_program_sale_year_report`
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        `year` = #{year,jdbcType=INTEGER},
      </if>
      <if test="registerIndex != null">
        register_index = #{registerIndex,jdbcType=BIGINT},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="firstOrderTime != null">
        first_order_time = #{firstOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yearLastOrderTime != null">
        year_last_order_time = #{yearLastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOrderSpuCode != null">
        first_order_spu_code = #{firstOrderSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="firstOrderSpuName != null">
        first_order_spu_name = #{firstOrderSpuName,jdbcType=VARCHAR},
      </if>
      <if test="orderTotalPrice != null">
        order_total_price = #{orderTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCount != null">
        order_total_count = #{orderTotalCount,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountPrepay != null">
        order_total_count_prepay = #{orderTotalCountPrepay,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountAfterpay != null">
        order_total_count_afterpay = #{orderTotalCountAfterpay,jdbcType=BIGINT},
      </if>
      <if test="orderTotalCountMix != null">
        order_total_count_mix = #{orderTotalCountMix,jdbcType=BIGINT},
      </if>
      <if test="orderTotalPoint != null">
        order_total_point = #{orderTotalPoint,jdbcType=BIGINT},
      </if>
      <if test="spuTotalCount != null">
        spu_total_count = #{spuTotalCount,jdbcType=BIGINT},
      </if>
      <if test="saleRanking != null">
        sale_ranking = #{saleRanking,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCode != null">
        best_sale_spu_code = #{bestSaleSpuCode,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuName != null">
        best_sale_spu_name = #{bestSaleSpuName,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuOrderCount != null">
        best_sale_spu_order_count = #{bestSaleSpuOrderCount,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCodeGroup != null">
        best_sale_spu_code_group = #{bestSaleSpuCodeGroup,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuNameGroup != null">
        best_sale_spu_name_group = #{bestSaleSpuNameGroup,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuCountGroup != null">
        best_sale_spu_count_group = #{bestSaleSpuCountGroup,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuCodeIndividual != null">
        best_sale_spu_code_individual = #{bestSaleSpuCodeIndividual,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuNameIndividual != null">
        best_sale_spu_name_individual = #{bestSaleSpuNameIndividual,jdbcType=VARCHAR},
      </if>
      <if test="bestSaleSpuCountIndividual != null">
        best_sale_spu_count_individual = #{bestSaleSpuCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="bestSaleSpuPrompt != null">
        best_sale_spu_prompt = #{bestSaleSpuPrompt,jdbcType=VARCHAR},
      </if>
      <if test="yearLatestOrderTime != null">
        year_latest_order_time = #{yearLatestOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinMallActivityCount != null">
        join_mall_activity_count = #{joinMallActivityCount,jdbcType=INTEGER},
      </if>
      <if test="joinMallActivityRewardCount != null">
        join_mall_activity_reward_count = #{joinMallActivityRewardCount,jdbcType=INTEGER},
      </if>
      <if test="joinMallBestActivityName != null">
        join_mall_best_activity_name = #{joinMallBestActivityName,jdbcType=VARCHAR},
      </if>
      <if test="joinMallBestActivityRanking != null">
        join_mall_best_activity_ranking = #{joinMallBestActivityRanking,jdbcType=BIGINT},
      </if>
      <if test="customerCount != null">
        customer_count = #{customerCount,jdbcType=BIGINT},
      </if>
      <if test="customerCountGroup != null">
        customer_count_group = #{customerCountGroup,jdbcType=BIGINT},
      </if>
      <if test="customerCountIndividual != null">
        customer_count_individual = #{customerCountIndividual,jdbcType=BIGINT},
      </if>
      <if test="customerTotalCount != null">
        customer_total_count = #{customerTotalCount,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    update `mini_program_sale_year_report`
    set user_id = #{userId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      `year` = #{year,jdbcType=INTEGER},
      register_index = #{registerIndex,jdbcType=BIGINT},
      register_time = #{registerTime,jdbcType=TIMESTAMP},
      role_type = #{roleType,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      first_order_time = #{firstOrderTime,jdbcType=TIMESTAMP},
      year_last_order_time = #{yearLastOrderTime,jdbcType=TIMESTAMP},
      first_order_spu_code = #{firstOrderSpuCode,jdbcType=VARCHAR},
      first_order_spu_name = #{firstOrderSpuName,jdbcType=VARCHAR},
      order_total_price = #{orderTotalPrice,jdbcType=BIGINT},
      order_total_count = #{orderTotalCount,jdbcType=BIGINT},
      order_total_count_prepay = #{orderTotalCountPrepay,jdbcType=BIGINT},
      order_total_count_afterpay = #{orderTotalCountAfterpay,jdbcType=BIGINT},
      order_total_count_mix = #{orderTotalCountMix,jdbcType=BIGINT},
      order_total_point = #{orderTotalPoint,jdbcType=BIGINT},
      spu_total_count = #{spuTotalCount,jdbcType=BIGINT},
      sale_ranking = #{saleRanking,jdbcType=BIGINT},
      best_sale_spu_code = #{bestSaleSpuCode,jdbcType=VARCHAR},
      best_sale_spu_name = #{bestSaleSpuName,jdbcType=VARCHAR},
      best_sale_spu_order_count = #{bestSaleSpuOrderCount,jdbcType=BIGINT},
      best_sale_spu_code_group = #{bestSaleSpuCodeGroup,jdbcType=VARCHAR},
      best_sale_spu_name_group = #{bestSaleSpuNameGroup,jdbcType=VARCHAR},
      best_sale_spu_count_group = #{bestSaleSpuCountGroup,jdbcType=BIGINT},
      best_sale_spu_code_individual = #{bestSaleSpuCodeIndividual,jdbcType=VARCHAR},
      best_sale_spu_name_individual = #{bestSaleSpuNameIndividual,jdbcType=VARCHAR},
      best_sale_spu_count_individual = #{bestSaleSpuCountIndividual,jdbcType=BIGINT},
      best_sale_spu_prompt = #{bestSaleSpuPrompt,jdbcType=VARCHAR},
      year_latest_order_time = #{yearLatestOrderTime,jdbcType=TIMESTAMP},
      join_mall_activity_count = #{joinMallActivityCount,jdbcType=INTEGER},
      join_mall_activity_reward_count = #{joinMallActivityRewardCount,jdbcType=INTEGER},
      join_mall_best_activity_name = #{joinMallBestActivityName,jdbcType=VARCHAR},
      join_mall_best_activity_ranking = #{joinMallBestActivityRanking,jdbcType=BIGINT},
      customer_count = #{customerCount,jdbcType=BIGINT},
      customer_count_group = #{customerCountGroup,jdbcType=BIGINT},
      customer_count_individual = #{customerCountIndividual,jdbcType=BIGINT},
      customer_total_count = #{customerTotalCount,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_sale_year_report`
    (id, user_id, `name`, `year`, register_index, register_time, role_type, province_code, 
      province_name, city_code, city_name, first_order_time, year_last_order_time, first_order_spu_code, 
      first_order_spu_name, order_total_price, order_total_count, order_total_count_prepay, 
      order_total_count_afterpay, order_total_count_mix, order_total_point, spu_total_count, 
      sale_ranking, best_sale_spu_code, best_sale_spu_name, best_sale_spu_order_count, 
      best_sale_spu_code_group, best_sale_spu_name_group, best_sale_spu_count_group, 
      best_sale_spu_code_individual, best_sale_spu_name_individual, best_sale_spu_count_individual, 
      best_sale_spu_prompt, year_latest_order_time, join_mall_activity_count, join_mall_activity_reward_count, 
      join_mall_best_activity_name, join_mall_best_activity_ranking, customer_count, 
      customer_count_group, customer_count_individual, customer_total_count)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.year,jdbcType=INTEGER}, #{item.registerIndex,jdbcType=BIGINT}, #{item.registerTime,jdbcType=TIMESTAMP}, 
        #{item.roleType,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, 
        #{item.cityCode,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, #{item.firstOrderTime,jdbcType=TIMESTAMP}, 
        #{item.yearLastOrderTime,jdbcType=TIMESTAMP}, #{item.firstOrderSpuCode,jdbcType=VARCHAR}, 
        #{item.firstOrderSpuName,jdbcType=VARCHAR}, #{item.orderTotalPrice,jdbcType=BIGINT}, 
        #{item.orderTotalCount,jdbcType=BIGINT}, #{item.orderTotalCountPrepay,jdbcType=BIGINT}, 
        #{item.orderTotalCountAfterpay,jdbcType=BIGINT}, #{item.orderTotalCountMix,jdbcType=BIGINT}, 
        #{item.orderTotalPoint,jdbcType=BIGINT}, #{item.spuTotalCount,jdbcType=BIGINT}, 
        #{item.saleRanking,jdbcType=BIGINT}, #{item.bestSaleSpuCode,jdbcType=VARCHAR}, 
        #{item.bestSaleSpuName,jdbcType=VARCHAR}, #{item.bestSaleSpuOrderCount,jdbcType=BIGINT}, 
        #{item.bestSaleSpuCodeGroup,jdbcType=VARCHAR}, #{item.bestSaleSpuNameGroup,jdbcType=VARCHAR}, 
        #{item.bestSaleSpuCountGroup,jdbcType=BIGINT}, #{item.bestSaleSpuCodeIndividual,jdbcType=VARCHAR}, 
        #{item.bestSaleSpuNameIndividual,jdbcType=VARCHAR}, #{item.bestSaleSpuCountIndividual,jdbcType=BIGINT}, 
        #{item.bestSaleSpuPrompt,jdbcType=VARCHAR}, #{item.yearLatestOrderTime,jdbcType=TIMESTAMP}, 
        #{item.joinMallActivityCount,jdbcType=INTEGER}, #{item.joinMallActivityRewardCount,jdbcType=INTEGER}, 
        #{item.joinMallBestActivityName,jdbcType=VARCHAR}, #{item.joinMallBestActivityRanking,jdbcType=BIGINT}, 
        #{item.customerCount,jdbcType=BIGINT}, #{item.customerCountGroup,jdbcType=BIGINT}, 
        #{item.customerCountIndividual,jdbcType=BIGINT}, #{item.customerTotalCount,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jan 02 20:38:07 GMT+08:00 2025. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_sale_year_report` (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=INTEGER}
        </if>
        <if test="'register_index'.toString() == column.value">
          #{item.registerIndex,jdbcType=BIGINT}
        </if>
        <if test="'register_time'.toString() == column.value">
          #{item.registerTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'role_type'.toString() == column.value">
          #{item.roleType,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'first_order_time'.toString() == column.value">
          #{item.firstOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'year_last_order_time'.toString() == column.value">
          #{item.yearLastOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'first_order_spu_code'.toString() == column.value">
          #{item.firstOrderSpuCode,jdbcType=VARCHAR}
        </if>
        <if test="'first_order_spu_name'.toString() == column.value">
          #{item.firstOrderSpuName,jdbcType=VARCHAR}
        </if>
        <if test="'order_total_price'.toString() == column.value">
          #{item.orderTotalPrice,jdbcType=BIGINT}
        </if>
        <if test="'order_total_count'.toString() == column.value">
          #{item.orderTotalCount,jdbcType=BIGINT}
        </if>
        <if test="'order_total_count_prepay'.toString() == column.value">
          #{item.orderTotalCountPrepay,jdbcType=BIGINT}
        </if>
        <if test="'order_total_count_afterpay'.toString() == column.value">
          #{item.orderTotalCountAfterpay,jdbcType=BIGINT}
        </if>
        <if test="'order_total_count_mix'.toString() == column.value">
          #{item.orderTotalCountMix,jdbcType=BIGINT}
        </if>
        <if test="'order_total_point'.toString() == column.value">
          #{item.orderTotalPoint,jdbcType=BIGINT}
        </if>
        <if test="'spu_total_count'.toString() == column.value">
          #{item.spuTotalCount,jdbcType=BIGINT}
        </if>
        <if test="'sale_ranking'.toString() == column.value">
          #{item.saleRanking,jdbcType=BIGINT}
        </if>
        <if test="'best_sale_spu_code'.toString() == column.value">
          #{item.bestSaleSpuCode,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_name'.toString() == column.value">
          #{item.bestSaleSpuName,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_order_count'.toString() == column.value">
          #{item.bestSaleSpuOrderCount,jdbcType=BIGINT}
        </if>
        <if test="'best_sale_spu_code_group'.toString() == column.value">
          #{item.bestSaleSpuCodeGroup,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_name_group'.toString() == column.value">
          #{item.bestSaleSpuNameGroup,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_count_group'.toString() == column.value">
          #{item.bestSaleSpuCountGroup,jdbcType=BIGINT}
        </if>
        <if test="'best_sale_spu_code_individual'.toString() == column.value">
          #{item.bestSaleSpuCodeIndividual,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_name_individual'.toString() == column.value">
          #{item.bestSaleSpuNameIndividual,jdbcType=VARCHAR}
        </if>
        <if test="'best_sale_spu_count_individual'.toString() == column.value">
          #{item.bestSaleSpuCountIndividual,jdbcType=BIGINT}
        </if>
        <if test="'best_sale_spu_prompt'.toString() == column.value">
          #{item.bestSaleSpuPrompt,jdbcType=VARCHAR}
        </if>
        <if test="'year_latest_order_time'.toString() == column.value">
          #{item.yearLatestOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'join_mall_activity_count'.toString() == column.value">
          #{item.joinMallActivityCount,jdbcType=INTEGER}
        </if>
        <if test="'join_mall_activity_reward_count'.toString() == column.value">
          #{item.joinMallActivityRewardCount,jdbcType=INTEGER}
        </if>
        <if test="'join_mall_best_activity_name'.toString() == column.value">
          #{item.joinMallBestActivityName,jdbcType=VARCHAR}
        </if>
        <if test="'join_mall_best_activity_ranking'.toString() == column.value">
          #{item.joinMallBestActivityRanking,jdbcType=BIGINT}
        </if>
        <if test="'customer_count'.toString() == column.value">
          #{item.customerCount,jdbcType=BIGINT}
        </if>
        <if test="'customer_count_group'.toString() == column.value">
          #{item.customerCountGroup,jdbcType=BIGINT}
        </if>
        <if test="'customer_count_individual'.toString() == column.value">
          #{item.customerCountIndividual,jdbcType=BIGINT}
        </if>
        <if test="'customer_total_count'.toString() == column.value">
          #{item.customerTotalCount,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>