<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramUserOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="point" jdbcType="BIGINT" property="point" />
    <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, user_id, order_id, atom_order_id, amount, supplier_id, point, order_create_time, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_user_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_user_order_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_user_order_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_user_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_user_order_info (id, user_id, order_id, 
      atom_order_id, amount, supplier_id, 
      point, order_create_time, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{atomOrderId,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{supplierId,jdbcType=VARCHAR}, 
      #{point,jdbcType=BIGINT}, #{orderCreateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_user_order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="orderCreateTime != null">
        order_create_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=BIGINT},
      </if>
      <if test="orderCreateTime != null">
        #{orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_user_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_user_order_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=BIGINT},
      </if>
      <if test="record.orderCreateTime != null">
        order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_user_order_info
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      point = #{record.point,jdbcType=BIGINT},
      order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_user_order_info
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=BIGINT},
      </if>
      <if test="orderCreateTime != null">
        order_create_time = #{orderCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_user_order_info
    set user_id = #{userId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      point = #{point,jdbcType=BIGINT},
      order_create_time = #{orderCreateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_user_order_info
    (id, user_id, order_id, atom_order_id, amount, supplier_id, point, order_create_time, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.atomOrderId,jdbcType=VARCHAR}, #{item.amount,jdbcType=BIGINT}, #{item.supplierId,jdbcType=VARCHAR}, 
        #{item.point,jdbcType=BIGINT}, #{item.orderCreateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 14:37:35 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_user_order_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=BIGINT}
        </if>
        <if test="'supplier_id'.toString() == column.value">
          #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="'point'.toString() == column.value">
          #{item.point,jdbcType=BIGINT}
        </if>
        <if test="'order_create_time'.toString() == column.value">
          #{item.orderCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>