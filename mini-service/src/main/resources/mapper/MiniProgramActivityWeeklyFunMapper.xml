<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramActivityWeeklyFunMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFun">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="wheel_parts" jdbcType="INTEGER" property="wheelParts" />
    <result column="order_start" jdbcType="TIMESTAMP" property="orderStart" />
    <result column="order_stop" jdbcType="TIMESTAMP" property="orderStop" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="register_start" jdbcType="TIMESTAMP" property="registerStart" />
    <result column="register_stop" jdbcType="TIMESTAMP" property="registerStop" />
    <result column="max_player" jdbcType="BIGINT" property="maxPlayer" />
    <result column="slogan" jdbcType="VARCHAR" property="slogan" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, activity_id, rule, description, wheel_parts, order_start, order_stop, order_count, 
    register_start, register_stop, max_player, slogan
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_activity_weekly_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_activity_weekly_fun
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity_weekly_fun
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity_weekly_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFun">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_weekly_fun (id, activity_id, rule, 
      description, wheel_parts, order_start, 
      order_stop, order_count, register_start, 
      register_stop, max_player, slogan
      )
    values (#{id,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{rule,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{wheelParts,jdbcType=INTEGER}, #{orderStart,jdbcType=TIMESTAMP}, 
      #{orderStop,jdbcType=TIMESTAMP}, #{orderCount,jdbcType=INTEGER}, #{registerStart,jdbcType=TIMESTAMP}, 
      #{registerStop,jdbcType=TIMESTAMP}, #{maxPlayer,jdbcType=BIGINT}, #{slogan,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFun">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_weekly_fun
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="wheelParts != null">
        wheel_parts,
      </if>
      <if test="orderStart != null">
        order_start,
      </if>
      <if test="orderStop != null">
        order_stop,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="registerStart != null">
        register_start,
      </if>
      <if test="registerStop != null">
        register_stop,
      </if>
      <if test="maxPlayer != null">
        max_player,
      </if>
      <if test="slogan != null">
        slogan,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="wheelParts != null">
        #{wheelParts,jdbcType=INTEGER},
      </if>
      <if test="orderStart != null">
        #{orderStart,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStop != null">
        #{orderStop,jdbcType=TIMESTAMP},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="registerStart != null">
        #{registerStart,jdbcType=TIMESTAMP},
      </if>
      <if test="registerStop != null">
        #{registerStop,jdbcType=TIMESTAMP},
      </if>
      <if test="maxPlayer != null">
        #{maxPlayer,jdbcType=BIGINT},
      </if>
      <if test="slogan != null">
        #{slogan,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_activity_weekly_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_weekly_fun
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.rule != null">
        rule = #{record.rule,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.wheelParts != null">
        wheel_parts = #{record.wheelParts,jdbcType=INTEGER},
      </if>
      <if test="record.orderStart != null">
        order_start = #{record.orderStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStop != null">
        order_stop = #{record.orderStop,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderCount != null">
        order_count = #{record.orderCount,jdbcType=INTEGER},
      </if>
      <if test="record.registerStart != null">
        register_start = #{record.registerStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registerStop != null">
        register_stop = #{record.registerStop,jdbcType=TIMESTAMP},
      </if>
      <if test="record.maxPlayer != null">
        max_player = #{record.maxPlayer,jdbcType=BIGINT},
      </if>
      <if test="record.slogan != null">
        slogan = #{record.slogan,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_weekly_fun
    set id = #{record.id,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      rule = #{record.rule,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      wheel_parts = #{record.wheelParts,jdbcType=INTEGER},
      order_start = #{record.orderStart,jdbcType=TIMESTAMP},
      order_stop = #{record.orderStop,jdbcType=TIMESTAMP},
      order_count = #{record.orderCount,jdbcType=INTEGER},
      register_start = #{record.registerStart,jdbcType=TIMESTAMP},
      register_stop = #{record.registerStop,jdbcType=TIMESTAMP},
      max_player = #{record.maxPlayer,jdbcType=BIGINT},
      slogan = #{record.slogan,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFun">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_weekly_fun
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="rule != null">
        rule = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="wheelParts != null">
        wheel_parts = #{wheelParts,jdbcType=INTEGER},
      </if>
      <if test="orderStart != null">
        order_start = #{orderStart,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStop != null">
        order_stop = #{orderStop,jdbcType=TIMESTAMP},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="registerStart != null">
        register_start = #{registerStart,jdbcType=TIMESTAMP},
      </if>
      <if test="registerStop != null">
        register_stop = #{registerStop,jdbcType=TIMESTAMP},
      </if>
      <if test="maxPlayer != null">
        max_player = #{maxPlayer,jdbcType=BIGINT},
      </if>
      <if test="slogan != null">
        slogan = #{slogan,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFun">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_weekly_fun
    set activity_id = #{activityId,jdbcType=VARCHAR},
      rule = #{rule,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      wheel_parts = #{wheelParts,jdbcType=INTEGER},
      order_start = #{orderStart,jdbcType=TIMESTAMP},
      order_stop = #{orderStop,jdbcType=TIMESTAMP},
      order_count = #{orderCount,jdbcType=INTEGER},
      register_start = #{registerStart,jdbcType=TIMESTAMP},
      register_stop = #{registerStop,jdbcType=TIMESTAMP},
      max_player = #{maxPlayer,jdbcType=BIGINT},
      slogan = #{slogan,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_weekly_fun
    (id, activity_id, rule, description, wheel_parts, order_start, order_stop, order_count, 
      register_start, register_stop, max_player, slogan)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, #{item.rule,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.wheelParts,jdbcType=INTEGER}, #{item.orderStart,jdbcType=TIMESTAMP}, 
        #{item.orderStop,jdbcType=TIMESTAMP}, #{item.orderCount,jdbcType=INTEGER}, #{item.registerStart,jdbcType=TIMESTAMP}, 
        #{item.registerStop,jdbcType=TIMESTAMP}, #{item.maxPlayer,jdbcType=BIGINT}, #{item.slogan,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 28 17:54:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_weekly_fun (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'rule'.toString() == column.value">
          #{item.rule,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'wheel_parts'.toString() == column.value">
          #{item.wheelParts,jdbcType=INTEGER}
        </if>
        <if test="'order_start'.toString() == column.value">
          #{item.orderStart,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_stop'.toString() == column.value">
          #{item.orderStop,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_count'.toString() == column.value">
          #{item.orderCount,jdbcType=INTEGER}
        </if>
        <if test="'register_start'.toString() == column.value">
          #{item.registerStart,jdbcType=TIMESTAMP}
        </if>
        <if test="'register_stop'.toString() == column.value">
          #{item.registerStop,jdbcType=TIMESTAMP}
        </if>
        <if test="'max_player'.toString() == column.value">
          #{item.maxPlayer,jdbcType=BIGINT}
        </if>
        <if test="'slogan'.toString() == column.value">
          #{item.slogan,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>