<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramActivityMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="target" jdbcType="INTEGER" property="target" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="stop_time" jdbcType="TIMESTAMP" property="stopTime" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="activity_type" jdbcType="INTEGER" property="activityType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="active" jdbcType="BIT" property="active" />
    <result column="create_uid" jdbcType="VARCHAR" property="createUid" />
    <result column="list_img" jdbcType="VARCHAR" property="listImg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="views" jdbcType="INTEGER" property="views" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="share_img" jdbcType="VARCHAR" property="shareImg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, name, target, start_time, stop_time, confirm_time, settlement_time, activity_type, 
    status, audit_status, active, create_uid, list_img, create_time, update_time, views, 
    is_delete, share_img
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_activity
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity (id, name, target, 
      start_time, stop_time, confirm_time, 
      settlement_time, activity_type, status, 
      audit_status, active, create_uid, 
      list_img, create_time, update_time, 
      views, is_delete, share_img
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{target,jdbcType=INTEGER}, 
      #{startTime,jdbcType=TIMESTAMP}, #{stopTime,jdbcType=TIMESTAMP}, #{confirmTime,jdbcType=TIMESTAMP}, 
      #{settlementTime,jdbcType=TIMESTAMP}, #{activityType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{active,jdbcType=BIT}, #{createUid,jdbcType=VARCHAR}, 
      #{listImg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{views,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{shareImg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="target != null">
        target,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="stopTime != null">
        stop_time,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="activityType != null">
        activity_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="active != null">
        active,
      </if>
      <if test="createUid != null">
        create_uid,
      </if>
      <if test="listImg != null">
        list_img,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="views != null">
        views,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="shareImg != null">
        share_img,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="target != null">
        #{target,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopTime != null">
        #{stopTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityType != null">
        #{activityType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="active != null">
        #{active,jdbcType=BIT},
      </if>
      <if test="createUid != null">
        #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="listImg != null">
        #{listImg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="views != null">
        #{views,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="shareImg != null">
        #{shareImg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.target != null">
        target = #{record.target,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.stopTime != null">
        stop_time = #{record.stopTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementTime != null">
        settlement_time = #{record.settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityType != null">
        activity_type = #{record.activityType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.active != null">
        active = #{record.active,jdbcType=BIT},
      </if>
      <if test="record.createUid != null">
        create_uid = #{record.createUid,jdbcType=VARCHAR},
      </if>
      <if test="record.listImg != null">
        list_img = #{record.listImg,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.views != null">
        views = #{record.views,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.shareImg != null">
        share_img = #{record.shareImg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      target = #{record.target,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      stop_time = #{record.stopTime,jdbcType=TIMESTAMP},
      confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      settlement_time = #{record.settlementTime,jdbcType=TIMESTAMP},
      activity_type = #{record.activityType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      active = #{record.active,jdbcType=BIT},
      create_uid = #{record.createUid,jdbcType=VARCHAR},
      list_img = #{record.listImg,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      views = #{record.views,jdbcType=INTEGER},
      is_delete = #{record.isDelete,jdbcType=INTEGER},
      share_img = #{record.shareImg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="target != null">
        target = #{target,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopTime != null">
        stop_time = #{stopTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityType != null">
        activity_type = #{activityType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="active != null">
        active = #{active,jdbcType=BIT},
      </if>
      <if test="createUid != null">
        create_uid = #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="listImg != null">
        list_img = #{listImg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="views != null">
        views = #{views,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="shareImg != null">
        share_img = #{shareImg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity
    set name = #{name,jdbcType=VARCHAR},
      target = #{target,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      stop_time = #{stopTime,jdbcType=TIMESTAMP},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      activity_type = #{activityType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      active = #{active,jdbcType=BIT},
      create_uid = #{createUid,jdbcType=VARCHAR},
      list_img = #{listImg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      views = #{views,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=INTEGER},
      share_img = #{shareImg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity
    (id, name, target, start_time, stop_time, confirm_time, settlement_time, activity_type, 
      status, audit_status, active, create_uid, list_img, create_time, update_time, views, 
      is_delete, share_img)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.target,jdbcType=INTEGER}, 
        #{item.startTime,jdbcType=TIMESTAMP}, #{item.stopTime,jdbcType=TIMESTAMP}, #{item.confirmTime,jdbcType=TIMESTAMP}, 
        #{item.settlementTime,jdbcType=TIMESTAMP}, #{item.activityType,jdbcType=INTEGER}, 
        #{item.status,jdbcType=INTEGER}, #{item.auditStatus,jdbcType=INTEGER}, #{item.active,jdbcType=BIT}, 
        #{item.createUid,jdbcType=VARCHAR}, #{item.listImg,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.views,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER}, 
        #{item.shareImg,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 09 15:09:20 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'target'.toString() == column.value">
          #{item.target,jdbcType=INTEGER}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'stop_time'.toString() == column.value">
          #{item.stopTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'confirm_time'.toString() == column.value">
          #{item.confirmTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'settlement_time'.toString() == column.value">
          #{item.settlementTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'activity_type'.toString() == column.value">
          #{item.activityType,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="'active'.toString() == column.value">
          #{item.active,jdbcType=BIT}
        </if>
        <if test="'create_uid'.toString() == column.value">
          #{item.createUid,jdbcType=VARCHAR}
        </if>
        <if test="'list_img'.toString() == column.value">
          #{item.listImg,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'views'.toString() == column.value">
          #{item.views,jdbcType=INTEGER}
        </if>
        <if test="'is_delete'.toString() == column.value">
          #{item.isDelete,jdbcType=INTEGER}
        </if>
        <if test="'share_img'.toString() == column.value">
          #{item.shareImg,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>