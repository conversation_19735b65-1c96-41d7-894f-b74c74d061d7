<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.PointSupplierMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.PointSupplier">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="full_name" jdbcType="VARCHAR" property="fullName" />
    <result column="abbreviation_name" jdbcType="VARCHAR" property="abbreviationName" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, full_name, abbreviation_name, code, contact, phone, create_time, update_time, 
    unique_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.PointSupplierExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from point_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from point_supplier
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from point_supplier
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.PointSupplierExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from point_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.PointSupplier">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into point_supplier (id, full_name, abbreviation_name, 
      code, contact, phone, 
      create_time, update_time, unique_id
      )
    values (#{id,jdbcType=VARCHAR}, #{fullName,jdbcType=VARCHAR}, #{abbreviationName,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{uniqueId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.PointSupplier">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into point_supplier
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fullName != null">
        full_name,
      </if>
      <if test="abbreviationName != null">
        abbreviation_name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="uniqueId != null">
        unique_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null">
        #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="abbreviationName != null">
        #{abbreviationName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.PointSupplierExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from point_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    update point_supplier
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fullName != null">
        full_name = #{record.fullName,jdbcType=VARCHAR},
      </if>
      <if test="record.abbreviationName != null">
        abbreviation_name = #{record.abbreviationName,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.contact != null">
        contact = #{record.contact,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.uniqueId != null">
        unique_id = #{record.uniqueId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    update point_supplier
    set id = #{record.id,jdbcType=VARCHAR},
      full_name = #{record.fullName,jdbcType=VARCHAR},
      abbreviation_name = #{record.abbreviationName,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      contact = #{record.contact,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      unique_id = #{record.uniqueId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.PointSupplier">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    update point_supplier
    <set>
      <if test="fullName != null">
        full_name = #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="abbreviationName != null">
        abbreviation_name = #{abbreviationName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uniqueId != null">
        unique_id = #{uniqueId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.PointSupplier">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    update point_supplier
    set full_name = #{fullName,jdbcType=VARCHAR},
      abbreviation_name = #{abbreviationName,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      unique_id = #{uniqueId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into point_supplier
    (id, full_name, abbreviation_name, code, contact, phone, create_time, update_time, 
      unique_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.fullName,jdbcType=VARCHAR}, #{item.abbreviationName,jdbcType=VARCHAR}, 
        #{item.code,jdbcType=VARCHAR}, #{item.contact,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.uniqueId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 17:42:53 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into point_supplier (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'full_name'.toString() == column.value">
          #{item.fullName,jdbcType=VARCHAR}
        </if>
        <if test="'abbreviation_name'.toString() == column.value">
          #{item.abbreviationName,jdbcType=VARCHAR}
        </if>
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'contact'.toString() == column.value">
          #{item.contact,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'unique_id'.toString() == column.value">
          #{item.uniqueId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>