<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.SkuOfferingInfoMapperExt">


    <select id="pageQueryProductBackList" resultType="com.chinamobile.retail.pojo.mapper.ProductBackListDO">
        SELECT
        sku.id skuId,
        sku.offering_code skuOfferingCode,
        sku.offering_name skuOfferingName,
        spu.offering_name spuOfferingName,
        sku.price,
        IF(p.delete_time is NULL,ps.full_name,NULL) pointSupplier,
        srr.partner_role_id partnerRoleId,
        srr.point_percent pointPercent,
        srr.point_limit pointLimit,
        sku.point_status pointStatus,
        GROUP_CONCAT(srt.province_code) provinceCode,
        GROUP_CONCAT(srt.city_code) cityCode
        FROM
        sku_offering_info sku
        LEFT JOIN spu_offering_info spu ON spu.offering_code = sku.spu_code
        LEFT JOIN category_info ca ON ca.spu_id = spu.id
        LEFT JOIN supplier_associate_product p ON p.product_id = sku.offering_code
        LEFT JOIN point_supplier ps ON ps.id = p.supplier_id
        LEFT JOIN sku_role_relation srr ON srr.sku_id = sku.id
        LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code
        WHERE sku.delete_time is NULL AND sku.offering_status = '1' AND spu.delete_time is NULL
        <if test="partnerRoleId != null">
            and srr.partner_role_id = #{partnerRoleId}
        </if>
        <if test="pointStatus != null and pointStatus.size() != 0">
            and sku.point_status in
            <foreach collection="pointStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="pointSupplierId != null and pointSupplierId.size() != 0">
            and ps.id in
            <foreach collection="pointSupplierId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="productType != null and productType.size() != 0">
            and ca.offering_class in
            <foreach collection="productType" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryParam != null and queryParam != ''">
            and (sku.offering_name like concat ('%',#{queryParam},'%') or spu.offering_name like concat ('%',#{queryParam},'%'))
        </if>
        <if test="provinceCodeList != null and provinceCodeList.size != 0">
            and (srt.province_code in
            <foreach collection="provinceCodeList" item="provinceCode" index="index" open="(" close=")" separator=",">
                #{provinceCode}
            </foreach>
            or srt.province_code = '000')
        </if>
        <if test="cityCodeList != null and cityCodeList.size != 0">
            and IFNULL(srt.city_code,'') in
            <foreach collection="cityCodeList" item="cityCode" index="index" open="(" close=")" separator=",">
                #{cityCode}
            </foreach>
        </if>
        group by sku.offering_code,srr.partner_role_id
        order by sku.update_time desc
        limit #{start},#{pageSize}
    </select>

    <select id="getRealProductName" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT
        min( st.real_product_name )
    FROM
        standard_service st
        JOIN atom_std_service asr ON asr.std_service_id = st.id
        JOIN atom_offering_info atom ON asr.atom_id = atom.id
    WHERE
        atom.sku_code = #{skuOfferingCode}
    </select>

    <select id="pageCountProductBackList" resultType="java.lang.Integer">
    select count(*) from
        (SELECT
            sku.id
        FROM
        sku_offering_info sku
        LEFT JOIN spu_offering_info spu ON spu.offering_code = sku.spu_code
        LEFT JOIN category_info ca ON ca.spu_id = spu.id
        LEFT JOIN supplier_associate_product p ON p.product_id = sku.offering_code
        LEFT JOIN point_supplier ps ON ps.id = p.supplier_id
        LEFT JOIN sku_role_relation srr ON srr.sku_id = sku.id
        LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code

        WHERE sku.delete_time is NULL AND sku.offering_status = '1' AND spu.delete_time is NULL
        <if test="partnerRoleId != null">
            and srr.partner_role_id = #{partnerRoleId}
        </if>
        <if test="pointStatus != null and pointStatus.size() != 0">
            and sku.point_status in
            <foreach collection="pointStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="pointSupplierId != null and pointSupplierId.size() != 0">
            and ps.id in
            <foreach collection="pointSupplierId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="productType != null and productType.size() != 0">
            and ca.offering_class in
            <foreach collection="productType" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryParam != null and queryParam != ''">
            and (sku.offering_name like concat ('%',#{queryParam},'%') or spu.offering_name like concat ('%',#{queryParam},'%'))
        </if>
        <if test="provinceCodeList != null and provinceCodeList.size != 0">
            and (srt.province_code in
            <foreach collection="provinceCodeList" item="provinceCode" index="index" open="(" close=")" separator=",">
                #{provinceCode}
            </foreach>
            or srt.province_code = '000')
        </if>
        <if test="cityCodeList != null and cityCodeList.size != 0">
            and IFNULL(srt.city_code,'') in
            <foreach collection="cityCodeList" item="cityCode" index="index" open="(" close=")" separator=",">
                #{cityCode}
            </foreach>
        </if>
        group by sku.offering_code,srr.partner_role_id ) temp
    </select>

    <select id="getSkuPointList" resultType="com.chinamobile.retail.pojo.mapper.SkuPointListDO">
    SELECT
        sku.offering_code skuOfferingCode,
        sku.offering_name skuOfferingName,
        sku.price,
        srr.point_percent pointPercent,
        srr.point_limit pointLimit,
        spu.img_url imgUrl
    FROM
        sku_offering_info sku
        LEFT JOIN spu_offering_info spu ON sku.spu_code = spu.offering_code
        LEFT JOIN sku_role_relation srr ON srr.sku_id = sku.id AND srr.partner_role_id = #{roleType}
        LEFT JOIN sku_release_target srt ON sku.offering_code = srt.sku_offering_code
    WHERE
        spu.offering_code = #{spuCode}
        AND sku.delete_time IS NULL
        AND sku.offering_status = '1'
        AND sku.point_status = 2
        AND spu.delete_time IS NULL
        AND spu.offering_status = '1'
        <if test="cityCode != null and cityCode != ''">
            and  ( srt.province_code = 000 OR (srt.province_code = #{provinceCode} and srt.city_code = #{cityCode}))
        </if>
        <if test="cityCode == null or cityCode == ''">
            and  ( srt.province_code = 000 OR srt.province_code = #{provinceCode} )
        </if>
    </select>

</mapper>