<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.SpuCoreComponentMapperExt">

    <!-- 分页查询SPU核心部件配置列表 -->
    <select id="pageCoreComponentList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageCoreComponentListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO">
        select
        a.id as id,
        spu.offering_code as spuCode,
        spu.offering_name as spuName,
        c.offering_class as spuOfferingClass,
        CASE
        WHEN c.offering_class = 'A04' THEN 'DICT产品增值'
        WHEN c.offering_class = 'A06' THEN '联合销售'
        WHEN c.offering_class = 'A07' THEN '合同履约'
        WHEN c.offering_class = 'A08' THEN 'OneNET独立'
        WHEN c.offering_class = 'A09' THEN 'OnePark独立服务'
        WHEN c.offering_class = 'A10' THEN 'OneTraffic独立服务'
        WHEN c.offering_class = 'A11' THEN '卡+X'
        WHEN c.offering_class = 'A12' THEN '行车卫士标准产品'
        WHEN c.offering_class = 'A13' THEN '软件服务'
        WHEN c.offering_class = 'A14' THEN 'OneCyber标准产品'
        WHEN c.offering_class = 'A15' THEN '千里眼独立服务'
        WHEN c.offering_class = 'A16' THEN '和对讲独立服务'
        WHEN c.offering_class = 'A17' THEN '云视讯独立服务'
        ELSE '其他'
        END as spuOfferingClassName,
        spu.offering_status as spuOfferingStatus,
        CASE
        WHEN a.id is not null THEN true
        ELSE false
        END as configStatus,
        u.name as creatorName,
        a.create_time as createTime,
        a.audit_status as auditStatus,
        a.status as status
        from spu_offering_info spu
        left join spu_core_component a on a.spu_code = spu.offering_code and a.is_delete = 0
        left join category_info c on c.spu_id = spu.id
        left join user u on u.user_id = a.create_uid

        <where>
            spu.offering_status = '1'
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="auditStatus != null ">
                and a.audit_status = #{auditStatus}
            </if>
            <if test="configStatus != null and configStatus == true">
                and a.id is not null
            </if>
            <if test="configStatus != null and configStatus == false">
                and a.id is null
            </if>
            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and a.audit_status in
                <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>

            <if test="spuCode != null and spuCode != ''">
                and spu.offering_code = #{spuCode}
            </if>

            <if test="spuName != null and spuName != ''">
                and spu.offering_name like concat('%',#{spuName}, '%')
            </if>
            <if test="spuOfferingClass != null and spuOfferingClass != ''">
                and c.offering_class = #{spuOfferingClass}
            </if>
            <if test="spuOfferingStatus != null and spuOfferingStatus != ''">
                and spu.offering_status = #{spuOfferingStatus}
            </if>

            <if test="createUserName != null and createUserName != ''">
                and u.name like concat('%',#{createUserName}, '%')
            </if>


        </where>
        <if test="auditStatusList != null ">
            order by a.create_time desc
        </if>
        <if test="auditStatusList == null ">
            order by spu.update_time desc
        </if>
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序首页总数 -->
    <select id="countCoreComponentList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageCoreComponentListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select
        spu.id
        from spu_offering_info spu
        left join spu_core_component a on a.spu_code = spu.offering_code and a.is_delete = 0
        left join category_info c on c.spu_id = spu.id
        left join user u on u.user_id = a.create_uid

        <where>
            spu.offering_status = '1'
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="auditStatus != null ">
                and a.audit_status = #{auditStatus}
            </if>
            <if test="configStatus != null and configStatus == true">
                and a.id is not null
            </if>
            <if test="configStatus != null and configStatus == false">
                and a.id is null
            </if>
            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and a.audit_status in
                <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>

            <if test="spuCode != null and spuCode != ''">
                and spu.offering_code = #{spuCode}
            </if>

            <if test="spuName != null and spuName != ''">
                and spu.offering_name like concat('%',#{spuName}, '%')
            </if>
            <if test="spuOfferingClass != null and spuOfferingClass != ''">
                and c.offering_class = #{spuOfferingClass}
            </if>
            <if test="spuOfferingStatus != null and spuOfferingStatus != ''">
                and spu.offering_status = #{spuOfferingStatus}
            </if>

            <if test="createUserName != null and createUserName != ''">
                and u.name like concat('%',#{createUserName}, '%')
            </if>

        </where>

        ) t
    </select>

    <!-- 分页查询SPU核心部件配置列表 -->
    <select id="getCoreComponentDetail" resultType="com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO">
        select a.id                  as id,
               spu.offering_code     as spuCode,
               spu.offering_name     as spuName,
               a.core_component_name as coreComponentName,
               a.core_component_img  as coreComponentImg,
               u.name                as creatorName,
               a.create_time         as createTime,
               a.audit_status        as auditStatus,
               a.status              as status
        from spu_offering_info spu
                 left join spu_core_component a on a.spu_code = spu.offering_code and a.is_delete = 0
                 left join category_info c on c.spu_id = spu.id
                 left join user u on u.user_id = a.create_uid
        where spu.offering_code = #{spuCode}
    </select>


    <select id="getUserName" parameterType="String" resultType="String">
        select u.name
        from user u
        where u.user_id = #{userId}
    </select>

    <select id="getSkuCoreComponentList" resultType="com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO">

        SELECT sku.spu_code           AS spuCode,
               sku.offering_code      AS skuCode,
               sku.offering_name      AS skuName,
               sc.core_component_name AS coreComponentName
        FROM sku_offering_info sku
                 LEFT JOIN sku_core_component sc
                           ON sku.spu_code = sc.spu_code AND sku.offering_code = sc.sku_code AND sc.is_delete = 0
        WHERE sku.spu_code = #{spuCode}
    </select>

    <!-- 搜索SPU核心部件配置列表 -->
    <select id="searchSpu"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO">
        select
        spu.offering_code as spuCode,
        spu.offering_name as spuName,
        a.core_component_name as coreComponentName,
        a.core_component_img as coreComponentImg
        from spu_core_component a
        inner join spu_offering_info spu on a.spu_code = spu.offering_code

        <where>
            and a.is_delete = 0
            <if test="keyword != null and keyword != ''">
                and (spu.offering_code = #{keyword}
                or spu.offering_name like concat('%',#{keyword}, '%'))
            </if>

        </where>
        order by a.create_time desc
    </select>

    <select id="searchSku" resultType="com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO">

        SELECT sku.spu_code AS spuCode,
        sku.offering_code AS skuCode,
        sku.offering_name AS skuName,
        sc.core_component_name AS coreComponentName
        FROM sku_core_component sc
        INNER JOIN sku_offering_info sku ON sku.spu_code = sc.spu_code AND sku.offering_code = sc.sku_code
        WHERE sc.is_delete = 0
        <if test="keyword != null and keyword != ''">
            and (sku.offering_code = #{keyword}
            or sku.offering_name like concat('%',#{keyword}, '%'))
        </if>

    </select>

    <select id="getSkuReleaseTargetList" resultType="com.chinamobile.retail.pojo.vo.miniprogram.SkuReleaseTargetVO">
        SELECT
        srt.sku_offering_code AS skuCode,
        srt.province_code AS provinceCode,
        srt.city_code AS cityCode,
        cpi.mall_name AS provinceName,
        cci.mall_name AS cityName
        FROM sku_release_target srt
        LEFT JOIN contract_province_info cpi ON srt.province_code = cpi.mall_code
        LEFT JOIN contract_city_info cci ON srt.city_code = cci.mall_code
        <where>
            <if test="skuList != null and skuList.size() != 0">
                srt.sku_offering_code in
                <foreach collection="skuList" item="skuCode" index="index" open="(" close=")" separator=",">
                    #{skuCode}
                </foreach>
            </if>
        </where>


    </select>

</mapper>
