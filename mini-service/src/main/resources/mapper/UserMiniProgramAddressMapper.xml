<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.UserMiniProgramAddressMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.UserMiniProgramAddress">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="addr1" jdbcType="VARCHAR" property="addr1" />
    <result column="addr2" jdbcType="VARCHAR" property="addr2" />
    <result column="addr3" jdbcType="VARCHAR" property="addr3" />
    <result column="addr4" jdbcType="VARCHAR" property="addr4" />
    <result column="usaddr" jdbcType="VARCHAR" property="usaddr" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="isDefault" jdbcType="INTEGER" property="isdefault" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, user_id, addr1, addr2, addr3, addr4, usaddr, name, phone, isDefault, create_time, 
    update_time, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddressExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_mini_program_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_mini_program_address
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_mini_program_address
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddressExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_mini_program_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddress">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_mini_program_address (id, user_id, addr1, 
      addr2, addr3, addr4, 
      usaddr, name, phone, 
      isDefault, create_time, update_time, 
      is_delete)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{addr1,jdbcType=VARCHAR}, 
      #{addr2,jdbcType=VARCHAR}, #{addr3,jdbcType=VARCHAR}, #{addr4,jdbcType=VARCHAR}, 
      #{usaddr,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{isdefault,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddress">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_mini_program_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="addr1 != null">
        addr1,
      </if>
      <if test="addr2 != null">
        addr2,
      </if>
      <if test="addr3 != null">
        addr3,
      </if>
      <if test="addr4 != null">
        addr4,
      </if>
      <if test="usaddr != null">
        usaddr,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="isdefault != null">
        isDefault,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="isdefault != null">
        #{isdefault,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddressExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_mini_program_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_mini_program_address
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.addr1 != null">
        addr1 = #{record.addr1,jdbcType=VARCHAR},
      </if>
      <if test="record.addr2 != null">
        addr2 = #{record.addr2,jdbcType=VARCHAR},
      </if>
      <if test="record.addr3 != null">
        addr3 = #{record.addr3,jdbcType=VARCHAR},
      </if>
      <if test="record.addr4 != null">
        addr4 = #{record.addr4,jdbcType=VARCHAR},
      </if>
      <if test="record.usaddr != null">
        usaddr = #{record.usaddr,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.isdefault != null">
        isDefault = #{record.isdefault,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_mini_program_address
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      addr1 = #{record.addr1,jdbcType=VARCHAR},
      addr2 = #{record.addr2,jdbcType=VARCHAR},
      addr3 = #{record.addr3,jdbcType=VARCHAR},
      addr4 = #{record.addr4,jdbcType=VARCHAR},
      usaddr = #{record.usaddr,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      isDefault = #{record.isdefault,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddress">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_mini_program_address
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        addr1 = #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        addr2 = #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        addr3 = #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        addr4 = #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        usaddr = #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="isdefault != null">
        isDefault = #{isdefault,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.UserMiniProgramAddress">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_mini_program_address
    set user_id = #{userId,jdbcType=VARCHAR},
      addr1 = #{addr1,jdbcType=VARCHAR},
      addr2 = #{addr2,jdbcType=VARCHAR},
      addr3 = #{addr3,jdbcType=VARCHAR},
      addr4 = #{addr4,jdbcType=VARCHAR},
      usaddr = #{usaddr,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      isDefault = #{isdefault,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_mini_program_address
    (id, user_id, addr1, addr2, addr3, addr4, usaddr, name, phone, isDefault, create_time, 
      update_time, is_delete)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.addr1,jdbcType=VARCHAR}, 
        #{item.addr2,jdbcType=VARCHAR}, #{item.addr3,jdbcType=VARCHAR}, #{item.addr4,jdbcType=VARCHAR}, 
        #{item.usaddr,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.isdefault,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isDelete,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 20 09:14:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_mini_program_address (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'addr1'.toString() == column.value">
          #{item.addr1,jdbcType=VARCHAR}
        </if>
        <if test="'addr2'.toString() == column.value">
          #{item.addr2,jdbcType=VARCHAR}
        </if>
        <if test="'addr3'.toString() == column.value">
          #{item.addr3,jdbcType=VARCHAR}
        </if>
        <if test="'addr4'.toString() == column.value">
          #{item.addr4,jdbcType=VARCHAR}
        </if>
        <if test="'usaddr'.toString() == column.value">
          #{item.usaddr,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'isDefault'.toString() == column.value">
          #{item.isdefault,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_delete'.toString() == column.value">
          #{item.isDelete,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>