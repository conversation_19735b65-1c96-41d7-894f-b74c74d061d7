package com.chinamobile.retail.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.dao.ext.SpuCoreComponentMapperExt;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.ShareUrlVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@SpringBootTest
class ProductServiceTest {

    @Resource
    private IProductService productService;

    @Resource
    private SpuCoreComponentMapperExt spuCoreComponentMapperExt;

    @Resource
    private ICoreComponentService coreComponentService;

    @Test
    void pageMiniProgramProduct() {
        PageProductParam param = new PageProductParam();
        param.setPageNum(1);
        param.setPageSize(10);
        param.setThirdDirectoryId("60103202503030326");
        param.setUserId("1331559793365991537");
        PageData<MiniProgramProductListVO> pageData = productService.pageMiniProgramProduct(param, "1331559793365991537");
        System.out.println(JSON.toJSONString(pageData));

    }

    @Test
    void productDetail() {
        ProductDetailParam param = new ProductDetailParam();
        param.setProvinceCode("200");
        param.setSpuCode("1000009582");
        MiniProgramProductDetailVO miniProgramProductDetailVO = productService.productDetail(param,null);
        System.out.println(JSON.toJSONString(miniProgramProductDetailVO));
    }

    @Test
    void productDetailByActivityId() {
        MiniProgramProductDetailVO miniProgramProductDetailVO = productService.productDetailByActivityId("1000007483","1042119485098143744", null);
        System.out.println(JSON.toJSONString(miniProgramProductDetailVO));
    }

    @Test
    void getShareUrl() {
        ShareUrlVO shareUrlVO = productService.getShareUrl("1000007483", "123456");
        System.out.println(JSON.toJSONString(shareUrlVO));
    }

    @Test
    void webSearchProduct() {
        WebSearchProductParam param = new WebSearchProductParam();
        param.setPageNum(1);
        param.setPageSize(10);
        param.setProvinceCode("230");
        PageData<MiniProgramProductListVO> pageData = productService.searchProductForWeb(param);
        System.out.println(JSON.toJSONString(pageData));
    }

    @Test
    void webSearchSceneProduct() {
        WebSearchProductParam param = new WebSearchProductParam();
        param.setPageNum(2);
        param.setPageSize(10);
        param.setKeyWord("1");
        PageData<MiniProgramProductListVO> pageData = productService.searchProductForWebScene(param);
        System.out.println(JSON.toJSONString(pageData));
    }

    @Test
    void searchProduct() {
        SearchProductParam param = new SearchProductParam();
        param.setSearchWord("千里眼");
        param.setUserId("1331559793365991650");
        PageData<MiniProgramProductListVO> pageData = productService.searchProduct(param);
        System.out.println(JSON.toJSONString(pageData));
    }

    @Test
    void coreList() {
        PageCoreComponentListParam param = new PageCoreComponentListParam();
        param.setAuditStatusList(Arrays.asList(1,2,3));
        List<SpuCoreComponentVO> data = spuCoreComponentMapperExt.pageCoreComponentList(param);
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    void getCoreComponentDetail() {
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId("1266337974619029505");
        SpuCoreComponentVO data = coreComponentService.getCoreComponentDetail("3000133483",loginIfo4Redis);
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    void searchSku() {
        List<SkuCoreComponentVO> data = spuCoreComponentMapperExt.searchSku("s");
        System.out.println(JSON.toJSONString(data));
    }
}
