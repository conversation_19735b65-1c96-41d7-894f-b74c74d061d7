package com.chinamobile.retail.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.dao.MiniProgramSaleYearReportMapper;
import com.chinamobile.retail.dao.UserMiniProgramMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunAward;
import com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReport;
import com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportExample;
import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.param.miniprogram.LoginOneClickParam;
import com.chinamobile.retail.pojo.param.miniprogram.UserAgreementListParam;
import com.chinamobile.retail.pojo.vo.UserCenterVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO;
import com.chinamobile.retail.pojo.vo.miniprogram.UserAgreementListVO;
import com.chinamobile.retail.service.impl.MiniActivityServiceImpl;
import com.chinamobile.retail.service.impl.MiniProgramUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Slf4j
class MiniProgramUserServiceTest {

    @Resource
    private MiniProgramUserService miniProgramUserService;
    @Resource
    MiniActivityServiceImpl miniActivityServiceImpl;
    @Resource
    private MiniProgramSaleYearReportMapper miniProgramSaleYearReportMapper;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Test
    void migrateFromManageAndCustomer() {
        String result = miniProgramUserService.migrateFromManageAndCustomer();
        System.out.println(result);
    }

    @Test
    void userAgreement() {
        PageData<UserAgreementListVO> userAgreementListVOS = miniProgramUserService.userAgreementList(new UserAgreementListParam(), true);
        System.out.println(JSON.toJSON(userAgreementListVOS));
    }

    @Test
    void getUserCenterInfo() {
        UserCenterVO userCenterVO = miniProgramUserService.getUserCenterInfo("1266337974619029504");
        System.out.println(JSON.toJSON(userCenterVO));
    }
    @Test
    void testDraw() {
        List<MiniProgramActivityWeeklyFunAward> prizes = new ArrayList<>();
        for(int i=5;i>0;i--){
            MiniProgramActivityWeeklyFunAward  miniProgramActivityWeeklyFunAward=new MiniProgramActivityWeeklyFunAward();
            miniProgramActivityWeeklyFunAward.setId(i+"");
            miniProgramActivityWeeklyFunAward.setProbability(i*5);
            prizes.add(miniProgramActivityWeeklyFunAward);
        }
//        miniProgramActivityWeeklyFunAward.
          String result =miniActivityServiceImpl.draw(prizes);
          log.info("result:{}",result);

    }

    @Test
    void getLoginSmsValidCode() {
        miniProgramUserService.getLoginSmsValidCode("KUlFFO8D6U5t0Uw6JH98PSFSHXyNSr4uhzWUrONBiMcIwg3YHDplHckuH80sK0IvoBXG5oJFBTjvj4d9fbpE09oWbjDeWx5Hq2BLD17zRNqNUh1CjKiGoCGzjjJ2lqJuIU6GFdnD9otpPOhDACVgrEc5v2JOgZEdwowDanqLONFWxHLyJ9OG9/Tm3QppMbaB/ca0oO6G/SPiW8hORl1zzpIoUzpmY3ga2aq3qy6G+OPhqGaqsd9vpi6qtglzG/cKinx5AqIOfmfKJ64wmFZUEPJ4FeJC7umYScYaEWfqlIC+pc2G7TkfmMFEgsXyxKwUL4nWuXvos+cpbK78ZOyWrA==");
    }

    @Test
    void loginOneClick() {
        LoginOneClickParam param = new LoginOneClickParam();
        param.setToken("H5HTTPS3a393cf68b0a7ccd5a8bda211f07f79e799da43c10c14707af8277d97dd5bb09");
        param.setUserInformation("iPhone%40%408.0.52%40%40iOS%2017.6.1%40%40b3d898d0f62ca5cb9173795ed1936fd5");
        miniProgramUserService.loginOneClick(param);
    }

    @Test
    void generateSaleReport() {
        miniProgramUserService.generateSaleReport(2024);
    }

    @Test
    void getSaleYearReport() {
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setMallUserId("1710142136453347639");
        SaleYearReportVO saleYearReportVO = miniProgramUserService.getSaleYearReport(2024, loginIfo4Redis);
        System.out.println(JSON.toJSONString(saleYearReportVO));
    }

    @Test
    void fakeSaleReport() throws ParseException {
        List<MiniProgramSaleYearReport> reports = miniProgramSaleYearReportMapper.selectByExample(
                new MiniProgramSaleYearReportExample().createCriteria()
                        .andUserIdEqualTo("11111111111")
                        .andYearEqualTo(2024)
                        .example()
        );
        if (CollectionUtils.isEmpty(reports)) {
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MiniProgramSaleYearReport report = reports.get(0);
        report.setRegisterIndex(107986L);
        report.setCustomerCount(32L);
        report.setOrderTotalPrice(37861000L);
        report.setOrderTotalCount(32L);
        report.setSaleRanking(158115L);
        report.setFirstOrderTime(sdf.parse("2024-01-04 13:00:00"));
        report.setFirstOrderSpuName("千里眼室内监控服务智能摄像头C31");
        report.setSpuTotalCount(15L);
        report.setBestSaleSpuName("千里眼室内监控服务智能摄像头C31");
        report.setBestSaleSpuOrderCount(12L);
        report.setBestSaleSpuPrompt("千里眼");
        report.setYearLatestOrderTime(sdf.parse("2024-05-24 23:00:00"));
        report.setCustomerTotalCount(32L);
        report.setCustomerCountIndividual(20L);
        report.setCustomerCountGroup(12L);
        report.setBestSaleSpuNameIndividual("千里眼室内监控服务智能摄像头C31");
        report.setBestSaleSpuNameGroup("智能出入安防可视版服务");
        report.setJoinMallActivityCount(10);
        report.setJoinMallActivityRewardCount(1);
        report.setOrderTotalCountPrepay(6L);
        report.setOrderTotalCountAfterpay(3L);
        report.setOrderTotalCountMix(3L);
        report.setYearLastOrderTime(sdf.parse("2024-12-27 15:00:00"));
        miniProgramSaleYearReportMapper.updateByPrimaryKeySelective(report);
    }


    @Test
    void addYGUser() {
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        userMiniProgram.setId(BaseServiceUtils.getId());
        userMiniProgram.setName("赵勇");
        userMiniProgram.setPhone("18323003535");
        userMiniProgram.setRoleType("10");
        userMiniProgram.setStatus("1");
        userMiniProgram.setCreateTime(new Date());
        userMiniProgram.setUpdateTime(new Date());

        userMiniProgramMapper.insertSelective(userMiniProgram);
    }
}
