<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.RoleAuthMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.RoleAuth">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="role_id" jdbcType="VARCHAR" property="roleId" />
    <id column="auth_id" jdbcType="VARCHAR" property="authId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    role_id, auth_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuthExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from role_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuth">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from role_auth
    where role_id = #{roleId,jdbcType=VARCHAR}
      and auth_id = #{authId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuthExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from role_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuth">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into role_auth (role_id, auth_id)
    values (#{roleId,jdbcType=VARCHAR}, #{authId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuth">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into role_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="authId != null">
        auth_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="authId != null">
        #{authId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.RoleAuthExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from role_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    update role_auth
    <set>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.authId != null">
        auth_id = #{record.authId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    update role_auth
    set role_id = #{record.roleId,jdbcType=VARCHAR},
      auth_id = #{record.authId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into role_auth
    (role_id, auth_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId,jdbcType=VARCHAR}, #{item.authId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 15:49:03 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into role_auth (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'role_id'.toString() == column.value">
          #{item.roleId,jdbcType=VARCHAR}
        </if>
        <if test="'auth_id'.toString() == column.value">
          #{item.authId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>