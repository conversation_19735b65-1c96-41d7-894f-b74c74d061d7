<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.DataPermissionMapperExt">
    <select id="listByRoleId" resultType="com.chinamobile.iot.sc.pojo.dto.DataPermissionDTO">
        SELECT
          dp.id id,
          dp.name name,
          dp.auth_code authCode,
          dp.system system,
          dp.create_time createTime,
          dp.update_time updateTime
        FROM role_data_permission rdp
        LEFT JOIN data_permission dp on dp.id = rdp.data_permission_id
        WHERE rdp.role_id = #{roleId}
    </select>
</mapper>