package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 大屏用户表
 *
 * <AUTHOR>
public class UserScreen implements Serializable {
    /**
     * 主键id
     *
     * Corresponding to the database column supply_chain..user_screen.id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String id;

    /**
     * 姓名
     *
     * Corresponding to the database column supply_chain..user_screen.name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String name;

    /**
     * 密码
     *
     * Corresponding to the database column supply_chain..user_screen.pwd
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String pwd;

    /**
     * 电话
     *
     * Corresponding to the database column supply_chain..user_screen.phone
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String phone;

    /**
     * 邮箱
     *
     * Corresponding to the database column supply_chain..user_screen.email
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String email;

    /**
     * 所属公司
     *
     * Corresponding to the database column supply_chain..user_screen.company
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String company;

    /**
     * 角色id
     *
     * Corresponding to the database column supply_chain..user_screen.role_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String roleId;

    /**
     * 0:管理级，1：省级
     *
     * Corresponding to the database column supply_chain..user_screen.account_level
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Integer accountLevel;

    /**
     * 是否停用
     *
     * Corresponding to the database column supply_chain..user_screen.is_cancel
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Boolean isCancel;

    /**
     * 是否注销
     *
     * Corresponding to the database column supply_chain..user_screen.is_logoff
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Boolean isLogoff;

    /**
     * 是否是超级管理员 0：不是 1：是
     *
     * Corresponding to the database column supply_chain..user_screen.is_admin
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Boolean isAdmin;

    /**
     * 账号所属省份
     *
     * Corresponding to the database column supply_chain..user_screen.province
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String province;

    /**
     * 创建人
     *
     * Corresponding to the database column supply_chain..user_screen.creator
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String creator;

    /**
     * 合作伙伴公司id
     *
     * Corresponding to the database column supply_chain..user_screen.company_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String companyId;

    /**
     * 合作伙伴公司名称
     *
     * Corresponding to the database column supply_chain..user_screen.company_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String companyName;

    /**
     * 单位类型：1：非省公司 2：省公司',
     *
     * Corresponding to the database column supply_chain..user_screen.company_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String companyType;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..user_screen.create_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..user_screen.update_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Date updateTime;

    /**
     * 账号来源（用户对象），iot-物联网公司（含外协），other-非物联网公司
     *
     * Corresponding to the database column supply_chain..user_screen.user_from
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String userFrom;

    /**
     * 账号类型，normal-正式账号，test-测试账号
     *
     * Corresponding to the database column supply_chain..user_screen.user_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String userType;

    /**
     * 用户种类，用于物联网公司用户区分内部用户跟外协，1 内部用户 2 外部用户
     *
     * Corresponding to the database column supply_chain..user_screen.iot_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Integer iotType;

    /**
     * 统一用户平台用户状态，0：正常，1：锁定（账号不可用，但可恢复），2：未启用（账号信息不完整 不可用），3：注销（此账号从此不可恢复）
     *
     * Corresponding to the database column supply_chain..user_screen.unified_status
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private Integer unifiedStatus;

    /**
     * 部门组织ID
     *
     * Corresponding to the database column supply_chain..user_screen.department_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String departmentId;

    /**
     * 部门组织名称
     *
     * Corresponding to the database column supply_chain..user_screen.department_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String departmentName;

    /**
     * 旧工号
     *
     * Corresponding to the database column supply_chain..user_screen.old_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String oldJobNumber;

    /**
     * 新工号
     *
     * Corresponding to the database column supply_chain..user_screen.new_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String newJobNumber;

    /**
     * 省份编码，多省份以逗号分隔
     *
     * Corresponding to the database column supply_chain..user_screen.be_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String beId;

    /**
     * 地市编码，多地市以逗号分隔
     *
     * Corresponding to the database column supply_chain..user_screen.location
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String location;

    /**
     * 地市名称,多个地市以逗号分隔
     *
     * Corresponding to the database column supply_chain..user_screen.location_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private String locationName;

    /**
     * Corresponding to the database table supply_chain..user_screen
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user_screen.id
     *
     * @return the value of supply_chain..user_screen.id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.id
     *
     * @param id the value for supply_chain..user_screen.id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.name
     *
     * @return the value of supply_chain..user_screen.name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.name
     *
     * @param name the value for supply_chain..user_screen.name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.pwd
     *
     * @return the value of supply_chain..user_screen.pwd
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getPwd() {
        return pwd;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withPwd(String pwd) {
        this.setPwd(pwd);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.pwd
     *
     * @param pwd the value for supply_chain..user_screen.pwd
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setPwd(String pwd) {
        this.pwd = pwd == null ? null : pwd.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.phone
     *
     * @return the value of supply_chain..user_screen.phone
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.phone
     *
     * @param phone the value for supply_chain..user_screen.phone
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.email
     *
     * @return the value of supply_chain..user_screen.email
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withEmail(String email) {
        this.setEmail(email);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.email
     *
     * @param email the value for supply_chain..user_screen.email
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.company
     *
     * @return the value of supply_chain..user_screen.company
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getCompany() {
        return company;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCompany(String company) {
        this.setCompany(company);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.company
     *
     * @param company the value for supply_chain..user_screen.company
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.role_id
     *
     * @return the value of supply_chain..user_screen.role_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.role_id
     *
     * @param roleId the value for supply_chain..user_screen.role_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId == null ? null : roleId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.account_level
     *
     * @return the value of supply_chain..user_screen.account_level
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Integer getAccountLevel() {
        return accountLevel;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withAccountLevel(Integer accountLevel) {
        this.setAccountLevel(accountLevel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.account_level
     *
     * @param accountLevel the value for supply_chain..user_screen.account_level
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setAccountLevel(Integer accountLevel) {
        this.accountLevel = accountLevel;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.is_cancel
     *
     * @return the value of supply_chain..user_screen.is_cancel
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Boolean getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withIsCancel(Boolean isCancel) {
        this.setIsCancel(isCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.is_cancel
     *
     * @param isCancel the value for supply_chain..user_screen.is_cancel
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setIsCancel(Boolean isCancel) {
        this.isCancel = isCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.is_logoff
     *
     * @return the value of supply_chain..user_screen.is_logoff
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Boolean getIsLogoff() {
        return isLogoff;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withIsLogoff(Boolean isLogoff) {
        this.setIsLogoff(isLogoff);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.is_logoff
     *
     * @param isLogoff the value for supply_chain..user_screen.is_logoff
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setIsLogoff(Boolean isLogoff) {
        this.isLogoff = isLogoff;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.is_admin
     *
     * @return the value of supply_chain..user_screen.is_admin
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Boolean getIsAdmin() {
        return isAdmin;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withIsAdmin(Boolean isAdmin) {
        this.setIsAdmin(isAdmin);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.is_admin
     *
     * @param isAdmin the value for supply_chain..user_screen.is_admin
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setIsAdmin(Boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.province
     *
     * @return the value of supply_chain..user_screen.province
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getProvince() {
        return province;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withProvince(String province) {
        this.setProvince(province);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.province
     *
     * @param province the value for supply_chain..user_screen.province
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.creator
     *
     * @return the value of supply_chain..user_screen.creator
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCreator(String creator) {
        this.setCreator(creator);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.creator
     *
     * @param creator the value for supply_chain..user_screen.creator
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.company_id
     *
     * @return the value of supply_chain..user_screen.company_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getCompanyId() {
        return companyId;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCompanyId(String companyId) {
        this.setCompanyId(companyId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.company_id
     *
     * @param companyId the value for supply_chain..user_screen.company_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.company_name
     *
     * @return the value of supply_chain..user_screen.company_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCompanyName(String companyName) {
        this.setCompanyName(companyName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.company_name
     *
     * @param companyName the value for supply_chain..user_screen.company_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.company_type
     *
     * @return the value of supply_chain..user_screen.company_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getCompanyType() {
        return companyType;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCompanyType(String companyType) {
        this.setCompanyType(companyType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.company_type
     *
     * @param companyType the value for supply_chain..user_screen.company_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCompanyType(String companyType) {
        this.companyType = companyType == null ? null : companyType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.create_time
     *
     * @return the value of supply_chain..user_screen.create_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.create_time
     *
     * @param createTime the value for supply_chain..user_screen.create_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.update_time
     *
     * @return the value of supply_chain..user_screen.update_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.update_time
     *
     * @param updateTime the value for supply_chain..user_screen.update_time
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.user_from
     *
     * @return the value of supply_chain..user_screen.user_from
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getUserFrom() {
        return userFrom;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withUserFrom(String userFrom) {
        this.setUserFrom(userFrom);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.user_from
     *
     * @param userFrom the value for supply_chain..user_screen.user_from
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setUserFrom(String userFrom) {
        this.userFrom = userFrom == null ? null : userFrom.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.user_type
     *
     * @return the value of supply_chain..user_screen.user_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getUserType() {
        return userType;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withUserType(String userType) {
        this.setUserType(userType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.user_type
     *
     * @param userType the value for supply_chain..user_screen.user_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setUserType(String userType) {
        this.userType = userType == null ? null : userType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.iot_type
     *
     * @return the value of supply_chain..user_screen.iot_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Integer getIotType() {
        return iotType;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withIotType(Integer iotType) {
        this.setIotType(iotType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.iot_type
     *
     * @param iotType the value for supply_chain..user_screen.iot_type
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setIotType(Integer iotType) {
        this.iotType = iotType;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.unified_status
     *
     * @return the value of supply_chain..user_screen.unified_status
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public Integer getUnifiedStatus() {
        return unifiedStatus;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withUnifiedStatus(Integer unifiedStatus) {
        this.setUnifiedStatus(unifiedStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.unified_status
     *
     * @param unifiedStatus the value for supply_chain..user_screen.unified_status
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setUnifiedStatus(Integer unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.department_id
     *
     * @return the value of supply_chain..user_screen.department_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getDepartmentId() {
        return departmentId;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withDepartmentId(String departmentId) {
        this.setDepartmentId(departmentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.department_id
     *
     * @param departmentId the value for supply_chain..user_screen.department_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId == null ? null : departmentId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.department_name
     *
     * @return the value of supply_chain..user_screen.department_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getDepartmentName() {
        return departmentName;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withDepartmentName(String departmentName) {
        this.setDepartmentName(departmentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.department_name
     *
     * @param departmentName the value for supply_chain..user_screen.department_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.old_job_number
     *
     * @return the value of supply_chain..user_screen.old_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getOldJobNumber() {
        return oldJobNumber;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withOldJobNumber(String oldJobNumber) {
        this.setOldJobNumber(oldJobNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.old_job_number
     *
     * @param oldJobNumber the value for supply_chain..user_screen.old_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setOldJobNumber(String oldJobNumber) {
        this.oldJobNumber = oldJobNumber == null ? null : oldJobNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.new_job_number
     *
     * @return the value of supply_chain..user_screen.new_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getNewJobNumber() {
        return newJobNumber;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withNewJobNumber(String newJobNumber) {
        this.setNewJobNumber(newJobNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.new_job_number
     *
     * @param newJobNumber the value for supply_chain..user_screen.new_job_number
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setNewJobNumber(String newJobNumber) {
        this.newJobNumber = newJobNumber == null ? null : newJobNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.be_id
     *
     * @return the value of supply_chain..user_screen.be_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.be_id
     *
     * @param beId the value for supply_chain..user_screen.be_id
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.location
     *
     * @return the value of supply_chain..user_screen.location
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.location
     *
     * @param location the value for supply_chain..user_screen.location
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..user_screen.location_name
     *
     * @return the value of supply_chain..user_screen.location_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public UserScreen withLocationName(String locationName) {
        this.setLocationName(locationName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_screen.location_name
     *
     * @param locationName the value for supply_chain..user_screen.location_name
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName == null ? null : locationName.trim();
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", pwd=").append(pwd);
        sb.append(", phone=").append(phone);
        sb.append(", email=").append(email);
        sb.append(", company=").append(company);
        sb.append(", roleId=").append(roleId);
        sb.append(", accountLevel=").append(accountLevel);
        sb.append(", isCancel=").append(isCancel);
        sb.append(", isLogoff=").append(isLogoff);
        sb.append(", isAdmin=").append(isAdmin);
        sb.append(", province=").append(province);
        sb.append(", creator=").append(creator);
        sb.append(", companyId=").append(companyId);
        sb.append(", companyName=").append(companyName);
        sb.append(", companyType=").append(companyType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", userFrom=").append(userFrom);
        sb.append(", userType=").append(userType);
        sb.append(", iotType=").append(iotType);
        sb.append(", unifiedStatus=").append(unifiedStatus);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", oldJobNumber=").append(oldJobNumber);
        sb.append(", newJobNumber=").append(newJobNumber);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", locationName=").append(locationName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserScreen other = (UserScreen) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPwd() == null ? other.getPwd() == null : this.getPwd().equals(other.getPwd()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getCompany() == null ? other.getCompany() == null : this.getCompany().equals(other.getCompany()))
            && (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getAccountLevel() == null ? other.getAccountLevel() == null : this.getAccountLevel().equals(other.getAccountLevel()))
            && (this.getIsCancel() == null ? other.getIsCancel() == null : this.getIsCancel().equals(other.getIsCancel()))
            && (this.getIsLogoff() == null ? other.getIsLogoff() == null : this.getIsLogoff().equals(other.getIsLogoff()))
            && (this.getIsAdmin() == null ? other.getIsAdmin() == null : this.getIsAdmin().equals(other.getIsAdmin()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getCompanyType() == null ? other.getCompanyType() == null : this.getCompanyType().equals(other.getCompanyType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUserFrom() == null ? other.getUserFrom() == null : this.getUserFrom().equals(other.getUserFrom()))
            && (this.getUserType() == null ? other.getUserType() == null : this.getUserType().equals(other.getUserType()))
            && (this.getIotType() == null ? other.getIotType() == null : this.getIotType().equals(other.getIotType()))
            && (this.getUnifiedStatus() == null ? other.getUnifiedStatus() == null : this.getUnifiedStatus().equals(other.getUnifiedStatus()))
            && (this.getDepartmentId() == null ? other.getDepartmentId() == null : this.getDepartmentId().equals(other.getDepartmentId()))
            && (this.getDepartmentName() == null ? other.getDepartmentName() == null : this.getDepartmentName().equals(other.getDepartmentName()))
            && (this.getOldJobNumber() == null ? other.getOldJobNumber() == null : this.getOldJobNumber().equals(other.getOldJobNumber()))
            && (this.getNewJobNumber() == null ? other.getNewJobNumber() == null : this.getNewJobNumber().equals(other.getNewJobNumber()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getLocationName() == null ? other.getLocationName() == null : this.getLocationName().equals(other.getLocationName()));
    }

    /**
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPwd() == null) ? 0 : getPwd().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getCompany() == null) ? 0 : getCompany().hashCode());
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getAccountLevel() == null) ? 0 : getAccountLevel().hashCode());
        result = prime * result + ((getIsCancel() == null) ? 0 : getIsCancel().hashCode());
        result = prime * result + ((getIsLogoff() == null) ? 0 : getIsLogoff().hashCode());
        result = prime * result + ((getIsAdmin() == null) ? 0 : getIsAdmin().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getCompanyType() == null) ? 0 : getCompanyType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUserFrom() == null) ? 0 : getUserFrom().hashCode());
        result = prime * result + ((getUserType() == null) ? 0 : getUserType().hashCode());
        result = prime * result + ((getIotType() == null) ? 0 : getIotType().hashCode());
        result = prime * result + ((getUnifiedStatus() == null) ? 0 : getUnifiedStatus().hashCode());
        result = prime * result + ((getDepartmentId() == null) ? 0 : getDepartmentId().hashCode());
        result = prime * result + ((getDepartmentName() == null) ? 0 : getDepartmentName().hashCode());
        result = prime * result + ((getOldJobNumber() == null) ? 0 : getOldJobNumber().hashCode());
        result = prime * result + ((getNewJobNumber() == null) ? 0 : getNewJobNumber().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getLocationName() == null) ? 0 : getLocationName().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..user_screen
     *
     * @mbg.generated Wed May 28 15:44:41 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        pwd("pwd", "pwd", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        email("email", "email", "VARCHAR", false),
        company("company", "company", "VARCHAR", false),
        roleId("role_id", "roleId", "VARCHAR", false),
        accountLevel("account_level", "accountLevel", "INTEGER", false),
        isCancel("is_cancel", "isCancel", "BIT", false),
        isLogoff("is_logoff", "isLogoff", "BIT", false),
        isAdmin("is_admin", "isAdmin", "BIT", false),
        province("province", "province", "VARCHAR", false),
        creator("creator", "creator", "VARCHAR", false),
        companyId("company_id", "companyId", "VARCHAR", false),
        companyName("company_name", "companyName", "VARCHAR", false),
        companyType("company_type", "companyType", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        userFrom("user_from", "userFrom", "VARCHAR", false),
        userType("user_type", "userType", "VARCHAR", false),
        iotType("iot_type", "iotType", "INTEGER", false),
        unifiedStatus("unified_status", "unifiedStatus", "INTEGER", false),
        departmentId("department_id", "departmentId", "VARCHAR", false),
        departmentName("department_name", "departmentName", "VARCHAR", false),
        oldJobNumber("old_job_number", "oldJobNumber", "VARCHAR", false),
        newJobNumber("new_job_number", "newJobNumber", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        locationName("location_name", "locationName", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..user_screen
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed May 28 15:44:41 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}