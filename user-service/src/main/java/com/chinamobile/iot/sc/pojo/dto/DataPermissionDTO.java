package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DataPermissionDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 数据权限名称
     */
    private String name;

    /**
     * 权限编码
     */
    private String authCode;

    /**
     * 模块 os-IoT应用商城运营支撑系统，screen-商城大屏
     */
    private String system;

    /**
     * 上级权限的id
     */
    private String parentId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 子权限
     */
    List<DataPermissionDTO> children;

}