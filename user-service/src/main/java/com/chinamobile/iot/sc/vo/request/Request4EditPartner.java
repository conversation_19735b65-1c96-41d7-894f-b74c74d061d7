package com.chinamobile.iot.sc.vo.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @package: com.chinamobile.iot.sc.vo.request
 * @ClassName: Request4EditPartner
 * @description: 修改合作伙伴信息-请求
 * @author: zyj
 * @create: 2021/12/24 9:49
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Request4EditPartner {
    @NotBlank(message = "用户id不能为空！")
    private String userId;

    @NotBlank(message = "归属系统")
    private String system;

    @NotBlank(message = "联系人姓名不能为空！")
    private String userName;
    @NotBlank(message = "手机号不能为空！")
    private String phone;
    @NotBlank(message = "邮箱不能为空！")
    private String email;
//    @NotBlank(message = "角色id不能为空！")
//    private String roleId;
    @NotBlank(message = "短信验证码不能为空！")
    private String newCode;

    /**
     * 是否发送短信
     */
    private Boolean isSend;

    private String from;
}
