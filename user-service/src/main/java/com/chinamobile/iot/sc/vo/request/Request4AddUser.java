package com.chinamobile.iot.sc.vo.request;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.vo
 * @ClassName: Data4AddUser
 * @description: 新增用户消息体
 * @author: zyj
 * @create: 2021/9/3 11:45
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class Request4AddUser implements Serializable {
    //所属系统，os-os系统，screen-大屏系统，partner-合作伙伴中心
    @NotBlank
    private String system;
    @NotBlank
    private String userName;
    private String pwd;
    @NotBlank
    private String phone;
    @NotBlank
    private String roleId;

    private String roleName;

    //账号来源（用户对象），iot-物联网公司（含外协），other-非物联网公司
    private String userFrom;

    //合作伙伴名
    private String partnerName;
    //邮箱
    @NotBlank
    private String email;
    //所属公司
    private String company;
    //所属组织
    private String departmentName;
    //部门ID
    private String departmentId;
    //员工工号
    private String userCode;

    //账号类型，normal-正式账号，test-测试账号
    private String userType;

    //用户种类，用于物联网公司用户区分内部用户跟外协，1 内部用户 2 外部用户
    private Integer iotType;

    //统一用户平台用户状态，0：正常，1：锁定（账号不可用，但可恢复），2：未启用（账号信息不完整 不可用），3：注销（此账号从此不可恢复）
    private Integer status;

    /**
     * 是否发送短信
     */
    private Boolean isSend;

    private String province;

    /**
     * 省份编码，多个以逗号分隔
     */
    private String beId;

    /**
     * 城市编码，多个以逗号分隔
     */
    private String location;

    /**
     * 前端用作用户编辑回显
     */
    private List<List<String>> provinceLocationList;

    /**
     * 城市名称，多个以逗号分隔
     */
    private String locationName;

    private Integer accountLevel;

    private String oldJobNumber;
    private String newJobNumber;

    /**
     * 统一社会信用代码
     */
    private String unifiedCode;

    /**
     *  统一用户平台用户名
     */
    private String account;

    /**
     *单位类型：1：非省公司 2：省公司
     */
    private String companyType;

    /**
     * 合作伙伴公司id
     */
    private String companyId;

    /**
     * 合作伙伴公司名称
     */
    private String companyName;

    /**
     * 是否为外部人员
     */
    private Boolean isExternal;

    /**
     * 地市域 地市域，全地市以all表示
     */
    private String region;

    /**
     * 地市域编码 ，全地市以all表示
     */
    private String regionId;


}
