package com.chinamobile.iot.sc.pojo.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.RowTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.UserInstallMapper;
import com.chinamobile.iot.sc.dao.UserMapper;
import com.chinamobile.iot.sc.dao.UserPartnerMapper;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.service.impl.UserServiceImpl;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/12/15 0:48
 * @description: 装维用户导入监听类
 **/
@Slf4j
public class UserInstallImportExcelListener extends AnalysisEventListener<ExcelUserInstallImport> {


    private LoginIfo4Redis loginInfo;

    private List<UserPartner> allPrimaryUser;


    private UserPartnerMapper userInstallMapperInject;

    private UserMapper userMapperInject;

    private String encryptKey;


    private List<BatchUserInstallRequestSucceed> succeedList = new ArrayList<>();

    private List<BatchUserInstallRequestFailed> failedList = new ArrayList<>();

    @Override
    public void invoke(ExcelUserInstallImport excelUserInstallImport, AnalysisContext analysisContext) {

        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        String failedReason = "";
        log.info("解析到一条数据:{},当前行:{}", excelUserInstallImport, currentRow);
        BatchUserInstallRequestSucceed batchUserInstallRequestSucceed = new BatchUserInstallRequestSucceed();
        BatchUserInstallRequestFailed batchProductRequestFailed = new BatchUserInstallRequestFailed();
        UserPartner primaryUser = null;
        String roleType = loginInfo.getRoleType();
        //入口已经根据权限过滤，这里只要识别主账号特殊处理就可以
        if (loginInfo.getIsPrimary() != null && loginInfo.getIsPrimary()) {
            if (!CollectionUtils.isEmpty(allPrimaryUser)) {
                primaryUser = allPrimaryUser.get(0);
                String installPartnerName = excelUserInstallImport.getInstallPartnerName();
                if (StringUtils.isEmpty(installPartnerName)) {
                    if (StringUtils.isNotEmpty(failedReason)) {
                        failedReason = failedReason.concat(",").concat(StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage()).concat("错误行" + currentRow);
                    } else {
                        failedReason = StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage().concat("错误行" + currentRow);
                    }
                } else if (StringUtils.isNotEmpty(installPartnerName) &&!primaryUser.getPartnerName().equals(installPartnerName)){
                    if (StringUtils.isNotEmpty(failedReason)) {
                        failedReason = failedReason.concat(",").concat("合作伙伴名称不匹配").concat("错误行" + currentRow);
                    } else {
                        failedReason = "合作伙伴名称不匹配".concat("错误行" + currentRow);
                    }
                }

            }
        } else {
            //校验合作伙伴名称
            String installPartnerName = excelUserInstallImport.getInstallPartnerName();
            if (StringUtils.isEmpty(installPartnerName)) {
                if (StringUtils.isNotEmpty(failedReason)) {
                    failedReason = failedReason.concat(",").concat(StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage()).concat("错误行" + currentRow);
                } else {
                    failedReason = StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage().concat("错误行" + currentRow);
                }
            } else {
                if (!CollectionUtils.isEmpty(allPrimaryUser)) {
                    boolean present = allPrimaryUser.stream().anyMatch(user -> user.getPartnerName().equals(installPartnerName));
                    if (!present) {
                        if (StringUtils.isNotEmpty(failedReason)) {
                            failedReason = failedReason.concat(",").concat(StatusConstant.USER_PRIMARY_PARTNER_NAME_NOT_EXIST.getMessage()).concat("错误行" + currentRow);
                        } else {
                            failedReason = StatusConstant.USER_PRIMARY_PARTNER_NAME_NOT_EXIST.getMessage().concat("错误行" + currentRow);
                        }
                    } else {
                        primaryUser = allPrimaryUser.stream().filter(user -> user.getPartnerName().equals(excelUserInstallImport.getInstallPartnerName())).findFirst().get();
                    }
                }
            }
        }
//        if (roleType.equals(ADMIN_ROLE) || roleType.equals(OPERATOR_ROLE)){
//            //校验合作伙伴名称
//            String installPartnerName = excelUserInstallImport.getInstallPartnerName();
//            if (StringUtils.isEmpty(installPartnerName)){
//                if (StringUtils.isNotEmpty(failedReason)){
//                    failedReason =failedReason.concat(",").concat(StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage()).concat("错误行"+currentRow);
//                }else {
//                    failedReason =StatusConstant.INSTALL_PARTNER_NAME_NOT_NULL.getMessage().concat("错误行"+currentRow);
//                }
//            }else {
//                if (!CollectionUtils.isEmpty(allPrimaryUser)){
//                    boolean present = allPrimaryUser.stream().anyMatch(user -> user.getPartnerName().equals(installPartnerName));
//                    if (!present){
//                        if (StringUtils.isNotEmpty(failedReason)){
//                            failedReason =failedReason.concat(",").concat(StatusConstant.USER_PRIMARY_PARTNER_NAME_NOT_EXIST.getMessage()).concat("错误行"+currentRow);
//                        }else {
//                            failedReason =StatusConstant.USER_PRIMARY_PARTNER_NAME_NOT_EXIST.getMessage().concat("错误行"+currentRow);
//                        }
//                    }else {
//                        primaryUser = allPrimaryUser.stream().filter(user -> user.getPartnerName().equals(excelUserInstallImport.getInstallPartnerName())).findFirst().get();
//                    }
//                }
//            }
//        }else if (roleType.equals(PARTNER_LORD_ROLE)){
//            if (!CollectionUtils.isEmpty(allPrimaryUser)){
//                primaryUser = allPrimaryUser.get(0);
//            }
//        }
        //校验装维人员名称
        String installName = excelUserInstallImport.getInstallName();
        if (StringUtils.isNotEmpty(installName)) {
            if (!RegexUtil.regexOperatorName(installName)) {
                if (StringUtils.isNotEmpty(failedReason)) {
                    failedReason = failedReason.concat(",").concat(StatusConstant.NAME_FORMAT_ERROR.getMessage()).concat("错误行" + currentRow);
                } else {
                    failedReason = StatusConstant.NAME_FORMAT_ERROR.getMessage().concat("错误行" + currentRow);
                }
            }
        } else {
            if (StringUtils.isNotEmpty(failedReason)) {
                failedReason = failedReason.concat(",").concat(StatusConstant.INSTALL_NAME_NOT_NULL.getMessage()).concat("错误行" + currentRow);
            } else {
                failedReason = StatusConstant.INSTALL_NAME_NOT_NULL.getMessage().concat("错误行" + currentRow);
            }
        }


        //校验安装人员电话
        String installPhone = excelUserInstallImport.getInstallPhone();
        if (StringUtils.isNotEmpty(installPhone)) {
            //手机格式校验
            if (!RegexUtil.regexPhone(excelUserInstallImport.getInstallPhone())) {
                if (StringUtils.isNotEmpty(failedReason)) {
                    failedReason = failedReason.concat(",").concat(StatusConstant.PHONE_ERROR.getMessage()).concat("错误行" + currentRow);
                } else {
                    failedReason = StatusConstant.PHONE_ERROR.getMessage().concat("错误行" + currentRow);
                }
            }
            //手机号是否被注册

            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(installPhone,encryptKey);
            List<UserPartner> users = userInstallMapperInject.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone)
                    .andIsCancelEqualTo(false).andRoleIdEqualTo(PARTNER_INSTALL_ROLE_ID).example());
            if (ObjectUtils.isNotEmpty(users)) {
                if (StringUtils.isNotEmpty(failedReason)) {
                    failedReason = failedReason.concat(",").concat(StatusConstant.PHONE_IS_EXIST.getMessage()).concat("错误行" + currentRow);
                } else {
                    failedReason = StatusConstant.PHONE_IS_EXIST.getMessage().concat("错误行" + currentRow);
                }
            }

            //手机号是否被OS系统注册，OS与合作伙伴中心不能共用手机号
            List<User> osUser = userMapperInject.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).example());
            if (ObjectUtils.isNotEmpty(osUser)) {
                failedReason = "手机号OS系统已注册".concat("错误行" + currentRow);
            }
        } else {
            if (StringUtils.isNotEmpty(failedReason)) {
                failedReason = failedReason.concat(",").concat(StatusConstant.INSTALL_PHONE_NOT_NULL.getMessage()).concat("错误行" + currentRow);
            } else {
                failedReason = StatusConstant.INSTALL_PHONE_NOT_NULL.getMessage().concat("错误行" + currentRow);
            }
        }

        //校验安装人员邮箱
        String installEmail = excelUserInstallImport.getInstallEmail();
        if (StringUtils.isNotEmpty(installEmail)) {
            if (!RegexUtil.regexEmail(installEmail)) {
                if (StringUtils.isNotEmpty(failedReason)) {
                    failedReason = failedReason.concat(",").concat(StatusConstant.EMAIL_FORMAT_ERROR.getMessage()).concat("错误行" + currentRow);
                } else {
                    failedReason = StatusConstant.EMAIL_FORMAT_ERROR.getMessage().concat("错误行" + currentRow);
                }
            }
        } else {
            if (StringUtils.isNotEmpty(failedReason)) {
                failedReason = failedReason.concat(",").concat(StatusConstant.INSTALL_EMAIL_NOT_NULL.getMessage()).concat("错误行" + currentRow);
            } else {
                failedReason = StatusConstant.INSTALL_EMAIL_NOT_NULL.getMessage().concat("错误行" + currentRow);
            }
        }

        if (StringUtils.isNotEmpty(failedReason)) {
            batchProductRequestFailed.setInstallName(excelUserInstallImport.getInstallName());
            batchProductRequestFailed.setInstallPhone(excelUserInstallImport.getInstallPhone());
            batchProductRequestFailed.setFailedReason(failedReason);
            failedList.add(batchProductRequestFailed);
        } else {
            BeanUtils.copyProperties(excelUserInstallImport, batchUserInstallRequestSucceed);
            if (primaryUser != null) {
                batchUserInstallRequestSucceed.setPrimaryUserId(primaryUser.getUserId());
                batchUserInstallRequestSucceed.setInstallPartnerName(primaryUser.getPartnerName());
                batchUserInstallRequestSucceed.setUserType(primaryUser.getUserType());
            }
            succeedList.add(batchUserInstallRequestSucceed);
        }


    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        if (RowTypeEnum.EMPTY.equals(context.readRowHolder().getRowType())) {
            doAfterAllAnalysed(context);
            return false;
        }
        return super.hasNext(context);
    }

    public UserInstallImportExcelListener(LoginIfo4Redis loginIfo4Redis, List<UserPartner> primaryUsers,
                                          UserPartnerMapper userInstallMapper, UserMapper userMapper,String encryptKey) {
        loginInfo = loginIfo4Redis;
        allPrimaryUser = primaryUsers;
        userInstallMapperInject = userInstallMapper;
        userMapperInject = userMapper;
        this.encryptKey = encryptKey;
    }


    public List<BatchUserInstallRequestSucceed> getSucceedListData() {
        return succeedList;
    }

    public List<BatchUserInstallRequestFailed> getFailedListData() {
        return failedList;
    }
}
