package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.JWTUtil;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.ContractCityInfoMapper;
import com.chinamobile.iot.sc.dao.ContractProvinceInfoMapper;
import com.chinamobile.iot.sc.dao.UserMapper;
import com.chinamobile.iot.sc.dao.UserScreenMapper;
import com.chinamobile.iot.sc.dao.ext.UserScreenMapperExt;
import com.chinamobile.iot.sc.enums.UserUnifiedStatusEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.LoginOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.AuthCode;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.mode.UserRoleVO;
import com.chinamobile.iot.sc.pojo.dto.CityDTO;
import com.chinamobile.iot.sc.pojo.dto.ProvinceDTO;
import com.chinamobile.iot.sc.pojo.dto.RoleInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.UserScreenDO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.RoleInfoService;
import com.chinamobile.iot.sc.service.UserScreenService;
import com.chinamobile.iot.sc.util.DesensitizationUtils;
import com.chinamobile.iot.sc.utils.GeneralUtils;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.RSAEncrypt;
import com.chinamobile.iot.sc.vo.AccessToken;
import com.chinamobile.iot.sc.vo.request.Request4Login;
import com.chinamobile.iot.sc.vo.request.RequestAddUserScreen;
import com.chinamobile.iot.sc.vo.request.RequestEditUserScreen;
import com.chinamobile.iot.sc.vo.request.RequestUserScreenPage;
import com.chinamobile.iot.sc.vo.response.DataUserScreen;
import com.chinamobile.iot.sc.vo.response.RoleAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.SYSTEM_SCREEN;
import static com.chinamobile.iot.sc.constant.StatusConstant.USER_NO_EXIST;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/8/15 10:25
 * @description: 大屏用户实现类
 **/
@Slf4j
@Service
public class UserScreenServiceImpl implements UserScreenService {


    @Resource
    private UserScreenMapper userScreenMapper;

    @Resource
    private UserScreenMapperExt userScreenMapperExt;

   @Resource
   private UserMapper userMapper;

//   @Resource
//   private RoleMapper roleMapper;


    @Resource
    private RoleAuthServiceImpl roleAuthService;

    @Value("${rsa.private.key}")
    private String RSAPrivateKey;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private LogService logService;

    @Resource
    private RoleInfoService roleInfoService;

    @Value("${supply.des.key}")
    private String encryptKey;

    @Resource
    private ProvinceCityConfig pcConfig;

    @Resource
    private ContractProvinceInfoMapper provinceMapper;

    @Resource
    private ContractCityInfoMapper cityMapper;

    private static final int MAX_ATTEMPTS = 5;
    private static final int LOCK_TIME = 5 * 60; // 5 minutes in seconds
    private static final int ATTEMPT_TIME_WINDOW = 30 * 60; // 30 minutes in seconds

    @Override
    public BaseAnswer adminLoginScreen(Request4Login request4Login) {
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();

        String ip = request.getRemoteAddr();
        String lockKey = "lock-" + ip + "-" + request4Login.getUserName();
        String attemptsKey = "login-" + ip + "-" + request4Login.getUserName();

        // Check if the account is locked
        if (redisTemplate.hasKey(lockKey)) {
            throw new BusinessException(StatusConstant.ACCOUNT_ADMIN_LOCK);
        }
        Integer attempts = (Integer) redisTemplate.opsForValue().get(attemptsKey);
        if (attempts == null) {
            attempts = 0;
        }
        //校验用户名是否存在
        List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andNameEqualTo(request4Login.getUserName()).andIsLogoffEqualTo(false).andIsCancelEqualTo(false).example());
        if (CollectionUtils.isEmpty(users)) {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",LogResultEnum.LOG_FAIL.code,USER_NO_EXIST.getMessage());
            throw new BusinessException(USER_NO_EXIST);
        }
        UserScreen user = users.get(0);
        String pwdDB = user.getPwd();
        //解密登录密码
//        String decodePassWord = null;
        try {
//            decodePassWord = RSAEncrypt.decrypt(request4Login.getPwd(), RSAPrivateKey);
            //Base64处理
//            log.debug("大屏密码测试 decodePWD = {}",decodePassWord);
//            String base64PWD = GeneralUtils.Base64Encode(decodePassWord);
//            log.debug("大屏密码测试 base64PWD = {}",base64PWD);
//            log.debug("大屏密码测试 pwdDB C = {}",GeneralUtils.diyBase64Pwd2DbPwd(base64PWD));
            if (!pwdDB.equals(request4Login.getPwd())) {
//                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",user.getId(), 0,LogResultEnum.LOG_FAIL.code,StatusConstant.PWD_NO_CORRECT.getMessage());
                attempts++;
                redisTemplate.opsForValue().set(attemptsKey, attempts, ATTEMPT_TIME_WINDOW,TimeUnit.SECONDS);

                if (attempts >= MAX_ATTEMPTS) {
                    // Lock the account for 5 minutes
                    redisTemplate.opsForValue().set(lockKey, 1, LOCK_TIME,TimeUnit.SECONDS);
                    throw new BusinessException(StatusConstant.ACCOUNT_ADMIN_LOCK_NOTICE);
                }
                throw new BusinessException(StatusConstant.PWD_NO_CORRECT, "密码错误，还可尝试"+(5-attempts)+"次，失败后将锁定5分钟");
            }else{
                redisTemplate.delete(attemptsKey);
            }
        } catch (Exception e) {
            if (attempts >= MAX_ATTEMPTS) {
//                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",user.getId(), 0,LogResultEnum.LOG_FAIL.code,StatusConstant.ACCOUNT_ADMIN_LOCK_NOTICE.getMessage());

                throw new BusinessException(StatusConstant.ACCOUNT_ADMIN_LOCK_NOTICE);
            }else{
//                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",user.getId(), 0,LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());

                throw new BusinessException(StatusConstant.PWD_NO_CORRECT,"密码错误，还可尝试"+(5-attempts)+"次，失败后将锁定5分钟");
            }
        }
        String token = generateToken(user);

        //添加登录日志
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",user.getId(), 0,LogResultEnum.LOG_SUCESS.code,null);
        return new BaseAnswer().setData(token);
    }

    @Override
    public BaseAnswer loginByCodeScreen(String phone) {
        BaseAnswer baseAnswer = new BaseAnswer();
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone,encryptKey);

        List<UserScreen> usersIsCancel = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andPhoneEqualTo(encryptPhone)
                        .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(usersIsCancel)) {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",null,0,LogResultEnum.LOG_FAIL.code,StatusConstant.USER_NO_EXIST.getMessage());

            throw new BusinessException(StatusConstant.USER_IS_CANCEL_FALSE);
        }

        List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(users)) {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",null,0,LogResultEnum.LOG_FAIL.code,StatusConstant.USER_NO_EXIST.getMessage());

            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }

        String token = generateToken(users.get(0));

        //添加登录日志
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-",users.get(0).getId(),0,LogResultEnum.LOG_SUCESS.code,null);
        return baseAnswer.setData(token);
    }

    /**切换角色*/
    @Override
    public BaseAnswer<String> changeRole(String userId, String system,LoginIfo4Redis loginIfo4Redis) {
        //校验当前登录系统匹配，不允许跨系统切换账号
        if (!system.equals(loginIfo4Redis.getSystem())) {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", null, 0,LogResultEnum.LOG_FAIL.code,StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR.getMessage());

            throw new BusinessException(StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR);
        }

        BaseAnswer<String> baseAnswer = new BaseAnswer<>();
        String token = "";
        if (SYSTEM_SCREEN.equals(system)) {
            UserScreen user = userScreenMapper.selectByPrimaryKey(userId);
            if (ObjectUtils.isEmpty(user)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", null, 0,LogResultEnum.LOG_FAIL.code,StatusConstant.USER_NO_EXIST.getMessage());

                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (user.getIsCancel() || (user.getUnifiedStatus() != null && !user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", null, 0,LogResultEnum.LOG_FAIL.code,StatusConstant.USER_NO_EXIST.getMessage());

                throw new BusinessException(StatusConstant.USER_NO_EXIST,"切换用户不可用");
            }

            if (!loginIfo4Redis.getPhone().equals(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey))) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", null, 0,LogResultEnum.LOG_FAIL.code,StatusConstant.CHANGE_DIFRENT_PHONE_ERROR.getMessage());

                throw new BusinessException(StatusConstant.CHANGE_DIFRENT_PHONE_ERROR);
            }

            //当前账号登出
            redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + loginIfo4Redis.getUserId());
            token = generateToken(user);

        } else {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", null, 0,LogResultEnum.LOG_FAIL.code,StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR.getMessage());

            throw new BusinessException(StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR,"切换系统不支持");
        }

        //添加登录日志
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code, "-", userId, 0,LogResultEnum.LOG_SUCESS.code,null);

        return baseAnswer.setData(token);
    }

    @Override
    public BaseAnswer<List<ProvinceDTO>> getProvinceCity() {
        List<ProvinceDTO> pcList = pcConfig.getProcityList();
        return BaseAnswer.success(pcList);
    }


    @Override
    public BaseAnswer<List<ProvinceDTO>> getProvinceCityList(List<String> provinceList) {
        HashMap<String, String> provinceNameCodeMap = new HashMap<>();

        HashMap<String, String> provinceCodeNameMap = new HashMap<>();

        HashMap<String, String> cityNameCodeMap = new HashMap<>();

        HashMap<String, String> cityCodeNameMap = new HashMap<>();
        List<ProvinceDTO> procityList = new ArrayList<>();
        List<String> excludeProvince = new ArrayList<>();
        List<ContractProvinceInfo> provinceInfoList = new ArrayList<>();
        excludeProvince.add("000");
        excludeProvince.add("001");
        excludeProvince.add("002");
        if(CollectionUtils.isEmpty(provinceList)){
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).example());
        }else{
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).andMallNameIn(provinceList).example());
        }
        for (ContractProvinceInfo pinfo : provinceInfoList) {
            ProvinceDTO pdto = new ProvinceDTO();
            List<CityDTO> cityList = new ArrayList<>();
            String pname = pinfo.getMallName();
            String pcode = pinfo.getMallCode();
            //构建省map
            provinceNameCodeMap.put(pname, pcode);
            provinceCodeNameMap.put(pcode, pname);

            pdto.setName(pname);
            pdto.setValue(pcode);
            List<ContractCityInfo> cityInfoList = cityMapper.selectByExample(new ContractCityInfoExample().createCriteria().andProvinceMallCodeEqualTo(pcode).example());
            for (ContractCityInfo cinfo : cityInfoList) {
                CityDTO cdto = new CityDTO();
                cdto.setName(cinfo.getMallName());
                cdto.setValue(cinfo.getMallCode());

                //构建城市Map
                cityNameCodeMap.put(cinfo.getMallName(), cinfo.getMallCode());
                cityCodeNameMap.put(cinfo.getMallCode(), cinfo.getMallName());

                cityList.add(cdto);
            }
            pdto.setChildren(cityList);
            procityList.add(pdto);
        }
        return BaseAnswer.success(procityList);
    }


    @Override
    public List<ProvinceDTO> getProvinceCityListByBeId(List<String> mallCode) {
        HashMap<String, String> provinceNameCodeMap = new HashMap<>();

        HashMap<String, String> provinceCodeNameMap = new HashMap<>();

        HashMap<String, String> cityNameCodeMap = new HashMap<>();

        HashMap<String, String> cityCodeNameMap = new HashMap<>();
        List<ProvinceDTO> procityList = new ArrayList<>();
        List<String> excludeProvince = new ArrayList<>();
        List<ContractProvinceInfo> provinceInfoList = new ArrayList<>();
        excludeProvince.add("000");
        excludeProvince.add("001");
        excludeProvince.add("002");
        if(CollectionUtils.isEmpty(mallCode)){
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).example());
        }else{
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeIn(mallCode).example());
        }
        for (ContractProvinceInfo pinfo : provinceInfoList) {
            ProvinceDTO pdto = new ProvinceDTO();
            List<CityDTO> cityList = new ArrayList<>();
            String pname = pinfo.getMallName();
            String pcode = pinfo.getMallCode();
            //构建省map
            provinceNameCodeMap.put(pname, pcode);
            provinceCodeNameMap.put(pcode, pname);

            pdto.setName(pname);
            pdto.setValue(pcode);
            List<ContractCityInfo> cityInfoList = cityMapper.selectByExample(new ContractCityInfoExample().createCriteria().andProvinceMallCodeEqualTo(pcode).example());
            for (ContractCityInfo cinfo : cityInfoList) {
                CityDTO cdto = new CityDTO();
                cdto.setName(cinfo.getMallName());
                cdto.setValue(cinfo.getMallCode());

                //构建城市Map
                cityNameCodeMap.put(cinfo.getMallName(), cinfo.getMallCode());
                cityCodeNameMap.put(cinfo.getMallCode(), cinfo.getMallName());

                cityList.add(cdto);
            }
            pdto.setChildren(cityList);
            procityList.add(pdto);
        }
        return procityList;
    }

    @Override
    public BaseAnswer<List<ProvinceDTO>> getProvinceCityListKx(List<String> provinceList) {
        HashMap<String, String> provinceNameCodeMap = new HashMap<>();

        HashMap<String, String> provinceCodeNameMap = new HashMap<>();

        HashMap<String, String> cityNameCodeMap = new HashMap<>();

        HashMap<String, String> cityCodeNameMap = new HashMap<>();
        List<ProvinceDTO> procityList = new ArrayList<>();
        List<String> excludeProvince = new ArrayList<>();
        List<ContractProvinceInfo> provinceInfoList = new ArrayList<>();
        excludeProvince.add("000");
        excludeProvince.add("001");
        excludeProvince.add("002");
        if(CollectionUtils.isEmpty(provinceList)){
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).example());
        }else{
            provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).andMallNameIn(provinceList).example());
        }
        for (ContractProvinceInfo pinfo : provinceInfoList) {
            ProvinceDTO pdto = new ProvinceDTO();
            List<CityDTO> cityList = new ArrayList<>();
            String pname = pinfo.getMallName();
            String pcode = pinfo.getMallCode();
            //构建省map
            provinceNameCodeMap.put(pname, pcode);
            provinceCodeNameMap.put(pcode, pname);

            pdto.setName(pname);
            pdto.setValue(pcode);
            List<ContractCityInfo> cityInfoList = cityMapper.selectByExample(new ContractCityInfoExample().createCriteria().andProvinceMallCodeEqualTo(pcode).example());
            for (ContractCityInfo cinfo : cityInfoList) {
                CityDTO cdto = new CityDTO();
                cdto.setName(cinfo.getMallName());
                cdto.setValue(cinfo.getMallCode());

                //构建城市Map
                cityNameCodeMap.put(cinfo.getMallName(), cinfo.getMallCode());
                cityCodeNameMap.put(cinfo.getMallCode(), cinfo.getMallName());

                cityList.add(cdto);
            }
            //默认一个标识为空地市的信息用于卡+x库存配置使用
            CityDTO cdto = new CityDTO();
            cdto.setName("无名地市");
            cdto.setValue("1111");
            cityList.add(cdto);
            pdto.setChildren(cityList);
            procityList.add(pdto);
        }
        return BaseAnswer.success(procityList);
    }

    @Override
    public BaseAnswer loginOutScreen(String userId) {
        redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + userId);
        return new BaseAnswer();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer addUserScreen(RequestAddUserScreen addUserScreen, String userId, LoginIfo4Redis loginIfo4Redis) {

        //获取创建者用户对象
        User currUser = userMapper.selectByPrimaryKey(userId);
        UserScreen userScreen = userScreenMapper.selectByPrimaryKey(userId);

        String decodePWD = null;
        //密码非空，则校验 base64解密后进行校验
        if (ObjectUtils.isNotEmpty(addUserScreen.getPwd())) {
            try {
                decodePWD = RSAEncrypt.decrypt(addUserScreen.getPwd(), RSAPrivateKey);
            } catch (Exception e) {
                throw new BusinessException(StatusConstant.PWD_DECODE_ERROR);
            }
            GeneralUtils.checkPwd(decodePWD);
        }
//        Role role = roleMapper.selectByPrimaryKey(addUserScreen.getRoleId());
        RoleInfoDTO role = roleInfoService.selectById(addUserScreen.getRoleId());
        String targetRoleType = role.getRoleType();
        //创建用户权限，统一由入口处理
//        if (!loginIfo4Redis.getIsAdmin()) {
//            String roleType = loginIfo4Redis.getRoleType();
//            if (!roleType.equals(OPERATOR_ROLE)){
//                throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//            }
//        }
        /**
         * 共同校验：
         * 1. 姓名-联系人校验
         * 2. 手机号校验
         * 3. 邮箱
         **/
        if (!RegexUtil.regexOperatorName(addUserScreen.getUserName())) {
            throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
        }
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(addUserScreen.getPhone(),encryptKey);
        if (StringUtils.isNotEmpty(addUserScreen.getPhone())) {
            //手机格式校验
            if (!RegexUtil.regexPhone(addUserScreen.getPhone())) {
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).example());
            if (!CollectionUtils.isEmpty(users)) {
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
            }
        }
        if (StringUtils.isNotEmpty(addUserScreen.getEmail())) {
            if (!RegexUtil.regexEmail(addUserScreen.getEmail())) {
                throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
            }
        }

        //保存用户信息
        String oneId = BaseServiceUtils.getId();
        UserScreen user = new UserScreen();
        user.setId(oneId);
        user.setName(addUserScreen.getUserName());
        user.setEmail(addUserScreen.getEmail());
        user.setCompany(addUserScreen.getCompany());
        user.setPhone(encryptPhone);
        user.setRoleId(addUserScreen.getRoleId());
        user.setAccountLevel(addUserScreen.getAccountLevel());
        user.setProvince(addUserScreen.getProvince());
        user.setBeId(addUserScreen.getBeId());
        user.setLocation(addUserScreen.getLocation());
        user.setLocationName(addUserScreen.getLocationName());
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        if (Optional.ofNullable(currUser).isPresent()){
            user.setCreator(currUser.getName());
        }else if (Optional.ofNullable(userScreen).isPresent()){
            user.setCreator(userScreen.getName());
        }
        user.setIsCancel(false);
        user.setIsAdmin(false);

        //若pwd非空，则设置密码
        if (StringUtils.isNotEmpty(decodePWD)) {
            user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(decodePWD));
        }
       userScreenMapper.insert(user);

        // 添加成功后的操作日志
        String content = "【新建商城大屏账号】\n"
        .concat("账户类型").concat(role.getName()).concat("\n")
        .concat("姓名").concat(user.getName()).concat("\n")
        .concat("联系电话").concat(DesensitizationUtils.replaceWithStar(addUserScreen.getPhone())).concat("\n")
        .concat("所属省域").concat(user.getProvince());
//        logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
//                UserManageOperateEnum.KANBAN_USER_MANAGE.code,
//                content);
        return new BaseAnswer();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer deleteUserScreen(String userId, LoginIfo4Redis loginIfo4Redis) {
        Boolean currIsAdmin = loginIfo4Redis.getIsAdmin();
//        if (!currIsAdmin){
//            String roleType = loginIfo4Redis.getRoleType();
//            if (!roleType.equals(OPERATOR_ROLE)){
//                throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//            }
//        }
        UserScreen user = userScreenMapper.selectByPrimaryKey(userId);
        Boolean isAdmin = user.getIsAdmin() == null ? false : user.getIsAdmin();

        // 超级管理员不能删除
        if (isAdmin != null && isAdmin) {
            throw new BusinessException(StatusConstant.NO_PERMIT_DELETE_ADMIN);
        }
        if (user.getIsCancel()){
            throw new BusinessException(StatusConstant.USER_LOGGED_OUT);
        }
        // 逻辑删除用户
        user.setIsCancel(true);
        user.setUpdateTime(new Date());
        userScreenMapper.updateByPrimaryKey(user);

        // 将删除的用户下线
        loginOutScreen(userId);

        // 删除成功后的操作日志
        RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
        String content = "【注销商城大屏账号】\n"
        .concat("账户类型").concat(role.getDescription()).concat("\n")
        .concat("姓名").concat(user.getName()).concat("\n")
        .concat("联系电话").concat(DesensitizationUtils.replaceWithStar(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey))).concat("\n")
        .concat("所属省域").concat(user.getProvince());
//        logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
//                UserManageOperateEnum.KANBAN_USER_MANAGE.code,
//                content);
        return new BaseAnswer();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editUserScreen(RequestEditUserScreen editUserScreen, LoginIfo4Redis loginIfo4Redis) {

//        if (!loginIfo4Redis.getIsAdmin()) {
//            String roleType = loginIfo4Redis.getRoleType();
//            if (!roleType.equals(OPERATOR_ROLE)){
//                throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//            }
//        }
         String newName = editUserScreen.getUserName();
         String newPhone = editUserScreen.getPhone();
         String newEmail = editUserScreen.getEmail();
         String newRoleId = editUserScreen.getRoleId();
        UserScreen user = userScreenMapper.selectByPrimaryKey(editUserScreen.getId());
        UserScreen oldUser = new UserScreen();
        BeanUtils.copyProperties(user,oldUser);
        if (!RegexUtil.regexOperatorName(newName)) {
            throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
        }
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(newPhone,encryptKey);
        if (StringUtils.isNotEmpty(newPhone)) {
            //手机格式校验
            if (!RegexUtil.regexPhone(newPhone)) {
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            if (!encryptPhone.equals(user.getPhone())){
                List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).example());
                if (!CollectionUtils.isEmpty(users)) {
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
                }
            }
        }
        if (StringUtils.isNotEmpty(newEmail)) {
            if (!RegexUtil.regexEmail(newEmail)) {
                throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
            }
        }

        if (!Optional.ofNullable(user).isPresent()) {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        Boolean isCancel = user.getIsCancel();
        if (isCancel){
            throw new BusinessException(StatusConstant.USER_LOGGED_OUT);
        }
        boolean isAdmin = user.getIsAdmin() == null ? false : user.getIsAdmin();
        // 超级管理员不能被其他用户编辑
        if (isAdmin) {
            throw new BusinessException(StatusConstant.NO_EDIT_ADMIN);
        }

        //修改用户信息
        user.setName(newName);
        user.setPhone(encryptPhone);
        user.setEmail(newEmail);
        user.setAccountLevel(editUserScreen.getAccountLevel());
        user.setProvince(editUserScreen.getProvince());
        user.setCompany(editUserScreen.getCompany());
        user.setRoleId(newRoleId);
        user.setUpdateTime(new Date());
        userScreenMapper.updateByPrimaryKey(user);

        // 修改成功后的操作日志
        RoleInfoDTO role = roleInfoService.selectById(newRoleId);
        RoleInfoDTO oldRole = roleInfoService.selectById(oldUser.getRoleId());
        String content = "【修改商城大屏账号】\n"
        .concat("账户类型由").concat(oldRole.getDescription())
                .concat("修改为").concat(role.getDescription())
                .concat("\n")
        .concat("姓名由").concat(oldUser.getName())
                .concat("修改为").concat(user.getName())
                .concat("\n")
        .concat("联系电话由").concat(DesensitizationUtils.replaceWithStar(IOTEncodeUtils.decryptIOTMessage(oldUser.getPhone(),encryptKey)))
                .concat("修改为").concat(DesensitizationUtils.replaceWithStar(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey)))
                .concat("\n")
        .concat("所属省域由").concat(oldUser.getProvince())
                .concat("修改为").concat(user.getProvince());
//        logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
//                UserManageOperateEnum.KANBAN_USER_MANAGE.code,
//                content);
        return new BaseAnswer();
    }

    @Override
    public BaseAnswer<PageData<DataUserScreen>> userScreenPage(RequestUserScreenPage userScreenPage, LoginIfo4Redis loginIfo4Redis) {
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        PageData<DataUserScreen> pageData = new PageData<>();
        BaseAnswer<PageData<DataUserScreen>> baseAnswer = new BaseAnswer<>();
//        if (!isAdmin){
//            String roleType = loginIfo4Redis.getRoleType();
//            if (!roleType.equals(OPERATOR_ROLE)){
//                throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//            }
//        }
        Integer pageNum = userScreenPage.getPageNum();
        Integer pageSize = userScreenPage.getPageSize();
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(userScreenPage.getPhone(),encryptKey);
        List<UserScreenDO> pageUserScreen = userScreenMapperExt.pageUserScreen(userScreenPage.getUserName(), encryptPhone, userScreenPage.getCompany(), userScreenPage.getAccountLevel(), userScreenPage.getProvince(), (pageNum - 1) * pageSize, pageSize);
        Long total = userScreenMapperExt.pageCountUserScreen(userScreenPage.getUserName(), encryptPhone, userScreenPage.getCompany(), userScreenPage.getAccountLevel(), userScreenPage.getProvince());
        List<DataUserScreen> collect = pageUserScreen.stream().map(userScreenDO -> {
            DataUserScreen dataUserScreen = new DataUserScreen();
            BeanUtils.copyProperties(userScreenDO, dataUserScreen);
            dataUserScreen.setPhone(IOTEncodeUtils.decryptIOTMessage(userScreenDO.getPhone(),encryptKey));
            return dataUserScreen;
        }).collect(Collectors.toList());
        pageData.setPage(pageNum);
        pageData.setCount(total);
        pageData.setData(collect);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    @Override
    public BaseAnswer<DataUserScreen> userScreenInfo(String userId) {
        BaseAnswer<DataUserScreen> baseAnswer = new BaseAnswer<>();
        DataUserScreen userScreen = new DataUserScreen();
        UserScreen screen = userScreenMapper.selectByPrimaryKey(userId);
        BeanUtils.copyProperties(screen,userScreen);
        //查询角色业务类型
        RoleInfoDTO role = roleInfoService.selectById(userScreen.getRoleId());
        userScreen.setRoleType(role.getRoleType());
        userScreen.setRoleName(role.getName());
        // 查询用户-角色-权限
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(userScreen.getRoleId());
        List<AuthCode> authCodes = answer.getData().getAuthCodes();
        userScreen.setAuthCodes(authCodes);

        //查询相同手机号的其他角色
        if(StringUtils.isNotEmpty(userScreen.getPhone())) {
            List<UserScreen> otherUsers = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                            .andIdNotEqualTo(userId).andPhoneEqualTo(userScreen.getPhone()).andIsCancelEqualTo(false).example())
                    .stream().filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherUsers)) {
                userScreen.setOtherRoles(otherUsers.stream().map(item -> {
                    UserRoleVO userRoleVO = new UserRoleVO();
                    userRoleVO.setUserId(item.getId());
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(item.getRoleId());
                    userRoleVO.setRoleName(roleInfoDTO.getName());
                    userRoleVO.setSystem(roleInfoDTO.getSystem());
                    userRoleVO.setRoleType(roleInfoDTO.getRoleType());
                    return userRoleVO;
                }).collect(Collectors.toList()));
            }

        }
        userScreen.setPhone(IOTEncodeUtils.decryptIOTMessage(userScreen.getPhone(),encryptKey));
        baseAnswer.setData(userScreen);
        return baseAnswer;

    }


    /**
     * 生产token，并且放入到redis
     *
     * @param
     * @return
     */
    private String generateToken(UserScreen user) {
        String userId = user.getId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(user.getIsAdmin() == null ? false : user.getIsAdmin());
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setUserName(user.getName());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey);
        loginIfo4Redis.setPhone(StringUtils.defaultIfEmpty(phone,"空白手机号"));
        // 封装loginIfo4Redis
        // 查询角色信息
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
        RoleAuthInfo roleAuthInfo = answer.getData();
        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        loginIfo4Redis.setSystem(SYSTEM_SCREEN);
        loginIfo4Redis.setRoleName(roleAuthInfo.getName());
        loginIfo4Redis.setProvince(user.getProvince());
        log.info("大屏缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        if (user.getIsAdmin()){
            redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis);
        }else {
            redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);
        }

        return token;
    }
}
