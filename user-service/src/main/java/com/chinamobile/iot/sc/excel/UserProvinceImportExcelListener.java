package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.RoleInfoMapper;
import com.chinamobile.iot.sc.dao.UserMapper;
import com.chinamobile.iot.sc.dao.UserPartnerMapper;
import com.chinamobile.iot.sc.dao.UserProvinceBusinessMapper;
import com.chinamobile.iot.sc.enums.UserCompanyEnum;
import com.chinamobile.iot.sc.enums.UserPartnerNameEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.UserPartnerImportFailDTO;
import com.chinamobile.iot.sc.pojo.dto.UserProvinceImportFailDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.vo.request.Request4AddUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;

import static com.chinamobile.iot.sc.common.BaseConstant.*;
import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_PROVINCE;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/11/1 9:40
 * @description:
 **/
@Slf4j
public class UserProvinceImportExcelListener extends AnalysisEventListener<UserProvinceImportExcel> {


    private RoleInfoMapper roleInfoMapper;


    private UserMapper userMapper;


    private UserPartnerMapper userPartnerMapper;


    private ProvinceCityConfig provinceCityConfig;



    private UserProvinceBusinessMapper userProvinceBusinessMapper;


    private String encryptKey;

    private List<Request4AddUser> userProvinceSuccessList = new ArrayList<>();

    private List<UserProvinceImportFailDTO> userProvinceImportFailList = new ArrayList<>();

    public UserProvinceImportExcelListener(RoleInfoMapper roleInfoMapper, UserMapper userMapper, UserPartnerMapper userPartnerMapper, ProvinceCityConfig provinceCityConfig, UserProvinceBusinessMapper userProvinceBusinessMapper, String encryptKey) {
        this.roleInfoMapper = roleInfoMapper;
        this.userMapper = userMapper;
        this.userPartnerMapper = userPartnerMapper;
        this.provinceCityConfig = provinceCityConfig;
        this.userProvinceBusinessMapper = userProvinceBusinessMapper;
        this.encryptKey = encryptKey;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (!"角色名称".equals(headMap.get(0)) || !"账号类型".equals(headMap.get(1)) || !"手机号".equals(headMap.get(2))
                || !"姓名".equals(headMap.get(3)) || !"邮箱".equals(headMap.get(4)) || !"所属单位".equals(headMap.get(5))
                || !"省域".equals(headMap.get(6))){
            throw new BusinessException(StatusConstant.IMPORT_EXCEL_ERROR);
        }
    }

    @Override
    public void invoke(UserProvinceImportExcel data, AnalysisContext context) {
        int currentRow = context.readRowHolder().getRowIndex()+1;
        String failedReason="";
        String name = data.getName();
        String email = data.getEmail();
        String phone = data.getPhone();
        String roleName = data.getRoleName();
        String userTypeName = data.getUserTypeName();
        String province = data.getProvince();
        String beId = null;
        String company = data.getCompany();

        RoleInfo roleInfo =null;
        List<List<String>> provinceLocationList = new ArrayList<>();
        if (StringUtils.isEmpty(userTypeName)){
            failedReason = failedReason+ "账号类型不能为空".concat("错误行"+currentRow);
        }else {
            if (!"正式账号".equals(userTypeName) && !"测试账号".equals(userTypeName)){
                failedReason = failedReason+ "账号类型为正式账号或者测试账号".concat("错误行"+currentRow);
            }else {
                if ("正式账号".equals(userTypeName)){
                    userTypeName ="normal";
                }else if ("测试账号".equals(userTypeName)){
                    userTypeName ="test";
                }
            }
        }
        if (StringUtils.isEmpty(roleName)){
            failedReason = failedReason+ "角色名称不能为空".concat("错误行"+currentRow);
        }else {
            List<RoleInfo> roleInfos = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria().andNameEqualTo(roleName).example());
            if (CollectionUtils.isEmpty(roleInfos)){
                failedReason = failedReason+ "角色名称不存在".concat("错误行"+currentRow);
            }else {
                roleInfo = roleInfos.get(0);
                String system = roleInfo.getSystem();
                if (!"province".equals(system)){
                    failedReason = failedReason+ "角色名称不属于省业管系统".concat("错误行"+currentRow);
                }
            }
        }

        if (StringUtils.isEmpty(phone)){
            failedReason = failedReason+ "手机号不能为空".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexPhone(phone)){
                failedReason = failedReason+ "手机号错误".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(name)){
            failedReason = failedReason+ "姓名不能为空".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexOperatorName(name)) {
                failedReason = failedReason+ "姓名由中英文组成，长度不限".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(email)){
            failedReason = failedReason+ "联系人邮箱不能为空".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexEmail(email)){
                failedReason = failedReason+ "邮箱格式错误错误".concat("错误行"+currentRow);
            }
        }
        //判断地市域
        if (StringUtils.isEmpty(company)){
            failedReason = failedReason+ "所属单位不能为空".concat("错误行"+currentRow);
        }else {
            String code = UserCompanyEnum.fromCode(company);
            if (StringUtils.isEmpty(code)){
                failedReason = failedReason+ "所属单位错误".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(province)){
            failedReason = failedReason+ "省域不能为空".concat("错误行"+currentRow);
        }else {
            HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
             beId = provinceNameCodeMap.get(province);
            if (StringUtils.isEmpty(beId)){
                failedReason = failedReason+ "省域名称错误".concat("错误行"+currentRow);
            }else {
                provinceLocationList.add(Collections.singletonList(beId));
            }
        }


        if (StringUtils.isNotEmpty(phone) && Optional.ofNullable(roleInfo).isPresent()){
            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);

            //手机号是否被注册相同角色账号
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone)
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(partners)) {
                failedReason = failedReason+"手机号合作伙伴中心已存在".concat("错误行"+currentRow);
            }

            //手机号是否被OS系统注册，OS与合作伙伴中心不能共用手机号
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (ObjectUtils.isNotEmpty(users)) {
                failedReason = failedReason+"新手机号已使用".concat("错误行"+currentRow);
            }

            //手机号是否被注册省业管员
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(userProvinceBusinesses)) {
                failedReason = failedReason+"手机号省业管员已注册".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(failedReason)){
            Request4AddUser request4AddUser = new Request4AddUser();
            request4AddUser.setCompany(company);
            request4AddUser.setBeId(beId);
            request4AddUser.setEmail(email);
            request4AddUser.setPhone(phone);
            request4AddUser.setProvince(province);
            request4AddUser.setProvinceLocationList(provinceLocationList);
            request4AddUser.setRoleId(roleInfo.getId());
            request4AddUser.setRoleName(roleInfo.getName());
            request4AddUser.setSystem("province");
            request4AddUser.setUserFrom("other");
            request4AddUser.setUserName(name);
            request4AddUser.setUserType(userTypeName);
            userProvinceSuccessList.add(request4AddUser);
        }else {
            UserProvinceImportFailDTO userProvinceImportFailDTO = new UserProvinceImportFailDTO();
            BeanUtils.copyProperties(data,userProvinceImportFailDTO);
            userProvinceImportFailDTO.setFailedReason(failedReason);
            userProvinceImportFailList.add(userProvinceImportFailDTO);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("账号开通（省业管中心）导入所有数据解析完成！");
    }

    public List<Request4AddUser> getUserProvinceSuccessList() {
        return userProvinceSuccessList;
    }

    public List<UserProvinceImportFailDTO> getUserProvinceImportFailList() {
        return userProvinceImportFailList;
    }
}
