package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.annotation.CodeValidMark;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.ProvinceDTO;
import com.chinamobile.iot.sc.service.UserScreenService;
import com.chinamobile.iot.sc.vo.request.Request4Login;
import com.chinamobile.iot.sc.vo.request.RequestAddUserScreen;
import com.chinamobile.iot.sc.vo.request.RequestEditUserScreen;
import com.chinamobile.iot.sc.vo.request.RequestUserScreenPage;
import com.chinamobile.iot.sc.vo.response.DataUserScreen;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.SCREEN_USER;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/8/15 10:17
 * @description: 大屏用户控制类
 **/
@RequestMapping("/base")
@RestController
public class UserScreenController {


    @Resource
    private UserScreenService userScreenService;

    /**
     * 大屏超级管理员登录
     * @param request4Login
     * @return
     */
    @CodeValidMark
    @PostMapping("/userScreen/adminLogin")
    public BaseAnswer adminScreenLogin(@RequestBody Request4Login request4Login){
        return userScreenService.adminLoginScreen(request4Login);
    }


    /**
     * 大屏管理员及省管理员验证码登录
     * @param phone
     * @return
     */
    @CodeValidMark(validCode =false,validSmsCode = true)
    @PostMapping("/userScreen/loginByCode")
    public BaseAnswer loginScreenByCode(@RequestParam String phone){
        return userScreenService.loginByCodeScreen(phone);
    }

    @PostMapping("/userScreen/changeRole") //对于多角色用户，调整当前登录角色
    public BaseAnswer changeRole(@RequestParam(value = "userId")  String userId,
                                 @RequestParam(value = "system")  String system,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userScreenService.changeRole(userId,system,loginIfo4Redis);
    }

    /**
     * 大屏用户登出
     * @param userId
     * @return
     */
    @PostMapping("/userScreen/loginOut")
    public BaseAnswer loginScreenOut(@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        return userScreenService.loginOutScreen(userId);
    }

    /**
     * 大屏用户新增
     * @param addUserScreen
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {SCREEN_USER})
    @PostMapping("/userScreen/add")
    public BaseAnswer addUserScreen(@Valid @RequestBody RequestAddUserScreen addUserScreen
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userScreenService.addUserScreen(addUserScreen, userId, loginIfo4Redis);
    }

    /**
     * 删除大屏用户
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {SCREEN_USER})
    @DeleteMapping("/userScreen/delete")
    public BaseAnswer deleteUserScreen(@RequestParam("userId") String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userScreenService.deleteUserScreen(userId, loginIfo4Redis);
    }

    /**
     * 编辑大屏用户信息
     * @param editUserScreen
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {SCREEN_USER})
    @PostMapping("/userScreen/edit")
    public BaseAnswer editUserScreen(@Validated @RequestBody RequestEditUserScreen editUserScreen
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userScreenService.editUserScreen(editUserScreen, loginIfo4Redis);
    }

    /**
     * 分页查询大屏用户信息
     * @param userScreenPage
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {SCREEN_USER})
    @GetMapping("/userScreen/page")
    public BaseAnswer<PageData<DataUserScreen>> userPageScreen(RequestUserScreenPage userScreenPage
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userScreenService.userScreenPage(userScreenPage, loginIfo4Redis);
    }

    /**
     * 查询大屏用户详情信息
     * @param userId
     * @return
     */
    @GetMapping({"/userScreen/info"})
    public BaseAnswer<DataUserScreen> userScreenInfo(@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        return userScreenService.userScreenInfo(userId);
    }


    /**
     * 查询省份城市信息
     * @return
     */
    @GetMapping("/userScreen/provinceCity")
    public BaseAnswer<List<ProvinceDTO>> getProvinceCity(){
        return userScreenService.getProvinceCity();
    }

    /**
     * 查询省份城市信息
     * @return
     */
    @GetMapping("/userScreen/provinceCityList")
    public BaseAnswer<List<ProvinceDTO>> getProvinceCityList(@RequestParam(value = "provinceList",required = false) List<String> provinceList){
        return userScreenService.getProvinceCityList(provinceList);
    }

    /**
     * 查询省份 地市信息 参数版本 用于k+x库存配置地市信息查询
     * @return
     */
    @GetMapping("/userScreen/queryProvinceCityListKx")
    public BaseAnswer<List<ProvinceDTO>> queryProvinceCityListKx(@RequestParam(value = "provinceList",required = false) List<String> provinceList){
        return userScreenService.getProvinceCityListKx(provinceList);
    }
}
