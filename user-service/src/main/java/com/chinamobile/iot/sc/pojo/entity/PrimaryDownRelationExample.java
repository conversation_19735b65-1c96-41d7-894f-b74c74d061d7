package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class PrimaryDownRelationExample {
    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelationExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelationExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        PrimaryDownRelationExample example = new PrimaryDownRelationExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelationExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelationExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPrimaryDownIdIsNull() {
            addCriterion("primary_down_id is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdIsNotNull() {
            addCriterion("primary_down_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdEqualTo(String value) {
            addCriterion("primary_down_id =", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdNotEqualTo(String value) {
            addCriterion("primary_down_id <>", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdNotEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdGreaterThan(String value) {
            addCriterion("primary_down_id >", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdGreaterThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdGreaterThanOrEqualTo(String value) {
            addCriterion("primary_down_id >=", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdGreaterThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLessThan(String value) {
            addCriterion("primary_down_id <", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLessThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLessThanOrEqualTo(String value) {
            addCriterion("primary_down_id <=", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLessThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_down_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLike(String value) {
            addCriterion("primary_down_id like", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdNotLike(String value) {
            addCriterion("primary_down_id not like", value, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdIn(List<String> values) {
            addCriterion("primary_down_id in", values, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdNotIn(List<String> values) {
            addCriterion("primary_down_id not in", values, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdBetween(String value1, String value2) {
            addCriterion("primary_down_id between", value1, value2, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdNotBetween(String value1, String value2) {
            addCriterion("primary_down_id not between", value1, value2, "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIsNull() {
            addCriterion("primary_user_id is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIsNotNull() {
            addCriterion("primary_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdEqualTo(String value) {
            addCriterion("primary_user_id =", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotEqualTo(String value) {
            addCriterion("primary_user_id <>", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThan(String value) {
            addCriterion("primary_user_id >", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("primary_user_id >=", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThan(String value) {
            addCriterion("primary_user_id <", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanOrEqualTo(String value) {
            addCriterion("primary_user_id <=", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("primary_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLike(String value) {
            addCriterion("primary_user_id like", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotLike(String value) {
            addCriterion("primary_user_id not like", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIn(List<String> values) {
            addCriterion("primary_user_id in", values, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotIn(List<String> values) {
            addCriterion("primary_user_id not in", values, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdBetween(String value1, String value2) {
            addCriterion("primary_user_id between", value1, value2, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotBetween(String value1, String value2) {
            addCriterion("primary_user_id not between", value1, value2, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdIsNull() {
            addCriterion("down_user_id is null");
            return (Criteria) this;
        }

        public Criteria andDownUserIdIsNotNull() {
            addCriterion("down_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andDownUserIdEqualTo(String value) {
            addCriterion("down_user_id =", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdNotEqualTo(String value) {
            addCriterion("down_user_id <>", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdNotEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdGreaterThan(String value) {
            addCriterion("down_user_id >", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdGreaterThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("down_user_id >=", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdGreaterThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdLessThan(String value) {
            addCriterion("down_user_id <", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdLessThanColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdLessThanOrEqualTo(String value) {
            addCriterion("down_user_id <=", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdLessThanOrEqualToColumn(PrimaryDownRelation.Column column) {
            addCriterion(new StringBuilder("down_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDownUserIdLike(String value) {
            addCriterion("down_user_id like", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdNotLike(String value) {
            addCriterion("down_user_id not like", value, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdIn(List<String> values) {
            addCriterion("down_user_id in", values, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdNotIn(List<String> values) {
            addCriterion("down_user_id not in", values, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdBetween(String value1, String value2) {
            addCriterion("down_user_id between", value1, value2, "downUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdNotBetween(String value1, String value2) {
            addCriterion("down_user_id not between", value1, value2, "downUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryDownIdLikeInsensitive(String value) {
            addCriterion("upper(primary_down_id) like", value.toUpperCase(), "primaryDownId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLikeInsensitive(String value) {
            addCriterion("upper(primary_user_id) like", value.toUpperCase(), "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andDownUserIdLikeInsensitive(String value) {
            addCriterion("upper(down_user_id) like", value.toUpperCase(), "downUserId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Fri May 13 14:27:49 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private PrimaryDownRelationExample example;

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        protected Criteria(PrimaryDownRelationExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public PrimaryDownRelationExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri May 13 14:27:49 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        void example(PrimaryDownRelationExample example);
    }
}