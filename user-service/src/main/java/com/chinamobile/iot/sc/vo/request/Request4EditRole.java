package com.chinamobile.iot.sc.vo.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.vo.request
 * @ClassName: Request4EditRole
 * @description: 编辑角色
 * @author: zyj
 * @create: 2021/11/2 17:21
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class Request4EditRole {

    @NotBlank
    private String roleId;

    @Size(min = 1,max = 20)
    @NotBlank
    private String name;

    @Size(min = 1)
    private List<String> authIds;

    private String des;

}
