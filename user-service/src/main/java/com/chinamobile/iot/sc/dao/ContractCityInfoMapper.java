package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.ContractCityInfo;
import com.chinamobile.iot.sc.pojo.entity.ContractCityInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ContractCityInfoMapper {
    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    long countByExample(ContractCityInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int deleteByExample(ContractCityInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int deleteByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int insert(ContractCityInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int insertSelective(ContractCityInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    List<ContractCityInfo> selectByExample(ContractCityInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    ContractCityInfo selectByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int updateByExampleSelective(@Param("record") ContractCityInfo record, @Param("example") ContractCityInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int updateByExample(@Param("record") ContractCityInfo record, @Param("example") ContractCityInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int updateByPrimaryKeySelective(ContractCityInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int updateByPrimaryKey(ContractCityInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int batchInsert(@Param("list") List<ContractCityInfo> list);

    /**
     *
     * @mbg.generated Mon Sep 25 10:38:06 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ContractCityInfo> list, @Param("selective") ContractCityInfo.Column ... selective);
}