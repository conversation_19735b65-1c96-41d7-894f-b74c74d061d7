package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/10/31 10:16
 * @description: 合作伙伴主导入excel
 **/
@Data
public class UserPartnerLordImportExcel {

    /**
     * 系统
     */
    @ExcelProperty(value = "系统",index = 0)
    private String system;

    /**
     * 合作伙伴名称
     */
    @ExcelProperty(value = "合作伙伴名称",index = 1)
    private String companyName;


    /**
     * 角色名称
     */
    @ExcelProperty(value = "角色名称",index = 2)
    private String roleName;


    /**
     * 账号类型
     */
    @ExcelProperty(value = "账号类型",index = 3)
    private String userTypeName;


    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名",index = 4)
    private String account;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号",index = 5)
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱",index = 6)
    private String email;

    /**
     * 是否为外部人员
     */
    @ExcelProperty(value = "是否为外部人员",index = 7)
    private String isExternal;

    /**
     * 省域/地市域
     */
    @ExcelProperty(value = "省域/地市域",index = 8)
    private String provinceLocation;

    /**
     * 社会统一信用代码
     */
    @ExcelProperty(value = "社会统一信用代码",index = 9)
    private String unifiedCode;



}
