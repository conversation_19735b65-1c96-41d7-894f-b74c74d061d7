package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.enums.UserPartnerNameEnum;
import com.chinamobile.iot.sc.enums.UserSystemEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.UserManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.dto.UserAdminImportFailDTO;
import com.chinamobile.iot.sc.pojo.dto.UserPartnerLordImportFailDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.vo.request.Request4AddUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;

import static com.chinamobile.iot.sc.common.BaseConstant.*;
import static com.chinamobile.iot.sc.constant.PartnerProvinceConstant.PROVINCE;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/10/31 10:16
 * @description:
 **/
@Slf4j
public class UserPartnerLordImportExcelListener extends AnalysisEventListener<UserPartnerLordImportExcel> {


    private RoleInfoMapper roleInfoMapper;


    private UserMapper userMapper;


    private UserPartnerMapper userPartnerMapper;



    private UserProvinceBusinessMapper userProvinceBusinessMapper;

    private UserScreenMapper userScreenMapper;

    private UserPartnerCompanyMapper userPartnerCompanyMapper;

    private ProvinceCityConfig provinceCityConfig;

    private LoginIfo4Redis loginIfo4Redis;


    private String encryptKey;

    public UserPartnerLordImportExcelListener(RoleInfoMapper roleInfoMapper, UserMapper userMapper,
                                              UserPartnerMapper userPartnerMapper, UserProvinceBusinessMapper userProvinceBusinessMapper,
                                              String encryptKey,UserScreenMapper userScreenMapper,UserPartnerCompanyMapper userPartnerCompanyMapper,
                                              ProvinceCityConfig provinceCityConfig,LoginIfo4Redis loginIfo4Redis) {
        this.roleInfoMapper = roleInfoMapper;
        this.userMapper = userMapper;
        this.userPartnerMapper = userPartnerMapper;
        this.userProvinceBusinessMapper = userProvinceBusinessMapper;
        this.encryptKey = encryptKey;
        this.userScreenMapper = userScreenMapper;
        this.userPartnerCompanyMapper = userPartnerCompanyMapper;
        this.provinceCityConfig = provinceCityConfig;
        this.loginIfo4Redis = loginIfo4Redis;
    }

    private List<Request4AddUser> userPartnerLordSuccessList = new ArrayList<>();

    private List<UserPartnerLordImportFailDTO> userPartnerLordImportFailList = new ArrayList<>();

 /*   @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (!"系统".equals(headMap.get(0)) || !"合作伙伴名称".equals(headMap.get(1)) || !"角色名称".equals(headMap.get(2)) || !"账号类型".equals(headMap.get(3))
                || !"姓名".equals(headMap.get(4)) || !"手机号".equals(headMap.get(5))
        || !"邮箱".equals(headMap.get(6)) || !"是否为外部人员".equals(headMap.get(7)) || !"省域/地市域".equals(headMap.get(8))
                || !"社会统一信用代码".equals(headMap.get(9))){
            throw new BusinessException(StatusConstant.IMPORT_EXCEL_ERROR);
        }
    }*/

    @Override
    public void invoke(UserPartnerLordImportExcel data, AnalysisContext context) {
        int currentRow = context.readRowHolder().getRowIndex()+1;
        String failedReason="";
        String name = data.getAccount();
        String email = data.getEmail();
        String phone = data.getPhone();
        String systemExcel = data.getSystem();
        String isExternalExcel = data.getIsExternal();
        Boolean isExternal = true;
        String companyName = data.getCompanyName();
        String roleName = data.getRoleName();
        String provinceLocation = data.getProvinceLocation();
        String unifiedCode = data.getUnifiedCode();
        String userTypeName = data.getUserTypeName();
        List<String> beIdList = new ArrayList<>();
        List<String> locationList = new ArrayList<>();
        List<String> beIdNameList = new ArrayList<>();
        List<String> locationNameList = new ArrayList<>();

        RoleInfo roleInfo =null;
        Boolean isSendBool = false;
        String companyType = null;
        String beId = null;
        //合作伙伴省
        String provinceName = null;
        String locationId = null;
        String companyId = null;
        List<List<String>> provinceLocationList = new ArrayList<>();

        if (StringUtils.isEmpty(roleName)){
            failedReason = failedReason+ "角色名称不能为空".concat("错误行"+currentRow);
        }else {
            List<RoleInfo> roleInfos = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria().andNameEqualTo(roleName).example());
            if (CollectionUtils.isEmpty(roleInfos)){
                failedReason = failedReason+ "角色名称不存在".concat("错误行"+currentRow);
            }else {
                roleInfo = roleInfos.get(0);
                String system = roleInfo.getSystem();
                if (!UserSystemEnum.PARTNERSYSTEM.systemCode.equals(system) && !UserSystemEnum.SCREENSYSTEM.systemCode.equals(system)
                        && !UserSystemEnum.PROVINCESYSTEM.systemCode.equals(system)){
                    failedReason = failedReason+ "角色名称不属于系统名称为合作伙伴中心或省业管中心或商城大屏".concat("错误行"+currentRow);
                }
            }

        }
        if (StringUtils.isEmpty(systemExcel)){
            failedReason = failedReason+ "系统名称不能为空".concat("错误行"+currentRow);
        }else {
            String systemName = UserSystemEnum.fromSystemName(systemExcel);
            if (StringUtils.isEmpty(systemName)){
                failedReason = failedReason+ "当前系统不存在不能为空".concat("错误行"+currentRow);
            }else {
                if (!UserSystemEnum.PARTNERSYSTEM.systemName.equals(systemExcel) && !UserSystemEnum.SCREENSYSTEM.systemName.equals(systemExcel)
                        && !UserSystemEnum.PROVINCESYSTEM.systemName.equals(systemExcel)){
                    failedReason = failedReason+ "系统名称为合作伙伴中心或省业管中心或商城大屏".concat("错误行"+currentRow);
                }
            }
        }
        if (StringUtils.isEmpty(userTypeName)){
            failedReason = failedReason+ "账号类型不能为空".concat("错误行"+currentRow);
        }else {
            if (!"正式账号".equals(userTypeName) && !"测试账号".equals(userTypeName)){
                failedReason = failedReason+ "账号类型为正式账号或者测试账号".concat("错误行"+currentRow);
            }else {
                if ("正式账号".equals(userTypeName)){
                    userTypeName ="normal";
                }else if ("测试账号".equals(userTypeName)){
                    userTypeName ="test";
                }
            }
        }
        if (StringUtils.isEmpty(roleName)){
            failedReason = failedReason+ "角色名称不能为空".concat("错误行"+currentRow);
        }else {
                List<RoleInfo> roleInfos = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria().andNameEqualTo(roleName).example());
                if (CollectionUtils.isEmpty(roleInfos)){
                    failedReason = failedReason+ "角色名称不存在".concat("错误行"+currentRow);
                }else {
                    roleInfo = roleInfos.get(0);
                }
        }


        if (StringUtils.isEmpty(name)){
            failedReason = failedReason+ "联系人姓名".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexOperatorName(name)) {
                failedReason = failedReason+ "姓名由中英文组成，长度不限".concat("错误行"+currentRow);
            }
        }
        if (StringUtils.isEmpty(phone)){
            failedReason = failedReason+ "联系人手机号不能为空".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexPhone(phone)){
                failedReason = failedReason+ "联系人手机号错误".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(email)){
            failedReason = failedReason+ "联系人邮箱不能为空".concat("错误行"+currentRow);
        }else {
            if (!RegexUtil.regexEmail(email)){
                failedReason = failedReason+ "邮箱格式错误错误".concat("错误行"+currentRow);
            }
        }

        List<UserPartnerCompany> userPartnerCompanies = userPartnerCompanyMapper.selectByExample(new UserPartnerCompanyExample().createCriteria().andCompanyNameEqualTo(companyName).example());
        if (CollectionUtils.isEmpty(userPartnerCompanies)){
            failedReason = failedReason+ "合作伙伴名称不存在".concat("错误行"+currentRow);
        }else {
            companyId = userPartnerCompanies.get(0).getId();
        }

        if (UserSystemEnum.PARTNERSYSTEM.systemCode.equals(systemExcel)){
            if (StringUtils.isEmpty(isExternalExcel)){
                failedReason = failedReason+ "合作伙伴中心用户是否为外部人员不能为空".concat("错误行"+currentRow);
            }else {
                //现在不根据单位类型判断
                String roleType = loginIfo4Redis.getRoleType();
                //系统管理员操作
                if (!SYSTEM_ADMIN_ROLE.equals(roleType)){
                    failedReason = failedReason+ "当前操作模板只能系统管理员操作".concat("错误行"+currentRow);
                }else {
                    //新增是主 随便选人员类型
                    String roleTypeAdd = roleInfo.getRoleType();
                    if (PARTNER_LORD_ROLE.equals(roleTypeAdd)) {
                        if (!"是".equals(isExternalExcel) && !"否".equals(isExternalExcel)) {
                            failedReason = failedReason + "是否是外部人员该为是或者否".concat("错误行" + currentRow);
                        } else {
                            if ("是".equals(isExternalExcel)) {
                                isExternal = true;
                            } else {
                                isExternal = false;
                            }
                        }
                        //新增主 判断合作伙伴主是否已经存在
                        List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                                .andPartnerNameEqualTo(companyName)
                                .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                        if (CollectionUtils.isNotEmpty(primaryUsers)) {
                            failedReason = failedReason + "当前新增的合作伙伴名称下的合作主角色已存在(合作主唯一)".concat("错误行" + currentRow);
                        }
                    } else {
                        if (!"是".equals(isExternalExcel) && !"否".equals(isExternalExcel)) {
                            failedReason = failedReason + "是否是外部人员该为是或者否".concat("错误行" + currentRow);
                        } else {
                        //新增其他合作伙伴判断主是否存在
                        List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                                .andPartnerNameEqualTo(companyName)
                                .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                        //新增装维主  判断装维主是否已经存在
                        if (PARTNER_INSTALL_LORD.equals(roleTypeAdd)) {
                            List<UserPartner> installUserLord = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                                    .andPartnerNameEqualTo(companyName)
                                    .andRoleIdEqualTo(roleInfo.getId()).andIsLogoffEqualTo(false).example());
                            if (CollectionUtils.isNotEmpty(installUserLord)) {
                                failedReason = failedReason + "当前新增的合作伙伴名称下的装维主已经存在(装维主唯一)".concat("错误行" + currentRow);
                            }
                        }
                        if (CollectionUtils.isEmpty(primaryUsers)) {
                            failedReason = failedReason + "当前新增合作伙伴主合作伙伴不存在".concat("错误行" + currentRow);
                        } else {
                            UserPartner partnerLord = primaryUsers.get(0);
                            Boolean isExternalLord = partnerLord.getIsExternal();
                            if (isExternalLord && "否".equals(isExternalExcel)) {
                                failedReason = failedReason + "合作主是外部人员，当前合作伙伴也应该是外部人员".concat("错误行" + currentRow);
                            } else {
                                if ("是".equals(isExternalExcel)) {
                                    isExternal = true;
                                } else {
                                    isExternal = false;
                                }
                            }
                        }
                    }
                }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(userPartnerCompanies)){
        UserPartnerCompany userPartnerCompany = userPartnerCompanies.get(0);
         companyType = userPartnerCompany.getCompanyType();
        if (StringUtils.isEmpty(companyType)){
            failedReason = failedReason+ "合作伙伴公司的单位类型不能为空".concat("错误行"+currentRow);
        }
        }
        String roleType = roleInfo.getRoleType();

        if (PARTNER_BAOLI.equals(roleType)){
            if (StringUtils.isEmpty(unifiedCode)){
                failedReason = failedReason+ "当角色类型是保理时社会统一信用代码必填".concat("错误行"+currentRow);
            }
        }
        //合作伙伴省
        if (UserSystemEnum.PARTNERSYSTEM.systemName.equals(systemExcel) && PROVINCE.equals(companyType) && PARTNER_PROVINCE.equals(roleType)){
            if (StringUtils.isEmpty(provinceLocation)){
                failedReason = failedReason+ "当角色类型是合作省管时地市域必填".concat("错误行"+currentRow);
            }else {
                 beId = UserPartnerNameEnum.fromCode(companyName);
                 provinceName = UserPartnerNameEnum.fromProvinceName(companyName);
                HashMap<String, String> cityNameCodeMap = provinceCityConfig.getCityNameCodeMap();
                 locationId = cityNameCodeMap.get(provinceLocation);
            }
        }
      //商城大屏

        if (UserSystemEnum.SCREENSYSTEM.systemName.equals(systemExcel)){
            if (StringUtils.isEmpty(provinceLocation)){
                failedReason = failedReason+ "省域不能为空".concat("错误行"+currentRow);
            }else {
                if ("全部".equals(provinceLocation)){
                    beIdList.add("所有省");
                    beIdNameList.add("所有省");
                }else {
                    //解析省域数据
                    HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
                    HashMap<String, String> cityNameCodeMap = provinceCityConfig.getCityNameCodeMap();
                    String[] split = provinceLocation.split(",");
                    for (String provinceNameScreen : split) {
                        String[] split1 = provinceNameScreen.split("-");
                        if (split1.length==1){
                            String beIdName = split1[0];
                            beIdNameList.add(beIdName);
                            beId = provinceNameCodeMap.get(beIdName);
                            if (StringUtils.isEmpty(beId)){
                                failedReason = failedReason+ "省域名称错误".concat("错误行"+currentRow);
                            }else {
                                beIdList.add(beId);
                                //封装redis需要的值
                                provinceLocationList.add(beIdList);
                            }
                        }else if (split1.length==2){
                            String beIdName = split1[0];
                            String locationName = split1[1];
                            beIdNameList.add(beIdName);
                            locationNameList.add(locationName);
                             beId = provinceNameCodeMap.get(beIdName);
                             locationId = cityNameCodeMap.get(locationName);
                            if (StringUtils.isEmpty(beId) && StringUtils.isEmpty(locationId)){
                                failedReason = failedReason+ "省域名称省份或者地市错误".concat("错误行"+currentRow);
                            }else {
                                //封装redis需要的值
                                beIdList.add(beId);
                                locationList.add(locationId);
                                List<String>  provinceLocationScreen = new ArrayList<>();
                                provinceLocationScreen.add(beId);
                                provinceLocationScreen.add(locationId);
                                provinceLocationList.add(provinceLocationScreen);
                            }
                        }else {
                            failedReason = failedReason+ "省域格式错误".concat("错误行"+currentRow);
                        }
                    }
                }
            }
        }

        //省业管中心
        if (UserSystemEnum.PROVINCESYSTEM.systemName.equals(systemExcel)){
            String[] parts = provinceLocation.split("/");
            //只有省份
            HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
            HashMap<String, String> cityNameCodeMap = provinceCityConfig.getCityNameCodeMap();
            if (parts.length==1){
                String beIdName = parts[0];
                beIdNameList.add(beIdName);
                beId = provinceNameCodeMap.get(beIdName);
                if (StringUtils.isEmpty(beId)){
                    failedReason = failedReason+ "省域名称错误".concat("错误行"+currentRow);
                }else {
                    beIdList.add(beId);
                    //封装redis需要的值
                    provinceLocationList.add(beIdList);
                }
            }else if (parts.length ==2){
                String beIdName = parts[0];
                String locationName = parts[1];
                beIdNameList.add(beIdName);
                locationNameList.add(locationName);
                beId = provinceNameCodeMap.get(beIdName);
                locationId = cityNameCodeMap.get(locationName);
                if (StringUtils.isEmpty(beId) && StringUtils.isEmpty(locationId)){
                    failedReason = failedReason+ "省域名称省份或者地市错误".concat("错误行"+currentRow);
                }else {
                    //封装redis需要的值
                    beIdList.add(beId);
                    locationList.add(locationId);
                    List<String>  provinceLocationBu = new ArrayList<>();
                    provinceLocationBu.add(beId);
                    provinceLocationBu.add(locationId);
                    provinceLocationList.add(provinceLocationBu);
                }
            }else {
                failedReason = failedReason+ "省业管中心省域格式错误".concat("错误行"+currentRow);
            }
        }



        if (StringUtils.isNotEmpty(phone) && Optional.ofNullable(roleInfo).isPresent()){
            if (UserSystemEnum.PROVINCESYSTEM.systemName.equals(systemExcel)
            || UserSystemEnum.PARTNERSYSTEM.systemName.equals(systemExcel)){
            //手机号是否被注册相同角色账号
                String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone)
                    .andRoleIdEqualTo(roleInfo.getId())
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(partners)) {
                failedReason = failedReason+"手机号合作伙伴中心已存在".concat("错误行"+currentRow);
            }

            //手机号是否被OS系统注册，OS与合作伙伴中心不能共用手机号
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (ObjectUtils.isNotEmpty(users)) {
                failedReason = failedReason+"新手机号已使用".concat("错误行"+currentRow);
            }

            //手机号是否被注册省业管员
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(userProvinceBusinesses)) {
                failedReason = failedReason+"手机号省业管员已注册".concat("错误行"+currentRow);
            }

            //校验合作伙伴名称是否存在
            List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPartnerNameEqualTo(companyName)
                            .andRoleIdEqualTo(roleInfo.getId())
                    .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(primaryUsers)) {
                failedReason = failedReason+"该合作伙伴的合作主已存在".concat("错误行"+currentRow);
            }
        }
        }else {
            //大屏
            //手机号是否被注册
            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
            List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(users)) {
                failedReason = failedReason+ "新手机号已使用！".concat("错误行"+currentRow);

            }
        }

        if (StringUtils.isEmpty(failedReason)){
            //合作伙伴中心
            Request4AddUser request4AddUser = new Request4AddUser();
            if (UserSystemEnum.PARTNERSYSTEM.systemName.equals(systemExcel)){
                request4AddUser.setCompany(companyType);
                request4AddUser.setEmail(email);
                request4AddUser.setIsSend(isSendBool);
                request4AddUser.setPartnerName(companyName);
                request4AddUser.setPhone(phone);
                request4AddUser.setRoleId(roleInfo.getId());
                request4AddUser.setRoleName(roleInfo.getName());
                request4AddUser.setSystem("partner");
                request4AddUser.setUserName(name);
                request4AddUser.setIsExternal(isExternal);
                request4AddUser.setCompanyId(companyId);
                request4AddUser.setCompanyType(companyType);
                request4AddUser.setRegion(provinceLocation);
                request4AddUser.setRegionId(locationId);
                request4AddUser.setUserType(userTypeName);
            }else if (UserSystemEnum.SCREENSYSTEM.systemName.equals(systemExcel)){
                //大屏
                request4AddUser.setBeId(String.join(",",beIdList));
                request4AddUser.setCompany(companyName);
                request4AddUser.setCompanyName(companyName);
                request4AddUser.setCompanyId(companyId);
                request4AddUser.setCompanyType(companyType);
                request4AddUser.setEmail(email);
                request4AddUser.setLocation(String.join(",",locationList));
                request4AddUser.setLocationName(String.join(",",locationNameList));
                request4AddUser.setPhone(phone);
                request4AddUser.setProvince(String.join(",",beIdNameList));
                request4AddUser.setProvinceLocationList(provinceLocationList);
                request4AddUser.setRoleId(roleInfo.getId());
                request4AddUser.setRoleName(roleInfo.getName());
                request4AddUser.setSystem("screen");
                request4AddUser.setUserFrom("other");
                request4AddUser.setUserName(name);
                request4AddUser.setUserType(userTypeName);
            }else {
                //省业管
                request4AddUser.setCompany(companyName);
                request4AddUser.setCompanyName(companyName);
                request4AddUser.setCompanyId(companyId);
                request4AddUser.setCompanyType(companyType);
                request4AddUser.setBeId(String.join(",",beIdList));
                request4AddUser.setEmail(email);
                request4AddUser.setPhone(phone);
                request4AddUser.setProvince(String.join(",",beIdNameList));
                request4AddUser.setProvinceLocationList(provinceLocationList);
                request4AddUser.setLocation(String.join(",",locationList));
                request4AddUser.setLocationName(String.join(",",locationNameList));
                request4AddUser.setRoleId(roleInfo.getId());
                request4AddUser.setRoleName(roleInfo.getName());
                request4AddUser.setSystem("province");
               // request4AddUser.setUserFrom("other");
                request4AddUser.setUserName(name);
                request4AddUser.setUserType(userTypeName);
            }
            userPartnerLordSuccessList.add(request4AddUser);
        }else {
            UserPartnerLordImportFailDTO userPartnerLordImportFailDTO = new UserPartnerLordImportFailDTO();
            BeanUtils.copyProperties(data,userPartnerLordImportFailDTO);
            userPartnerLordImportFailDTO.setFailedReason(failedReason);
            userPartnerLordImportFailList.add(userPartnerLordImportFailDTO);
        }


    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("账号开通（合作伙伴主）导入所有数据解析完成！");
    }

    public List<Request4AddUser> getUserPartnerLordSuccessList() {
        return userPartnerLordSuccessList;
    }

    public List<UserPartnerLordImportFailDTO> getUserPartnerLordImportFailList() {
        return userPartnerLordImportFailList;
    }
}
