package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 角色操作权限表
 *
 * <AUTHOR>
public class RoleOperatePermission implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private String id;

    /**
     * 角色ID
     *
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private String roleId;

    /**
     * 操作权限ID
     *
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private String operatePermissionId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..role_operate_permission.id
     *
     * @return the value of supply_chain..role_operate_permission.id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public RoleOperatePermission withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_operate_permission.id
     *
     * @param id the value for supply_chain..role_operate_permission.id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..role_operate_permission.role_id
     *
     * @return the value of supply_chain..role_operate_permission.role_id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public RoleOperatePermission withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_operate_permission.role_id
     *
     * @param roleId the value for supply_chain..role_operate_permission.role_id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * This method returns the value of the database column supply_chain..role_operate_permission.operate_permission_id
     *
     * @return the value of supply_chain..role_operate_permission.operate_permission_id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public String getOperatePermissionId() {
        return operatePermissionId;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public RoleOperatePermission withOperatePermissionId(String operatePermissionId) {
        this.setOperatePermissionId(operatePermissionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_operate_permission.operate_permission_id
     *
     * @param operatePermissionId the value for supply_chain..role_operate_permission.operate_permission_id
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public void setOperatePermissionId(String operatePermissionId) {
        this.operatePermissionId = operatePermissionId;
    }

    /**
     * This method returns the value of the database column supply_chain..role_operate_permission.create_time
     *
     * @return the value of supply_chain..role_operate_permission.create_time
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public RoleOperatePermission withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_operate_permission.create_time
     *
     * @param createTime the value for supply_chain..role_operate_permission.create_time
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..role_operate_permission.update_time
     *
     * @return the value of supply_chain..role_operate_permission.update_time
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public RoleOperatePermission withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_operate_permission.update_time
     *
     * @param updateTime the value for supply_chain..role_operate_permission.update_time
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", roleId=").append(roleId);
        sb.append(", operatePermissionId=").append(operatePermissionId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RoleOperatePermission other = (RoleOperatePermission) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getOperatePermissionId() == null ? other.getOperatePermissionId() == null : this.getOperatePermissionId().equals(other.getOperatePermissionId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getOperatePermissionId() == null) ? 0 : getOperatePermissionId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Apr 10 15:36:33 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        roleId("role_id", "roleId", "VARCHAR", false),
        operatePermissionId("operate_permission_id", "operatePermissionId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Apr 10 15:36:33 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}