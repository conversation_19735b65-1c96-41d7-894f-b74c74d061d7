package com.chinamobile.iot.sc.vo.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 已存在的用户添加account
 * <AUTHOR>
 */
@Data
public class ExistUserAddAccount {

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    /**
     * account
     */
    @NotEmpty(message = "account不能为空")
    private String account;

}
