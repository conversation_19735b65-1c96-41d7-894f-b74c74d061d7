package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.UserPartnerCompanyMapper;
import com.chinamobile.iot.sc.pojo.dto.UserHistoryCompanyFailDTO;
import com.chinamobile.iot.sc.pojo.dto.UserPartnerImportFailDTO;
import com.chinamobile.iot.sc.pojo.entity.UserPartnerCompany;
import com.chinamobile.iot.sc.vo.request.Request4AddUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2025/6/6 10:48
 * @description:
 **/
@Slf4j
public class UserHistoryCompanyExcelListener extends AnalysisEventListener<UserHistoryCompanyExcel> {


    private List<UserPartnerCompany> successList = new ArrayList<>();

    private List<UserHistoryCompanyFailDTO> failList = new ArrayList<>();

    private UserPartnerCompanyMapper userPartnerCompanyMapper;

    public UserHistoryCompanyExcelListener(UserPartnerCompanyMapper userPartnerCompanyMapper) {
        this.userPartnerCompanyMapper = userPartnerCompanyMapper;
    }

    @Override
    public void invoke(UserHistoryCompanyExcel data, AnalysisContext context) {
        int currentRow = context.readRowHolder().getRowIndex()+1;
        String failedReason="";
        String companyType = data.getCompanyType();
        String companyName = data.getCompanyName();
        Date date = new Date();
        if (StringUtils.isEmpty(companyType)){
            failedReason = failedReason+ "公司类型不能为空".concat("错误行"+currentRow);
        }else {
            if (!"省公司".equals(companyType) && !"非省公司".equals(companyType)){
                failedReason = failedReason+ "公司类型只能还是省公司或非省公司".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(companyName)){
            failedReason = failedReason+ "公司名称不能为空".concat("错误行"+currentRow);
        }
        if (StringUtils.isEmpty(failedReason)){
            UserPartnerCompany userPartnerCompany = new UserPartnerCompany();
            userPartnerCompany.setId(BaseServiceUtils.getId());
            userPartnerCompany.setCompanyType("非省公司".equals(companyType) ? "1" : "2");
            userPartnerCompany.setCompanyName(companyName);
            userPartnerCompany.setCreateTime(date);
            userPartnerCompany.setUpdateTime(date);
            userPartnerCompany.setCreateName("admin");
            successList.add(userPartnerCompany);
        }else {
            UserHistoryCompanyFailDTO userHistoryCompanyFailDTO = new UserHistoryCompanyFailDTO();
            userHistoryCompanyFailDTO.setCompanyType(companyType);
            userHistoryCompanyFailDTO.setCompanyName(companyName);
            userHistoryCompanyFailDTO.setFailedReason(failedReason);
            failList.add(userHistoryCompanyFailDTO);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("导入历史合作伙伴公司信息 导入所有数据解析完成！");
    }

    public List<UserPartnerCompany> getSuccessList() {
        return successList;
    }

    public List<UserHistoryCompanyFailDTO> getFailList() {
        return failList;
    }
}
