package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.RoleManage;
import com.chinamobile.iot.sc.pojo.entity.RoleManageExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RoleManageMapper {
    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    long countByExample(RoleManageExample example);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int deleteByExample(RoleManageExample example);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int insert(RoleManage record);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int insertSelective(RoleManage record);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    List<RoleManage> selectByExample(RoleManageExample example);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    RoleManage selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int updateByExampleSelective(@Param("record") RoleManage record, @Param("example") RoleManageExample example);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int updateByExample(@Param("record") RoleManage record, @Param("example") RoleManageExample example);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int updateByPrimaryKeySelective(RoleManage record);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int updateByPrimaryKey(RoleManage record);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int batchInsert(@Param("list") List<RoleManage> list);

    /**
     *
     * @mbg.generated Fri Apr 28 15:18:47 CST 2023
     */
    int batchInsertSelective(@Param("list") List<RoleManage> list, @Param("selective") RoleManage.Column ... selective);
}