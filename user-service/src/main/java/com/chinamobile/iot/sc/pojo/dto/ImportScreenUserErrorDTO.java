package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/9/12 15:28
 */
@Data
public class ImportScreenUserErrorDTO {
    @Excel(name = "所属系统")
    private String system;

    @Excel(name = "所属角色")
    private String roleName;

    @Excel(name = "用户对象")
    private String userFrom;

    @Excel(name = "账号类型")
    private String userType;

    @Excel(name = "手机号")
    private String phone;

    @Excel(name = "姓名")
    private String userName;

    @Excel(name = "邮箱")
    private String email;

    @Excel(name = "所属单位")
    private String company;

    @Excel(name = "省域")
    private String province;

    @Excel(name = "失败原因")
    private String failReason;


}
