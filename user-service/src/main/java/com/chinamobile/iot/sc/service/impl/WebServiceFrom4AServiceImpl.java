package com.chinamobile.iot.sc.service.impl;

import com.asiainfo.uap.util.des.EncryptInterface;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.Data4AConfig;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.vo.ManyUserOperate4AVO;
import com.chinamobile.iot.sc.service.WebServiceFrom4AService;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.ManyUserOperate4AUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.Constant.REDIS_PRE_KEY_4A_SESSIONID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/11
 * @description 从4A接口获取数据的service实现类
 */
@Service
@Slf4j
public class WebServiceFrom4AServiceImpl implements WebServiceFrom4AService {

    @Resource
    private Data4AConfig data4AConfig;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private UserServiceImpl userService;

    @Value("${supply.des.key}")
    private String encryptKey;

    @Override
    public ManyUserOperate4AVO check4AManyUserPassOperater(String userId,
                                                           String operateId) throws Exception {
        ManyUserOperate4AVO manyUserOperate4AVO = new ManyUserOperate4AVO();
        String serviceId = data4AConfig.getServiceId();
        // 校验4A金库是否触发的返回信息
//        String userId = "1024729530327584768";
        BaseAnswer<Data4User> data4UserBaseAnswer = userService.userInfo(userId,true,"");
        if (!"00000".equals(data4UserBaseAnswer.getStateCode())) {
            throw new BusinessException(BaseErrorConstant.USER_INVAILD);
        }
        Map responseInfoMap = ManyUserOperate4AUtil.check4AManyUserPassOperater(operateId,
                serviceId, IOTEncodeUtils.encryptIOTMessage(data4UserBaseAnswer.getData().getPhone(),encryptKey), data4AConfig.getUrl(), data4AConfig.getTargetNameSpace());

        String resOperateId = responseInfoMap.get("OPERATEID") + "";
        String resServiceId = responseInfoMap.get("SERVICEID") + "";
        if (!serviceId.equals(resServiceId)
                || !operateId.equals(resOperateId)) {
            log.error("订单导出返回的4A标识错误,OPERATEID:{},SERVICEID:{}", resOperateId, resServiceId);
            throw new BusinessException(BaseErrorConstant.RETURN_4A_DATA_ERROR);
        }

        String rsp = responseInfoMap.get("RSP") + "";
        manyUserOperate4AVO.setAllowManyUserOperate(rsp);
        manyUserOperate4AVO.setPolicyFlag(responseInfoMap.get("POLICYFLAG") + "");
        manyUserOperate4AVO.setSvnSn(responseInfoMap.get("SVNSN") + "");
        if ("-1".equals(rsp)) {
            manyUserOperate4AVO.setErrDesc(responseInfoMap.get("ERRDESC") + "");
        } else if ("0".equals(rsp)) {
            // 需要金库操作
        /*    BaseAnswer<Data4User> data4UserBaseAnswer = userService.userInfo(userId,true);
            if (!"00000".equals(data4UserBaseAnswer.getStateCode())) {
                throw new BusinessException(BaseErrorConstant.USER_INVAILD);
            }*/
            manyUserOperate4AVO.setSubLoginName(EncryptInterface.desEncryptData(data4UserBaseAnswer.getData().getAccount()));
            manyUserOperate4AVO.setServerIp(data4AConfig.getServerIp());
            manyUserOperate4AVO.setServerPort(data4AConfig.getServerPort());
            manyUserOperate4AVO.setAppCode(serviceId);
            String encryptOperCode = EncryptInterface.desEncryptData(operateId);
            manyUserOperate4AVO.setOperCode(encryptOperCode);
            String sessionId = BaseServiceUtils.getId();
            manyUserOperate4AVO.setSessionId(sessionId);
            // 将临时的session id存放到redis中
            redisTemplate.opsForValue().set(REDIS_PRE_KEY_4A_SESSIONID.concat(sessionId), sessionId, 600, TimeUnit.SECONDS);
        }
        return manyUserOperate4AVO;
    }
}
