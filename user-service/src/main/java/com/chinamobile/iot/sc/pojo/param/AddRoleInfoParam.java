package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/14 09:26
 * @description TODO
 */
@Data
public class AddRoleInfoParam {

    /**
     * 所属系统 os-IoT应用商城运营支撑系统，screen-商城大屏
     */
    @NotNull(message = "所属系统不能为空")
    private String system;
    /**
     * 角色名称
     */
    @NotNull(message = "角色名称不能为空")
    private String name;
    /**
     * 管理中心名称
     */
    private String administrationCenter;
    /**
     * 是否4A登录
     */
    private Boolean login4a;
    /**
     * 描述
     */
    @NotNull(message = "角色描述不能为空")
    private String description;
    /**
     * 数据权限列表
     */
    @NotNull(message = "数据权限不能为空")
    private List<String> dataPermissionIds;

    /**
     * 操作权限列表
     */
    private List<String> authIds;
}
