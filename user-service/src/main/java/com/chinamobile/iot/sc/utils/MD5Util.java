package com.chinamobile.iot.sc.utils;

import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import java.security.MessageDigest;

/**
 * created by l<PERSON>xiang on 2022/6/1 11:23
 */
public class MD5Util {

    public static String md5(String str){
        // 加密后的16进制字符串
        String hexStr = "";
        try {
            // 此 MessageDigest 类为应用程序提供信息摘要算法的功能
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            // 转换为MD5码
            byte[] digest = md5.digest(str.getBytes("utf-8"));
            hexStr = ByteUtils.toHexString(digest);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hexStr;
    }
}
