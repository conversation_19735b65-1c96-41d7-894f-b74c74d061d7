package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.JWTUtil;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.Data4AConfig;
import com.chinamobile.iot.sc.config.GioConfig;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.*;
import com.chinamobile.iot.sc.entity.user.EditFinancingInfoRequest;
import com.chinamobile.iot.sc.enums.UserPartnerNameEnum;
import com.chinamobile.iot.sc.enums.UserUnifiedStatusEnum;
import com.chinamobile.iot.sc.enums.log.*;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.InstallFeignClient;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.RoleMessageByAuthCodeDO;
import com.chinamobile.iot.sc.pojo.mapper.UserAndRoleByAuthDO;
import com.chinamobile.iot.sc.pojo.mapper.UserDO;
import com.chinamobile.iot.sc.pojo.param.FollowUserParam;
import com.chinamobile.iot.sc.pojo.vo.RoleManageListItemVO;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.DesensitizationUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.chinamobile.iot.sc.utils.GeneralUtils;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.MD5Util;
import com.chinamobile.iot.sc.utils.RSAEncrypt;
import com.chinamobile.iot.sc.vo.AccessToken;
import com.chinamobile.iot.sc.vo.request.*;
import com.chinamobile.iot.sc.vo.response.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.encoding.XMLType;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.namespace.QName;
import javax.xml.rpc.Call;
import javax.xml.rpc.ParameterMode;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chinamobile.iot.sc.common.BaseConstant.*;
import static com.chinamobile.iot.sc.common.Constant.SCREEN_USER_ECHO_LIST;
import static com.chinamobile.iot.sc.common.Constant.USER_PROVINCE_BUSINESS_LIST;
import static com.chinamobile.iot.sc.constant.Basics4AConstant.TOKEN_4ALOGIN__RESULT;
import static com.chinamobile.iot.sc.constant.PartnerProvinceConstant.NONPROVINCE;
import static com.chinamobile.iot.sc.constant.PartnerProvinceConstant.PROVINCE;
import static com.chinamobile.iot.sc.util.DateUtils.DATETIME_FORMAT_T;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: UserServiceImpl
 * @description: 用户-登录服务层
 * @author: zyj
 * @create: 2021/9/2 14:57
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class UserServiceImpl {
    @Value("${rsa.private.key}")
    private String RSAPrivateKey;
    @Value("${execute.4aLogin.sign:true}")
    private boolean login4aSing;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private UserMapper userMapper;
    //    @Resource
//    private RoleMapper roleMapper;
    @Resource
    private UserHistoryMapper userHistoryMapper;
    @Resource
    private PartnerAddressMapper addressMapper;
    @Resource
    private CaptchaServiceImpl captchaService;
    @Resource
    private RoleAuthServiceImpl roleAuthService;
    @Resource
    private IotFeignClient iotServiceFeign;

    @Resource
    private PrimaryDownRelationMapper primaryDownRelationMapper;
    @Resource
    private PrimaryDownRelationMapperExt primaryDownRelationMapperExt;
    @Resource
    private UserMapperExt userMapperExt;

    @Resource
    private Data4AConfig data4AConfig;

    @Resource
    private IotFeignClient iotFeignClient;

    @Resource
    private InstallFeignClient installFeignClient;

    @Resource
    private LogService logService;

    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private RoleDataPermissionService roleDataPermissionService;

    @Resource
    private RoleInfoService roleInfoService;

    @Resource
    private UserScreenMapperExt userScreenMapperExt;

    @Resource
    private UserProvinceBusinessMapperExt userProvinceBusinessMapperExt;

    @Resource
    private UserPartnerMapperExt userPartnerMapperExt;

    @Resource
    private UserPartnerMapper userPartnerMapper;

    @Resource
    private UserScreenMapper userScreenMapper;

    @Resource
    private UserProvinceBusinessMapper userProvinceBusinessMapper;

    @Resource
    private RoleInfoMapper roleInfoMapper;

    @Resource
    private UserScreenService userScreenService;

    @Value("${supply.des.key}")
    private String encryptKey;

    @Autowired
    private GioConfig gioConfig;

    @Autowired
    private UserProductPipeService userProductPipeService;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));


    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    @Autowired
    private ProvinceCityConfig provinceCityConfig;

    public BaseAnswer adminLogin(Request4Login request4Login) {
        //超级管理员，通过用户名-密码登录
        //校验用户名是否存在
        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andNameEqualTo(request4Login.getUserName()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
        if (ObjectUtils.isEmpty(users)) {
            //添加登录日志
            String content = String.format("-");

            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, null, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        //agentManger用户放到最后，登录优先级最低
        users = Stream.concat(users.stream().filter(x->!StringUtils.equals(x.getRoleId(),"907998143042379999")),
                users.stream().filter(x -> StringUtils.equals(x.getRoleId(),"907998143042379999")))
                .collect(Collectors.toList());
        //获取密码校验
        User user = users.get(0);
        String pwdDB = user.getPwd();
        log.info("密码测试 pwdDB = {}", pwdDB);
        //解密登录密码
        String decodePWD = null;
        try {
            decodePWD = RSAEncrypt.decrypt(request4Login.getPwd(), RSAPrivateKey);
            //Base64处理
            log.debug("密码测试 decodePWD = {}", decodePWD);
            String base64PWD = GeneralUtils.Base64Encode(decodePWD);
            log.debug("密码测试 base64PWD = {}", base64PWD);
            log.debug("密码测试 pwdDB C = {}", GeneralUtils.diyBase64Pwd2DbPwd(base64PWD));
            if (!pwdDB.equals(GeneralUtils.diyBase64Pwd2DbPwd(base64PWD))) {
                //添加登录日志
                String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), "admin", user.getUserId());
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());
                throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
            }
        } catch (Exception e) {
            //添加登录日志
            String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), "admin", user.getUserId());
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());

            throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
        }
//        Role role = roleMapper.selectByPrimaryKey(user.getRoleId());
//        String roleType = role.getRoleType();
//        if (login4aSing) {
//            boolean boolType = ADMIN_ROLE.equals(roleType) || OPERATOR_ROLE.equals(roleType) || MANAGER_STAFF_ROLE.equals(roleType)
//                || PRODUCT_OPERATOR_IMPORT_ROLE.equals(roleType) || PRODUCT_OPERATOR_FRAME_ROLE.equals(roleType) || PRODUCT_OPERATOR_RECHECK_ROLE.equals(roleType)
//                || PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType) || POINT_MANAGER.equals(roleType) || PRODUCT_RUN.equals(roleType);
//            if (boolType) {
//                throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A);
//            }
//        }

        if (login4aSing) {
            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            if (roleInfoDTO.getLogin4a()) {
                //添加登录日志
                String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), "admin", user.getUserId());
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_LOGIN_TO_4A.getMessage());

                throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A);
            }
        }
        String token = generateToken(user);

        //添加登录日志
        String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), "admin", user.getUserId());
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);
        return new BaseAnswer().setData(token);

    }


    public BaseAnswer loginByMSCode(String phone, String userId) {
        //通过手机-验证码登录
        //没被注销的,可以登录os的账号
        BaseAnswer baseAnswer = new BaseAnswer();
        //user表手机号加密匹配之后再进行匹配
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
        //停用 没注销
        List<User> usersIsCancel = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone)
                        .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .filter(user -> {
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
                    return roleInfoDTO.getIsLoginOs();
                })
                .collect(Collectors.toList());
        //合作伙伴用户
        List<UserPartner> partnersCancel = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream().filter(user -> {

            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());
        //省业管员用户
        List<UserProvinceBusiness> userProvinceBusinessesCancel = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream().filter(user -> {
            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());

        //完全正常用户
        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone)
                        .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .filter(user -> {
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
                    return roleInfoDTO.getIsLoginOs();
                })
                .collect(Collectors.toList());
        //合作伙伴用户
        List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream().filter(user -> {

            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());
        //省业管员用户
        List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream().filter(user -> {
            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());
        if ((ObjectUtils.isNotEmpty(usersIsCancel) && ObjectUtils.isEmpty(users))
                || (ObjectUtils.isNotEmpty(partnersCancel) && ObjectUtils.isEmpty(partners))
                ||  (ObjectUtils.isNotEmpty(userProvinceBusinessesCancel) && ObjectUtils.isEmpty(userProvinceBusinesses))) {
            //添加登录日志
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());

            throw new BusinessException(StatusConstant.USER_IS_CANCEL_FALSE);
        }

        //无语哟  就为了一个提示语 查询多次判断 完全正常状态
        if (ObjectUtils.isEmpty(users) && ObjectUtils.isEmpty(partners)&&  ObjectUtils.isEmpty(userProvinceBusinesses)) {
            //添加登录日志
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());

            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        String token = "";
        if (ObjectUtils.isNotEmpty(users)) {
//            User user;
//            if (users.size() > 1) {
//                if (StringUtils.isBlank(userId)) {
//                    throw new BusinessException(StatusConstant.LORD_DOWN_ERROR);
//                }
//                user = userMapper.selectByPrimaryKey(userId);
//            } else {
//                // 生成token
//                user = users.get(0);
//                userId = user.getUserId();
//            }
            //agentManger用户放到最后，登录优先级最低
            users = Stream.concat(users.stream().filter(x->!StringUtils.equals(x.getRoleId(),"907998143042379999")),
                            users.stream().filter(x -> StringUtils.equals(x.getRoleId(),"907998143042379999")))
                    .collect(Collectors.toList());
            User user = users.get(0);
            userId = user.getUserId();
            //判断用户角色，
            if (Optional.ofNullable(user).isPresent()) {
//            Role role = roleMapper.selectByPrimaryKey(user.getRoleId());
//            String roleType = role.getRoleType();
//            if (login4aSing) {
//                boolean boolType = ADMIN_ROLE.equals(roleType) || OPERATOR_ROLE.equals(roleType) || MANAGER_STAFF_ROLE.equals(roleType)
//                    || PRODUCT_OPERATOR_IMPORT_ROLE.equals(roleType) || PRODUCT_OPERATOR_FRAME_ROLE.equals(roleType) || PRODUCT_OPERATOR_RECHECK_ROLE.equals(roleType)
//                    || PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType) || POINT_MANAGER.equals(roleType) || PRODUCT_RUN.equals(roleType);
//                if (boolType) {
//                    throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A);
//                }
//            }
                if (login4aSing) {
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
                    if (roleInfoDTO.getLogin4a()) {
                        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_LOGIN_TO_4A.getMessage());

                        throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A);
                    }
                }
                token = generateToken(user);
            }
        } else if (ObjectUtils.isNotEmpty(partners)){
            //合作伙伴
//            UserPartner user;
//            if (partners.size() > 1) {
//                if (StringUtils.isBlank(userId)) {
//                    throw new BusinessException(StatusConstant.LORD_DOWN_ERROR);
//                }
//                user = userPartnerMapper.selectByPrimaryKey(userId);
//            } else {
//                // 生成token
//                user = partners.get(0);
//                userId = user.getUserId();
//            }
            UserPartner user = partners.get(0);
            userId = user.getUserId();
            token = generateToken(user);
        }else if (ObjectUtils.isNotEmpty(userProvinceBusinesses)){
            UserProvinceBusiness userProvinceBusiness = userProvinceBusinesses.get(0);
            userId = userProvinceBusiness.getUserId();
            token = generateToken(userProvinceBusiness);
        }

        //添加登录日志
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_SUCESS.code, null);

        return baseAnswer.setData(token);

    }

    /**
     * 切换角色
     */
    public BaseAnswer<String> changeRole(String userId, String system, LoginIfo4Redis loginIfo4Redis) {
        //校验当前登录系统匹配，不允许跨系统切换账号
        if (!system.equals(loginIfo4Redis.getSystem())) {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR.getMessage());
            throw new BusinessException(StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR);
        }

        BaseAnswer<String> baseAnswer = new BaseAnswer<>();
        String token = "";
        if (SYSTEM_OS.equals(system)) {
            User user = userMapper.selectByPrimaryKey(userId);
            if (ObjectUtils.isNull(user)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (user.getIsCancel() || (user.getUnifiedStatus() != null && !user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                throw new BusinessException(StatusConstant.USER_NO_EXIST, "切换用户不可用");
            }

            if (!loginIfo4Redis.getPhone().equals(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey))) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.CHANGE_DIFRENT_PHONE_ERROR.getMessage());
                throw new BusinessException(StatusConstant.CHANGE_DIFRENT_PHONE_ERROR);
            }

            //当前账号登出
            redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + loginIfo4Redis.getUserId());
            token = generateToken(user);

        } else if (SYSTEM_PARTNER.equals(system)) {
            UserPartner user = userPartnerMapper.selectByPrimaryKey(userId);
            if (ObjectUtils.isNull(user)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (user.getIsCancel()) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                throw new BusinessException(StatusConstant.USER_NO_EXIST, "切换用户不可用");
            }

            if (!loginIfo4Redis.getPhone().equals(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey))) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.CHANGE_DIFRENT_PHONE_ERROR.getMessage());
                throw new BusinessException(StatusConstant.CHANGE_DIFRENT_PHONE_ERROR);
            }

            //当前账号登出
            redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + loginIfo4Redis.getUserId());
            token = generateToken(user);

        }else if(PROVINCE_MANAGEMENT.equals(system)){
            //省业管员角色切换
            UserProvinceBusiness user = userProvinceBusinessMapper.selectByPrimaryKey(userId);
            if (ObjectUtils.isNull(user)) {
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (user.getIsCancel()) {
                throw new BusinessException(StatusConstant.USER_NO_EXIST, "切换用户不可用");
            }

            if (!loginIfo4Redis.getPhone().equals(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey))) {
                throw new BusinessException(StatusConstant.CHANGE_DIFRENT_PHONE_ERROR);
            }

            //当前账号登出
            redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + loginIfo4Redis.getUserId());
            token = generateToken(user);
        } else {
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR.getMessage());
            throw new BusinessException(StatusConstant.CHANGE_DIFRENT_SYSTEM_ERROR, "切换系统不支持");
        }

        //添加登录日志
        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", userId, 0, LogResultEnum.LOG_SUCESS.code, null);

        return baseAnswer.setData(token);
    }

    /**
     * 4A登录校验
     *
     * @param token4a
     * @param appAcctId
     * @return
     */
    public BaseAnswer loginBy4aVerify(String token4a, String appAcctId) {
        BaseAnswer baseAnswer = new BaseAnswer();
        User user = new User();
        Date date = new Date();
        String dateToStr = DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
        String osToken = null;

        //登录成功失败定义
        String operateResult = null;
        String rspText = null;
        String mainAcctIdText = null;
        String appAcctIdText = null;
        //请求参数
        try {
            String requestLoginInfo = checkAiuapTokenSoapParam(token4a, appAcctId, dateToStr);
            log.debug("{}校验4A登录请求信息:{}", appAcctId, requestLoginInfo);
            // 访问4A服务返回ResponseInfo
            org.apache.axis.client.Service service = new org.apache.axis.client.Service();
            Call call = service.createCall();
            call.setTargetEndpointAddress(data4AConfig.getUrlLogin());

            // 定义包名和接口方法
            call.setOperationName(new QName(data4AConfig.getTargetLoginNameSpace(), "CheckAiuapTokenSoap"));

            // 设置参数
            call.addParameter("RequestInfo", XMLType.XSD_STRING, ParameterMode.IN);
            call.setReturnType(XMLType.XSD_STRING);

            //返回结果
            String responseLoginInfo = (String) call.invoke(new Object[]{requestLoginInfo});
            log.debug("{}校验4A登录返回信息:{}", appAcctId, responseLoginInfo);
            //解析返回xml
            Document document = DocumentHelper.parseText(responseLoginInfo);
            //读取根节点
            Element rootElement = document.getRootElement();
            //获取根节点的子节点
            Element body = rootElement.element("BODY");
            Element rsp = body.element("RSP");
            rspText = rsp.getText();
            Element mainAcctId = body.element("MAINACCTID");
            //主账号
            mainAcctIdText = mainAcctId.getText();

            //成功
            if (StringUtils.isNotBlank(rspText) && TOKEN_4ALOGIN__RESULT.equals(rspText)) {
                Element appAcctIdElement = body.element("APPACCTID");
                //从账号
                appAcctIdText = appAcctIdElement.getText();

                if (StringUtils.isNotBlank(appAcctIdText)) {
                    //查询在os系统用户信息
                    //手机号是否被注册,校验从账号是否是手机号（超管没有手机号，传的name）
                    List<User> users = null;
                    //通过用户名直接查
                    users = userMapper.selectByExample(new UserExample().createCriteria().andAccountEqualTo(appAcctIdText)
                                    .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                            .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                            .collect(Collectors.toList());
//                    if (RegexUtil.regexPhone(appAcctIdText)) {
//                        //user表手机号加密存储
//                        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(appAcctIdText,encryptKey);
//                        users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone)
//                                        .andIsCancelEqualTo(false).example()).stream()
//                                .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
//                                .collect(Collectors.toList());
//                    } else {
//                        //查询是否是超管
//                        users = userMapper.selectByExample(new UserExample().createCriteria().andNameEqualTo(appAcctIdText)
//                                        .andIsCancelEqualTo(false).example()).stream()
//                                .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
//                                .collect(Collectors.toList());
//                    }
                    if (CollectionUtils.isEmpty(users)) {
//                        log.debug("手机号不存在phone:{}", appAcctIdText);
                        log.warn("从账号不存在： {}", appAcctIdText);
                        String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(),
                                IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey), user.getUserId());
                        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());

                        throw new BusinessException(StatusConstant.USER_NO_EXIST);
                    }
                    //agentManger用户放到最后，登录优先级最低
                    users = Stream.concat(users.stream().filter(x->!StringUtils.equals(x.getRoleId(),"907998143042379999")),
                                    users.stream().filter(x -> StringUtils.equals(x.getRoleId(),"907998143042379999")))
                            .collect(Collectors.toList());
                    //在os存在生成token返回前端
                    user = users.get(0);
                    osToken = generateToken(user, mainAcctIdText);
                    operateResult = "0";

                    //添加登录日志
                    String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(),
                            IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey), user.getUserId());
//                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId());
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);

                }
            } else {
                //失败
                log.error("{}4A系统验证失败账号：{}", appAcctId, mainAcctIdText);
                operateResult = "1";
                //添加登录日志
                String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(),
                        IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey), user.getUserId());
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_LOGIN_TO_4A_AUTHENTICATION_FAIL.getMessage());

                throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A_AUTHENTICATION_FAIL);
            }

        } catch (Exception e) {
            log.error("{}4A登录校验错误：{}", appAcctId, e);
            operateResult = "1";
            //添加登录日志
            String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(),
                    IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey), user.getUserId());
//            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(),0);
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, e.getMessage());

            throw new BusinessException(StatusConstant.USER_LOGIN_TO_4A_AUTHENTICATION_FAIL, e.getMessage());
        } finally {
            //4A日志采集，放入redis
            log.info("{}，4A日志信息开始采集", mainAcctIdText);
            packagingLog4aMessage(user, mainAcctIdText, rspText, appAcctIdText, date, operateResult, dateToStr);
        }

        return baseAnswer.setData(osToken);
    }


    /**
     * 封装4A日志信息
     *
     * @param user
     * @param mainAcctIdText
     * @param rspText
     * @param appAcctIdText
     * @param date
     * @param operateResult
     * @param dateToStr
     */
    private void packagingLog4aMessage(User user, String mainAcctIdText, String rspText, String appAcctIdText, Date date, String operateResult, String dateToStr) {

        AuditLogParam auditLogParam = new AuditLogParam();
        auditLogParam.setIdentityName(data4AConfig.getIdentityName());
        auditLogParam.setResourceKind(data4AConfig.getResourceKind());
        auditLogParam.setResourceCode(data4AConfig.getServiceId());
        auditLogParam.setIdrCreationTime("");
        auditLogParam.setMainAccountName(mainAcctIdText);
        if (StringUtils.isNotBlank(rspText) && TOKEN_4ALOGIN__RESULT.equals(rspText)) {
            auditLogParam.setSubAccountName(appAcctIdText);
        } else {
            auditLogParam.setSubAccountName("");
        }
        auditLogParam.setOperateTime(DateUtils.dateToStr(date, DATETIME_FORMAT_T));
        auditLogParam.setOpTypeId(data4AConfig.getAuditLogType());
        auditLogParam.setOpTypeName("系统登录");
        auditLogParam.setOpLevelId(data4AConfig.getOpLevelId());
        if (ObjectUtils.isNotNull(user)) {
            String name = user.getName();
            String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
            if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(phone)) {
                auditLogParam.setOperateContent("姓名=".concat(user.getName()).concat(",登录账号：") + phone);
            } else {
                auditLogParam.setOperateContent("");
            }
        } else {
            auditLogParam.setOperateContent("");
        }
        auditLogParam.setOperateResult(operateResult);
        auditLogParam.setModuleId("");
        auditLogParam.setModuleName("");
        auditLogParam.setClientNetworkAddress("");
        auditLogParam.setClientName("");
        auditLogParam.setClientAddress("");
        auditLogParam.setClientPort("");
        auditLogParam.setClientMac("");
        auditLogParam.setClientCpuSerial("");
        auditLogParam.setServerAddress(data4AConfig.getServerIp());
        auditLogParam.setServerPort(data4AConfig.getServerPort());
        auditLogParam.setServerMac("");
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_LOGIN_LOG + dateToStr + mainAcctIdText, auditLogParam, 36, TimeUnit.HOURS);
    }

    /**
     * 封装4A校验请求参数
     *
     * @param token
     * @param appAcctId
     * @param dateToStr
     * @return
     */
    private String checkAiuapTokenSoapParam(String token, String appAcctId, String dateToStr) {
        //创建4a服务请求参数
        Document document = DocumentHelper.createDocument();
        //创建根节点
        Element userReq = document.addElement("USERREQ");
        Element head = userReq.addElement("HEAD");
        Element code = head.addElement("CODE");
        code.setText("");
        Element sid = head.addElement("SID");
        sid.setText("");
        Element timestamp = head.addElement("TIMESTAMP");
        timestamp.setText(dateToStr);
        Element serviceId = head.addElement("SERVICEID");
        serviceId.setText(data4AConfig.getServiceId());
        Element body = userReq.addElement("BODY");
        Element appAcctIdElement = body.addElement("APPACCTID");
        appAcctIdElement.setText(appAcctId);
        Element tokenElement = body.addElement("TOKEN");
        tokenElement.setText(token);
        return document.asXML();
    }

    /**
     * 获取主从账号信息
     *
     * @param phone
     * @return
     */
    public BaseAnswer<List<Data4User>> chooseLordFromUser(String phone, HttpServletRequest request) {
        String sessionId = request.getParameter("sessionId");
        String validCode = request.getParameter("validCode");
        log.info("获取的登录request参数：session：{}，validCode：{}", sessionId, validCode);
        captchaService.valid(validCode, sessionId,phone);
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone)
                        .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .collect(Collectors.toList());
        List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone)
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        List<Data4User> data4Users = null;
        if (ObjectUtils.isNotEmpty(users)) {
            data4Users = ConvertToDataUser(users);
        } else if (ObjectUtils.isNotEmpty(partners)) {
            data4Users = partnerToDataUser(partners);
        } else {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }

        return baseAnswer.setData(data4Users);

    }

    /**
     * 退出登录
     *
     * @param userId
     */
    public BaseAnswer loginOut(String userId) {
        return loginOut(userId, true);
    }

    /**
     * 退出登录
     *
     * @param userId
     */
    private BaseAnswer loginOut(String userId, Boolean log) {
        if (log) {
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.LOG_OUT.code,
                    "【退出登陆】", LogResultEnum.LOG_SUCESS.code, null);
        }
        redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + userId);
        return new BaseAnswer();
    }

    public BaseAnswer changePwd(String userId, String pwd, String phone) {
        User user = userMapper.selectByPrimaryKey(userId);
        if (ObjectUtils.isNotNull(user)) {
            //密码校验
            GeneralUtils.checkPwd(pwd);
            user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(pwd));
            //手机号校验
            if (ObjectUtils.isNotNull(phone)) {
                if (!GeneralUtils.checkPhone(phone)) {
                    throw new BusinessException(StatusConstant.PHONE_ERROR);
                }
                user.setPhone(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey));
            }
            //更新用户信息
            userMapper.updateByPrimaryKey(user);
        } else {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        return new BaseAnswer();
    }

    /**
     * 验证电话号码是否有效
     *
     * @param phone
     * @return
     */
    public BaseAnswer<Boolean> validPhone(String phone) {
        BaseAnswer<Boolean> answer = new BaseAnswer<>();
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
        long userCount = userMapper.countByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone).example());
        long partnerCount = userPartnerMapper.countByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone).example());
        if (userCount > 0 || partnerCount > 0) {
            answer.setData(true);
        } else {
            answer.setData(false);
        }
        return answer;
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer pwdModify(Request4ModPwd request4ModPwd, String userId,String ip) {
        //解密新、旧登录密码
        String decodeOldPWD = null;
        String decodeNewPWD = null;
        //查询对应用户信息
        User user = userMapper.selectByPrimaryKey(userId);

        //验证用户是否存在
        if (ObjectUtils.isEmpty(user)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【修改密码】", userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
            });

            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        //验证密码非空
        if (ObjectUtils.isEmpty(request4ModPwd.getOldPwd()) || ObjectUtils.isEmpty(request4ModPwd.getNewPwd())) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【修改密码】", userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_IS_NULL_ERROR.getMessage());
            });
            throw new BusinessException(StatusConstant.PWD_IS_NULL_ERROR);
        }
        //解密新旧密码
        try {
            decodeOldPWD = RSAEncrypt.decrypt(request4ModPwd.getOldPwd(), RSAPrivateKey);
            decodeNewPWD = RSAEncrypt.decrypt(request4ModPwd.getNewPwd(), RSAPrivateKey);
            //Base64处理
            String base64OldPWD = GeneralUtils.Base64Encode(decodeOldPWD);
            String base64NewPWD = GeneralUtils.Base64Encode(decodeNewPWD);
            if (!user.getPwd().equals(GeneralUtils.diyBase64Pwd2DbPwd(base64OldPWD))) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                            "【修改密码】", userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());
                });
                throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
            }
            //校验新密码
            GeneralUtils.checkPwd(decodeNewPWD);
            //更新密码
            user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(base64NewPWD));
            userMapper.updateByPrimaryKey(user);

            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                    "【修改密码】", LogResultEnum.LOG_SUCESS.code, null);
        } catch (Exception e) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【修改密码】", userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());
            });
            throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
        }
        return new BaseAnswer();
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer modifyPhone(String newPhone, Integer newCode, String phone, String userId) {
        if (newPhone.equals(phone)) {

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "新旧号码一致");
        }
        //手动验证短信验证码
//        captchaService.validSmsCode(newPhone,newCode);
        //如果已使用新号码，就不能修改
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(newPhone, encryptKey);
        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(newPhone)
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
        if (ObjectUtils.isNotEmpty(users)) {
            throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
        }
        // 修改对应用户的号码
        User user = userMapper.selectByPrimaryKey(userId);
        user.setPhone(encryptPhone);
        userMapper.updateByPrimaryKey(user);
        //手动删除
        captchaService.deleteEditSmsCodeInRedis(newPhone);

        return new BaseAnswer();
    }

    /**
     * 查询当前用户的管理的合作伙伴信息
     *
     * @param loginIfo4Redis
     * @return
     */
    public BaseAnswer getPartnerUserMessage(String userId, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        BaseAnswer baseAnswer = new BaseAnswer();
        //
//        //是超管或运管  查询所有从合作伙伴名称
//        if (isAdmin || OPERATOR_ROLE.equals(roleType)) {
//            ArrayList<Boolean> list = new ArrayList<>();
//            list.add(true);
//            list.add(false);
//            List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andIsPrimaryIn(list).andIsCancelEqualTo(false).example());
//            List<String> partnerNames = users.stream().map(User::getPartnerName).distinct().collect(Collectors.toList());
//            baseAnswer.setData(partnerNames);
//        }
//        //是主合作伙伴 查询当前合作伙伴下从的名称
//     /*   if (PARTNER_LORD_ROLE.equals(roleType)) {
//            User user = userMapper.selectById(userId);
//            QueryWrapper<User> userWrapper = new QueryWrapper<>();
//            userWrapper.ne("is_primary", false);
//            userWrapper.eq("is_cancel", false);
//            userWrapper.eq("partner_name", user.getPartnerName());
//            List<User> userList = userMapper.selectList(userWrapper);
//            userList.stream().map(User::getPartnerName)
//            baseAnswer
//        }*/
//        //主从从合作伙伴 只查询当前合作伙伴的名称
//        if (PARTNER_ROLE.equals(roleType) || PARTNER_LORD_ROLE.equals(roleType)) {
//            User user = userMapper.selectByPrimaryKey(userId);
//            List<String> list = new ArrayList<>();
//            list.add(user.getPartnerName());
//            baseAnswer.setData(list);
//        }
        /**合伙伙伴特殊处理，其他角色通过接口权限控制*/
        //主从从合作伙伴 只查询当前合作伙伴的名称
        if (loginIfo4Redis.getIsPartner()) {
            List<String> list = new ArrayList<>();
            list.add(loginIfo4Redis.getPartnerName());
            baseAnswer.setData(list);
        } else {
            List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsPrimaryIsNotNull()
                    .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
            List<String> partnerNames = users.stream().map(UserPartner::getPartnerName).distinct().collect(Collectors.toList());
            baseAnswer.setData(partnerNames);
        }
        return baseAnswer;
    }

    /**
     * 创建合作伙伴省管 查询已创建的合作伙伴名称
     * @param loginIfo4Redis
     * @return
     */
    public  BaseAnswer getProvincePartnerCompanyName(LoginIfo4Redis loginIfo4Redis){
        BaseAnswer baseAnswer = new BaseAnswer();
        //主从从合作伙伴 只查询当前合作伙伴的名称
        if (loginIfo4Redis.getIsPartner()) {
            List<String> list = new ArrayList<>();
            list.add(loginIfo4Redis.getPartnerName());
            baseAnswer.setData(list);
        } else {
            List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsPrimaryIsNotNull()
                    .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).andCompanyTypeEqualTo(PROVINCE).example());
            List<String> partnerNames = users.stream().map(UserPartner::getPartnerName).distinct().collect(Collectors.toList());
            baseAnswer.setData(partnerNames);
        }
        return baseAnswer;
    }


    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer addUser(Request4AddUser request4AddUser, String userId, LoginIfo4Redis loginIfo4Redis, boolean addLog, String ip) {
        String decodePWD = null;

        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(loginIfo4Redis.getRoleId());
        RoleInfo roleInfoAdd = roleInfoMapper.selectByPrimaryKey(request4AddUser.getRoleId());
        StringBuilder content = new StringBuilder();
        if(roleInfo!=null){
            content.append("【新建用户账号】\n所属系统: " + roleInfoAdd.getSystem()
                    + ",所属角色: " + roleInfoAdd.getName() + "; 姓名: "
                    + LogService.custNameDesensitization(request4AddUser.getUserName()) + ",联系电话" + LogService.replaceWithStar(request4AddUser.getPhone()));
        }else{
            content.append("【新建用户账号】");
        }
        //密码非空，则校验 base64解密后进行校验
        if (ObjectUtils.isNotEmpty(request4AddUser.getPwd())) {
            try {
                decodePWD = RSAEncrypt.decrypt(request4AddUser.getPwd(), RSAPrivateKey);
            } catch (Exception e) {

                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_DECODE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PWD_DECODE_ERROR);
            }
            GeneralUtils.checkPwd(decodePWD);
        }

        RoleInfoDTO role = roleInfoService.selectById(request4AddUser.getRoleId());
        String targetRoleType = role.getRoleType();
        /**直接根据角色所持有的账户管理权限进行判断是否越权*/
        checkAuthRight(loginIfo4Redis.getRoleId(), request4AddUser.getRoleId());
        // 根据创建用户-角色类型，校验字段
        /**
         * 共同校验：
         * 1. 姓名-联系人校验
         * 2. 手机号校验
         * 3. 邮箱
         **/
        if (!RegexUtil.regexOperatorName(request4AddUser.getUserName())) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NAME_FORMAT_ERROR.getMessage());
            });
            throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
        }
        if (ObjectUtils.isNotNull(request4AddUser.getPhone())) {
            if (!RegexUtil.regexPhone(request4AddUser.getPhone())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
        }

        if (ObjectUtils.isNotEmpty(request4AddUser.getEmail())) {
            if (!RegexUtil.regexEmail(request4AddUser.getEmail())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.EMAIL_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
            }
        }

        Data4User data4User = new Data4User();
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(request4AddUser.getPhone(), encryptKey);
        if (SYSTEM_OS.equals(request4AddUser.getSystem())) {
            /**OS系统增加用户*/
            if (ObjectUtils.isEmpty(request4AddUser.getDepartmentId())
                    || ObjectUtils.isEmpty(request4AddUser.getDepartmentName())
                    || ObjectUtils.isEmpty(request4AddUser.getOldJobNumber())
                    || ObjectUtils.isEmpty(request4AddUser.getNewJobNumber())
                    || ObjectUtils.isNull(request4AddUser.getStatus())
                    || ObjectUtils.isNull(request4AddUser.getIotType())
                    || ObjectUtils.isNull(request4AddUser.getAccount())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "同一用户平台信息字段不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "同一用户平台信息字段不能为空");
            }

            if (ObjectUtils.isEmpty(request4AddUser.getUserType())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "用户类型不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "用户类型不能为空");
            }

            //统一平台账号是否被注册相同角色账号
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andAccountEqualTo(request4AddUser.getAccount())
                    .andRoleIdEqualTo(request4AddUser.getRoleId())
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(users)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.ACCOUNT_NAME_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.ACCOUNT_NAME_IS_EXIST);
            }

            //手机号是否被注册相同角色账号
            users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone)
                    .andRoleIdEqualTo(request4AddUser.getRoleId())
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(users)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
            }

            //手机号是否被合作伙伴中心注册，OS与合作伙伴中心不能共用手机号
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (ObjectUtils.isNotEmpty(partners)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号合作伙伴中心已存在");
            }

            //手机号是否被注册省业管员
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isNotEmpty(userProvinceBusinesses)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号省业管员已注册");
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号省业管员已注册");
            }

            //保存用户信息
            String oneId = BaseServiceUtils.getId();
            User user = new User();
            user.setUserId(oneId);
            user.setName(request4AddUser.getUserName());
            user.setPartnerName(request4AddUser.getPartnerName());
            user.setEmail(request4AddUser.getEmail());
            user.setCompany(request4AddUser.getCompany());
            user.setPhone(encryptPhone);
            user.setRoleId(request4AddUser.getRoleId());
            user.setRemark("调用接口生成");
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setCreator(loginIfo4Redis.getUserName());
            user.setIsCancel(false);
            user.setOrgId("scyy-team");
            user.withUserType(request4AddUser.getUserType())
                    .withIotType(request4AddUser.getIotType())
                    .withUnifiedStatus(request4AddUser.getStatus())
                    .withUserFrom(request4AddUser.getUserFrom())
                    .withDepartmentId(request4AddUser.getDepartmentId())
                    .withDepartmentName(request4AddUser.getDepartmentName())
                    .withOldJobNumber(request4AddUser.getOldJobNumber())
                    .withNewJobNumber(request4AddUser.getNewJobNumber())
                    .withAccount(request4AddUser.getAccount());
            //如果新增的是业务管理员 is_admin字段赋值

            if (ADMIN_ROLE.equals(role.getRoleType())){
                user.setIsAdmin(true);
            }
            //若pwd非空，则设置密码
            if (ObjectUtils.isNotEmpty(decodePWD)) {
                user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(decodePWD));
            }

            userMapper.insertSelective(user);
            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(request4AddUser.getPhone());
        } else if (SYSTEM_PARTNER.equals(request4AddUser.getSystem())) {
            /**合作伙伴中心添加用户*/

            if (ObjectUtils.isEmpty(request4AddUser.getPartnerName())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_NAME_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PARTNER_NAME_ERROR);
            }

            //手机号是否被注册相同角色账号
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone)
                    .andRoleIdEqualTo(request4AddUser.getRoleId())
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(partners)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
            }

            //手机号是否被OS系统注册，OS与合作伙伴中心不能共用手机号
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (ObjectUtils.isNotEmpty(users)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号OS系统已注册");
            }

            //手机号是否被注册省业管员
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isNotEmpty(userProvinceBusinesses)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号省业管员已注册");
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号省业管员已注册");
            }

            //保存用户信息
            String oneId = BaseServiceUtils.getId();
            UserPartner user = new UserPartner();
            user.setUserId(oneId);

            if (PARTNER_LORD_ROLE.equals(targetRoleType)) {
                //主伙伴
                String companyType = request4AddUser.getCompanyType();
                user.setCompanyType(companyType);
                //判断单位类型是否是省公司
                if (PROVINCE.equals(companyType)){
                    String partnerName = request4AddUser.getPartnerName();
                    String beId = UserPartnerNameEnum.fromCode(partnerName);
                    //当选择的单位类型是省公司 校验合作伙伴名称是否在31个省公司
                    if (StringUtils.isBlank(beId)){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.MUST_PROVINCIAL_COMPANY.getMessage());
                        });
                        throw new BusinessException(StatusConstant.MUST_PROVINCIAL_COMPANY.getStateCode(), StatusConstant.MUST_PROVINCIAL_COMPANY.getMessage());
                    }
                    String provinceName = UserPartnerNameEnum.fromProvinceName(partnerName);
                    user.setProvince(provinceName);
                    user.setBeId(beId);
                }else if(NONPROVINCE.equals(companyType)){
                   //如果是非省公司 合作伙伴名称不能是31个省公司名称一样
                    String partnerName = request4AddUser.getPartnerName();
                    String beId = UserPartnerNameEnum.fromCode(partnerName);
                    //当选择的单位类型是非省公司 校验合作伙伴名称是否在31个省公司，不能一样
                    if (StringUtils.isNotBlank(beId)){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.MUST_PROVINCIAL_NOT_COMPANY.getMessage());
                        });
                        throw new BusinessException(StatusConstant.MUST_PROVINCIAL_NOT_COMPANY.getStateCode(), StatusConstant.MUST_PROVINCIAL_NOT_COMPANY.getMessage());
                    }
                }
                if (ObjectUtils.isEmpty(request4AddUser.getIsSend())) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.WHETHER_SEND_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.WHETHER_SEND_ERROR);
                }

                if (ObjectUtils.isEmpty(request4AddUser.getUserType())) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "用户类型不能为空");
                    });
                    throw new BusinessException(StatusConstant.PARAM_ERROR, "用户类型不能为空");
                }

                //校验合作伙伴名称是否存在
                List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                        .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isNotEmpty(primaryUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.EXIST_COOPERATION_NAME_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.EXIST_COOPERATION_NAME_ERROR);
                }

                user.setIsPrimary(true);
                user.setIsSend(request4AddUser.getIsSend());
                Boolean isExternal = request4AddUser.getIsExternal();
             //现在和省公司类型没关系 主合作随便选
               /* if (NONPROVINCE.equals(companyType)){
                    //合作伙伴公司是非省  默认为外部
                    if (!isExternal){
                        throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PARTNER);
                    }
                }*/
                user.setIsExternal(request4AddUser.getIsExternal());
                user.setUserType(request4AddUser.getUserType());
            } else if (PARTNER_ROLE.equals(targetRoleType)) {
                //超管新增或者主合作伙伴 判断主合作的单位类型否是省公司，
                String partnerName = request4AddUser.getPartnerName();
                String beId = UserPartnerNameEnum.fromCode(partnerName);
                //当选择的单位类型是省公司 校验合作伙伴名称是否在31个省公司 不是就按照原来逻辑 是按照省公司来
                if (StringUtils.isNotBlank(beId)){
                    String provinceName = UserPartnerNameEnum.fromProvinceName(partnerName);
                    user.setProvince(provinceName);
                    user.setBeId(beId);
                    user.setCompanyType(PROVINCE);
                    user.setLocation(request4AddUser.getRegion());
                    user.setLocationId(request4AddUser.getRegionId());
                }else {
                    user.setCompanyType(StringUtils.isNotBlank(request4AddUser.getCompanyType()) ? request4AddUser.getCompanyType() : NONPROVINCE);
                   // throw new BusinessException(StatusConstant.MUST_PROVINCIAL_COMPANY.getStateCode(), StatusConstant.MUST_PROVINCIAL_COMPANY.getMessage());
                }

                //从伙伴
                List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName()).andIsCancelEqualTo(false)
                        .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isEmpty(primaryUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR);
                }
                UserPartner partnerLord = primaryUsers.get(0);
                user.setIsPrimary(false);
                user.setIsSend(true);
                user.setUserType(partnerLord.getUserType());
                Boolean isExternal = partnerLord.getIsExternal();
                if (isExternal && !request4AddUser.getIsExternal()){
                    throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PERSONNEL);
                }
                if (isExternal){
                    user.setIsExternal(true);
                }else {
                    user.setIsExternal(request4AddUser.getIsExternal());
                }
                // 插入主从绑定关系
                PrimaryDownRelation primaryDownRelation = new PrimaryDownRelation();
                primaryDownRelation.setPrimaryDownId(BaseServiceUtils.getId());
                String lordUserId = partnerLord.getUserId();
                primaryDownRelation.setPrimaryUserId(lordUserId);
                primaryDownRelation.setDownUserId(oneId);
                primaryDownRelationMapper.insert(primaryDownRelation);
            }else if(PARTNER_PROVINCE.equals(targetRoleType)){
                //合作伙伴省管员新增
                //判断主合作伙伴单位是否是省公司 才能创建
                String roleType = loginIfo4Redis.getRoleType();
                List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                        .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                if (PARTNER_LORD_ROLE.equals(roleType)){
                    UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(userId);
                    String companyType = userPartner.getCompanyType();
                    if (NONPROVINCE.equals(companyType)){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "合作伙伴主不是省公司的不能创建合作伙伴省管员");
                        });
                        throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE,"合作伙伴主不是省公司的不能创建合作伙伴省管员");
                    }
                }
                if (CollectionUtils.isEmpty(primaryUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR);
                }
                String partnerName = request4AddUser.getPartnerName();
                String beId = UserPartnerNameEnum.fromCode(partnerName);
                UserPartner partnerLord = primaryUsers.get(0);
                Boolean isExternal = partnerLord.getIsExternal();
                if (isExternal && !request4AddUser.getIsExternal()){
                    throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PERSONNEL);
                }
                if (isExternal){
                    user.setIsExternal(true);
                }else {
                    user.setIsExternal(request4AddUser.getIsExternal());
                }
                //当选择的单位类型是省公司 校验合作伙伴名称是否在31个省公司 不是就按照原来逻辑 是按照省公司来
                if (StringUtils.isNotBlank(beId)){
                    String provinceName = UserPartnerNameEnum.fromProvinceName(partnerName);
                    user.setProvince(provinceName);
                    user.setBeId(beId);
                    user.setCompanyType(PROVINCE);
                    user.setLocation(request4AddUser.getRegion());
                    user.setLocationId(request4AddUser.getRegionId());
                }else {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.MUST_PROVINCIAL_COMPANY.getMessage());
                    });
                    //user.setCompanyType(StringUtils.isNotBlank(request4AddUser.getCompanyType()) ? request4AddUser.getCompanyType() : NONPROVINCE);
                    throw new BusinessException(StatusConstant.MUST_PROVINCIAL_COMPANY.getStateCode(), StatusConstant.MUST_PROVINCIAL_COMPANY.getMessage());
                }
           //TODO 插入主从绑定关系  现在主从关系表新增了合作伙伴省管？

            } else if (PARTNER_INSTALL_ROLE.equals(targetRoleType) || PARTNER_INSTALL_MANAGER.equals(targetRoleType)
            || PARTNER_INSTALL_LORD.equals(targetRoleType) || PARTNER_INSTALL_SUB.equals(targetRoleType) || PARTNER_BUSINESS.equals(targetRoleType)) {
                //合作伙伴售后 （先更名为装维师傅）   装维主  装维管理  装维从
                String roleType = roleInfo.getRoleType();
                if (PARTNER_LORD_ROLE.equals(roleType) && PARTNER_INSTALL_MANAGER.equals(targetRoleType)){
                    //合作伙伴主不能创建装维管理员
                    throw new BusinessException(StatusConstant.USER_INSTALL_NOT_CREATE);
                }
                //产品要求统一公司下装维主只能有一个账号
                if (PARTNER_INSTALL_LORD.equals(targetRoleType)){
                    List<UserPartner> installUserLord = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                            .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                            .andRoleIdEqualTo(role.getId()).andIsLogoffEqualTo(false).example());
                    if (CollectionUtils.isNotEmpty(installUserLord)) {
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.INSTALL_USER_EXIST.getMessage());
                        });
                        throw new BusinessException(StatusConstant.INSTALL_USER_EXIST);
                    }
                }
                List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                        .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isEmpty(primaryUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR);
                }
                UserPartner partnerLord = primaryUsers.get(0);
                user.setIsSend(true);
                user.setUserType(request4AddUser.getUserType());
                Boolean isExternal = partnerLord.getIsExternal();
                if (isExternal && !request4AddUser.getIsExternal()){
                    throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PERSONNEL);
                }
                if (isExternal){
                    user.setIsExternal(true);
                }else {
                    user.setIsExternal(request4AddUser.getIsExternal());
                }
            } else if (PARTNER_BAOLI.equals(targetRoleType)) {
                //合作伙伴保理
                if (ObjectUtils.isEmpty(request4AddUser.getUnifiedCode())) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "统一社会信用代码不能为空");
                    });
                    throw new BusinessException(StatusConstant.PARAM_ERROR, "统一社会信用代码不能为空");
                }

                //产品要求统一合作伙伴名称不能创建多个保理账号
                List<UserPartner> baoliUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                        .andRoleIdEqualTo(role.getId()).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isNotEmpty(baoliUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.BAOLI_USER_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.BAOLI_USER_EXIST);
                }

                List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(request4AddUser.getPartnerName())
                        .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isEmpty(primaryUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR);
                }
                UserPartner partnerLord = primaryUsers.get(0);
                Boolean isExternal = partnerLord.getIsExternal();
                if (isExternal && !request4AddUser.getIsExternal()){
                    throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PERSONNEL);
                }
                if (isExternal){
                    user.setIsExternal(true);
                }else {
                    user.setIsExternal(request4AddUser.getIsExternal());
                }
                user.setIsSend(true);
                user.setUserType(request4AddUser.getUserType());
                user.setUnifiedCode(request4AddUser.getUnifiedCode());
                // 初始状态设为待注册
                user.setCjStatus("unregistered");
            }
            //保存用户信息
            //查询一下要新增的用户是否是相同电话 相同合作伙伴公司 相同角色 已经注销的用户  为了绑定历史订单查询 做更新操作
        /*    List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPartnerNameEqualTo(request4AddUser.getPartnerName())
                    .andPhoneEqualTo(encryptPhone).andRoleIdEqualTo(request4AddUser.getRoleId()).andIsLogoffEqualTo(true).example());*/
            user.setName(request4AddUser.getUserName());
            user.setPartnerName(request4AddUser.getPartnerName());
            user.setCompanyId(request4AddUser.getCompanyId());
            user.setCompanyType(request4AddUser.getCompanyType());
            user.setUserType(request4AddUser.getUserType());
            user.setEmail(request4AddUser.getEmail());
            user.setPhone(encryptPhone);
            user.setRoleId(request4AddUser.getRoleId());
            user.setRemark("调用接口生成");
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setCreator(loginIfo4Redis.getUserName());
            user.setIsCancel(false);
            if (ObjectUtils.isNotEmpty(decodePWD)) {
                user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(decodePWD));
            }
            //进行更新操作
         /*   if (CollectionUtils.isNotEmpty(userPartners)){

            }else {

            }*/
            userPartnerMapper.insertSelective(user);

            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(request4AddUser.getPhone());

        } else if (SYSTEM_SCREEN.equals(request4AddUser.getSystem())) {
            /**增加大屏系统用户*/
            //手机号是否被注册
            List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isNotEmpty(users)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
            }

            //保存用户信息
            String oneId = BaseServiceUtils.getId();
            UserScreen user = new UserScreen();
            user.setId(oneId);
            user.setName(request4AddUser.getUserName());
            user.setEmail(request4AddUser.getEmail());
            user.setCompany(request4AddUser.getCompany());
            user.setCompanyName(request4AddUser.getCompanyName());
            user.setCompanyType(request4AddUser.getCompanyType());
            user.setCompanyId(request4AddUser.getCompanyId());
            user.setPhone(encryptPhone);
            user.setRoleId(request4AddUser.getRoleId());
            user.setAccountLevel(request4AddUser.getAccountLevel());
            user.setProvince(request4AddUser.getProvince());
            user.setBeId(request4AddUser.getBeId());
            user.setLocation(request4AddUser.getLocation());
            user.setLocationName(request4AddUser.getLocationName());
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setCreator(loginIfo4Redis.getUserName());
            user.setIsCancel(false);
            user.setIsAdmin(false);
            user.withUserType(request4AddUser.getUserType())
                    .withIotType(request4AddUser.getIotType())
                    .withUnifiedStatus(request4AddUser.getStatus())
                    .withUserFrom(request4AddUser.getUserFrom())
                    .withDepartmentId(request4AddUser.getDepartmentId())
                    .withDepartmentName(request4AddUser.getDepartmentName())
                    .withOldJobNumber(request4AddUser.getOldJobNumber())
                    .withNewJobNumber(request4AddUser.getNewJobNumber());

            if (ObjectUtils.isNotEmpty(decodePWD)) {
                user.setPwd(GeneralUtils.diyBase64Pwd2DbPwd(decodePWD));
            }
            userScreenMapper.insertSelective(user);

            BeanUtils.copyProperties(user, data4User);
            data4User.setUserId(user.getId());
            data4User.setPhone(request4AddUser.getPhone());
            //新增大屏用户成功 存储前端需要回显数据
            redisTemplate.opsForValue().set(SCREEN_USER_ECHO_LIST+request4AddUser.getPhone(),request4AddUser.getProvinceLocationList());
        }else if (PROVINCE_MANAGEMENT.equals(request4AddUser.getSystem())){
            /**
             *  新增省业管员用户
             */

            //合作伙伴手机号是否被注册相同角色账号
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone)
                    .andIsLogoffEqualTo(false)
                    .example());
            if (ObjectUtils.isNotEmpty(partners)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号合作伙伴中心已注册");
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号合作伙伴中心已注册");
            }

            //手机号是否被OS系统注册，OS与合作伙伴中心不能共用手机号
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (ObjectUtils.isNotEmpty(users)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号OS系统已注册");
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号OS系统已注册");
            }

            //省业管员手机号是否被注册
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isNotEmpty(userProvinceBusinesses)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号省业管员已注册");
                });
                throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号省业管员已注册");
            }
          //保存用户信息
            String oneId = BaseServiceUtils.getId();
            UserProvinceBusiness user = new UserProvinceBusiness();
            user.withUserId(oneId)
                    .withName(request4AddUser.getUserName())
             .withPhone(encryptPhone)
            .withRemark("调用接口生成")
            .withRoleId(request4AddUser.getRoleId())
            .withIsCancel(false)
            .withIsLogoff(false)
            .withEmail(request4AddUser.getEmail())
            .withCreator(loginIfo4Redis.getUserName())
            .withUserType(request4AddUser.getUserType())
            .withCanEnable(false)
            .withCompany(request4AddUser.getCompany())
            .withCompanyName(request4AddUser.getCompanyName())
            .withCompanyType(request4AddUser.getCompanyType())
            .withCompanyId(request4AddUser.getCompanyId())
            .withCreateTime(new Date())
            .withUpdateTime(new Date())
            .withProvince(request4AddUser.getProvince())
            .withBeId(request4AddUser.getBeId())
            .withLocation(request4AddUser.getLocation())
            .withLocationName(request4AddUser.getLocationName());

            userProvinceBusinessMapper.insertSelective(user);
            //新增省业管员用户成功 存储前端需要回显数据
            redisTemplate.opsForValue().set(USER_PROVINCE_BUSINESS_LIST+request4AddUser.getPhone(),request4AddUser.getProvinceLocationList());
        }

        /**
         * 新增成功,操作日志
         */
//        encapsulationOperationLog(data4User, null, "0");
        String userFrom = request4AddUser.getUserFrom();
        if (addLog) {
            if (roleInfo != null) {
                if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                    //运营账号管理开通
                    logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                            UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                            content.toString(), LogResultEnum.LOG_SUCESS.code, null);
                }else {
                    //合作伙伴管理开通
                    logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                            UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                            content.toString(), LogResultEnum.LOG_SUCESS.code, null);
                }

            }
        }
        return new BaseAnswer();
    }


    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editUser(Request4EditUser request4EditUser, LoginIfo4Redis loginIfo4Redis, String ip) {
        //检测当前用户是否有所修改用户的管理权限
        checkAuthRight(loginIfo4Redis.getRoleId(), request4EditUser.getRoleId());

        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(loginIfo4Redis.getRoleId());
        RoleInfo roleInfoEdit = roleInfoMapper.selectByPrimaryKey(request4EditUser.getRoleId());
        StringBuilder content = new StringBuilder();
        if(roleInfo!=null){
            content.append("【修改用户账号】\n所属系统: " + roleInfoEdit.getSystem()
                    + ",所属角色: " + roleInfoEdit.getName() + "; 姓名: "
                    + request4EditUser.getUserName() + ",联系电话" + LogService.replaceWithStar(request4EditUser.getPhone()));
        }else{
            content.append("【修改用户账号】");
        }
        //OS系统用户不能修改信息
        if (SYSTEM_OS.equals(request4EditUser.getSystem())) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "OS系统用户不能修改");
            });
            throw new BusinessException(StatusConstant.PARAM_ERROR, "OS系统用户不能修改");
        }

        Data4User oldUser = new Data4User();
        Data4User newUser = new Data4User();
        BeanUtils.copyProperties(request4EditUser, newUser);
        newUser.setName(request4EditUser.getUserName());
        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(request4EditUser.getPhone(), encryptKey);
        if (SYSTEM_SCREEN.equals(request4EditUser.getSystem())) {
            if (ObjectUtils.isEmpty(request4EditUser.getUserFrom())) {
                  executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "大屏系统用户对象不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "大屏系统用户对象不能为空");
            }
//            if (USER_FROM_IOT.equals(request4EditUser.getUserFrom())) {
//                throw new BusinessException(StatusConstant.PARAM_ERROR, "大屏系统物联网用户不能修改");
//            }

            if (ObjectUtils.isEmpty(request4EditUser.getCompany())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "所属公司不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "所属公司不能为空");
            }

            if (ObjectUtils.isEmpty(request4EditUser.getProvince())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "省域不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "省域不能为空");
            }

            String newName = request4EditUser.getUserName();
            String newPhone = request4EditUser.getPhone();
            String newEmail = request4EditUser.getEmail();
            String newRoleId = request4EditUser.getRoleId();
            UserScreen user = userScreenMapper.selectByPrimaryKey(request4EditUser.getUserId());
            if (!Optional.ofNullable(user).isPresent()) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }
            BeanUtils.copyProperties(user, oldUser);
            if (!RegexUtil.regexOperatorName(newName)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NAME_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
            }
            //手机格式校验
            if (!RegexUtil.regexPhone(newPhone)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            if (!encryptPhone.equals(user.getPhone())) {
                List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .andIdNotEqualTo(request4EditUser.getUserId())
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (ObjectUtils.isNotEmpty(users)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
                }
            }

            if (!RegexUtil.regexEmail(newEmail)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.EMAIL_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
            }


            Boolean isCancel = user.getIsCancel();
            if (isCancel) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_LOGGED_OUT.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_LOGGED_OUT);
            }
            boolean isAdmin = user.getIsAdmin() == null ? false : user.getIsAdmin();
            // 超级管理员不能被其他用户编辑
            if (isAdmin) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_EDIT_ADMIN.getMessage());
                });
                throw new BusinessException(StatusConstant.NO_EDIT_ADMIN);
            }

            //修改用户信息
            user.setName(newName);
            user.setPhone(encryptPhone);
            user.setEmail(newEmail);
            if (ObjectUtils.isNotEmpty(request4EditUser.getUserType())) {
                user.setUserType(request4EditUser.getUserType());
            }
            user.setProvince(request4EditUser.getProvince());
            user.setBeId(request4EditUser.getBeId());
            user.setLocation(request4EditUser.getLocation());
            user.setLocationName(request4EditUser.getLocationName());
            user.setCompany(request4EditUser.getCompany());
            user.setRoleId(newRoleId);
            user.setUpdateTime(new Date());
            userScreenMapper.updateByPrimaryKey(user);
            //编辑大屏用户成功 存储前端需要回显数据
            redisTemplate.opsForValue().set(SCREEN_USER_ECHO_LIST + request4EditUser.getPhone(), request4EditUser.getProvinceLocationList());
        } else if (SYSTEM_PARTNER.equals(request4EditUser.getSystem())) {
            /**修改合作伙伴中心用户*/
            String newName = request4EditUser.getUserName();
            String newPhone = request4EditUser.getPhone();
            String newRoleId = request4EditUser.getRoleId();
            String newEmail = request4EditUser.getEmail();
            String newPartnerName = request4EditUser.getPartnerName();
            Boolean newIsSend = request4EditUser.getIsSend();
            String region = request4EditUser.getRegion();
            String regionId = request4EditUser.getRegionId();
            Boolean isExternalNew= request4EditUser.getIsExternal();

            UserPartner user = userPartnerMapper.selectByPrimaryKey(request4EditUser.getUserId());
            if (!ObjectUtils.isNotEmpty(user)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }
            BeanUtils.copyProperties(user, oldUser);

            if (!RegexUtil.regexOperatorName(newName)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NAME_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
            }

            if (StringUtils.isBlank(newPartnerName)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_NAME_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PARTNER_NAME_ERROR);
            }
            //合作伙伴 伙伴名称不能修改
            if (!newPartnerName.equals(user.getPartnerName())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.UPDATE_PARTNER_NAME_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.UPDATE_PARTNER_NAME_ERROR);
            }

            String companyType = user.getCompanyType();
            Boolean isExternal = user.getIsExternal();
            String roleId = user.getRoleId();
          //  RoleInfo roleInfoEdit = roleInfoMapper.selectByPrimaryKey(roleId);
            String roleType = roleInfoEdit.getRoleType();
            //如果不是主 主又是外部 从必须是外部
            List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                    .andPartnerNameEqualTo(user.getPartnerName())
                    .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
            if (!PARTNER_LORD_ROLE.equals(roleType)){
                //查询主合作伙伴
               if (CollectionUtils.isNotEmpty(primaryUsers)){
                   Boolean isExternalprimary = primaryUsers.get(0).getIsExternal();
                   if (isExternalprimary && !isExternalNew){
                       throw new BusinessException(StatusConstant.ACCOUNT_EXTERNAL_PERSONNEL);
                   }
               }else {
                   throw new BusinessException(StatusConstant.NOT_EXIST_COOPERATION_NAME_ERROR);
               }

            }
            if (!RegexUtil.regexPhone(newPhone)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            if (!encryptPhone.equals(user.getPhone())) {
                //校验手机号是否被注册
                List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .andUserIdNotEqualTo(request4EditUser.getUserId())
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (ObjectUtils.isNotEmpty(users)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
                }

                //合作伙伴中心的手机号好需要检测是否在OS系统中存在
                List<User> osUsers = userMapper.selectByExample(new UserExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(osUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在OS系统中已存在");
                }
                List<UserProvinceBusiness> oldUserProvince= userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(oldUserProvince)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号在省业中心中已存在");
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在省业中心中已存在");
                }
            }
            //修改用户信息
            user.setName(newName != null ? newName : user.getName());
            user.setPhone(encryptPhone != null ? encryptPhone : user.getPhone());
            user.setEmail(newEmail != null ? newEmail : user.getEmail());
            user.setRoleId(newRoleId != null ? newRoleId : user.getRoleId());
            user.setIsSend(newIsSend != null ? newIsSend : user.getIsSend());
            user.setLocation(region !=null ? region : user.getLocation());
            user.setLocationId(regionId !=null ? regionId : user.getLocationId());
           /* if (user.getIsPrimary() != null && user.getIsPrimary() && ObjectUtils.isNotEmpty(request4EditUser.getUserType())) {
                user.setUserType(request4EditUser.getUserType());
            }*/
            if (ObjectUtils.isNotEmpty(request4EditUser.getUserType())) {
                user.setUserType(request4EditUser.getUserType());
            }
            user.setIsExternal(isExternalNew);
            user.setUpdateTime(new Date());
            userPartnerMapper.updateByPrimaryKey(user);
            //现在不根据公司类型判断
            //如果修改的是合作主，判断修改是  内部到外部不  是主下关联相同公司所有合作从都默认修改为外部人员
            if (PARTNER_LORD_ROLE.equals(roleType)){
                if (!isExternal && isExternalNew){
                    //查询其他合作伙伴 修改
                    List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsLogoffEqualTo(false)
                            .andPartnerNameEqualTo(user.getPartnerName()).example());
                    List<UserPartner> collect = userPartners.stream().filter(userPartner -> !userPartner.getUserId().equals(primaryUsers.get(0).getUserId())).collect(Collectors.toList());
                    for (UserPartner userPartner : collect) {
                        userPartner.setIsExternal(isExternalNew);
                        userPartner.setUpdateTime(new Date());
                        userPartnerMapper.updateByPrimaryKeySelective(userPartner);
                    }
                }
            }


        }else if(PROVINCE_MANAGEMENT.equals(request4EditUser.getSystem())){
            /**修改省业管理员*/
            if (ObjectUtils.isEmpty(request4EditUser.getCompany())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "所属公司不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "所属公司不能为空");
            }

            if (ObjectUtils.isEmpty(request4EditUser.getProvince())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "所属公司不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "省域不能为空");
            }

            String newName = request4EditUser.getUserName();
            String newPhone = request4EditUser.getPhone();
            String newEmail = request4EditUser.getEmail();
            String newRoleId = request4EditUser.getRoleId();
            UserProvinceBusiness user = userProvinceBusinessMapper.selectByPrimaryKey(request4EditUser.getUserId());
            if (!Optional.ofNullable(user).isPresent()) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }
            BeanUtils.copyProperties(user, oldUser);
            if (!RegexUtil.regexOperatorName(newName)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NAME_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
            }
            //手机格式校验
            if (!RegexUtil.regexPhone(newPhone)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }

            if (!encryptPhone.equals(user.getPhone())) {
                //校验手机号是否被注册
                List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .example());
                if (ObjectUtils.isNotEmpty(users)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号在合作伙伴中心系统中已存在");
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号在合作伙伴中心系统中已存在");
                }

                //合作伙伴中心的手机号好需要检测是否在OS系统中存在
                List<User> osUsers = userMapper.selectByExample(new UserExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(osUsers)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号在OS系统中已存在");
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在OS系统中已存在");
                }

                List<UserProvinceBusiness> oldUserProvince= userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(oldUserProvince)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "手机号在省业中心中已存在");
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在省业中心中已存在");
                }
            }

            //修改用户信息
            user.setName(newName);
            user.setPhone(encryptPhone);
            user.setEmail(newEmail);
            if (ObjectUtils.isNotEmpty(request4EditUser.getUserType())) {
                user.setUserType(request4EditUser.getUserType());
            }
            user.setProvince(request4EditUser.getProvince());
            user.setBeId(request4EditUser.getBeId());
            user.setLocation(request4EditUser.getLocation());
            user.setLocationName(request4EditUser.getLocationName());
            user.setCompany(request4EditUser.getCompany());
            user.setRoleId(newRoleId);
            user.setUpdateTime(new Date());
            userProvinceBusinessMapper.updateByPrimaryKey(user);
            //新增省业管员用户成功 存储前端需要回显数据
            redisTemplate.opsForValue().set(USER_PROVINCE_BUSINESS_LIST+request4EditUser.getPhone(),request4EditUser.getProvinceLocationList());
        }

        /**
         * 编辑成功,操作日志
         */

//        encapsulationOperationLog(newUser, oldUser, "1");

        if (roleInfoEdit != null) {
            StringBuilder contentUpdate = new StringBuilder();
            String userTypeOld = oldUser.getUserType();
            String nameOld = oldUser.getName();
            String emailOld = oldUser.getEmail();
            String phoneOld = oldUser.getPhone();
            Boolean isExternalOld = oldUser.getIsExternal();
            String userTypeNew = newUser.getUserType();
            String nameNew = newUser.getName();
            String emailNew = newUser.getEmail();
            String phoneNew= newUser.getPhone();
            Boolean isExternalNew = newUser.getIsExternal();
            contentUpdate.append("【修改用户账号】\n所属系统: " + roleInfoEdit.getSystem()
                    + ",所属角色: " + roleInfoEdit.getName() + "; 姓名: "
                    + request4EditUser.getUserName() + ",联系电话" + LogService.replaceWithStar(request4EditUser.getPhone()));
           if (StringUtils.isNotBlank(userTypeOld) && StringUtils.isNotBlank(userTypeNew)){
               if (!userTypeOld.equals(userTypeNew)){
                   contentUpdate.append("账号类型由").append(userTypeOld = userTypeOld.equals("test") ? "测试账号" :"正式账号")
                           .append("修改为").append(userTypeNew = userTypeNew.equals("test") ? "测试账号" :"正式账号" );
               }
           }
            if (!nameOld.equals(nameNew)){
                contentUpdate.append("姓名由").append(LogService.custNameDesensitization(nameOld)).append("修改为").append(LogService.custNameDesensitization(nameNew));
            }
            if (!emailOld.equals(emailNew)){
                contentUpdate.append("邮箱由").append(emailOld).append("修改为").append(emailNew);
            }
            if (!phoneOld.equals(phoneNew)){
                contentUpdate.append("电话由").append(LogService.replaceWithStar(IOTEncodeUtils.decryptIOTMessage(phoneOld, encryptKey))).append("修改为").append(LogService.replaceWithStar(phoneNew));
            }
           String isExternalOldStr ="是";
            if (!String.valueOf(isExternalOld).equals(String.valueOf(isExternalNew))){
                contentUpdate.append("是否是外部人员由").append( isExternalOldStr= isExternalOld ? "是" : "否").append("修改为").append(isExternalOldStr= isExternalNew ? "是" : "否");
            }

            String userFrom = oldUser.getUserFrom();
            if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                        contentUpdate.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                        contentUpdate.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }
        }
        return new BaseAnswer();

    }

    /***接口命名问题，这个接口可以用于其他角色修改自己的信息，
     * 确定修改名称，手机号，邮箱，统一平台的用户不能修改自身*/
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editPartner(Request4EditPartner request, LoginIfo4Redis loginIfo4Redis,String ip) {
        String newName = request.getUserName();
        String newPhone = request.getPhone();
        String newEmail = request.getEmail();
        //校验短信验证码
        Boolean verificationCode = RegexUtil.regexVerificationCode(request.getNewCode());
        if (!verificationCode){
            throw new BusinessException(StatusConstant.PARAM_ERROR, "短信验证码为6位数的数字");
        }
        captchaService.validEditCode(newPhone,Integer.valueOf(request.getNewCode()));
        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(loginIfo4Redis.getRoleId());
        StringBuilder content = new StringBuilder();
        if(roleInfo!=null){
           content.append("【修改用户账号】\n所属系统: " + roleInfo.getSystem()
                   + ",所属角色: " + roleInfo.getName() + "; 姓名: "
                   + request.getUserName() + ",联系电话" + LogService.replaceWithStar(request.getPhone()));
        }else{
            content.append("【修改用户账号】");
        }

//        if (!RegexUtil.regexNumber(request.getNewCode())) {
//            throw new BusinessException(StatusConstant.MUST_NUMBER_ERROR);
//        }
//        Integer newCode = Integer.valueOf(request.getNewCode());
//        //手动验证短信验证码
//        captchaService.validEditCode(newPhone, newCode);

        if (SYSTEM_OS.equals(request.getSystem())) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "OS系统用户不能修改");
            });
             throw new BusinessException(StatusConstant.PARAM_ERROR, "OS系统用户不能修改");
        }

        if (!RegexUtil.regexPhone(newPhone)) {
             executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
            });
            throw new BusinessException(StatusConstant.PHONE_ERROR);
        }

        String encryptPhone = IOTEncodeUtils.encryptIOTMessage(newPhone, encryptKey);

        if (SYSTEM_PARTNER.equals(request.getSystem())) {
            UserPartner user = userPartnerMapper.selectByPrimaryKey(request.getUserId());
            if (!ObjectUtils.isNotEmpty(user)) {
                  executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (!encryptPhone.equals(user.getPhone())) {
                //校验手机号是否被注册
                List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andUserIdNotEqualTo(user.getUserId())
                        .andRoleIdEqualTo(user.getRoleId())
                        .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(users)) {
                     executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
                }

                //合作伙伴中心的手机号好需要检测是否在OS系统中存在
                List<User> osUsers = userMapper.selectByExample(new UserExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).example());
                if (ObjectUtils.isNotEmpty(osUsers)) {
                       executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在OS系统中已存在");
                }
                List<UserProvinceBusiness> oldUserProvince= userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(oldUserProvince)) {
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在省业中心中已存在");
                }
            }

            //修改用户信息
            user.setName(newName != null ? newName : user.getName());
            user.setPhone(encryptPhone != null ? encryptPhone : user.getPhone());
            user.setEmail(newEmail != null ? newEmail : user.getEmail());
            user.setUpdateTime(new Date());
            userPartnerMapper.updateByPrimaryKey(user);

        } else if (SYSTEM_SCREEN.equals(request.getSystem())) {
            if (ObjectUtils.isEmpty(request.getFrom())) {
                   executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "大屏系统用户对象不能为空");
                });
                throw new BusinessException(StatusConstant.PARAM_ERROR, "大屏系统用户对象不能为空");
            }
//            if (USER_FROM_IOT.equals(request.getFrom())) {
//                throw new BusinessException(StatusConstant.PARAM_ERROR, "大屏系统物联网用户不能修改");
//            }

            UserScreen user = userScreenMapper.selectByPrimaryKey(request.getUserId());
            if (!Optional.ofNullable(user).isPresent()) {
                  executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_NO_EXIST.getMessage());
                });
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }

            if (!RegexUtil.regexOperatorName(newName)) {
                   executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NAME_FORMAT_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
            }
            //手机格式校验
            if (!RegexUtil.regexPhone(newPhone)) {
                   executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            if (!encryptPhone.equals(user.getPhone())) {
                List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .andIdNotEqualTo(user.getId()).example());
                if (ObjectUtils.isNotEmpty(users)) {
                      executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_IS_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
                }
            }

            //修改用户信息
            user.setName(newName != null ? newName : user.getName());
            user.setPhone(encryptPhone != null ? encryptPhone : user.getPhone());
            user.setEmail(newEmail != null ? newEmail : user.getEmail());
            user.setUpdateTime(new Date());
            userScreenMapper.updateByPrimaryKey(user);
        }else if (PROVINCE_MANAGEMENT.equals(request.getSystem())) {
          /*  if (ObjectUtils.isEmpty(request.getCompany())) {
                throw new BusinessException(StatusConstant.PARAM_ERROR, "所属公司不能为空");
            }

            if (ObjectUtils.isEmpty(request.getProvince())) {
                throw new BusinessException(StatusConstant.PARAM_ERROR, "省域不能为空");
            }*/
            UserProvinceBusiness user = userProvinceBusinessMapper.selectByPrimaryKey(request.getUserId());
            if (!Optional.ofNullable(user).isPresent()) {
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }
            if (!RegexUtil.regexOperatorName(newName)) {
                throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
            }
            //手机格式校验
            if (!RegexUtil.regexPhone(newPhone)) {
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }

            if (!encryptPhone.equals(user.getPhone())) {
                //校验手机号是否被注册
                List<UserPartner> users = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .example());
                if (ObjectUtils.isNotEmpty(users)) {
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST,"手机号在合作伙伴中心中已存在");
                }

                //合作伙伴中心的手机号好需要检测是否在OS系统中存在
                List<User> osUsers = userMapper.selectByExample(new UserExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(osUsers)) {
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在OS系统中已存在");
                }

                List<UserProvinceBusiness> oldUserProvince= userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
                if (ObjectUtils.isNotEmpty(oldUserProvince)) {
                    throw new BusinessException(StatusConstant.PHONE_IS_EXIST, "手机号在省业中心中已存在");
                }
            }

            //修改用户信息
            user.setName(newName != null ? newName : user.getName());
            user.setPhone(encryptPhone != null ? encryptPhone : user.getPhone());
            user.setEmail(newEmail != null ? newEmail : user.getEmail());
         /*   if (ObjectUtils.isNotEmpty(request.getUserType())) {
                user.setUserType(request.getUserType());
            }
            user.setProvince(request4EditUser.getProvince());
            user.setBeId(request4EditUser.getBeId());
            user.setLocation(request4EditUser.getLocation());
            user.setLocationName(request4EditUser.getLocationName());
            user.setCompany(request4EditUser.getCompany());
            user.setRoleId(newRoleId);*/
            user.setUpdateTime(new Date());
            userProvinceBusinessMapper.updateByPrimaryKey(user);
        }
        //修改成功之后再删除验证码
        captchaService.deleteEditSmsCodeInRedis(newPhone);


        if (roleInfo != null) {

            logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        }
        return new BaseAnswer();
    }

    @Transactional(rollbackFor = Exception.class)
    public void enableUser(Request4EnableUser request4EnableUser, LoginIfo4Redis loginIfo4Redis, String ip) {
        //获取删除用户id
        String userId = request4EnableUser.getUserId();
        Boolean enable = request4EnableUser.getEnable();
        Data4User data4User = new Data4User();
        Date now = new Date();
        String enableContent = enable ? "启用" : "停用";
        StringBuilder content = new StringBuilder();
        content.append("【" + enableContent + "用户账号】");

        if (SYSTEM_OS.equals(request4EnableUser.getSystem())) {
            /**OS系统用户删除*/
            User user = userMapper.selectByPrimaryKey(userId);
            Boolean isAdmin = user.getIsAdmin() == null ? false : user.getIsAdmin();
            // 超级管理员不能删除
            if (isAdmin != null && isAdmin && user.getRoleId().equals(SYSTEM_USER_ROLE_ID)) {

                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_DELETE_ADMIN.getMessage());
                });
                throw new BusinessException(StatusConstant.NO_PERMIT_DELETE_ADMIN);
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), user.getRoleId())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());
                });

                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }

            //  查询修改用户是否存在流程中的产品、
            if (!enable) {
               /* BaseAnswer<Integer> productInReviewCount = iotFeignClient.getProductInReviewCount(userId);
                Integer data = productInReviewCount.getData();
                if (data > 0) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.USER_EXIST_UNAUDITED.getMessage());
                    });
                    throw new BusinessException(StatusConstant.USER_EXIST_UNAUDITED);
                }*/
                BaseAnswer<Boolean> booleanBaseAnswer = iotServiceFeign.hasRunningFlowInstance(userId);
                Boolean flowInstanceData = booleanBaseAnswer.getData();
                if(flowInstanceData){
                    throw new BusinessException(StatusConstant.PRODUCT_FLOW_EXIST);
                }

            } else {
                // 启用的时候判断是否有同手机同roleId用户 有的话报错
                List<User> existUser = userMapper.selectByExample(new UserExample().createCriteria()
                        .andPhoneEqualTo(user.getPhone())
                        .andUserIdNotEqualTo(userId)
                        .andIsCancelEqualTo(false)
                        .andIsLogoffEqualTo(false)
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (CollectionUtils.isNotEmpty(existUser)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.ENABLE_USER_EXIST.getMessage());
                    });

                    throw new BusinessException(StatusConstant.ENABLE_USER_EXIST);
                }
           //启用的时候判断 统一用户状态
                Integer unifiedStatus = user.getUnifiedStatus();
                if (0!=unifiedStatus){
                    throw new BusinessException(StatusConstant.USER_UNIFIED_STATUS);
                }
            }

            // 逻辑删除用户
            user.setIsCancel(!enable);
            user.setUpdateTime(now);
            userMapper.updateByPrimaryKeySelective(user);
            // 保存至用户历史表
//            UserHistory userHistory = new UserHistory();
//            BeanUtils.copyProperties(user, userHistory);
//            userHistoryMapper.insert(userHistory);

            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));


        } else if (SYSTEM_PARTNER.equals(request4EnableUser.getSystem())) {
            UserPartner user = userPartnerMapper.selectByPrimaryKey(userId);
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), user.getRoleId())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());
                });

                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }

            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());

            //当要禁用的账号是主账号, 判断从账号是否存在商品或订单未处理，判断其合作伙伴售后是否有售后订单未处理，判断其合作伙伴保理账号是否有订单未处理，
            // 并且把对应从账号以及合作伙伴售后都禁用
            //禁用从账号只需判断用户是否存在商品或者订单未处理
            //禁用装维用户只需判断用后是否存在派单中售后订单
            //禁用合作伙伴保理用户判断其是否有订单未处理
            if (!enable) {
                if (PARTNER_LORD_ROLE.equals(roleInfoDTO.getRoleType())) {
                    RoleInfo partnerRole = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                            .andRoleTypeEqualTo(PARTNER_ROLE).example()).get(0);
                    List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                            .andPartnerNameEqualTo(user.getPartnerName()).andRoleIdEqualTo(partnerRole.getId()).example());

                    if (CollectionUtils.isNotEmpty(userPartners)) {
                        // 校验当前用户下从账号下是否有商品或订单未处理，若有则不能禁用，没有则禁用从账号
                        for (UserPartner partner : userPartners) {
                            BaseAnswer<Boolean> answer = null;
                            try {
                                answer = iotServiceFeign.judgeInfoByUserId(partner.getUserId());
                            } catch (Exception e) {
                                log.error("内部调用iotService服务，查询用户下是否有商品或未处理订单异常！", e);
                                executorService.execute(() -> {
                                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.QUERY_ORDER_ERROR.getMessage());
                                });

                                throw new BusinessException(StatusConstant.QUERY_ORDER_ERROR);
                            }
                            if (answer != null && answer.getData()) {
                                log.info("该用户下存在商品或订单：{}", partner.getUserId());
                                executorService.execute(() -> {
                                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.EXIST_GOODS_ORDER_ERROR.getMessage());
                                });

                                throw new BusinessException(StatusConstant.EXIST_GOODS_ORDER_ERROR);
                            }

                            partner.setIsCancel(true);
                            partner.setUpdateTime(now);
                            partner.setCanEnable(false);
                            userPartnerMapper.updateByPrimaryKeySelective(partner);
                        }
                    }
                    //装维4个角色用户
                    List<String> roleTypeList = new ArrayList<>();
                    roleTypeList.add(PARTNER_INSTALL_MANAGER);
                    roleTypeList.add(PARTNER_INSTALL_LORD);
                    roleTypeList.add(PARTNER_INSTALL_SUB);
                    roleTypeList.add(PARTNER_INSTALL_ROLE);
                    //注销主合作伙伴下的装维管理员 装维主 装维从
                    List<RoleInfo> roleInfos = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                            .andRoleTypeIn(roleTypeList).example());
                    List<String> collect = roleInfos.stream().map(RoleInfo::getId).collect(Collectors.toList());
                    List<UserPartner> userInstalls = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                            .andPartnerNameEqualTo(user.getPartnerName()).andRoleIdIn(collect).example());

                    if (CollectionUtils.isNotEmpty(userInstalls)) {
                        //  查询装维用户是否存在派单中订单
                        for (UserPartner installer : userInstalls) {
                            BaseAnswer<Long> installUserAfterSaleOrder = installFeignClient.getInstallUserAfterSaleOrder(installer.getUserId());
                            if (installUserAfterSaleOrder != null && installUserAfterSaleOrder.getData() != null) {
                                Long data = installUserAfterSaleOrder.getData();
                                if (data > 0) {
                                    executorService.execute(() -> {
                                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.INSTALL_USER_AFTER_SALE_ORDER.getMessage());
                                    });
                                    throw new BusinessException(StatusConstant.INSTALL_USER_AFTER_SALE_ORDER);
                                }
                            }
                            installer.setIsCancel(true);
                            installer.setUpdateTime(now);
                            installer.setCanEnable(false);
                            userPartnerMapper.updateByPrimaryKeySelective(installer);
                        }
                    }

                    RoleInfo baoliRole = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                            .andRoleTypeEqualTo(PARTNER_BAOLI).example()).get(0);
                    List<UserPartner> userBaolis = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                            .andPartnerNameEqualTo(user.getPartnerName()).andRoleIdEqualTo(baoliRole.getId()).example());

                    if (CollectionUtils.isNotEmpty(userBaolis)) {
                        //  查询保理用户能否关闭
                        for (UserPartner baoli : userBaolis) {
                            BaseAnswer<Boolean> hasBaoliOrder = iotFeignClient.judgeUserIfHaveEffectiveTrade(baoli.getUserId());
                            if (hasBaoliOrder != null && hasBaoliOrder.getData() != null && !hasBaoliOrder.getData()) {
                                executorService.execute(() -> {
                                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.BAOLI_USER_HAS_TRADE_ORDER.getMessage());
                                });
                                throw new BusinessException(StatusConstant.BAOLI_USER_HAS_TRADE_ORDER);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(userBaolis)) {
                        for (UserPartner province : userBaolis) {
                            province.setIsCancel(true);
                            province.setUpdateTime(now);
                            province.setCanEnable(false);
                            userPartnerMapper.updateByPrimaryKeySelective(province);
                        }
                    }

                    //注销主合作伙伴下的合作伙伴省管用户
                    RoleInfo provinceRole = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                            .andRoleTypeEqualTo(PARTNER_PROVINCE).example()).get(0);
                    List<UserPartner> userProvinces = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                            .andPartnerNameEqualTo(user.getPartnerName()).andRoleIdEqualTo(provinceRole.getId()).example());

                    if (CollectionUtils.isNotEmpty(userProvinces)) {
                        for (UserPartner province : userProvinces) {
                            province.setIsCancel(true);
                            province.setUpdateTime(now);
                            province.setCanEnable(false);
                            userPartnerMapper.updateByPrimaryKeySelective(province);
                        }
                    }

                } else if (PARTNER_ROLE.equals(roleInfoDTO.getRoleType())) {
                    //检测从伙伴是否有订单或商品需要处理
                    BaseAnswer<Boolean> answer = null;
                    try {
                        answer = iotServiceFeign.judgeInfoByUserId(user.getUserId());
                    } catch (Exception e) {
                        log.error("内部调用iotService服务，查询用户下是否有商品或未处理订单异常！", e);
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.QUERY_ORDER_ERROR.getMessage());
                        });
                        throw new BusinessException(StatusConstant.QUERY_ORDER_ERROR);
                    }
                    if (answer != null && answer.getData()) {
                        log.info("该用户下存在商品或订单：{}", user.getUserId());
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.EXIST_GOODS_ORDER_ERROR.getMessage());
                        });
                        throw new BusinessException(StatusConstant.EXIST_GOODS_ORDER_ERROR);
                    }
                }else if(PARTNER_PROVINCE.equals(roleInfoDTO.getRoleType())){
                    //TODO 目前没有其他逻辑处理 不用校验啥

                } else if (PARTNER_INSTALL_ROLE.equals(roleInfoDTO.getRoleType())||
                        PARTNER_INSTALL_MANAGER_ROLE.equals(roleInfoDTO.getRoleType()) ||
                        PARTNER_INSTALL_LORD_ROLE.equals(roleInfoDTO.getRoleType())||
                        PARTNER_INSTALL_SUB_ROLE.equals(roleInfoDTO.getRoleType())) {
                    BaseAnswer<Long> installUserAfterSaleOrder = installFeignClient.getInstallUserAfterSaleOrder(user.getUserId());
                    if (installUserAfterSaleOrder != null && installUserAfterSaleOrder.getData() != null) {
                        Long data = installUserAfterSaleOrder.getData();
                        if (data > 0) {
                            executorService.execute(() -> {
                                logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.INSTALL_USER_AFTER_SALE_ORDER.getMessage());
                            });
                            throw new BusinessException(StatusConstant.INSTALL_USER_AFTER_SALE_ORDER);
                        }
                    }
                } else if (PARTNER_BAOLI.equals(roleInfoDTO.getRoleType())) {
                    BaseAnswer<Boolean> hasBaoliOrder = iotFeignClient.judgeUserIfHaveEffectiveTrade(user.getUserId());
                    if (hasBaoliOrder != null && hasBaoliOrder.getData() != null && !hasBaoliOrder.getData()) {
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                    content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.BAOLI_USER_HAS_TRADE_ORDER.getMessage());
                        });
                        throw new BusinessException(StatusConstant.BAOLI_USER_HAS_TRADE_ORDER);
                    }
                }
            } else {
                // 启用的时候判断是否有同手机同roleId用户 有的话报错
                List<UserPartner> existUser = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPhoneEqualTo(user.getPhone())
                        .andUserIdNotEqualTo(userId)
                        .andIsCancelEqualTo(false)
                        .andIsLogoffEqualTo(false)
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (CollectionUtils.isNotEmpty(existUser)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.ENABLE_USER_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.ENABLE_USER_EXIST);
                }

                // 启用的时候公司下面所有账号都启用
                UserPartner userPartner = new UserPartner();
                userPartner.setCanEnable(true);
                UserPartnerExample userPartnerExample = new UserPartnerExample();
                userPartnerExample.createCriteria().andPartnerNameEqualTo(user.getPartnerName());
                userPartnerMapper.updateByExampleSelective(userPartner, userPartnerExample);
            }
            // 逻辑删除用户
            user.setIsCancel(!enable);
            user.setUpdateTime(new Date());
            userPartnerMapper.updateByPrimaryKeySelective(user);


            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));

        } else if (SYSTEM_SCREEN.equals(request4EnableUser.getSystem())) {
            /**大屏系统用户删除*/
            UserScreen user = userScreenMapper.selectByPrimaryKey(userId);
            Boolean isAdmin = user.getIsAdmin() == null ? false : user.getIsAdmin();

            // 超级管理员不能删除
            if (isAdmin != null && isAdmin) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_DELETE_ADMIN.getMessage());
                });
                throw new BusinessException(StatusConstant.NO_PERMIT_DELETE_ADMIN);
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), user.getRoleId())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());
                });
                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }
            if (enable) {
                // 启用的时候判断是否有同手机同roleId用户 有的话报错
                List<UserScreen> existUser = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                        .andPhoneEqualTo(user.getPhone())
                        .andIdNotEqualTo(userId)
                        .andIsCancelEqualTo(false)
                        .andIsLogoffEqualTo(false)
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (CollectionUtils.isNotEmpty(existUser)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.ENABLE_USER_EXIST.getMessage());
                    });
                    throw new BusinessException(StatusConstant.ENABLE_USER_EXIST);
                }
                //启用的时候判断 统一用户状态
                Integer unifiedStatus = user.getUnifiedStatus();
                if (unifiedStatus != null && 0!=unifiedStatus){
                    throw new BusinessException(StatusConstant.USER_UNIFIED_STATUS);
                }

            }
//            if (user.getIsCancel()) {
//                throw new BusinessException(StatusConstant.USER_LOGGED_OUT);
//            }
            // 逻辑删除用户
            user.setIsCancel(!enable);
            user.setUpdateTime(new Date());
            userScreenMapper.updateByPrimaryKeySelective(user);
            BeanUtils.copyProperties(user, data4User);
            data4User.setUserId(userId);
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey));
        } else if (PROVINCE_MANAGEMENT.equals(request4EnableUser.getSystem())) {
            /**省业管理用户删除*/
            UserProvinceBusiness user = userProvinceBusinessMapper.selectByPrimaryKey(userId);
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), user.getRoleId())) {
                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }
            //查询修改用户是否存在流程中的产品、
            if (!enable) {
                //停用
                BaseAnswer<Boolean> booleanBaseAnswer = iotServiceFeign.hasRunningFlowInstance(userId);
                Boolean flowInstanceData = booleanBaseAnswer.getData();
                if(flowInstanceData){
                    throw new BusinessException(StatusConstant.PRODUCT_FLOW_EXIST);
                }

                user.setCanEnable(true);
            } else {
                // 启用的时候判断是否有同手机同roleId用户 有的话报错
                List<UserProvinceBusiness> existUser = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andPhoneEqualTo(user.getPhone())
                        .andUserIdNotEqualTo(userId)
                        .andIsCancelEqualTo(false)
                        .andIsLogoffEqualTo(false)
                        .andRoleIdEqualTo(user.getRoleId()).example());
                if (CollectionUtils.isNotEmpty(existUser)) {
                    throw new BusinessException(StatusConstant.ENABLE_USER_EXIST);
                }
                user.setCanEnable(false);
            }

            // 逻辑删除用户
            user.setIsCancel(!enable);
            user.setUpdateTime(now);
            userProvinceBusinessMapper.updateByPrimaryKeySelective(user);

            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey));
        }

        // 将删除的用户下线
        if (!enable) {
            loginOut(userId, false);
        }
        //删除成功，操作日志
//        encapsulationOperationLog(data4User, null, "2");
        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(data4User.getRoleId());
        if (roleInfo != null) {
            StringBuilder contentSucess = new StringBuilder("【" + enableContent + "用户账号】\n所属系统: " + roleInfo.getSystem()
                    + ",所属角色: " + roleInfo.getName() + "; 姓名: "
                    + LogService.custNameDesensitization(data4User.getName()) + ",联系电话" + LogService.replaceWithStar(data4User.getPhone()));
            String userFrom = data4User.getUserFrom();
            if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }
        }
    }



    public BaseAnswer<PageData<Data4User>> userPage(Request4UserPage request4UserPage, LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer<PageData<Data4User>> baseAnswer = new BaseAnswer<>();
//        PageData<Data4User> userPageData = new PageData<>();
//        List<String> queryRoleIds = new ArrayList<>();
//        List<String> queryUserIds = new ArrayList<>();
//        Integer pageNum = request4UserPage.getPageNum() != null ? request4UserPage.getPageNum() : 1;
//        Integer pageSize = request4UserPage.getPageSize() != null ? request4UserPage.getPageSize() : 10;
//
//        String queryInfo = request4UserPage.getQueryInfo();
//        String roleType = request4UserPage.getRoleType();
//        String userId = request4UserPage.getUserId();
//        String userName = request4UserPage.getUserName();
//        List<RoleInfo> roles = roleIn..selectByExample(new RoleExample().createCriteria().andRoleTypeEqualTo(roleType).example());
//        if (roles.isEmpty()) {
//            return baseAnswer;
//        }
//        String queryRoleId = roles.get(0).getId();
//        String userRoleType = loginIfo4Redis.getRoleType();
//        List<UserDO> users = new ArrayList<>();
//        long total = 0;
//        if (loginIfo4Redis.getIsAdmin()) {
//            //当前登录是超管，可查询所有角色的账号
//            users = userMapperExt.page4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId, (pageNum - 1) * pageSize, pageSize);
//            total = userMapperExt.pageCount4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId);
//        } else if (loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()) {
//            //当前登录是合作伙伴主,可查询他下面的合作伙伴从账号
//            List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(loginIfo4Redis.getUserId()).example());
//            queryUserIds = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(queryUserIds)) {
//                //这里添加一个不存在的用户id，避免当主账号没有从账号时，queryUserIds为空，起不到限制查询用户id范围的作用
//                queryUserIds.add("-1");
//            }
//            users = userMapperExt.page4User(queryInfo, userId, queryInfo, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId, (pageNum - 1) * pageSize, pageSize);
//            total = userMapperExt.pageCount4User(queryInfo, userId, queryInfo, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId);
//        } else {
//            //根据角色所持有的账户管理权限进行查询
//            RoleManageListItemVO roleManageListItemVO = roleInfoService.getRoleManageByRole(loginIfo4Redis.getRoleId());
//            if (roleManageListItemVO == null || CollectionUtils.isEmpty(roleManageListItemVO.getManageRoles())) {
//                return baseAnswer;
//            }
//            queryRoleIds = roleManageListItemVO.getManageRoles().stream()
//                    .map(RoleManageListItemVO::getRoleId).collect(Collectors.toList());
//            users = userMapperExt.page4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId, (pageNum - 1) * pageSize, pageSize);
//            total = userMapperExt.pageCount4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId);
//        }
////        if (OPERATOR_ROLE.equals(userRoleType)) {
////            //当前登录是运管,可查询所有合作伙伴主从账号,客服人员账号
////            List<Role> partnerRoles = roleMapper.selectByExample(new RoleExample().createCriteria().andRoleTypeIn(new ArrayList<String>() {{
////                add(PARTNER_ROLE);
////                add(PARTNER_LORD_ROLE);
////                add(MANAGER_STAFF_ROLE);
////            }}).example());
////            queryRoleIds = partnerRoles.stream().map(p -> {
////                return p.getId();
////            }).collect(Collectors.toList());
////            users = userMapperExt.page4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId, (pageNum - 1) * pageSize, pageSize);
////            total = userMapperExt.pageCount4User(queryInfo, userId, userName, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId);
////        }
////        if (PARTNER_LORD_ROLE.equals(userRoleType)) {
////            //当前登录是合作伙伴主,可查询他下面的合作伙伴从账号
////            List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(loginIfo4Redis.getUserId()).example());
////            queryUserIds = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
////            if (CollectionUtils.isEmpty(queryUserIds)) {
////                //这里添加一个不存在的用户id，避免当主账号没有从账号时，queryUserIds为空，起不到限制查询用户id范围的作用
////                queryUserIds.add("-1");
////            }
////            users = userMapperExt.page4User(queryInfo, userId, queryInfo, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId, (pageNum - 1) * pageSize, pageSize);
////            total = userMapperExt.pageCount4User(queryInfo, userId, queryInfo, request4UserPage.getPartnerName(), queryRoleIds, queryUserIds, queryRoleId);
////        }
//        //当前登录是合作伙伴从,不能查询用户列表
//        //当前登录是客服，不能查询用户列表
//
//        //转成Data4User
//        List<User> collect = users.stream().map(u -> {
//            User user = new User();
//            BeanUtils.copyProperties(u, user);
//            return user;
//        }).collect(Collectors.toList());
//        List<Data4User> data4Users = ConvertToDataUser(collect);
//        userPageData.setPage(pageNum);
//        userPageData.setCount(total);
//        userPageData.setData(data4Users);
//        baseAnswer.setData(userPageData);
        return baseAnswer;
    }

    /**
     * 商品配置合作伙伴时 查询用户信息
     *
     * @param cooperatorId
     * @param loginIfo4Redis
     * @return
     */
    public BaseAnswer<List<Data4User>> listQueryUserByCooperatorId(String cooperatorId, String operate, LoginIfo4Redis loginIfo4Redis) {
        //商品未配置合作伙伴，超管 运管 查询所有主合作伙伴
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();

        String roleType = loginIfo4Redis.getRoleType();
        //根据配置主从伙伴校验，不区分角色
//        //配置主合作伙伴 operate 0:配置主合作  1：配置从合作
//        if (StringUtils.isBlank(cooperatorId) && "0".equals(operate)) {
//            //还未配置合作伙伴 查询所有主合作伙伴
//            if (ADMIN_ROLE.equals(roleType) || OPERATOR_ROLE.equals(roleType)) {
//                List<User> userList = userMapper.selectByExample(new UserExample().createCriteria().andIsCancelEqualTo(false).andIsPrimaryEqualTo(true).example());
//                List<Data4User> data4Users = ConvertToDataUser(userList);
//                baseAnswer.setData(data4Users);
//            }
//            //配置从合作伙伴
//        } else if (StringUtils.isNotBlank(cooperatorId) && "1".equals(operate)) {
//            //商品已配置合作伙伴， 主合作伙伴查询旗下从合作伙伴
//            BaseAnswer<Data4User> answer = userInfo(cooperatorId);
//            Data4User user = answer.getData();
//            boolean isPrimary = user.getIsPrimary() == null ? false : user.getIsPrimary();
//            if (user == null) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "合作伙伴不存在");
//            }
//            if (ADMIN_ROLE.equals(roleType) || OPERATOR_ROLE.equals(roleType) || PARTNER_LORD_ROLE.equals(roleType)) {
//                if (isPrimary && user.getIsPrimary() != null) {
//                    List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(cooperatorId).example());
//                    if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
//                        List<String> downUserId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
//                        //通过从合作用户id查询用户信息
//                        List<User> userList = userMapper.selectByExample(new UserExample().createCriteria().andIsCancelEqualTo(false).andIsPrimaryEqualTo(false).andUserIdIn(downUserId).example());
//                        //转成Data4User
//                        List<Data4User> data4Users = ConvertToDataUser(userList);
//                        baseAnswer.setData(data4Users);
//                    }
//                } else if (!isPrimary && user.getIsPrimary() != null) {
//                    String partnerName = user.getPartnerName();
//                    UserExample example = new UserExample().createCriteria().andIsCancelEqualTo(false).andPartnerNameEqualTo(partnerName).andIsPrimaryEqualTo(true).example();
//                    List<User> users = userMapper.selectByExample(example);
//                    List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(users.get(0).getUserId()).example());
//                    if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
//                        List<String> downUserId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
//                        //通过从合作用户id查询用户信息
//                        List<User> userList = userMapper.selectByExample(new UserExample().createCriteria().andIsCancelEqualTo(false).andIsPrimaryEqualTo(false).andUserIdIn(downUserId).example());
//                        //转成Data4User
//                        List<Data4User> data4Users = ConvertToDataUser(userList);
//                        baseAnswer.setData(data4Users);
//                    }
//                }
//            }
//            //重新配置主合作伙伴
//        } else if (StringUtils.isNotBlank(cooperatorId) && "0".equals(operate)) {
//            if (ADMIN_ROLE.equals(roleType) || OPERATOR_ROLE.equals(roleType)) {
//                List<User> userList = userMapper.selectByExample(new UserExample().createCriteria().andIsCancelEqualTo(false).andIsPrimaryEqualTo(true).example());
//                List<Data4User> data4Users = ConvertToDataUser(userList);
//                baseAnswer.setData(data4Users);
//            }
//        }

        //配置主合作伙伴 operate 0:配置主合作  1：配置从合作
        if ("0".equals(operate)) {
            if (PARTNER_LORD_ROLE.equals(roleType)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "主合作伙伴不能查询其他主信息");
            }
            //还未配置合作伙伴 查询所有主合作伙伴
            List<UserPartner> userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                    .andIsPrimaryEqualTo(true).example());
            List<Data4User> data4Users = partnerToDataUser(userList);
            baseAnswer.setData(data4Users);
            //配置从合作伙伴
        } else if (StringUtils.isNotBlank(cooperatorId) && "1".equals(operate)) {
            //商品已配置合作伙伴， 主合作伙伴查询旗下从合作伙伴
            BaseAnswer<Data4User> answer = partnerInfo(cooperatorId, false);
            Data4User user = answer.getData();
            boolean isPrimary = user.getIsPrimary() == null ? false : user.getIsPrimary();
            if (user == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "合作伙伴不存在");
            }

            if (isPrimary && user.getIsPrimary() != null) {
                List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(cooperatorId).example());
                if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
                    List<String> downUserId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(downUserId)) {
                        //通过从合作用户id查询用户信息
                        List<UserPartner> userList = null;
                        if (downUserId.size() > 1) {
                            userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                                    .andIsPrimaryEqualTo(false).andUserIdIn(downUserId).example());
                        } else {
                            userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                                    .andIsPrimaryEqualTo(false).andUserIdEqualTo(downUserId.get(0)).example());
                        }
                        //转成Data4User
                        List<Data4User> data4Users = partnerToDataUser(userList);
                        baseAnswer.setData(data4Users);
                    }

                }
            } else if (!isPrimary && user.getIsPrimary() != null) {
                String partnerName = user.getPartnerName();
                UserPartnerExample example = new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .andPartnerNameEqualTo(partnerName).andIsPrimaryEqualTo(true).example();
                List<UserPartner> users = userPartnerMapper.selectByExample(example);
                List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(users.get(0).getUserId()).example());
                if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
                    List<String> downUserId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(downUserId)) {
                        //通过从合作用户id查询用户信息
                        List<UserPartner> userList = null;
                        if (downUserId.size() > 1) {
                            userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                                    .andIsPrimaryEqualTo(false).andUserIdIn(downUserId).example());
                        } else {
                            userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                                    .andIsPrimaryEqualTo(false).andUserIdEqualTo(downUserId.get(0)).example());
                        }
                        //转成Data4User
                        List<Data4User> data4Users = partnerToDataUser(userList);
                        baseAnswer.setData(data4Users);
                    }
                }
            }
        }
        return baseAnswer;

    }

    /**
     * 查询用户信息
     *
     * @param userId
     * @param needByInner-是否内部服务调用， 内部服务调用不查询用不到权限及同手机号的其他账号提高速度
     * @param operationModule-判断操作模块，日志记录使用      1：运营账号管理 2：合作伙伴管理 3：系统账户查询 是空的话 就内部使用
     * @return
     */
    public BaseAnswer<Data4User> userInfo(String userId, Boolean needByInner,String operationModule) {
        //先查询OS用户
        BaseAnswer<Data4User> baseAnswer = osUserInfo(userId, needByInner);

        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
         /*   roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
           if (roleInfo != null) {
                StringBuilder content = new StringBuilder("【查看用户详情】\n所属系统: " + roleInfo.getSystem()
                       + "\n所属角色: " + roleInfo.getName() + "\n 姓名: "
                       + baseAnswer.getData().getName() + "\n联系电话" + LogService.replaceWithStar(baseAnswer.getData().getPhone()));
               logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString());
            }*/
            return baseAnswer;
        }
        //查询合作伙伴用户
        baseAnswer = partnerInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
         /*   roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
           if (roleInfo != null) {
               StringBuilder content = new StringBuilder("【查看用户详情】\n所属系统: " + roleInfo.getSystem()
                       + "\n所属角色: " + roleInfo.getName() + "\n 姓名: "
                       + baseAnswer.getData().getName() + "\n联系电话" + LogService.replaceWithStar(baseAnswer.getData().getPhone()));
               logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString());
          }*/
            return baseAnswer;
        }
        //查询大屏用户
        baseAnswer = screenerInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {

        /*    roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
            if (roleInfo != null) {
                StringBuilder content = new StringBuilder("【查看用户详情】\n所属系统: " + roleInfo.getSystem()
                        + "\n所属角色: " + roleInfo.getName() + "\n 姓名: "
                        + baseAnswer.getData().getName() + "\n联系电话" + LogService.replaceWithStar(baseAnswer.getData().getPhone()));
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString());
            }*/
            return baseAnswer;
        }

        //查询省业管员用户
        baseAnswer = provinceBusinessInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
         /*   roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
            if (roleInfo != null) {
                StringBuilder content = new StringBuilder("【查看用户详情】\n所属系统: " + roleInfo.getSystem()
                        + "\n所属角色: " + roleInfo.getName() + "\n 姓名: "
                        + baseAnswer.getData().getName() + "\n联系电话" + LogService.replaceWithStar(baseAnswer.getData().getPhone()));
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString());
            }*/
            return baseAnswer;
        }

        RoleInfo  roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
        if (roleInfo != null) {
            StringBuilder content = new StringBuilder("【查看用户详情】\n所属系统: " + roleInfo.getSystem()
                    + "\n所属角色: " + roleInfo.getName() + "\n 姓名: "
                    +LogService.custNameDesensitization(baseAnswer.getData().getName())  + "\n联系电话" + LogService.replaceWithStar(baseAnswer.getData().getPhone()));
            String userFrom = baseAnswer.getData().getUserFrom();
            if (StringUtils.isNotBlank(operationModule) && "1".equals(operationModule)){
                //运营账号管理
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else if(StringUtils.isNotBlank(operationModule) && "2".equals(operationModule)){
                //合作伙伴管理
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else if(StringUtils.isNotBlank(operationModule) && "3".equals(operationModule)){
                //系统账户查询
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_SYSTEM_QUERY_MANAGE.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else {
                //为空内部使用不打印日志
            }

        }

        return baseAnswer;

    }

    /**
     * 查询用户信息无日志版本方便记录日志
     *
     * @param userId
     * @param needByInner-是否内部服务调用， 内部服务调用不查询用不到权限及同手机号的其他账号提高速度
     * @return
     */
    public BaseAnswer<Data4User> userInfoNoLog(String userId, Boolean needByInner) {
        //先查询OS用户
        log.info("hwf test userInfoNoLog userid = {}", userId);
        BaseAnswer<Data4User> baseAnswer = osUserInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
            RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
            return baseAnswer;
        }
        //查询合作伙伴用户
        baseAnswer = partnerInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
            RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(baseAnswer.getData().getRoleId());
            return baseAnswer;
        }
        //查询大屏用户
        baseAnswer = screenerInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
            return baseAnswer;
        }
        //查询省业管员用户
        baseAnswer = provinceBusinessInfo(userId, needByInner);
        if (ObjectUtils.isNotEmpty(baseAnswer.getData())) {
            return baseAnswer;
        }

        return baseAnswer;
    }

    /**
     * 查询用户信息
     *
     * @param userId
     * @return
     */
    public BaseAnswer<Data4User> osUserInfo(String userId, Boolean needByInner) {
        BaseAnswer<Data4User> baseAnswer = new BaseAnswer<>();
        Data4User data4User = new Data4User();
        // 查询对应user信息
        User user = userMapper.selectByPrimaryKey(userId);
        if (user != null) {
            //查询OS用户
            BeanUtils.copyProperties(user, data4User);
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));

            //查询角色业务类型
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            data4User.setRoleType(role.getRoleType());
            data4User.setRoleName(role.getName());
            data4User.setSystem(role.getSystem());
            data4User.setAdministrationCenter(role.getAdministrationCenter());
            if (!needByInner) {
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                //查询相同手机号的其他角色
                List<User> otherUsers = userMapper.selectByExample(new UserExample().createCriteria()
                        .andUserIdNotEqualTo(userId).andPhoneEqualTo(user.getPhone()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false)
                        .example()).stream().filter(item -> item.getUnifiedStatus() == null
                        || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherUsers)) {
                    data4User.setOtherRoles(otherUsers.stream().map(item -> {
                        UserRoleVO userRoleVO = new UserRoleVO();
                        userRoleVO.setUserId(item.getUserId());
                        RoleInfoDTO roleInfoDTO = roleInfoService.selectById(item.getRoleId());
                        userRoleVO.setRoleName(roleInfoDTO.getName());
                        userRoleVO.setSystem(roleInfoDTO.getSystem());
                        userRoleVO.setRoleType(roleInfoDTO.getRoleType());
                        userRoleVO.setIsLoginOs(roleInfoDTO.getIsLoginOs());
                        return userRoleVO;
                    }).filter(UserRoleVO::getIsLoginOs).collect(Collectors.toList()));
                }
            }
            baseAnswer.setData(data4User);
        }
        return baseAnswer;

    }

    /**
     * 查询合作伙伴用户信息
     *
     * @param userId
     * @return
     */
    public BaseAnswer<Data4User> partnerInfo(String userId, Boolean needByInner) {
        BaseAnswer<Data4User> baseAnswer = new BaseAnswer<>();
        Data4User data4User = new Data4User();
        // 查询对应user信息
        UserPartner user = userPartnerMapper.selectByPrimaryKey(userId);
        if (user != null) {
            BeanUtils.copyProperties(user, data4User);
            data4User.setProvincePartner(user.getProvince());
            data4User.setIsExternal(user.getIsExternal());
            data4User.setBeIdPartner(user.getBeId());
            data4User.setLocationPartner(user.getLocation());
            data4User.setLocationIdPartner(user.getLocationId());
            data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey));
            data4User.setUserFrom(USER_FROM_OTHER);
            data4User.setPartnerId(user.getUserId());
            data4User.setCompany(user.getPartnerName());
            //查询角色业务类型
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            data4User.setRoleType(role.getRoleType());
            data4User.setRoleName(role.getName());
            data4User.setSystem(role.getSystem());
            data4User.setAdministrationCenter(role.getAdministrationCenter());
            //判断不是主合作伙伴的关联的主的外部字段
            if (!PARTNER_LORD_ROLE.equals(role.getRoleType())){
                UserPartnerExample userExample = new UserPartnerExample().createCriteria()
                        .andCompanyIdEqualTo(user.getCompanyId()).andIsPrimaryEqualTo(true)
                        .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example();
                Optional<UserPartner> first = userPartnerMapper.selectByExample(userExample).stream().findFirst();
                data4User.setIsExternalPrimary(first.get().getIsExternal());
            }

            if (!needByInner) {
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                //查询相同手机号的其他角色
                List<UserPartner> otherUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andUserIdNotEqualTo(userId).andPhoneEqualTo(user.getPhone()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isNotEmpty(otherUsers)) {
                    data4User.setOtherRoles(otherUsers.stream().map(item -> {
                        UserRoleVO userRoleVO = new UserRoleVO();
                        userRoleVO.setUserId(item.getUserId());
                        RoleInfoDTO roleInfoDTO = roleInfoService.selectById(item.getRoleId());
                        userRoleVO.setRoleName(roleInfoDTO.getName());
                        userRoleVO.setSystem(roleInfoDTO.getSystem());
                        userRoleVO.setRoleType(roleInfoDTO.getRoleType());
                        userRoleVO.setIsLoginOs(roleInfoDTO.getIsLoginOs());
                        return userRoleVO;
                    }).filter(UserRoleVO::getIsLoginOs).collect(Collectors.toList()));
                }
            }
            baseAnswer.setData(data4User);
        }
        return baseAnswer;
    }

    public BaseAnswer<List<Data4User>> partnerInfo(List<String> userIds) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();

        List<String> filterArr = userIds.stream().filter(item -> item != null && !item.equals("-1") && !item.equals("")).collect(Collectors.toList());
        // 查询对应user信息
        List<UserPartner> users = null;
        if (CollectionUtils.isNotEmpty(filterArr)) {
            if (filterArr.size() > 1) {
                users = userPartnerMapper.selectByExample(
                        new UserPartnerExample().createCriteria().andUserIdIn(filterArr).example());
            } else {
                users = userPartnerMapper.selectByExample(
                        new UserPartnerExample().createCriteria().andUserIdEqualTo(filterArr.get(0)).example());
            }
        }
        baseAnswer.setData(partnerToDataUser(users));
        return baseAnswer;
    }


    /**
     * 同步userIDs查询用户信息
     * @param userIds
     * @return
     */
    public BaseAnswer<List<Data4User>> userInfoByUserIds(List<String> userIds) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();

        List<String> filterArr = userIds.stream().filter(item -> item != null && !item.equals("-1") && !item.equals("")).collect(Collectors.toList());
        // 查询对应user信息
        List<User> users = null;
        if (CollectionUtils.isNotEmpty(filterArr)) {
            if (filterArr.size() > 1) {
                users = userMapper.selectByExample(
                        new UserExample().createCriteria().andUserIdIn(filterArr).example());
            } else {
                users = userMapper.selectByExample(
                        new UserExample().createCriteria().andUserIdEqualTo(filterArr.get(0)).example());
            }
        }
        baseAnswer.setData(ConvertToDataUser(users));
        return baseAnswer;
    }


    /**
     * 查询大屏用户信息
     *
     * @param userId
     * @return
     */
    public BaseAnswer<Data4User> screenerInfo(String userId, Boolean needByInner) {
        BaseAnswer<Data4User> baseAnswer = new BaseAnswer<>();
        Data4User data4User = new Data4User();
        // 查询对应user信息
        UserScreen user = userScreenMapper.selectByPrimaryKey(userId);
        if (user != null) {
            String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);


            //获取前端需要回显的数组数据
            List<List<String>> lists = (List<List<String>>) redisTemplate.opsForValue().get(SCREEN_USER_ECHO_LIST + phone);

            BeanUtils.copyProperties(user, data4User);
            if (CollectionUtils.isNotEmpty(lists)) {
                data4User.setProvinceLocationList(lists);
            }
            data4User.setPhone(phone);
            //查询角色业务类型
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            data4User.setRoleType(role.getRoleType());
            data4User.setRoleName(role.getName());
            data4User.setSystem(role.getSystem());
            data4User.setAdministrationCenter(role.getAdministrationCenter());
            if (!needByInner) {
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                //查询相同手机号的其他角色
                List<UserScreen> otherUsers = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                                .andIdNotEqualTo(userId).andPhoneEqualTo(user.getPhone()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example())
                        .stream().filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherUsers)) {
                    data4User.setOtherRoles(otherUsers.stream().map(item -> {
                        UserRoleVO userRoleVO = new UserRoleVO();
                        userRoleVO.setUserId(item.getId());
                        RoleInfoDTO roleInfoDTO = roleInfoService.selectById(item.getRoleId());
                        userRoleVO.setRoleName(roleInfoDTO.getName());
                        userRoleVO.setSystem(roleInfoDTO.getSystem());
                        userRoleVO.setRoleType(roleInfoDTO.getRoleType());
                        userRoleVO.setIsLoginOs(roleInfoDTO.getIsLoginOs());
                        return userRoleVO;
                    }).filter(UserRoleVO::getIsLoginOs).collect(Collectors.toList()));
                }
            }
            baseAnswer.setData(data4User);
        }
        return baseAnswer;
    }

    /**
     * 查询省頁管員用户信息
     *
     * @param userId
     * @return
     */
    public BaseAnswer<Data4User> provinceBusinessInfo(String userId, Boolean needByInner) {
        BaseAnswer<Data4User> baseAnswer = new BaseAnswer<>();
        Data4User data4User = new Data4User();
        // 查询对应user信息
        UserProvinceBusiness user = userProvinceBusinessMapper.selectByPrimaryKey(userId);
        if (user != null) {
            String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);


            //获取前端需要回显的数组数据
            List<List<String>> lists = (List<List<String>>) redisTemplate.opsForValue().get(USER_PROVINCE_BUSINESS_LIST + phone);

            BeanUtils.copyProperties(user, data4User);
            if (CollectionUtils.isNotEmpty(lists)){
                data4User.setProvinceLocationList(lists);
            }
            data4User.setPhone(phone);
            //查询角色业务类型
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            data4User.setRoleType(role.getRoleType());
            data4User.setRoleName(role.getName());
            data4User.setSystem(role.getSystem());
            data4User.setAdministrationCenter(role.getAdministrationCenter());
            if (!needByInner) {
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                //查询相同手机号的其他角色
                List<UserProvinceBusiness> otherUsers = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                        .andUserIdNotEqualTo(userId).andPhoneEqualTo(user.getPhone()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
                if (CollectionUtils.isNotEmpty(otherUsers)) {
                    data4User.setOtherRoles(otherUsers.stream().map(item -> {
                        UserRoleVO userRoleVO = new UserRoleVO();
                        userRoleVO.setUserId(item.getUserId());
                        RoleInfoDTO roleInfoDTO = roleInfoService.selectById(item.getRoleId());
                        userRoleVO.setRoleName(roleInfoDTO.getName());
                        userRoleVO.setSystem(roleInfoDTO.getSystem());
                        userRoleVO.setRoleType(roleInfoDTO.getRoleType());
                        userRoleVO.setIsLoginOs(roleInfoDTO.getIsLoginOs());
                        return userRoleVO;
                    }).filter(UserRoleVO::getIsLoginOs).collect(Collectors.toList()));
                }
            }
            baseAnswer.setData(data4User);
        }
        return baseAnswer;
    }



    /**
     * 查询用户信息
     *
     * @param phone
     * @return
     */
    public BaseAnswer<List<Data4User>> userInfoByPhone(String phone) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        UserExample userExample = new UserExample();
        UserExample.Criteria criteria = userExample.createCriteria();
        criteria.andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey)).andIsCancelEqualTo(Boolean.FALSE).andIsLogoffEqualTo(false);
        List<User> users = userMapper.selectByExample(userExample).stream()
                .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(users)) {
            List<Data4User> data4Users = new ArrayList<>();
            for (User user : users) {
                // 查询对应user信息
                Data4User data4User = new Data4User();
                data4User.setUserId(user.getUserId());
                data4User.setName(user.getName());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                data4User.setIsAdmin(user.getIsAdmin());
                data4User.setRoleId(user.getRoleId());
                data4User.setPwd(user.getPwd());
                data4User.setIsPrimary(user.getIsPrimary());
                data4User.setIsSend(user.getIsSend());
                //20211216-zyj 合作伙伴名称
                data4User.setPartnerName(user.getPartnerName());
                data4User.setCompany(user.getCompany());
                data4User.setEmail(user.getEmail());
                //查询角色业务类型
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setRoleType(role.getRoleType());
                data4User.setRoleName(role.getName());
                data4User.setSystem(role.getSystem());
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                data4Users.add(data4User);
            }

            baseAnswer.setData(data4Users);
        }
        return baseAnswer;
    }

    /**
     * 查询用户信息
     *
     * @param phone
     * @return
     */
    public BaseAnswer<List<Data4User>> partnerInfoByPhone(String phone) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        UserPartnerExample userExample = new UserPartnerExample();
        UserPartnerExample.Criteria criteria = userExample.createCriteria();
        criteria.andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey)).andIsCancelEqualTo(Boolean.FALSE).andIsLogoffEqualTo(false);
        List<UserPartner> users = userPartnerMapper.selectByExample(userExample);
        if (CollectionUtils.isNotEmpty(users)) {
            List<Data4User> data4Users = new ArrayList<>();
            for (UserPartner user : users) {
                // 查询对应user信息
                Data4User data4User = new Data4User();
                BeanUtils.copyProperties(user, data4User);
//                data4User.setUserId(user.getUserId());
//                data4User.setName(user.getName());
//                data4User.setPhone(user.getPhone());
                data4User.setProvincePartner(user.getProvince());
                data4User.setBeIdPartner(user.getBeId());
                data4User.setLocationPartner(user.getLocation());
                data4User.setLocationIdPartner(user.getLocationId());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey));
                data4User.setIsAdmin(false);
//                data4User.setRoleId(user.getRoleId());
//                data4User.setPwd(user.getPwd());
//                data4User.setIsPrimary(user.getIsPrimary());
//                data4User.setIsSend(user.getIsSend());
                //20211216-zyj 合作伙伴名称
//                data4User.setPartnerName(user.getPartnerName());
                data4User.setCompany(null);
//                data4User.setEmail(user.getEmail());
                //查询角色业务类型
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setRoleType(role.getRoleType());
                data4User.setRoleName(role.getName());
                data4User.setSystem(role.getSystem());
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                data4Users.add(data4User);
            }

            baseAnswer.setData(data4Users);
        }
        return baseAnswer;
    }

    public BaseAnswer<List<Data4User>> partnerInfoByPhoneList(List<String> phoneList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(phoneList)){
            throw new BusinessException("10004","电话号码不能为空");
        }

        List<String> encryptPhoneList = new ArrayList<>();

        phoneList.stream().forEach(phone->{
            encryptPhoneList.add(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey));
        });

        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        UserPartnerExample userExample = new UserPartnerExample();
        UserPartnerExample.Criteria criteria = userExample.createCriteria();
        criteria.andPhoneIn(encryptPhoneList).andIsCancelEqualTo(Boolean.FALSE).andIsLogoffEqualTo(false);
        List<UserPartner> users = userPartnerMapper.selectByExample(userExample);
        if (CollectionUtils.isNotEmpty(users)) {
            List<Data4User> data4Users = new ArrayList<>();
            for (UserPartner user : users) {
                // 查询对应user信息
                Data4User data4User = new Data4User();
                BeanUtils.copyProperties(user, data4User);
//                data4User.setUserId(user.getUserId());
//                data4User.setName(user.getName());
//                data4User.setPhone(user.getPhone());
                data4User.setProvincePartner(user.getProvince());
                data4User.setBeIdPartner(user.getBeId());
                data4User.setLocationPartner(user.getLocation());
                data4User.setLocationIdPartner(user.getLocationId());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey));
                data4User.setIsAdmin(false);
//                data4User.setRoleId(user.getRoleId());
//                data4User.setPwd(user.getPwd());
//                data4User.setIsPrimary(user.getIsPrimary());
//                data4User.setIsSend(user.getIsSend());
                //20211216-zyj 合作伙伴名称
//                data4User.setPartnerName(user.getPartnerName());
                data4User.setCompany(null);
//                data4User.setEmail(user.getEmail());
                //查询角色业务类型
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setRoleType(role.getRoleType());
                data4User.setRoleName(role.getName());
                data4User.setSystem(role.getSystem());
                // 查询用户-角色-权限
                BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
                List<AuthCode> authCodes = answer.getData().getAuthCodes();
                data4User.setAuthCodes(authCodes);

                data4Users.add(data4User);
            }

            baseAnswer.setData(data4Users);
        }
        return baseAnswer;
    }

    /**
     * @param roleId: 角色id
     * @Description: 根据角色id查询用户集合
     * @return: java.util.List<com.chinamobile.iot.sc.pojo.entity.User.User>>
     * @Author: zyj
     * @date: 2021/11/2 17:44
     */
    public BaseAnswer<List<Data4User>> userListByRoleId(String roleId) {
        BaseAnswer<List<Data4User>> answer = new BaseAnswer<>();
        if (ObjectUtils.isNotEmpty(roleId)) {
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andRoleIdEqualTo(roleId).example());
            if (CollectionUtils.isNotEmpty(users)) {
                answer.setData(ConvertToDataUser(users));
                return answer;
            }

            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample()
                    .createCriteria().andRoleIdEqualTo(roleId).example());
            if (CollectionUtils.isNotEmpty(partners)) {
                answer.setData(partnerToDataUser(partners));
                return answer;
            }

            List<UserScreen> screens = userScreenMapper.selectByExample(new UserScreenExample()
                    .createCriteria().andRoleIdEqualTo(roleId).example());
            if (CollectionUtils.isNotEmpty(screens)) {
                answer.setData(partnerToDataUser(partners));
                return answer;
            }

        }
        return answer;
    }

    /**
     * 查询全量用户信息
     *
     * @return
     */
    public List<User> userListAll() {
        return userMapper.selectByExample(
                new UserExample().createCriteria()
                        .andRoleIdIsNotNull()
                        .andRoleIdNotEqualTo("")
                        .example());
    }

    /**
     * 查询该角色下的用户数量
     *
     * @param roleId
     * @return
     */
    public long countByRoleId(String roleId, String system) {
        long count = 0;
        if (SYSTEM_OS.equals(system)) {
            count = userMapper.countByExample(
                    new UserExample().createCriteria()
                            .andRoleIdEqualTo(roleId)
                            .andIsCancelEqualTo(false)
                            .andIsLogoffEqualTo(false)
                            .example());
        } else if (SYSTEM_PARTNER.equals(system)) {
            count = userPartnerMapper.countByExample(
                    new UserPartnerExample().createCriteria()
                            .andRoleIdEqualTo(roleId)
                            .andIsCancelEqualTo(false)
                            .andIsLogoffEqualTo(false)
                            .example());
        } else if (SYSTEM_SCREEN.equals(system)) {
            count = userScreenMapper.countByExample(
                    new UserScreenExample().createCriteria()
                            .andRoleIdEqualTo(roleId)
                            .andIsCancelEqualTo(false)
                            .example());
        }else if (PROVINCE_MANAGEMENT.equals(system)){
            count = userProvinceBusinessMapper.countByExample(
                    new UserProvinceBusinessExample().createCriteria()
                            .andRoleIdEqualTo(roleId)
                            .andIsCancelEqualTo(false)
                            .andIsLogoffEqualTo(false)
                            .example());
        }

        return count;
    }

    public BaseAnswer valid(String sessionId, String validCode,String account) {
        captchaService.valid(sessionId, validCode,account);
        return new BaseAnswer();
    }

    /**
     * 获取主合作伙伴下的从用户id
     *
     * @param userId
     * @return
     */
    public BaseAnswer<List<String>> getDownUserId(String userId) {
        List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(userId).example());
        BaseAnswer<List<String>> baseAnswer = new BaseAnswer<>();
        List<String> downUserId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
            downUserId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
      /*      UserPartnerExample userPartnerExample = new UserPartnerExample();
            UserPartnerExample.Criteria criteria = userPartnerExample.createCriteria();
            criteria.andUserIdIn(downUserId).andIsCancelEqualTo(false).andIsLogoffEqualTo(false);
            List<UserPartner> userPartners = userPartnerMapper.selectByExample(userPartnerExample);
            if (CollectionUtils.isNotEmpty(userPartners)){
                downUserId = userPartners.stream().map(UserPartner::getUserId).collect(Collectors.toList());
            }else {
                downUserId =new ArrayList<>();
            }*/
            baseAnswer.setData(downUserId);
        }
        return baseAnswer;
    }

    /**
     * 通过主合作伙伴id查询从合作伙伴信息
     *
     * @param userId
     * @return
     */
    public BaseAnswer<List<UserFollowListVO>> getFollowUserByMainUserId(String userId) {
        List<PrimaryDownRelation> primaryDownRelations = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andPrimaryUserIdEqualTo(userId).example());
        BaseAnswer<List<UserFollowListVO>> baseAnswer = new BaseAnswer<>();
        List<String> followId;
        List<UserFollowListVO> data4Users = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(primaryDownRelations)) {
            followId = primaryDownRelations.stream().map(PrimaryDownRelation::getDownUserId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(followId)) {
                for (String id : followId) {
                    UserPartner user = userPartnerMapper.selectByPrimaryKey(id);
                    Boolean isCancel = user.getIsCancel();
                    Boolean isLogoff = user.getIsLogoff();
                    if (isCancel && isLogoff){
                        continue;
                    }
                    //查询角色业务类型
                    RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                    UserFollowListVO userFollowListVO = new UserFollowListVO();
                    BeanUtils.copyProperties(user, userFollowListVO);
                    userFollowListVO.setRoleType(role.getRoleType());
                    userFollowListVO.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                    data4Users.add(userFollowListVO);
                }
            }
        }
        baseAnswer.setData(data4Users);
        return baseAnswer;
    }

    /**
     * 获取商品配置时使用的合作伙伴从账号
     * @param followUserParam
     * @return
     */
    public BaseAnswer<List<UserFollowListVO>> getFollowUserByMainUser(FollowUserParam followUserParam) {
        BaseAnswer baseAnswer = new BaseAnswer();
        String phone = followUserParam.getPhone();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(phone)){
            phone = IOTEncodeUtils.encryptIOTMessage(phone, encryptKey);
            followUserParam.setPhone(phone);
        }

        List<UserFollowListVO> userFollowListVOList = userPartnerMapperExt.listDownUser(followUserParam);
        if (CollectionUtils.isNotEmpty(userFollowListVOList)){
            userFollowListVOList.stream().forEach(userFollowListVO -> {
                String currentPhone = userFollowListVO.getPhone();
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(currentPhone)){
                    userFollowListVO.setPhone(IOTEncodeUtils.decryptIOTMessage(currentPhone, encryptKey));
                }
            });
        }

        baseAnswer.setData(userFollowListVOList);
        return baseAnswer;
    }

    /**
     * 通过从用户id查询主合作伙伴
     *
     * @param userId
     * @return
     */
    public BaseAnswer<Data4User> getPrimaryUserPhones(String userId) {
        BaseAnswer<Data4User> baseAnswer = new BaseAnswer<>();
        List<PrimaryDownRelation> primaryDownRelationArr = primaryDownRelationMapper.selectByExample(new PrimaryDownRelationExample().createCriteria().andDownUserIdEqualTo(userId).example());
        if (ObjectUtils.isNotEmpty(primaryDownRelationArr)) {
            PrimaryDownRelation primaryDownRelation = primaryDownRelationArr.get(0);
            if (Optional.ofNullable(primaryDownRelation).isPresent()) {
                BaseAnswer<Data4User> data4UserBaseAnswer = partnerInfo(primaryDownRelation.getPrimaryUserId(), true);
                return data4UserBaseAnswer;
            }
        }
        return baseAnswer;
    }

    /**
     * 新增原来单个的合作伙伴同步一个相同的主账号（个人调用）
     *
     * @param roleId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer updateDownPartnerOne(String roleId) {
        //查询的单个合作用户
        List<UserDO> userList = userMapperExt.getPartnerUserByRoleId(roleId);
        log.info("查询的用户：{}", userList);
        for (UserDO user : userList) {
            String oneId = BaseServiceUtils.getId();
            String userId = user.getUserId();
            User userNew = new User();
            userNew.setUserId(oneId);
            userNew.setName(user.getName());
            userNew.setPartnerName(user.getPartnerName());
            userNew.setEmail(user.getEmail());
            userNew.setCompany(user.getCompany());
            userNew.setPhone(user.getPhone());
            userNew.setRoleId("907998143042379778");
            userNew.setRemark("调用接口生成");
            userNew.setCreateTime(new Date());
            userNew.setUpdateTime(new Date());
            userNew.setCreator("admin");
            userNew.setIsPrimary(true);
            userNew.setIsSend(true);
            userNew.setIsCancel(false);
            userMapper.insert(userNew);
            //新增主从关系表
            PrimaryDownRelation primaryDownRelation = new PrimaryDownRelation();
            primaryDownRelation.setPrimaryDownId(BaseServiceUtils.getId());
            primaryDownRelation.setPrimaryUserId(oneId);
            primaryDownRelation.setDownUserId(user.getUserId());
            primaryDownRelationMapper.insert(primaryDownRelation);
        }

        return new BaseAnswer();
    }


    /**
     * 新增多个一样的合作伙伴的主合作伙伴
     *
     * @param addUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer insertMultiPartnerLord(Request4AddUser addUser) {
        List<UserPartner> userList = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                .andPartnerNameEqualTo(addUser.getPartnerName()).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
        if (CollectionUtils.isEmpty(userList)) {
            throw new BusinessException(StatusConstant.PARTNER_NAME_ERROR);
        }
        //新增主用户
        String oneId = BaseServiceUtils.getId();
        UserPartner userNew = new UserPartner();
        userNew.setUserId(oneId);
        userNew.setName(addUser.getUserName());
        userNew.setPartnerName(addUser.getPartnerName());
        userNew.setEmail(addUser.getEmail());
//        userNew.setCompany(addUser.getCompany());
        userNew.setPhone(IOTEncodeUtils.encryptIOTMessage(addUser.getPhone(), encryptKey));
        userNew.setRoleId("907998143042379778");
        userNew.setRemark("调用接口生成");
        userNew.setCreateTime(new Date());
        userNew.setUpdateTime(new Date());
        userNew.setCreator("admin");
        userNew.setIsPrimary(true);
        userNew.setIsSend(true);
        userNew.setIsCancel(false);
        userPartnerMapper.insert(userNew);
        //新增主从关系表
        for (UserPartner user : userList) {
            PrimaryDownRelation primaryDownRelation = new PrimaryDownRelation();
            primaryDownRelation.setPrimaryDownId(BaseServiceUtils.getId());
            primaryDownRelation.setPrimaryUserId(oneId);
            primaryDownRelation.setDownUserId(user.getUserId());
            primaryDownRelationMapper.insert(primaryDownRelation);
        }

        return new BaseAnswer();
    }


    /**
     * 生产token，并且放入到redis
     *
     * @param
     * @return
     */
    private String generateToken(User user) {
        String userId = user.getUserId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);

        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(user.getIsAdmin() == null ? false : user.getIsAdmin());
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setIsSend(user.getIsSend());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
        loginIfo4Redis.setPhone(org.apache.commons.lang3.StringUtils.defaultIfBlank(phone, "空白手机号"));
        loginIfo4Redis.setUserName(user.getName());
        if (ObjectUtils.isNotEmpty(user.getIsPrimary())) {
            loginIfo4Redis.setIsPrimary(user.getIsPrimary());
        }
        // 封装loginIfo4Redis
        // 查询角色信息
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
        RoleAuthInfo roleAuthInfo = answer.getData();
        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        loginIfo4Redis.setRoleName(roleAuthInfo.getName());
        // 设置退换货地址flag标识符
        Boolean isReAddrNull = false;
        // 用户类型为合作伙伴时，校验退换货地址是否为空
        if (ObjectUtils.isNotEmpty(user.getRoleId())) {
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            loginIfo4Redis.setSystem(role.getSystem());
            if (PARTNER_ROLE.equals(role.getRoleType())) {
                // 查询退货地址是否为空
                PartnerAddressExample example = new PartnerAddressExample();
                example.createCriteria().andPartnerIdEqualTo(user.getUserId());
                List<PartnerAddress> addressList = addressMapper.selectByExample(example);
                if (ObjectUtils.isEmpty(addressList)) {
                    isReAddrNull = true;
                }
            }
        }
        loginIfo4Redis.setIsReAddrNull(isReAddrNull);
//        BaseAnswer<Data4User> userBaseAnswer = getPrimaryUserPhones(userId);
//        if (userBaseAnswer != null && userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && userBaseAnswer.getData() != null) {
//            loginIfo4Redis.setPartnerLordUserId(userBaseAnswer.getData().getUserId());
//            loginIfo4Redis.setIsPartner(true);
//        } else {
//            BaseAnswer<List<String>> idBaseAnswer = getDownUserId(userId);
//            if (idBaseAnswer != null && idBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && !ObjectUtils.isEmpty(idBaseAnswer.getData())) {
//                loginIfo4Redis.setIsPartner(true);
//            }
//        }
        log.info("缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);

        return token;
    }

    /**
     * 生产token，并且放入到redis
     * 4A用 多个字段
     *
     * @param
     * @return
     */
    private String generateToken(User user, String mainAcctIdText) {
        String userId = user.getUserId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注Î销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);

        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setMainAcctId(mainAcctIdText);
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(user.getIsAdmin() == null ? false : user.getIsAdmin());
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setIsSend(user.getIsSend());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
        loginIfo4Redis.setPhone(org.apache.commons.lang3.StringUtils.defaultIfBlank(phone, "空白手机号"));
        loginIfo4Redis.setUserName(user.getName());
        if (ObjectUtils.isNotEmpty(user.getIsPrimary())) {
            loginIfo4Redis.setIsPrimary(user.getIsPrimary());
        }
        // 封装loginIfo4Redis
        // 查询角色信息
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
        RoleAuthInfo roleAuthInfo = answer.getData();
        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        loginIfo4Redis.setRoleName(roleAuthInfo.getName());
        // 设置退换货地址flag标识符
        Boolean isReAddrNull = false;
        // 用户类型为合作伙伴时，校验退换货地址是否为空
        if (ObjectUtils.isNotEmpty(user.getRoleId())) {
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            loginIfo4Redis.setSystem(role.getSystem());
            if (PARTNER_ROLE.equals(role.getRoleType())) {
                // 查询退货地址是否为空
                PartnerAddressExample example = new PartnerAddressExample();
                example.createCriteria().andPartnerIdEqualTo(user.getUserId());
                List<PartnerAddress> addressList = addressMapper.selectByExample(example);
                if (ObjectUtils.isEmpty(addressList)) {
                    isReAddrNull = true;
                }
            }
        }
        loginIfo4Redis.setIsReAddrNull(isReAddrNull);
//        BaseAnswer<Data4User> userBaseAnswer = getPrimaryUserPhones(userId);
//        if (userBaseAnswer != null && userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && userBaseAnswer.getData() != null) {
//            loginIfo4Redis.setPartnerLordUserId(userBaseAnswer.getData().getUserId());
//            loginIfo4Redis.setIsPartner(true);
//        } else {
//            BaseAnswer<List<String>> idBaseAnswer = getDownUserId(userId);
//            if (idBaseAnswer != null && idBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && !ObjectUtils.isEmpty(idBaseAnswer.getData())) {
//                loginIfo4Redis.setIsPartner(true);
//            }
//        }
        log.info("缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);

        return token;
    }
    /**
     * 生产token，并且放入到redis
     *
     * @param
     * @return
     */
    private String generateToken(UserPartner user) {
        String userId = user.getUserId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);

        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(false);
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setIsSend(user.getIsSend());
        loginIfo4Redis.setIsExternal(user.getIsExternal());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
        loginIfo4Redis.setPhone(org.apache.commons.lang3.StringUtils.defaultIfBlank(phone, "空白手机号"));
        loginIfo4Redis.setUserName(user.getName());
        loginIfo4Redis.setPartnerName(user.getPartnerName());
        loginIfo4Redis.setCompanyType(user.getCompanyType());
        if (ObjectUtils.isNotEmpty(user.getIsPrimary())) {
            loginIfo4Redis.setIsPrimary(user.getIsPrimary());
        }
        // 封装loginIfo4Redis
        // 查询角色信息
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
        RoleAuthInfo roleAuthInfo = answer.getData();
        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        loginIfo4Redis.setRoleName(roleAuthInfo.getName());
        // 设置退换货地址flag标识符
        Boolean isReAddrNull = false;
        // 用户类型为合作伙伴时，校验退换货地址是否为空
        if (ObjectUtils.isNotEmpty(user.getRoleId())) {
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            loginIfo4Redis.setSystem(role.getSystem());
            if (PARTNER_ROLE.equals(role.getRoleType())) {
                // 查询退货地址是否为空
                PartnerAddressExample example = new PartnerAddressExample();
                example.createCriteria().andPartnerIdEqualTo(user.getUserId());
                List<PartnerAddress> addressList = addressMapper.selectByExample(example);
                if (ObjectUtils.isEmpty(addressList)) {
                    isReAddrNull = true;
                }
            }

            //合作伙伴现在一种4种角色，其中合作伙伴主，合作伙伴从这两种角色的isPartner值为true，表示其他业务视作合作伙伴；
            // 合作伙伴保理以及售后安装人员两种角色isPartner值为false，其他业务不视为合作伙伴
            loginIfo4Redis.setIsPartner(PARTNER_ROLE.equals(role.getRoleType())
                    || PARTNER_LORD_ROLE.equals(role.getRoleType()));
        }
        loginIfo4Redis.setIsReAddrNull(isReAddrNull);
//        BaseAnswer<Data4User> userBaseAnswer = getPrimaryUserPhones(userId);
//        if (userBaseAnswer != null && userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && userBaseAnswer.getData() != null) {
//            loginIfo4Redis.setPartnerLordUserId(userBaseAnswer.getData().getUserId());
//            loginIfo4Redis.setIsPartner(true);
//        } else {
//            BaseAnswer<List<String>> idBaseAnswer = getDownUserId(userId);
//            if (idBaseAnswer != null && idBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && !ObjectUtils.isEmpty(idBaseAnswer.getData())) {
//                loginIfo4Redis.setIsPartner(true);
//            }
//        }

        log.info("缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);

        return token;
    }




    /**
     * 生产省业管token，并且放入到redis
     *
     * @param
     * @return
     */
    private String generateToken(UserProvinceBusiness user) {
        String userId = user.getUserId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);

        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(false);
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setIsSend(user.getIsSend());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(),encryptKey);
        loginIfo4Redis.setPhone(org.apache.commons.lang3.StringUtils.defaultIfBlank(phone, "空白手机号"));
        loginIfo4Redis.setUserName(user.getName());
        loginIfo4Redis.setBeId(user.getBeId());
        loginIfo4Redis.setLocation(user.getLocation());
        /*if (ObjectUtils.isNotEmpty(user.getIsPrimary())) {
            loginIfo4Redis.setIsPrimary(user.getIsPrimary());
        }*/
        // 封装loginIfo4Redis
        // 查询角色信息
        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
        RoleAuthInfo roleAuthInfo = answer.getData();
        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        loginIfo4Redis.setRoleName(roleAuthInfo.getName());
        // 设置退换货地址flag标识符
        Boolean isReAddrNull = false;
        // 用户类型为合作伙伴时，校验退换货地址是否为空
        if (ObjectUtils.isNotEmpty(user.getRoleId())) {
            RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
            loginIfo4Redis.setSystem(role.getSystem());
            if (PARTNER_ROLE.equals(role.getRoleType())) {
                // 查询退货地址是否为空
                PartnerAddressExample example = new PartnerAddressExample();
                example.createCriteria().andPartnerIdEqualTo(user.getUserId());
                List<PartnerAddress> addressList = addressMapper.selectByExample(example);
                if (ObjectUtils.isEmpty(addressList)) {
                    isReAddrNull = true;
                }
            }
        }
        loginIfo4Redis.setIsReAddrNull(isReAddrNull);
//        BaseAnswer<Data4User> userBaseAnswer = getPrimaryUserPhones(userId);
//        if (userBaseAnswer != null && userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && userBaseAnswer.getData() != null) {
//            loginIfo4Redis.setPartnerLordUserId(userBaseAnswer.getData().getUserId());
//            loginIfo4Redis.setIsPartner(true);
//        } else {
//            BaseAnswer<List<String>> idBaseAnswer = getDownUserId(userId);
//            if (idBaseAnswer != null && idBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) && !ObjectUtils.isEmpty(idBaseAnswer.getData())) {
//                loginIfo4Redis.setIsPartner(true);
//            }
//        }
        log.info("缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);

        return token;
    }

    private List<Data4User> ConvertToDataUser(List<User> users) {
        List<Data4User> data4Users = null;
        if (ObjectUtils.isNotEmpty(users)) {
            data4Users = new ArrayList<>();
            for (User user : users) {
                Data4User data4User = new Data4User();
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setUserId(user.getUserId());
                data4User.setName(user.getName());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                data4User.setIsAdmin(user.getIsAdmin());
                data4User.setRemark(user.getRemark());
                data4User.setRoleId(user.getRoleId());
                data4User.setRoleName(role.getName());
                data4User.setRoleType(role.getRoleType());
                data4User.setSystem(role.getSystem());
                data4User.setIsCancel(user.getIsAdmin());
                data4User.setEmail(user.getEmail());
                data4User.setCompany(user.getCompany());
                data4User.setPartnerName(user.getPartnerName());
                data4User.setCreator(user.getCreator());
                data4User.setCreateTime(user.getCreateTime());
                data4User.setUpdateTime(user.getUpdateTime());
                data4User.setIsPrimary(user.getIsPrimary());
                data4User.setIsSend(user.getIsSend());
                data4User.setUserType(user.getUserType());
                data4Users.add(data4User);
            }
        }
        return data4Users;
    }

    private List<Data4User> partnerToDataUser(List<UserPartner> users) {
        List<Data4User> data4Users = null;
        if (ObjectUtils.isNotEmpty(users)) {
            data4Users = new ArrayList<>();
            for (UserPartner user : users) {
                Data4User data4User = new Data4User();
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setUserId(user.getUserId());
                data4User.setName(user.getName());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                data4User.setIsAdmin(false);
                data4User.setRemark(user.getRemark());
                data4User.setRoleId(user.getRoleId());
                data4User.setRoleName(role.getName());
                data4User.setRoleType(role.getRoleType());
                data4User.setSystem(role.getSystem());
                data4User.setIsCancel(user.getIsCancel());
                data4User.setEmail(user.getEmail());
                data4User.setCompany(null);
                data4User.setPartnerName(user.getPartnerName());
                data4User.setCreator(user.getCreator());
                data4User.setCreateTime(user.getCreateTime());
                data4User.setUpdateTime(user.getUpdateTime());
                data4User.setIsPrimary(user.getIsPrimary());
                data4User.setIsSend(user.getIsSend());
                data4User.setUserType(user.getUserType());
                data4User.setCjStatus(user.getCjStatus());
                data4User.setCjApplyTime(user.getCjApplyTime());
                data4User.setCjAdvice(user.getCjAdvice());
                data4User.setUnifiedCode(user.getUnifiedCode());
                data4Users.add(data4User);
            }
        }
        return data4Users;
    }

    private List<Data4User> screenerToDataUser(List<UserScreen> users) {
        List<Data4User> data4Users = null;
        if (ObjectUtils.isNotEmpty(users)) {
            data4Users = new ArrayList<>();
            for (UserScreen user : users) {
                Data4User data4User = new Data4User();
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setUserId(user.getId());
                data4User.setName(user.getName());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                data4User.setIsAdmin(false);
                data4User.setRoleId(user.getRoleId());
                data4User.setRoleName(role.getName());
                data4User.setRoleType(role.getRoleType());
                data4User.setSystem(role.getSystem());
                data4User.setIsCancel(user.getIsCancel());
                data4User.setEmail(user.getEmail());
                data4User.setCompany(null);
                data4User.setCreator(user.getCreator());
                data4User.setCreateTime(user.getCreateTime());
                data4User.setUpdateTime(user.getUpdateTime());
                data4User.setUserType(user.getUserType());
                data4User.setUserFrom(user.getUserFrom());
                data4User.setIotType(user.getIotType());
                data4User.setUnifiedStatus(user.getUnifiedStatus());
                data4User.setUnifiedStatus(user.getUnifiedStatus());
                data4Users.add(data4User);
            }
        }
        return data4Users;
    }

    private List<Data4User> provinceBusinessToDataUser(List<UserProvinceBusiness> users) {
        List<Data4User> data4Users = null;
        if (ObjectUtils.isNotEmpty(users)) {
            data4Users = new ArrayList<>();
            for (UserProvinceBusiness user : users) {
                Data4User data4User = new Data4User();
                RoleInfoDTO role = roleInfoService.selectById(user.getRoleId());
                data4User.setUserId(user.getUserId());
                data4User.setName(user.getName());
                data4User.setPhone(IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey));
                data4User.setIsAdmin(false);
                data4User.setRoleId(user.getRoleId());
                data4User.setRoleName(role.getName());
                data4User.setRoleType(role.getRoleType());
                data4User.setSystem(role.getSystem());
                data4User.setIsCancel(user.getIsCancel());
                data4User.setEmail(user.getEmail());
                data4User.setCompany(null);
                data4User.setCreator(user.getCreator());
                data4User.setCreateTime(user.getCreateTime());
                data4User.setUpdateTime(user.getUpdateTime());
                data4User.setUserType(user.getUserType());
                data4Users.add(data4User);
            }
        }
        return data4Users;
    }


    /**
     * @param currRoleType: 当前用户角色类型
     * @param targetAuths:  被创建用户角色类型
     * @Description: 校验当前用户-角色，是否有创建此用户的权限
     * @return: void
     * @Author: zyj
     */
//    private void checkAuthRight(String currRoleType, String targetRoleType, List<Auth> targetAuths) {
//        if (ObjectUtils.isNotEmpty(currRoleType) && ObjectUtils.isNotEmpty(targetRoleType)
//                && ObjectUtils.isNotEmpty(targetAuths)) {
//            if (ADMIN_ROLE.equals(currRoleType)) {
//
//                // 对应运营管理员角色只能创建合作伙伴, 不能有角色、运营管理员管理等权限
//            } else if (OPERATOR_ROLE.equals(currRoleType)) {
//                for (Auth auth : targetAuths) {
//                    if (USER_ROLE_AUTH.equals(auth.getAuthCode()) || USER_OPERATOR_AUTH.equals(auth.getAuthCode())) {
//                        throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                    }
//                }
//                //主合作伙伴可创建从用户
//            } else if (PARTNER_LORD_ROLE.equals(currRoleType)) {
//                for (Auth auth : targetAuths) {
//                    if (USER_ROLE_AUTH.equals(auth.getAuthCode()) || USER_OPERATOR_AUTH.equals(auth.getAuthCode())
//                            || USER_PARTNER_AUTH.equals(auth.getAuthCode()) || MANAGER_STAFF_AUTH.equals(auth.getAuthCode())) {
//                        throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                    }
//                }
//                // 从合作伙伴角色不能创建用户
//            } else if (PARTNER_ROLE.equals(currRoleType)) {
//                for (Auth auth : targetAuths) {
//                    if (USER_ROLE_AUTH.equals(auth.getAuthCode()) || USER_OPERATOR_AUTH.equals(auth.getAuthCode())
//                            || USER_PARTNER_AUTH.equals(auth.getAuthCode()) || USER_PARTNER_LORD_AUTH.equals(auth.getAuthCode())
//                            || MANAGER_STAFF_AUTH.equals(auth.getAuthCode())) {
//                        throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                    }
//                }
//                //客服管理员不能创建用户
//            } else if (MANAGER_STAFF_ROLE.equals(currRoleType)) {
//                for (Auth auth : targetAuths) {
//                    if (USER_ROLE_AUTH.equals(auth.getAuthCode()) || USER_OPERATOR_AUTH.equals(auth.getAuthCode())
//                            || USER_PARTNER_AUTH.equals(auth.getAuthCode()) || USER_PARTNER_LORD_AUTH.equals(auth.getAuthCode())
//                            || MANAGER_STAFF_AUTH.equals(auth.getAuthCode())) {
//                        throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                    }
//                }
//            }
//
//        }
//    }

    /**
     * @param checkRoleType:  校验角色类型
     * @param checkAuthCode:  校验权限码
     * @param targetRoleType: 目标用户角色类型
     * @param authCodes:      目标用户角色权限集合
     * @Description: 校验操作用户权限
     * @return: void
     * @Author: zyj
     */
//    private void checkDeleteAuth(String checkRoleType, String targetRoleType, String checkAuthCode, List<AuthCode> authCodes) {
//        if (checkRoleType.equals(targetRoleType)) {
//            boolean isOperAuth = false;
//            for (AuthCode authCode : authCodes) {
//                if (checkAuthCode.equals(authCode.getCode())) {
//                    isOperAuth = true;
//                }
//            }
//            if (!isOperAuth) {
//                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
//            }
//        }
//    }

    /**
     * 验证当前用户是否拥有权限
     *
     * @param checkAuthCode
     * @param authCodes
     * @param isOper
     * @return
     */
    private Boolean checkDeleteAuths(String checkAuthCode, List<AuthCode> authCodes, Boolean isOper) {
        if (isOper) {
            return isOper;
        }
        boolean isOperAuth = false;
        for (AuthCode authCode : authCodes) {
            if (checkAuthCode.equals(authCode.getCode())) {
                isOperAuth = true;
                break;
            }
            if (CollectionUtils.isNotEmpty(authCode.getChildren())) {
                isOperAuth = checkDeleteAuths(checkAuthCode, authCode.getChildren(), isOperAuth);
                if (isOperAuth) {
                    break;
                }
            }
        }
        return isOperAuth;

    }

    private boolean checkHasRoleManage(String currRoleId, String targetRoleId) {
        RoleManageListItemVO roleManageListItemVO = roleInfoService.getRoleManageByRole(currRoleId);
        return roleManageListItemVO != null && CollectionUtils.isNotEmpty(roleManageListItemVO.getManageRoles())
                && roleManageListItemVO.getManageRoles().stream().anyMatch(item -> item.getRoleId().equals(targetRoleId));
    }

    /**
     * 根据当前用户角色和被创建用户角色类型判断是否能够创建
     *
     * @param currRoleId
     * @param targetRoleId
     */
    private void checkAuthRight(String currRoleId, String targetRoleId) {
        if (ObjectUtils.isNotEmpty(currRoleId) && ObjectUtils.isNotEmpty(currRoleId)) {
            if (!checkHasRoleManage(currRoleId, targetRoleId)) {
                throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
            }
//            if (ADMIN_ROLE.equals(currRoleType)) {
//
//                // 对应运营管理员角色只能创建合作伙伴, 不能有角色、运营管理员管理等权限
//            } else if (OPERATOR_ROLE.equals(currRoleType)) {
//                if (ADMIN_ROLE.equals(targetRoleType) || OPERATOR_ROLE.equals(targetRoleType)) {
//                    throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                }
//                //主合作伙伴可创建从用户
//            } else if (PARTNER_LORD_ROLE.equals(currRoleType)) {
//                if (ADMIN_ROLE.equals(targetRoleType) || OPERATOR_ROLE.equals(targetRoleType)
//                        || PARTNER_LORD_ROLE.equals(targetRoleType) || MANAGER_STAFF_ROLE.equals(targetRoleType)) {
//                    throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                }
//                // 从合作伙伴角色不能创建用户
//            } else if (PARTNER_ROLE.equals(currRoleType)) {
//                if (ADMIN_ROLE.equals(targetRoleType) || OPERATOR_ROLE.equals(targetRoleType)
//                        || PARTNER_LORD_ROLE.equals(targetRoleType) || MANAGER_STAFF_ROLE.equals(targetRoleType)
//                        || PARTNER_ROLE.equals(targetRoleType)) {
//                    throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                }
//                //客服管理员不能创建用户
//            } else if (MANAGER_STAFF_ROLE.equals(currRoleType)) {
//                if (ADMIN_ROLE.equals(targetRoleType) || OPERATOR_ROLE.equals(targetRoleType)
//                        || PARTNER_LORD_ROLE.equals(targetRoleType) || MANAGER_STAFF_ROLE.equals(targetRoleType)
//                        || PARTNER_ROLE.equals(targetRoleType)) {
//                    throw new BusinessException(StatusConstant.NO_PERMIT_CREATE);
//                }
//            }

        }
    }


    public BaseAnswer backLogin(Request4Login request4Login) {
        //校验用户名是否存在
/*        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                        .andIsCancelEqualTo(false).example()).stream()
                .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .collect(Collectors.toList());
        List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());*/
        //停用 没注销
        List<User> usersIsCancel = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                        .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .filter(user -> {
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
                    return roleInfoDTO.getIsLoginOs();
                })
                .collect(Collectors.toList());
        //合作伙伴用户
        List<UserPartner> partnersCancel = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream().filter(user -> {

            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());
        //省业管员用户
        List<UserProvinceBusiness> userProvinceBusinessesCancel = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example()).stream().filter(user -> {
            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());

        List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                .filter(user -> user.getUnifiedStatus() == null || user.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                .filter(user -> {
                    RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
                    return roleInfoDTO.getIsLoginOs();
                })
                .collect(Collectors.toList());
        //合作伙伴用户
        List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(false).andIsCancelEqualTo(false).example()).stream().filter(user -> {

            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());


        //省业管员用户
        List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria().andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(request4Login.getUserName(),encryptKey))
                .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream().filter(user -> {
            RoleInfoDTO roleInfoDTO = roleInfoService.selectById(user.getRoleId());
            return roleInfoDTO.getIsLoginOs();
        }).collect(Collectors.toList());
        if ((ObjectUtils.isNotEmpty(usersIsCancel) && ObjectUtils.isEmpty(users))
                || (ObjectUtils.isNotEmpty(partnersCancel) && ObjectUtils.isEmpty(partners))
                ||  (ObjectUtils.isNotEmpty(userProvinceBusinessesCancel) && ObjectUtils.isEmpty(userProvinceBusinesses))) {
            throw new BusinessException(StatusConstant.USER_IS_CANCEL_FALSE);
        }

        if (ObjectUtils.isEmpty(users) && ObjectUtils.isEmpty(partners) && ObjectUtils.isEmpty(userProvinceBusinesses)) {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        String token = "";
        if (ObjectUtils.isNotEmpty(users)) {
            //os系统用户

            //agentManger用户放到最后，登录优先级最低
            users = Stream.concat(users.stream().filter(x->!StringUtils.equals(x.getRoleId(),"907998143042379999")),
                            users.stream().filter(x -> StringUtils.equals(x.getRoleId(),"907998143042379999")))
                    .collect(Collectors.toList());
            User user = users.get(0);
            if ("907921766251245569".equals(user.getRoleId())) {

                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, "-", LogResultEnum.LOG_FAIL.code, "超管不能使用此接口登录!");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "超管不能使用此接口登录!");
            }
            //后台登录接口，密码原文 统一为  "手机号+wlx"进行MD5运算的结果
            String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
            String pwd = MD5Util.md5(phone + "wlw");
            //解密登录密码
            String decodePWD = null;
            try {
                decodePWD = RSAEncrypt.decrypt(request4Login.getPwd(), RSAPrivateKey);
                if (!pwd.equals(decodePWD)) {
                    String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), user.getPhone(), user.getUserId());
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());

                    throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
                }
            } catch (Exception e) {
                String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), user.getPhone(), user.getUserId());
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());

                throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
            }
            token = generateToken(user);

            String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), phone, user.getUserId());
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);
        } else if (ObjectUtils.isNotEmpty(partners)){
            //合作伙伴
            UserPartner user = partners.get(0);
            //后台登录接口，密码原文 统一为  "手机号+wlx"进行MD5运算的结果
            String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
            String pwd = MD5Util.md5(phone + "wlw");
            //解密登录密码
            String decodePWD = null;
            try {
                decodePWD = RSAEncrypt.decrypt(request4Login.getPwd(), RSAPrivateKey);
                if (!pwd.equals(decodePWD)) {
                    String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), user.getPhone(), user.getUserId());
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());
                    throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
                }
            } catch (Exception e) {
                String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), user.getPhone(), user.getUserId());
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PWD_NO_CORRECT.getMessage());
                throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
            }
            token = generateToken(user);

            String content = String.format("姓名=%s,登录账号%s,系统id=%s", user.getName(), user.getPhone(), user.getUserId());
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, user.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);

        }else {
            UserProvinceBusiness userProvinceBusiness = userProvinceBusinesses.get(0);
            //后台登录接口，密码原文 统一为  "手机号+wlx"进行MD5运算的结果
            String phone = IOTEncodeUtils.decryptIOTMessage(userProvinceBusiness.getPhone(),encryptKey);
            String pwd = MD5Util.md5(phone + "wlw");
            //解密登录密码
            String decodePWD = null;
            try {
                decodePWD = RSAEncrypt.decrypt(request4Login.getPwd(), RSAPrivateKey);
                if (!pwd.equals(decodePWD)) {
                    throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
                }
            } catch (Exception e) {
                throw new BusinessException(StatusConstant.PWD_NO_CORRECT);
            }
            token = generateToken(userProvinceBusiness);


            String content = String.format("姓名=%s,登录账号%s,系统id=%s", userProvinceBusiness.getName(),phone, userProvinceBusiness.getUserId());
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code, content, userProvinceBusiness.getUserId());
        }
        return new BaseAnswer().setData(token);
    }


    /**
     * 查询所有主合作伙伴信息
     *
     * @return
     */
    public List<UserPartner> getAllPrimaryUserCooperator(LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType) && !SYSTEM_ADMIN_ROLE.equals(roleType)){
            throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
        }
        UserPartnerExample userExample = new UserPartnerExample().createCriteria().andIsPrimaryEqualTo(true).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example();
        List<UserPartner> users = userPartnerMapper.selectByExample(userExample);
        return users;
    }

    public List<UserPartner> getAllPrimaryUser() {
        UserPartnerExample userExample = new UserPartnerExample().createCriteria().andIsPrimaryEqualTo(true).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example();
        List<UserPartner> users = userPartnerMapper.selectByExample(userExample);
        return users;
    }


    /**
     * 封装用户操作日志信息
     *
     * @param newUser
     * @param oldUser
     * @param operation 用户操作标识：0:新增，1：更新，2：删除
     */
    private void encapsulationOperationLog(Data4User newUser, Data4User oldUser, String operation) {

        String content = "";
        String roleId = newUser.getRoleId();
        RoleInfoDTO role = roleInfoService.selectById(roleId);
        String targetRoleType = role.getRoleType();
        String newUserName = newUser.getName();
        String newUserPhone = newUser.getPhone();
        String partnerName = newUser.getPartnerName();
        String newUserEmail = newUser.getEmail();
        //TODO 操作日志待用户管理功能调整完成之后再调整
        //新增操作
        if ("0".equals(operation)) {
            //运营管理员
            if (targetRoleType.equals(OPERATOR_ROLE) || targetRoleType.equals(MANAGER_STAFF_ROLE) || targetRoleType.equals(POINT_MANAGER)) {
                if (targetRoleType.equals(OPERATOR_ROLE)) {
                    content = "【新建运管账号】\n";
                } else if (targetRoleType.equals(MANAGER_STAFF_ROLE)) {
                    content = "【新建客服账号】\n";
                } else {
                    content = "【新建积分管理员账号】\n";
                }
                content = content.concat("姓名:").concat(newUserName).concat("\n");
                content = content.concat("联系电话:").concat(DesensitizationUtils.replaceWithStar(newUserPhone));
            } else if (targetRoleType.equals(PARTNER_LORD_ROLE)) {
                content = "【新建合作伙伴主账号】\n";
                content = content.concat("合作伙伴名称:").concat(partnerName).concat("\n");
                content = content.concat("合作伙伴联系人:").concat(newUserName).concat("\n");
                content = content.concat("联系电话:").concat(DesensitizationUtils.replaceWithStar(newUserPhone)).concat("\n");
                content = content.concat("邮箱:").concat(newUserEmail);
            } else if (targetRoleType.equals(PARTNER_ROLE)) {
                content = "【新建合作伙伴从账号】\n";
                content = content.concat("所属合作伙伴名称:").concat(partnerName).concat("\n");
                //查询从合作伙伴主账号
                UserExample example = new UserExample().createCriteria().andIsPrimaryEqualTo(true).andPartnerNameEqualTo(partnerName)
                        .andIsCancelEqualTo(false).example();
                BaseAnswer<Data4User> answer = getPrimaryUserPhones(newUser.getUserId());
                if (ObjectUtils.isNotEmpty(answer) && ObjectUtils.isNotEmpty(answer.getData())) {
                    content = content.concat("所属合作伙伴主账号:").concat(answer.getData().getName()).concat("\n");
                }
                content = content.concat("联系人:").concat(newUserName).concat("\n");
                content = content.concat("联系电话:").concat(DesensitizationUtils.replaceWithStar(newUserPhone)).concat("\n");
                content = content.concat("邮箱:").concat(newUserEmail);
            }
            log.info("新增用户日志记录content：{}", content);
        } else if ("1".equals(operation)) {
            //更新
            String oldUserName = oldUser.getName();
            String oldUserPhone = oldUser.getPhone();
            String oldUserEmail = oldUser.getEmail();
            //运营管理员
            if (targetRoleType.equals(OPERATOR_ROLE) || targetRoleType.equals(MANAGER_STAFF_ROLE) || targetRoleType.equals(POINT_MANAGER)) {
                if (targetRoleType.equals(OPERATOR_ROLE)) {
                    content = "【修改运管账号】\n";
                } else if (targetRoleType.equals(MANAGER_STAFF_ROLE)) {
                    content = "【修改客服账号】\n";
                } else {
                    content = "【修改积分管理员账号】\n";
                }
                if (!newUserName.equals(oldUserName)) {
                    content = content.concat("姓名由:").concat(oldUserName).concat("修改为:").concat(newUserName).concat("\n");
                }
                if (!newUserPhone.equals(oldUserPhone)) {
                    content = content.concat("联系电话由:").concat(DesensitizationUtils.replaceWithStar(oldUserPhone)).concat("修改为:").concat(DesensitizationUtils.replaceWithStar(newUserPhone)).concat("\n");
                }

                if (!newUserEmail.equals(oldUserEmail)) {
                    content = content.concat("邮箱由:").concat(oldUserEmail).concat("修改为:").concat(newUserEmail);
                }
            } else if (targetRoleType.equals(PARTNER_LORD_ROLE) || PARTNER_ROLE.equals(targetRoleType)) {
                if (targetRoleType.equals(PARTNER_LORD_ROLE)) {
                    content = "【修改合作伙伴主账号】\n";
                } else {
                    content = "【修改合作伙伴从账号】\n";
                }

                //查询从合作伙伴主账号
                if (!newUserName.equals(oldUserName)) {
                    content = content.concat("联系人由:").concat(oldUserName).concat("修改为:").concat(newUserName).concat("\n");
                }
                if (!newUserPhone.equals(oldUserPhone)) {
                    content = content.concat("联系电话由:").concat(DesensitizationUtils.replaceWithStar(oldUserPhone)).concat("修改为:").concat(DesensitizationUtils.replaceWithStar(newUserPhone)).concat("\n");
                }
                if (!newUserEmail.equals(oldUserEmail)) {
                    content = content.concat("邮箱由:").concat(oldUserEmail).concat("修改为:").concat(newUserEmail);
                }

            }
            log.info("编辑用户日志记录content：{}", content);
        } else if ("2".equals(operation)) {
            //删除
            if (targetRoleType.equals(OPERATOR_ROLE) || targetRoleType.equals(MANAGER_STAFF_ROLE) || targetRoleType.equals(POINT_MANAGER)) {
                if (targetRoleType.equals(OPERATOR_ROLE)) {
                    content = "【注销运管账号】\n";
                } else if (targetRoleType.equals(MANAGER_STAFF_ROLE)) {
                    content = "【注销客服账号】\n";
                } else {
                    content = "【注销积分管理员账号】\n";
                }
                content = content.concat("姓名:").concat(newUserName).concat("\n");
                content = content.concat("联系电话:").concat(DesensitizationUtils.replaceWithStar(newUserPhone));

            } else if (targetRoleType.equals(PARTNER_LORD_ROLE) || PARTNER_ROLE.equals(targetRoleType)) {
                if (targetRoleType.equals(PARTNER_LORD_ROLE)) {
                    content = "【注销合作伙伴主账号】\n";
                    content = content.concat("合作伙伴名称:").concat(partnerName).concat("\n");
                } else {
                    content = "【注销合作伙伴从账号】\n";
                    content = content.concat("所属合作伙伴名称:").concat(partnerName).concat("\n");
                    UserExample example = new UserExample().createCriteria().andIsPrimaryEqualTo(true)
                            .andPartnerNameEqualTo(newUser.getPartnerName()).andIsCancelEqualTo(false).example();
                    List<User> users = userMapper.selectByExample(example).stream()
                            .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                            .collect(Collectors.toList());
                    User primaryUser = null;
                    if (ObjectUtils.isNotEmpty(users)) {
                        primaryUser = users.get(0);
                        content = content.concat("所属合作伙伴主账号:").concat(primaryUser.getName()).concat("\n");
                    }
                }

                content = content.concat("联系人:").concat(newUserName).concat("\n");
                content = content.concat("联系电话:").concat(DesensitizationUtils.replaceWithStar(newUserPhone)).concat("\n");
                content = content.concat("邮箱:").concat(newUserEmail);
            }
            log.info("删除用户日志记录content：{}", content);
        }
//        if (StringUtils.isNotBlank(content)) {
//            if (targetRoleType.equals(OPERATOR_ROLE)) {
//                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.OPERATE_ADMIN_MANAGE.code, content);
//            } else if (targetRoleType.equals(MANAGER_STAFF_ROLE)) {
//                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.SUPPORT_ADMIN_MANAGE.code, content);
//            } else if (targetRoleType.equals(POINT_MANAGER)) {
//                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.POINTS_ADMIN_MANAGE.code, content);
//            } else if (targetRoleType.equals(PARTNER_LORD_ROLE)) {
//                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.PARTNER_MASTER_MANAGE.code, content);
//            } else if (PARTNER_ROLE.equals(targetRoleType)) {
//                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.PARTNER_SLAVE_MANAGE.code, content);
//            }
//        }
    }

    public BaseAnswer<List<UserListItemVO>> getUserListAndProductOperation(RequestUserListParam param, LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer<List<UserListItemVO>> baseAnswer = new BaseAnswer<>();
       /* RoleManageListItemVO roleInfos = roleInfoService.getRoleManageByRole(loginIfo4Redis.getRoleId());
        if (CollectionUtils.isEmpty(roleInfos.getManageRoles())) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有用户管理管理权限");
        }
        if (StringUtils.isNotBlank(param.getRoleId())) {
            if (!roleInfos.getManageRoles().stream().anyMatch(role -> role.getRoleId().equals(param.getRoleId()))) {
                throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有所查询角色的管理权限");
            }
        }*/
        String roleType = loginIfo4Redis.getRoleType();
        String userName = loginIfo4Redis.getUserName();
        String roleName = loginIfo4Redis.getRoleName();
        if (!ADMIN_ROLE.equals(roleType) && !"产品经理".equals(roleName) && !"运营支撑管理员".equals(roleName)
                && !"备案管理员".equals(roleName)  && !"产品运营管理员".equals(roleName)
                && !"产品运营负责人".equals(roleName)  && !"配置专员".equals(roleName)
                && !"省业管员".equals(roleName) && !"订单管理员".equals(roleName)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有所查询角色用户的管理权限");
        }

        List<UserListItemVO> userList = new ArrayList<>();

      //  PageHelper.startPage(param.getPageNum(), param.getPageSize());
        //对手机号加密，加密之后进行完全匹配
        param.setPhone(IOTEncodeUtils.encryptIOTMessage(param.getPhone(),encryptKey));
        if (SYSTEM_OS.equals(param.getSystem())) {
            userList = userMapperExt.getUserList(param, null);
        } else if (SYSTEM_SCREEN.equals(param.getSystem())) {
            userList = userScreenMapperExt.getUserList(param, null);
        } else if (SYSTEM_PARTNER.equals(param.getSystem())) {
            if (loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()) {
                //主合作伙伴只能查询自己的同合作伙伴名称的用户信息
                param.setPartnerName(loginIfo4Redis.getPartnerName());
            }
            if(StringUtils.isNotBlank(param.getCjStatus())){
                param.setRoleId(PARTNER_BAOLI_ROLE_ID);
                if(param.getCjStatus().equals("all")){
                    param.setCjStatus(null);
                }
            }
            userList = userPartnerMapperExt.getUserList(param, null);
        }else if(PROVINCE_MANAGEMENT.equals(param.getSystem())){
            userList = userProvinceBusinessMapperExt.getProvinceBusinessUserList(param,null);
        }else {
            //system没传就查询所有的表数据
            List<UserListItemVO>  userOsList = userMapperExt.getUserList(param, null);
           if (CollectionUtils.isNotEmpty(userOsList)){
               userList.addAll(userOsList);
           }

           List<UserListItemVO> userScreenList = userScreenMapperExt.getUserList(param, null);
           if (CollectionUtils.isNotEmpty(userScreenList)){
               userList.addAll(userScreenList);
           }
            if (loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()) {
                //主合作伙伴只能查询自己的同合作伙伴名称的用户信息
                param.setPartnerName(loginIfo4Redis.getPartnerName());
            }
            if(StringUtils.isNotBlank(param.getCjStatus())){
                param.setRoleId(PARTNER_BAOLI_ROLE_ID);
                if(param.getCjStatus().equals("all")){
                    param.setCjStatus(null);
                }
            }
            List<UserListItemVO>  userPartnerList = userPartnerMapperExt.getUserList(param, null);
            if (CollectionUtils.isNotEmpty(userPartnerList)){
                userList.addAll(userScreenList);
            }
            List<UserListItemVO>  userProvinceList = userProvinceBusinessMapperExt.getProvinceBusinessUserList(param,null);
            if (CollectionUtils.isNotEmpty(userProvinceList)){
                userList.addAll(userProvinceList);
            }

        }
        userList.stream().forEach(user->{
            String phone = user.getPhone();
            if (StringUtils.isNotBlank(phone)){
                phone = IOTEncodeUtils.decryptIOTMessage(phone,encryptKey);
                user.setPhone(DesensitizationUtils.replaceWithStar(phone));
            }
        });
      /*  PageInfo<UserListItemVO> dataPage = new PageInfo<>(userList);
        PageData<UserListItemVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setCount(dataPage.getTotal());
        pageData.setData(userList);*/
        baseAnswer.setData(userList);
        return baseAnswer;
    }


    public BaseAnswer<PageData<UserListItemVO>> getUserList(RequestUserListParam param, LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer<PageData<UserListItemVO>> baseAnswer = new BaseAnswer<>();
        RoleManageListItemVO roleInfos = roleInfoService.getRoleManageByRole(loginIfo4Redis.getRoleId());
        if (CollectionUtils.isEmpty(roleInfos.getManageRoles())) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有用户管理管理权限");
        }
        if (StringUtils.isNotBlank(param.getRoleId())) {
            if (!roleInfos.getManageRoles().stream().anyMatch(role -> role.getRoleId().equals(param.getRoleId()))) {
                throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有所查询角色的管理权限");
            }
        }

        List<UserListItemVO> userList = null;
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        //对手机号加密，加密之后进行完全匹配
        param.setPhone(IOTEncodeUtils.encryptIOTMessage(param.getPhone(), encryptKey));
        if (StringUtils.isBlank(param.getSystem()) || SYSTEM_OS.equals(param.getSystem())) {
            userList = userMapperExt.getUserList(param,
                    roleInfos.getManageRoles().stream().map(RoleManageListItemVO::getRoleId).collect(Collectors.toList()));
        } else if (SYSTEM_SCREEN.equals(param.getSystem())) {
            userList = userScreenMapperExt.getUserList(param,
                    roleInfos.getManageRoles().stream().map(RoleManageListItemVO::getRoleId).collect(Collectors.toList()));
        } else if (SYSTEM_PARTNER.equals(param.getSystem())) {
            if (loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()) {
                //主合作伙伴只能查询自己的同合作伙伴名称的用户信息
                param.setPartnerName(loginIfo4Redis.getPartnerName());
            }
            if (StringUtils.isNotBlank(param.getCjStatus())) {
                param.setRoleId(PARTNER_BAOLI_ROLE_ID);
                if (param.getCjStatus().equals("all")) {
                    param.setCjStatus(null);
                }
            }
            userList = userPartnerMapperExt.getUserList(param,
                    roleInfos.getManageRoles().stream().map(RoleManageListItemVO::getRoleId).collect(Collectors.toList()));
        }else if(PROVINCE_MANAGEMENT.equals(param.getSystem())){
            userList = userProvinceBusinessMapperExt.getProvinceBusinessUserList(param,
                    roleInfos.getManageRoles().stream().map(RoleManageListItemVO::getRoleId).collect(Collectors.toList()));
        }
        userList.stream().forEach(user -> {
            String phone = user.getPhone();
            if (StringUtils.isNotBlank(phone)) {
                phone = IOTEncodeUtils.decryptIOTMessage(phone, encryptKey);
                user.setPhone(DesensitizationUtils.replaceWithStar(phone));
            }
        });
        PageInfo<UserListItemVO> dataPage = new PageInfo<>(userList);
        PageData<UserListItemVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setCount(dataPage.getTotal());
        pageData.setData(userList);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    public BaseAnswer<List<Data4User>> getPartnerList(QueryPartnerListParam param) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        UserPartnerExample example = new UserPartnerExample();
        UserPartnerExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(param.getUserId())) {
            criteria.andUserIdEqualTo(param.getUserId());
        }

        if (StringUtils.isNotBlank(param.getName())) {
            criteria.andNameEqualTo(param.getName());
        }

        if (StringUtils.isNotBlank(param.getPhone())) {
            criteria.andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(param.getPhone(), encryptKey));
        }

        if (StringUtils.isNotBlank(param.getPartnerName())) {
            criteria.andPartnerNameEqualTo(param.getPartnerName());
        }

        if (StringUtils.isNotBlank(param.getUserType())) {
            criteria.andUserTypeEqualTo(param.getUserType());
        }

        if (ObjectUtils.isNotNull(param.getIsCancel())) {
            criteria.andIsCancelEqualTo(param.getIsCancel());
        }

        if (ObjectUtils.isNotNull(param.getIsPrimary())) {
            criteria.andIsPrimaryEqualTo(param.getIsPrimary());
        }

        if (ObjectUtils.isNotNull(param.getIsSend())) {
            criteria.andIsSendEqualTo(param.getIsSend());
        }

        if (ObjectUtils.isNotNull(param.getRoleId())) {
            criteria.andRoleIdEqualTo(param.getRoleId());
        }

        List<UserPartner> userList = userPartnerMapper.selectByExample(example);
        List<Data4User> users = userList.stream().map(item -> {
            Data4User user = new Data4User();
            BeanUtils.copyProperties(item, user);
            user.setPhone(IOTEncodeUtils.decryptIOTMessage(item.getPhone(), encryptKey));
            return user;
        }).collect(Collectors.toList());


        return baseAnswer.setData(users);
    }

    public BaseAnswer<List<Data4User>> getPartnerListWeb(QueryPartnerListParam param) {
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        UserPartnerExample example = new UserPartnerExample();
        UserPartnerExample.Criteria criteria = example.createCriteria();
        criteria.andRoleIdEqualTo("1109180984878157833");
        criteria.andIsCancelEqualTo(false);
        criteria.andIsLogoffEqualTo(false);
        if (StringUtils.isNotBlank(param.getKeyWord())) {
            criteria.andNameLike("%"+param.getKeyWord() + "%");

            UserPartnerExample.Criteria criteria1 = example.or();
            criteria1.andRoleIdEqualTo("1109180984878157833");
            criteria1.andIsCancelEqualTo(false);
            criteria1.andIsLogoffEqualTo(false);
            criteria1.andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(param.getKeyWord(), encryptKey));
        }

        List<UserPartner> userList = userPartnerMapper.selectByExample(example);
        List<Data4User> users = userList.stream().map(item -> {
            Data4User user = new Data4User();
            BeanUtils.copyProperties(item, user);
            user.setPhone(IOTEncodeUtils.decryptIOTMessage(item.getPhone(), encryptKey));
            return user;
        }).collect(Collectors.toList());


        return baseAnswer.setData(users);
    }

    /**
     * 根据公司信息获取合作获取主账号信息
     * @param partnerName
     * @return
     */
    public Data4User getUserPartnerPrimaryByPartnerName(String partnerName){
        UserPartnerExample userPartnerExample = new UserPartnerExample();
        userPartnerExample.createCriteria()
                .andRoleIdEqualTo("907998143042379778")
                .andPartnerNameEqualTo(partnerName);
        List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(userPartnerExample);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userPartnerList)){
            UserPartner userPartner = userPartnerList.get(0);
            Data4User data4User = new Data4User();
            BeanUtils.copyProperties(userPartner,data4User);
            return data4User;
        }
        return null;
    }

    /**
     * 根据省份编码获取省公司的合作伙伴主账号信息
     * @param beId
     * @return
     */
    public Data4User getUserPartnerPrimaryByBeId(String beId){
        UserPartnerExample userPartnerExample = new UserPartnerExample();
        userPartnerExample.createCriteria()
                .andRoleIdEqualTo("907998143042379778")
                .andBeIdEqualTo(beId)
                .andCompanyTypeEqualTo("2");
        List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(userPartnerExample);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userPartnerList)){
            UserPartner userPartner = userPartnerList.get(0);
            Data4User data4User = new Data4User();
            BeanUtils.copyProperties(userPartner,data4User);
            return data4User;
        }
        return null;
    }


    /**
     * 查询省业中心用户信息
     * @param roleId
     * @param beIds
     * @param locations
     * @return
     */
    public BaseAnswer<List<Data4User>> getUserProvinceBusiness(String roleId,List<String> beIds,List<String> locations){
        BaseAnswer<List<Data4User>> baseAnswer = new BaseAnswer<>();
        if (org.apache.commons.lang3.StringUtils.isEmpty(roleId)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "角色id不能为空");
        }
     /*   if (CollectionUtils.isNotEmpty(beIds) && CollectionUtils.isEmpty(locations)){
            locations.add("all");
        }*/
        List<UserProvinceBusiness> provinceBusinessList = userProvinceBusinessMapperExt.getProvinceBusinessUserListByRoleTypes(roleId, beIds, locations);
        List<Data4User> data4Users = provinceBusinessToDataUser(provinceBusinessList);
        baseAnswer.setData(data4Users);

        return baseAnswer;
    }

    @Transactional(rollbackFor = Exception.class)
    public void unifyUserSync(List<UnifyUserSyncDTO> syncUsers) {
        log.info("统一用户推送unifyUserSync：{}",syncUsers);
        if (CollectionUtils.isEmpty(syncUsers)) {
            return;
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        Date now = new Date();

        List<String> userCodes = syncUsers.stream().map(UnifyUserSyncDTO::getUserCode).distinct().collect(Collectors.toList());
        List<User> updateOsUser = userMapper.selectByExample(new UserExample().createCriteria()
                .andNewJobNumberIn(userCodes).example());
        List<UserScreen> updateScreenUser = userScreenMapper.selectByExample(new UserScreenExample().createCriteria()
                .andNewJobNumberIn(userCodes).example());
        if (CollectionUtils.isEmpty(updateOsUser) && CollectionUtils.isEmpty(updateScreenUser)) {
            return;
        }
        log.info("统一用户推送updateOsUser：{},updateScreenUser:{}",updateOsUser,updateScreenUser);
        //按照工号组织所查出来用户，方便后续对照更新
        Map<String, List<User>> osMap = new LinkedHashMap<>();
        Map<String, List<UserScreen>> screenMap = new LinkedHashMap<>();
        updateOsUser.forEach(item -> {
            List<User> list = osMap.get(item.getNewJobNumber());
            if (list == null) {
                list = new ArrayList<>();
                list.add(item);
                osMap.put(item.getNewJobNumber(), list);
            } else {
                list.add(item);
            }
        });
        updateScreenUser.forEach(item -> {
            List<UserScreen> list = screenMap.get(item.getNewJobNumber());
            if (list == null) {
                list = new ArrayList<>();
                list.add(item);
                screenMap.put(item.getNewJobNumber(), list);
            } else {
                list.add(item);
            }
        });

        syncUsers.forEach(syncUser -> {
            List<User> osUser = osMap.get(syncUser.getUserCode());
            List<UserScreen> screenUser = screenMap.get(syncUser.getUserCode());
            log.info("统一用户推送updateOsUserMap：{},updateScreenUserMap:{}",osUser,screenUser);
            if ("update".equals(syncUser.getOperationMode())) {
                if (CollectionUtils.isNotEmpty(osUser)) {
                    osUser.forEach(item -> {
                        Integer status = syncUser.getStatus();
                        item.setEmail(syncUser.getEmail());
                        item.setName(syncUser.getCn());
                        item.setPhone(IOTEncodeUtils.encryptIOTMessage(syncUser.getFirstPhone(), encryptKey));
                        item.setDepartmentId(syncUser.getOrgCode());
                        item.setDepartmentName(syncUser.getOrgShortName());
                        item.setIotType(syncUser.getUserType());
                        item.setCompany(syncUser.getOrgFullName().substring(0, syncUser.getOrgFullName().indexOf("\\")));
                        item.setUnifiedStatus(syncUser.getStatus());
                        item.setOldJobNumber(syncUser.getUserOldCode());
                        item.setNewJobNumber(syncUser.getUserCode());
                        item.setUpdateTime(now);
                        item.setAccount(syncUser.getUsername());

                        if (0!=status){
                            //更新状态不是0正常状态用户数据都进行os用户停用
                            log.info("统一用户平台推送不正常用户（Os）停用userIdOs：{}",item.getUserId());
                            item.setIsCancel(true);
                            // 将删除的用户下线
                            loginOut(item.getUserId(), false);
                        }
                        userMapper.updateByPrimaryKey(item);
                        //删除成功，操作日志
                        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(item.getRoleId());
                        if (roleInfo != null) {
                            StringBuilder contentSucess = new StringBuilder("【" +"停用用户账号】\n所属系统: " + roleInfo.getSystem()
                                    + ",所属角色: " + roleInfo.getName() + "; 姓名: "
                                    + LogService.custNameDesensitization(item.getName()) + ",联系电话" + LogService.replaceWithStar(item.getPhone()));
                            String userFrom = item.getUserFrom();
                            if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
                            }else {
                                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
                            }
                        }
                    });
                }

                if (CollectionUtils.isNotEmpty(screenUser)) {
                    screenUser.forEach(item -> {
                        Integer status = syncUser.getStatus();
                        item.setEmail(syncUser.getEmail());
                        item.setName(syncUser.getCn());
                        item.setPhone(IOTEncodeUtils.encryptIOTMessage(syncUser.getFirstPhone(), encryptKey));
                        item.setDepartmentId(syncUser.getOrgCode());
                        item.setDepartmentName(syncUser.getOrgShortName());
                        item.setIotType(syncUser.getUserType());
                        item.setCompany(syncUser.getOrgFullName().substring(0, syncUser.getOrgFullName().indexOf("\\")));
                        item.setUnifiedStatus(syncUser.getStatus());
                        item.setOldJobNumber(syncUser.getUserOldCode());
                        item.setNewJobNumber(syncUser.getUserCode());
                        item.setUpdateTime(now);
                        if (0!=status){
                            log.info("统一用户平台推送不正常用户(大屏)停用userIdScreen：{}",item.getId());
                            //更新状态不是0正常状态用户数据都进行os用户停用
                            item.setIsCancel(true);
                            // 将删除的用户下线
                            loginOut(item.getId(), false);
                        }
                        userScreenMapper.updateByPrimaryKey(item);
                        //删除成功，操作日志
                        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(item.getRoleId());
                        if (roleInfo != null) {
                            StringBuilder contentSucess = new StringBuilder("【" +"停用用户账号】\n所属系统: " + roleInfo.getSystem()
                                    + ",所属角色: " + roleInfo.getName() + "; 姓名: "
                                    + LogService.custNameDesensitization(item.getName()) + ",联系电话" + LogService.replaceWithStar(item.getPhone()));
                            String userFrom = item.getUserFrom();
                            if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
                            }else {
                                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                                        contentSucess.toString(), LogResultEnum.LOG_SUCESS.code, null);
                            }
                        }
                    });
                }
            } else if ("delete".equals(syncUser.getOperationMode())) {
                if (CollectionUtils.isNotEmpty(osUser)) {
                    osUser.forEach(item -> {
                        item.setUnifiedStatus(UserUnifiedStatusEnum.CANCEL.code);
                        item.setUpdateTime(now);
                        Integer status = syncUser.getStatus();
                        if (1!=status){
                            //更新状态不是0正常状态用户数据都进行os用户停用
                            item.setIsCancel(true);
                        }
                        userMapper.updateByPrimaryKey(item);
                    });
                }

                if (CollectionUtils.isNotEmpty(screenUser)) {
                    screenUser.forEach(item -> {
                        item.setUnifiedStatus(UserUnifiedStatusEnum.CANCEL.code);
                        item.setUpdateTime(now);
                        Integer status = syncUser.getStatus();
                        if (1!=status){
                            //更新状态不是0正常状态用户数据都进行os用户停用
                            item.setIsCancel(true);
                        }
                        userScreenMapper.updateByPrimaryKey(item);
                    });
                }
            }
        });
        log.info("执行完毕统一用户推送信息UnifiedUserMessage.");
    }

    /**
     * 条件查询所有主合作伙伴信息
     */
    public List<UserPartner> userPrimaryList(String queryInfo) {
        List<UserPartner> users = userPartnerMapperExt.getPrimaryUserList(queryInfo);
        return users;
    }

    /**
     * 查询合作伙伴省公司用户通过省编码
     * @param beId
     * @return
     */
    public List<UserPartner> getUserPartnerByProvince(String beId){
        List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andCompanyTypeEqualTo("2").andBeIdEqualTo(beId).example());
        return userPartners;
    }

    /**
     * 修改产金用户信息
     */
    public BaseAnswer<Void> editFinancingInfo(EditFinancingInfoRequest request) {
        UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(request.getUserId());
        userPartner.setCjStatus(request.getStatus());
        if (request.getAdvice() != null) {
            userPartner.setCjAdvice(request.getAdvice());
        }
        userPartner.setCjApplyTime(new Date());
        userPartnerMapper.updateByPrimaryKeySelective(userPartner);
        return BaseAnswer.success(null);
    }

    public void logoffUser(Request4LogoffUser request, LoginIfo4Redis loginIfo4Redis) {
        String system = request.getSystem();
        String userId = request.getUserId();
        String roleId = null;
        String userName = null;
        String phone = null;
        Boolean isCancel = null;
        Date now = new Date();
        Data4User data4User = new Data4User();

        if (SYSTEM_OS.equals(system)) {
            User user = userMapper.selectByPrimaryKey(userId);
            Boolean isAdmin = user.getIsAdmin();
            if (isAdmin != null && isAdmin && user.getRoleId().equals(SYSTEM_USER_ROLE_ID)) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, "超管账号不可注销");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "超管账号不可注销");
            }
            isCancel = user.getIsCancel();
            roleId = user.getRoleId();
            if (isCancel != null && !isCancel) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, "账号停用后才可注销");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "账号停用后才可注销");
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), roleId)) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());

                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }
            user.setIsLogoff(true);
            user.setUpdateTime(now);
            userMapper.updateByPrimaryKey(user);
            userName = user.getName();
            phone = user.getPhone();
            BeanUtils.copyProperties(user, data4User);
        } else if (SYSTEM_PARTNER.equals(system)) {
            UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(userId);

            isCancel = userPartner.getIsCancel();
            roleId = userPartner.getRoleId();
            RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(roleId);
            if (isCancel != null && !isCancel) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, "账号停用后才可注销");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "账号停用后才可注销");
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), roleId)) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());

                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }

            String roleType = roleInfo.getRoleType();
            if (PARTNER_LORD_ROLE.equals(roleType)){
                //判断注销的是否是主合作  旗下所有相同公司用户都注销
                //获取其他相同公司角色用户 都注销
                List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                        .andPartnerNameEqualTo(userPartner.getPartnerName()).andIsCancelEqualTo(true).andIsLogoffEqualTo(false).example());
                for (UserPartner partner : userPartners) {
                    partner.setIsLogoff(true);
                    partner.setUpdateTime(now);
                    userPartnerMapper.updateByPrimaryKey(partner);
                }
            }else {
                userPartner.setIsLogoff(true);
                userPartner.setUpdateTime(now);
                userPartnerMapper.updateByPrimaryKey(userPartner);
            }

            userName = userPartner.getName();
            phone = userPartner.getPhone();
            BeanUtils.copyProperties(userPartner, data4User);
        } else if (SYSTEM_SCREEN.equals(system)) {
            UserScreen userScreen = userScreenMapper.selectByPrimaryKey(userId);
            isCancel = userScreen.getIsCancel();
            roleId = userScreen.getRoleId();
            if (isCancel != null && !isCancel) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, "账号停用后才可注销");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "账号停用后才可注销");
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), roleId)) {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, StatusConstant.NO_PERMIT_OPERATE.getMessage());

                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }
            userScreen.setIsLogoff(true);
            userScreen.setUpdateTime(now);
            userScreenMapper.updateByPrimaryKey(userScreen);
            userName = userScreen.getName();
            phone = userScreen.getPhone();
            BeanUtils.copyProperties(userScreen, data4User);
        }else if (PROVINCE_MANAGEMENT.equals(system)){
            UserProvinceBusiness userProvinceBusiness = userProvinceBusinessMapper.selectByPrimaryKey(userId);
            isCancel = userProvinceBusiness.getIsCancel();
            roleId = userProvinceBusiness.getRoleId();
            if(isCancel != null && !isCancel){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"账号停用后才可注销");
            }
            if (!checkHasRoleManage(loginIfo4Redis.getRoleId(), roleId)) {
                throw new BusinessException(StatusConstant.NO_PERMIT_OPERATE);
            }
            userProvinceBusiness.setIsLogoff(true);
            userProvinceBusiness.setUpdateTime(now);
            userProvinceBusinessMapper.updateByPrimaryKey(userProvinceBusiness);
            userName = userProvinceBusiness.getName();
            phone = userProvinceBusiness.getPhone();
            BeanUtils.copyProperties(userProvinceBusiness, data4User);
        } else {
            logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, "-", LogResultEnum.LOG_FAIL.code, "所属系统错误");

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "所属系统错误");
        }
        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(roleId);
        if (roleInfo != null) {
            StringBuilder content = new StringBuilder("【注销用户账号】\n所属系统: " + system
                    + "\n所属角色: " + roleInfo.getName() + "\n姓名: "
                    +LogService.custNameDesensitization(userName)  + "\n联系电话" + LogService.replaceWithStar(IOTEncodeUtils.decryptIOTMessage(phone, encryptKey)));
            String userFrom = data4User.getUserFrom();
            if (StringUtils.isNotBlank(userFrom) && "iot".equals(userFrom)){
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_OPERATION_MANAGE.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }else {
                logService.recordOperateLog(ModuleEnum.USER_MANAGE.code,
                        UserManageOperateEnum.USER_PARTNER_MANAGE.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }

        }
    }

    public void importScreenUser(MultipartFile file, LoginIfo4Redis loginIfo4Redis, String ip) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        List<ImportScreenUserErrorDTO> failList = new ArrayList<>();
        //成功导入的用户日志
        List<String> logUser = new ArrayList<>();
        InputStream inputStream = null;
        //标记是否 没有失败的导入记录
        boolean noError = true;
        try {
            inputStream = file.getInputStream();
            //物联网公司(含外协)
            List<Object> list = EasyExcel.read(inputStream, ImportScreenUserIotDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isNotEmpty(list)) {
                for (Object o : list) {
                    ImportScreenUserIotDTO dto = (ImportScreenUserIotDTO) o;
                    Request4AddUser request4AddUser = new Request4AddUser();
                    try {
                        String userType = dto.getUserType();
                        if ("测试账号".equals(userType)) {
                            dto.setUserType("test");
                        } else if ("正式账号".equals(userType)) {
                            dto.setUserType("normal");
                        } else {
                            StringBuilder content = new StringBuilder("【导入大屏用户】\n" + String.join("\n", logUser));
                            logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString(), LogResultEnum.LOG_FAIL.code, "账号类型错误");
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "账号类型错误");
                        }
                        List<RoleInfo> roleInfoList = getRoleInfoList(dto.getRoleName());
                        BeanUtils.copyProperties(dto, request4AddUser);
                        //获取统一用户平台的信息
                        String account = dto.getAccount();
                        UnifyUserDTO unifyUserDTO = userProductPipeService.packagingUnifyUserMessage(account);
                        BeanUtils.copyProperties(unifyUserDTO, request4AddUser);
                        request4AddUser.setStatus(unifyUserDTO.getUniStatus());
                        request4AddUser.setPhone(unifyUserDTO.getFirstPhone());
                        request4AddUser.setOldJobNumber(unifyUserDTO.getUserOldCode());
                        request4AddUser.setNewJobNumber(unifyUserDTO.getUserCode());
                        request4AddUser.setRoleId(roleInfoList.get(0).getId());
                        request4AddUser.setUserFrom("iot");
                        request4AddUser.setSystem(SYSTEM_SCREEN);
                        request4AddUser.setUserName(unifyUserDTO.getCn());
                        request4AddUser.setDepartmentId(unifyUserDTO.getOrgCode());
                        request4AddUser.setDepartmentName(unifyUserDTO.getOrgShortName());
                        addUser(request4AddUser, null, loginIfo4Redis, false, ip);
                        logUser.add("所属角色:" + dto.getRoleName() + ",姓名:" + request4AddUser.getUserName() + ",联系电话:" + LogService.replaceWithStar(request4AddUser.getPhone()));
                    } catch (Exception e) {
                        //保存单个用户的失败日志
                        noError = false;
                        ImportScreenUserErrorDTO errorDto = new ImportScreenUserErrorDTO();
                        BeanUtils.copyProperties(request4AddUser, errorDto);
                        BeanUtils.copyProperties(dto, errorDto);
                        errorDto.setUserFrom("iot");
                        errorDto.setFailReason(e instanceof BusinessException ? ((BusinessException) e).getStatus().getMessage() : e.getMessage());
                        failList.add(errorDto);
                    }
                }
            }


            inputStream = file.getInputStream();
            //非物联网公司
            List<Object> listOther = EasyExcel.read(inputStream, ImportScreenUserOtherDTO.class, null)
                    .sheet(1).headRowNumber(1).doReadSync();
            if (CollectionUtils.isNotEmpty(listOther)) {
                for (Object o : listOther) {
                    ImportScreenUserOtherDTO dto = (ImportScreenUserOtherDTO) o;
                    try {
                        //转换用户类型，中文 -> 英文
                        String userType = dto.getUserType();
                        if ("测试账号".equals(userType)) {
                            dto.setUserType("test");
                        } else if ("正式账号".equals(userType)) {
                            dto.setUserType("normal");
                        } else {
                            StringBuilder content = new StringBuilder("【导入大屏用户】\n" + String.join("\n", logUser));
                            logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString(), LogResultEnum.LOG_FAIL.code, "账号类型错误");

                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "账号类型错误");
                        }
                        List<RoleInfo> roleInfoList = getRoleInfoList(dto.getRoleName());
                        Request4AddUser request4AddUser = new Request4AddUser();
                        BeanUtils.copyProperties(dto, request4AddUser);
                        request4AddUser.setRoleId(roleInfoList.get(0).getId());
                        request4AddUser.setUserFrom("other");
                        request4AddUser.setSystem(SYSTEM_SCREEN);
                        addUser(request4AddUser, null, loginIfo4Redis, false, ip);

                        //保存成功日志
                        logUser.add("所属角色:" + dto.getRoleName() + ",姓名:" + dto.getUserName() + ",联系电话:" + LogService.replaceWithStar(dto.getPhone()));
                    } catch (Exception e) {
                        noError = false;
                        ImportScreenUserErrorDTO errorDto = new ImportScreenUserErrorDTO();
                        BeanUtils.copyProperties(dto, errorDto);
                        errorDto.setUserFrom("other");
                        errorDto.setFailReason(e instanceof BusinessException ? ((BusinessException) e).getStatus().getMessage() : e.getMessage());
                        failList.add(errorDto);
                    }
                }
            }


        } catch (Exception e) {
            noError = false;
            log.error("读取文件出错", e);
            ImportScreenUserErrorDTO dto = new ImportScreenUserErrorDTO();
            dto.setFailReason("读取文件出错:" + e.getMessage());
            failList.add(dto);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //记录导入成功的日志
        if (CollectionUtils.isNotEmpty(logUser)) {
            StringBuilder content = new StringBuilder("【导入大屏用户】\n" + String.join("\n", logUser));
            logService.recordOperateLog(ModuleEnum.USER_MANAGE.code, UserManageOperateEnum.USER_ACCOUNT_MANAGE.code, content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        }

        if (CollectionUtils.isNotEmpty(failList)) {
            //生成用户导入失败的excel
            writeFailReason(response, failList);
        }
        if (noError) {
            //如果没有发生错误，直接把返回信息写入header中，便于区分发生错误时直接返回二进制流的情况
            response.addHeader("stateCode", "00000");
            response.addHeader("message", "导入成功");
        }
    }

    private List<RoleInfo> getRoleInfoList(String roleName) {
        RoleInfoExample roleInfoExample = new RoleInfoExample().createCriteria().andNameEqualTo(roleName).example();
        List<RoleInfo> roleInfoList = roleInfoMapper.selectByExample(roleInfoExample);
        if (roleInfoList.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "角色名称:" + roleName + "不存在");
        }
        if (roleInfoList.size() > 1) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "角色名称:" + roleName + "存在重复数据");
        }
        return roleInfoList;
    }

    private void writeFailReason(HttpServletResponse response, List<ImportScreenUserErrorDTO> failList) {
        Workbook workbook = ExcelUtils.exportSimpleExcel("failReason", ImportScreenUserErrorDTO.class, failList);
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            workbook.write(outputStream);
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
        }
    }


    /**
     * os合作伙伴数据导出
     * @param roleIds
     * @throws Exception
     */
    public void exportOsPartnerMessage(List<String> roleIds) throws Exception{

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<UserPartnerExportDTO> list = userPartnerMapperExt.getUserListByRoleId(roleIds);
        for (UserPartnerExportDTO dto : list) {
            String phone = dto.getPhone();
            dto.setPhone(IOTEncodeUtils.decryptIOTMessage(phone,encryptKey));
        }
        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
        EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "合作伙伴明细", "list",
                list, null);
        easyExcelDTOList.add(easyExcelDTO);

        String excelName = "合作伙伴用户信息";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("templates/date_partner_export.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出合作伙伴
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());

    }


    /**
     * 导入数据割接的合作伙伴用户
     * @param file
     */
    @Transactional(rollbackFor = Exception.class)
    public void importPartnerUser(MultipartFile file ) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        InputStream inputStream = null;

        try {
            inputStream = file.getInputStream();
            //物联网公司(含外协)
            List<Object> list = EasyExcel.read(inputStream, UserPartnerExportDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            if(CollectionUtils.isNotEmpty(list)){
                for (Object o : list) {
                    UserPartnerExportDTO dto = (UserPartnerExportDTO) o;

                  /*  if ("主合作伙伴".equals(dto.getUserType()) && "省公司".equals(dto.getCompanyType())){
                        //校验合作伙伴名称是否存在
                        List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria()
                                .andPartnerNameEqualTo(dto.getProvince())
                                .andIsPrimaryEqualTo(true).andIsLogoffEqualTo(false).example());
                        if (CollectionUtils.isNotEmpty(primaryUsers)) {
                            throw new BusinessException(EXIST_COOPERATION_NAME_ERROR,primaryUsers.get(0).getPartnerName().concat(EXIST_COOPERATION_NAME_ERROR.getMessage()));
                        }
                    }*/
                    UserPartner userPartner = new UserPartner();
                    userPartner.setUserId(dto.getId());
                    String companyType = dto.getCompanyType();
                    String beId = null;
                    if (StringUtils.isNotBlank(companyType)){
                        if ("非省公司".equals(companyType)){
                            userPartner.setCompanyType("1");
                        }else if ("省公司".equals(companyType)){
                            userPartner.setCompanyType("2");
                            String province = dto.getProvince();
                            if (StringUtils.isNotBlank(province)){
                                userPartner.setPartnerName(province);
                                beId = UserPartnerNameEnum.fromCode(province);
                                if (StringUtils.isBlank(beId)){
                                      throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"省域名称错误");
                                }else {
                                    userPartner.setBeId(beId);
                                    userPartner.setProvince(UserPartnerNameEnum.fromProvinceName(province));
                                }
                            }else {
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"省域名称不能为空");
                            }
                        }else {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"单位类型错误");
                        }
                    }

                    String userType = dto.getUserType();
                    //从合作伙伴的时候才有地市域
                    if ("从合作伙伴".equals(userType) && "省公司".equals(dto.getCompanyType())){
                    String region = dto.getLocation();
                    if (StringUtils.isNotBlank(region)){
                      if ("全地市".equals(region)){
                          userPartner.setLocation("全地市");
                          userPartner.setLocationId("all");
                      }else {
                          List<String> provinceList = new ArrayList<>();
                          provinceList.add(beId);
                          List<ProvinceDTO> provinceCityListByBeId = userScreenService.getProvinceCityListByBeId(provinceList);
                          List<CityDTO> children = provinceCityListByBeId.get(0).getChildren();
                          Optional<CityDTO> first = children.stream().filter(cityDTO -> cityDTO.getName().equals(region)).findFirst();
                          if (!first.isPresent()){
                              throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"地市域名称错误");
                          }else {
                              userPartner.setLocation(region);
                              userPartner.setLocationId(first.get().getValue());
                          }
                      }
                    }else {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"地市域名称不能为空");
                    }
                    }
                    userPartnerMapper.updateByPrimaryKeySelective(userPartner);
                }
            }

        } catch (Exception e) {
            log.error("读取文件出错",e);
            e.printStackTrace();
        }finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }


    /**
     * 获取新增主 从 省管合作伙伴查询合作伙伴名称
     * @return
     */
    public List<ProvincePartnerDTO> getProvinceCompanyList(){
        List<ProvincePartnerDTO> list = UserPartnerNameEnum.fromCodeAndName();
        return list;
    }


    public BaseAnswer<List<UserAndRoleByAuthDO>> getUserAndRoleMessageByAuthCode(){
        BaseAnswer<List<UserAndRoleByAuthDO>> baseAnswer = new BaseAnswer<>();
        List<UserAndRoleByAuthDO> roleMessageByAuthId = userMapperExt.getRoleMessageByAuthId();
        return  baseAnswer.setData(roleMessageByAuthId);
    }

    /**
     * 根据权限编码查询角色信息
     * @param authCodes
     * @return
     */
    public List<RoleMessageByAuthCodeDO>  getRoleMessageByAuthCode(List<String> authCodes){
       return userMapperExt.queryRoleMessageByAuthCode(authCodes);
    }

    public BaseAnswer refreshGio() {
        log.info("开始刷新gio用户权限");
        /**
         * 1.从gio查询已注册gio的用户列表
         * 2.找到对应的大屏用户列表及其区域
         * 3.为gio用户设置区域权限
         */
        //1.从gio查询已注册gio的用户列表
        String url = String.format(gioConfig.getUsersUrlFormat(),gioConfig.getEnterpriseId(),gioConfig.getProjectId(),gioConfig.getSpaceId());
        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization",gioConfig.getAuthorization());
        //gio用户和区域信息
        Map<String,Map<String,List<String>>> gioUserIdAndAreaMap = new HashMap<>();
        Map<String,String> gioUserIdAndScreenUserIdMap = new HashMap<>();
        Object o = null;
        try {
            log.info("获取用户列表请求地址:{},请求头:{}",url,headerParams);
            o = HttpUtil.getWithoutSSL(url, headerParams, 10000, 10000);
        } catch (Exception e) {
            log.error("获取gio用户列表发生异常",e);
            return BaseAnswer.success("获取gio用户列表发生异常");
        }
        String responseStr = (String) o;
        log.info("获取用户列表响应:{}",responseStr);
        JSONArray jsonArray = JSONObject.parseArray(responseStr);
        log.info("gio用户数量:{}",jsonArray.size());
        HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
//        ArrayList<String> allProvinceCodeList = new ArrayList<>(provinceNameCodeMap.values());
        //根据gio研发反馈，exprs传递空数组表示不限制区域
        ArrayList<String> allProvinceCodeList = new ArrayList<>();
        //2.找到对应的大屏用户列表及其区域
        for (Object o1 : jsonArray) {
            JSONObject jsonObject = (JSONObject) o1;
            String gioUserId = jsonObject.getString("id");
            String screenUserId = jsonObject.getString("name");
            UserScreen userScreen = userScreenMapper.selectByPrimaryKey(screenUserId);
            if(userScreen == null){
                log.error("大屏用户id:{}不存在",screenUserId);
                continue;
            }
            //大屏区域信息转化为gio区域信息,直接使用编码
            Map<String,List<String>> areaMap = new HashMap<>();
            List<String> beIdList = new ArrayList<>();
            String beId = userScreen.getBeId();
            String province = userScreen.getProvince();
            if(org.apache.commons.lang3.StringUtils.isEmpty(beId) && org.apache.commons.lang3.StringUtils.isNotEmpty(province)){
                //线上有一部分老数据，没有beId,但是有province（中文），需要兼容处理
                if("所有省".equals(province)){
                    beIdList = allProvinceCodeList;
                }else {
                    String[] provinceNameArr = province.split(",");
                    beIdList = Arrays.stream(provinceNameArr).map(p -> {
                        String provinceCode = provinceNameCodeMap.get(p);
                        return provinceCode;
                    }).collect(Collectors.toList());
                }
            }
            if("所有省".equals(beId)){
                beIdList = allProvinceCodeList;
            }else if(StringUtils.isNotBlank(beId)){
                //部分省
                beIdList.addAll(Arrays.asList(beId.split(",")));
            }
            areaMap.put("beId",beIdList);
            String location = userScreen.getLocation();
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(location)){
                areaMap.put("location",Arrays.asList(location.split(",")));
            }
            gioUserIdAndAreaMap.put(gioUserId,areaMap);
            gioUserIdAndScreenUserIdMap.put(gioUserId,screenUserId);
        }
        List<String> failScreenUserInfoList = new ArrayList<>();
        //3.为gio用户设置区域权限
        headerParams.put("X-Product-Unique-Id","UBA");
        if(!gioUserIdAndAreaMap.isEmpty()){
            for (Map.Entry<String, Map<String,List<String>>> entry : gioUserIdAndAreaMap.entrySet()) {
                String gioUserId = entry.getKey();
                String screenUserId = gioUserIdAndScreenUserIdMap.get(gioUserId);
                Map<String,List<String>> areaMap = entry.getValue();
                List<String> beIdList = areaMap.get("beId");
                List<String> locationList = areaMap.get("location");
                JSONObject requestParam = new JSONObject();
                requestParam.put("op","and");
                JSONArray exprs = new JSONArray();
                if(CollectionUtils.isNotEmpty(beIdList)){
                    //省信息
                    JSONObject expr = new JSONObject();
                    expr.put("key","vmix_per_province");
                    expr.put("valueType","string");
                    expr.put("op","in");
                    expr.put("values",beIdList);
                    exprs.add(expr);
                }
                if(CollectionUtils.isNotEmpty(locationList)){
                    //市信息
                    JSONObject expr = new JSONObject();
                    expr.put("key","vmix_per_city");
                    expr.put("valueType","string");
                    expr.put("op","in");
                    expr.put("values",locationList);
                    exprs.add(expr);
                }
                requestParam.put("exprs",exprs);
                log.info("刷新gio用户:{},大屏用户:{}的请求内容:{}",gioUserId, screenUserId,JSON.toJSONString(requestParam));
                String updateRowLevelPermissionUrl = String.format(gioConfig.getUpdateRowLevelPermissionUrlFormat(),gioConfig.getSpaceId(),gioUserId);
                log.info("刷新gio用户:{},大屏用户:{}的请求地址:{}",gioUserId, screenUserId,updateRowLevelPermissionUrl);
                Integer statusCode = null;
                String result = "";
                try {
                    CloseableHttpResponse response = HttpUtil.doPutJsonReturnResponseWithoutSSL(updateRowLevelPermissionUrl, headerParams, requestParam, 10000, 10000);
                    statusCode = response.getStatusLine().getStatusCode();
                    HttpEntity httpEntity = response.getEntity();
                    if (Optional.ofNullable(httpEntity).isPresent()) {
                        result = EntityUtils.toString(httpEntity, "UTF-8");
                    }
                    log.info("收到刷新gio用户:{},大屏用户:{}的响应,code:{},内容:{}",gioUserId,screenUserId,statusCode,result);
                } catch (Exception e) {
                    log.error("刷新gio用户:{},大屏用户:{}权限发生异常",gioUserId, screenUserId,e);
                    failScreenUserInfoList.add(screenUserId+":系统异常");
                    continue;
                }
                if(statusCode.intValue() != 200){
                    //操作失败
                    String errMsg = null;
                    if(StringUtils.isNotBlank(result)){
                        JSONObject jsonObject = JSON.parseObject(result);
                        JSONObject error = (JSONObject)jsonObject.getJSONArray("errors").get(0);
                        errMsg = error.getString("message");
                    }
                    failScreenUserInfoList.add(StringUtils.isNotBlank(errMsg) ? screenUserId+":"+errMsg : screenUserId);
                }
            }
        }
        log.info("完成刷新gio用户权限");
        if(CollectionUtils.isNotEmpty(failScreenUserInfoList)){
            String resultStr = "操作失败的大屏用户id："+String.join(",",failScreenUserInfoList);
            log.error("刷新gio用户失败信息:"+resultStr);
            return BaseAnswer.success(resultStr);
        }else {
            return BaseAnswer.success("ok");
        }
    }
}

