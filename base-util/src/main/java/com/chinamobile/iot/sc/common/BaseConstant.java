package com.chinamobile.iot.sc.common;

/**
 * @package: com.chinamobile.iot.sc.constant
 * @ClassName: BaseConstant
 * @description: 用户-权限常量
 * @author: zyj
 * @create: 2021/11/8 10:13
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public class BaseConstant {
    public static final String RETURN_ADDRESS_NULL = "-1";
    /**
     * 业务角色类型：
     * 1. 超级管理员：admin
     * 2. 运营管理员：operator
     * 3. 合作伙伴：partner
     * 4. 客服管理员：manager
     */
    public static final String ADMIN_ROLE = "admin";

    /**
     * 系统管理员
     */
    public static final String SYSTEM_ADMIN_ROLE = "systemAdmin";

    public static final String OPERATOR_ROLE = "operator";
    //从合作伙伴
    public static final String PARTNER_ROLE = "partner";
    //主合作伙伴
    public static final String PARTNER_LORD_ROLE = "partnerLord";
    //合作伙伴售后（安装师傅）
    public static final String PARTNER_INSTALL_ROLE = "partnerInstall";

    //装维管理员
    public static final String PARTNER_INSTALL_MANAGER_ROLE = "partnerInstallManager";

    //装维服务商主
    public static final String PARTNER_INSTALL_LORD_ROLE = "partnerInstallLord";

    //装维服务商从
    public static final String PARTNER_INSTALL_SUB_ROLE = "partnerInstallSub";

    //合作伙伴保理
    public static final String PARTNER_BAOLI = "partnerBaoli";

    public static final String PARTNER_BUSINESS = "partnerBusiness";

    //装维管理员
    public static final String PARTNER_INSTALL_MANAGER = "partnerInstallManager";

    //装维主
    public static final String PARTNER_INSTALL_LORD = "partnerInstallLord";

    //装维从
    public static final String PARTNER_INSTALL_SUB = "partnerInstallSub";

    //省业管员
    public static final String PROVINCE_MANAGEMENT_ROLE = "provinceManagement";


    //采购管理员
    public static final String PROVINCE_ORDER_MANAGER_ROLE = "provinceOrderManager";


    //合作伙伴省管
    public static final String PARTNER_PROVINCE = "partnerProvince";


    /**
     *客服管理员
     */
    public static final String MANAGER_STAFF_ROLE = "manager";

    /**
     * 大屏角色类型
     */
    public static final String SCREEN_ADMIN_ROLE = "screenAdmin";

    public static final String SCREEN_MANAGER_ROLE = "screenManager";

    public static final String SCREEN_PROVINCE_ROLE = "screenProvince";

    /**
     * 产品运管类型
     */

    public static final String PRODUCT_OPERATOR_IMPORT_ROLE = "productOperatorImport";

    public static final String PRODUCT_OPERATOR_FRAME_ROLE = "productOperatorFrame";

    public static final String PRODUCT_OPERATOR_RECHECK_ROLE = "productOperatorRecheck";

    public static final String PRODUCT_OPERATOR_ULTIMATELY_ROLE = "productOperatorUltimately";

    //角色管理
    public static final String USER_ROLE_AUTH = "user_role";
    //运营管理员管理
    public static final String USER_OPERATOR_AUTH = "user_operator";
    //合作伙伴从管理
    public static final String USER_PARTNER_AUTH = "user_partner";
    //合作伙伴主管理
    public static final String USER_PARTNER_LORD_AUTH = "user_partnerLord";
    //客服管理员
    public static final String MANAGER_STAFF_AUTH  = "user_manager";

    /**
     * 产品运营管理
     */
    public static final String PRODUCT_RUN  = "product_run";
    /**
     * 分销积分管理员
     */
    public static final String POINT_MANAGER  = "pointManager";

    public static final String H5_LOGIN ="h5_login";

    /**
     * 权限码
     * 联合销售订单 修改为 商城同步订单 2022.02.16 zyj 根据(OS原型-v1.3-0128)权限列表原型需求修改
     */
    //联合销售订单-查看详情
    public static final String IOT_ORDER_DETAIL = "order_consignment_detail";
    //联合销售订单-订单处理
    public static final String IOT_ORDER_HANDLE = "order_consignment_handle";
    //联合销售订单-订单查询
    public static final String IOT_ORDER_QUERY = "order_consignment_query";
    //联合销售订单-异常订单取消
    public static final String IOT_ORDER_CANCEL = "order_consignment_cancel";
    //联合销售订单-订单催单
    public static final String IOT_ORDER_REMINDER = "order_consignment_reminder";
    //联合销售订单-订单备注
    public static final String IOT_ORDER_REMARK = "order_consignment_remark";
    //合同履约订单-订单导入
    public static final String ORDER_OTHER_IMPORT = "order_other_import";
    //合同履约订单-订单处理
    public static final String ORDER_OTHER_HANDLE = "order_other_handle";
    //合同履约订单-订单查询
    public static final String ORDER_OTHER_QUERY = "order_other_query";
    //外部电商订单-订单查询
   // public static final String ORDER_ESTORE_QUERY = "order_estore_query";
    //联合销售发票-查看详情
    public static final String IOT_INVOICE_DETAIL = "invoice_consignment_detail";
    //联合销售发票-发票处理
    public static final String IOT_INVOICE_HANDLE = "invoice_consignment_handle";
    //联合销售发票-发票查询
    public static final String IOT_INVOICE_QUERY = "invoice_consignment_query";
    //联合销售发票冲红-查看详情
    public static final String IOT_REVINVO_DETAIL = "revinvo_consignment_detail";
    //联合销售发票冲红-冲红处理
    public static final String IOT_REVINVO_HANDLE = "revinvo_consignment_handle";
    //联合销售发票冲红-冲红查询
    public static final String IOT_REVINVO_QUERY = "revinvo_consignment_query";
    //iot代销类商品-配置合作伙伴
    public static final String IOT_MERCHANDISE_BUSINESS = "merchandise_consignment_business";
    //iot代销类商品-配置库存
    public static final String IOT_MERCHANDISE_INVENTORY = "merchandise_consignment_inventory";
    //iot代销类商品-商品查询
    public static final String IOT_MERCHANDISE_QUERY = "merchandise_consignment_query";
    //iot代销类商品-库存查询
    public static final String IOT_MERCHANDISE_INVENTORY_QUERY = "merchandise_consignment_inventoryQuery";
    //iot代销类商品-库存导出
    public static final String IOT_MERCHANDISE_INVENTORY_EXPORT = "merchandise_consignment_inventory_export";
    //iot-角色管理
    public static final String IOT_USER_ROLE = "user_role";
    //iot-运营管理员管理
    public static final String IOT_USER_OPERATOR = "user_operator";
    //iot-合作伙伴从管理
    public static final String IOT_USER_PARTNER = "user_partner";
    //iot-合作伙伴主管理
    public static final String IOT_USER_PARTNER_LORD = "user_partnerLord";
    //售后管理人员
    public static final String IOT_USER_AFTER_SALE_LORD = "user_after_sale";

    //k3省物料
    public static final String K3_PROVINCE_MATERIAL="k3_province_material";

    //iot-客服管理
    public static final String IOT_USER_MANAGER = "user_manager";

    // 订单客服管理员管理
    public static final String IOT_USER_CSSTAFF = "user_csStaff";
    //非独立履约联系人
    public static final String USER_DEPENDENT = "user_dependent";
    //账户信息管理
    public static final String ACCOUNT_INFO = "account_info";
    //退换货地址
    public static final String ACCOUNT_READDRESS = "account_reAddress";

    //我的退换货地址
    public static final String MY_READDRESS = "my_address";
    //退换/售后处理
    public static final String REAFTER_CONSIGNMENT_HANDLE = "reAfter_consignment_handle";
    //退换/售后查询
    public static final String REAFTER_CONSIGNMENT_QUERY = "reAfter_consignment_query";
    //退换/售后 异常单取消
    public static final String REAFTER_CONSIGNMENT_CANCEL = "reAfter_consignment_cancel";

    // 分销中心
    public static final String RETAIL = "retail";
    //专区管理
    public static final String ACTIVITY = "activity";

    //大屏管理员
    public static final String SCREEN_USER = "screen_user";
    //大屏数据查询
    public static final String SCREEN_DATA_QUERY = "screen_data_query";
    //产品管理
    public static final String PRODUCT_OPERATION_MANAGEMENT = "product_operation_management";

    //产品引入
    public static final String PRODUCT_APPLY_MANAGE = "product_apply_manage";

    //产品上下架
    public static final String PRODUCT_UP_DOWN_FRAME = "product_up_down_frame";

    //售后服务商品配置
    public static final String AFTERMARKET_PRODUCT_CONFIG = "aftermarket_product_config";

    /**
     * 特殊售后请求
     */
    public static final String ORDER_CONSIGNMENT_SPECIAL_AFTER_REQUEST = "order_consignment_special_after_request";

    //引入初审
    public static final String PRODUCT_APPLY_MANAGE_VERIFY_FIRST = "product_apply_manage_verify_first";
    //引入复审
    public static final String PRODUCT_APPLY_MANAGE_VERIFY_AGAIN = "product_apply_manage_verify_again";
    //引入终审
    public static final String PRODUCT_APPLY_MANAGE_VERIFY_FINAL = "product_apply_manage_verify_final";

    //产品上下架初审
    public static final String PRODUCT_UP_DOWN_FRAME_VERIFY_FIRST = "product_up_down_frame_verify_first";
    //产品上下架复审
    public static final String PRODUCT_UP_DOWN_FRAME_VERIFY_AGAIN = "product_up_down_frame_verify_again";
    //产品上下架终审
    public static final String PRODUCT_UP_DOWN_FRAME_VERIFY_FINAL = "product_up_down_frame_verify_final";
    //产品上下架确认
    public static final String PRODUCT_UP_DOWN_FRAME_VERIFY_CONFIRM = "product_up_down_frame_verify_confirm";

    //公告列表审核权限
    public static final String SYSTEM_SETTING_ANNOUNCEMENT_AUDIT= "system_setting_announcement_audit";

    //订单导出-全量数据
    public static final String ORDER_EXPORT_ALL = "order_export";
    //订单导出(去敏收货信息)
    public static final String ORDER_EXPORT_WITHOUT_RECEIVER = "order_export_without_receiver";
    //订单导出(去敏收入省及金额等)
    public static final String ORDER_EXPORT_WITHOUT_PRICE_SO = "order_export_without_price_so";

    /***************** 数据权限 *****************/
    // 商品订单
    public static final String DATA_PERMISSION_ORDER_SYSTEM = "data_permission_order_system";
    public static final String DATA_PERMISSION_ORDER_COMPANY = "data_permission_order_company";
    public static final String DATA_PERMISSION_ORDER_PERSONAL = "data_permission_order_personal";

    // 售后订单
    public static final String DATA_PERMISSION_AFTER_MARKET_SYSTEM = "data_permission_after_market_order_system";
    public static final String DATA_PERMISSION_AFTER_MARKET_COMPANY = "data_permission_after_market_order_company";
    public static final String DATA_PERMISSION_AFTER_MARKET_PERSONAL = "data_permission_after_market_order_personal";

    // 联合销售开具发票
    public static final String DATA_PERMISSION_INVOICE_SYSTEM = "data_permission_invoice_system";
    public static final String DATA_PERMISSION_INVOICE_COMPANY = "data_permission_invoice_company";
    public static final String DATA_PERMISSION_INVOICE_PERSONAL = "data_permission_invoice_personal";

    // 联合销售冲红发票
    public static final String DATA_PERMISSION_REVERSE_INVOICE_SYSTEM = "data_permission_reverse_invoice_system";
    public static final String DATA_PERMISSION_REVERSE_INVOICE_COMPANY = "data_permission_reverse_invoice_company";
    public static final String DATA_PERMISSION_REVERSE_INVOICE_PERSONAL = "data_permission_reverse_invoice_personal";

    // 商品
    public static final String DATA_PERMISSION_PRODUCT_SYSTEM = "data_permission_product_system";
    public static final String DATA_PERMISSION_PRODUCT_COMPANY = "data_permission_product_company";
    public static final String DATA_PERMISSION_PRODUCT_PERSONAL = "data_permission_product_personal";

    // 商品退换
    public static final String DATA_PERMISSION_PRODUCT_REFUND_SYSTEM = "data_permission_product_refund_system";
    public static final String DATA_PERMISSION_PRODUCT_REFUND_COMPANY = "data_permission_product_refund_company";
    public static final String DATA_PERMISSION_PRODUCT_REFUND_PERSONAL = "data_permission_product_refund_personal";

    // 售后服务退换
    public static final String DATA_PERMISSION_AFTER_MARKET_REFUND_SYSTEM = "data_permission_after_market_refund_system";
    public static final String DATA_PERMISSION_AFTER_MARKET_REFUND_COMPANY = "data_permission_after_market_refund_company";
    public static final String DATA_PERMISSION_AFTER_MARKET_REFUND_PERSONAL = "data_permission_after_market_refund_personal";

    // 产品引入管理
    public static final String DATA_PERMISSION_NEW_PRODUCT_SYSTEM = "data_permission_new_product_system";
    public static final String DATA_PERMISSION_NEW_PRODUCT_COMPANY = "data_permission_new_product_company";
    public static final String DATA_PERMISSION_NEW_PRODUCT_PERSONAL = "data_permission_new_product_personal";

    // 上下架管理
    public static final String DATA_PERMISSION_ONLINE_OFFLINE_SYSTEM = "data_permission_online_offline_system";
    public static final String DATA_PERMISSION_ONLINE_OFFLINE_COMPANY = "data_permission_online_offline_company";
    public static final String DATA_PERMISSION_ONLINE_OFFLINE_PERSONAL = "data_permission_online_offline_personal";

     //上架流程
     public static final String DATA_PERMISSION_PUTAWAY_SYSTEM = "data_permission_putaway_system";
    public static final String DATA_PERMISSION_PUTAWAY_COMPANY = "data_permission_putaway_company";
    public static final String DATA_PERMISSION_PUTAWAY_PERSONAL = "data_permission_putaway_personal";

    //下架流程
    public static final String DATA_PERMISSION_DESCEND_SYSTEM = "data_permission_descend_system";
    public static final String DATA_PERMISSION_DESCEND_COMPANY = "data_permission_descend_company";
    public static final String DATA_PERMISSION_DESCEND_PERSONAL = "data_permission_descend_personal";

    //变更流程
    public static final String DATA_PERMISSION_ALTERATION_SYSTEM = "data_permission_alteration_system";
    public static final String DATA_PERMISSION_ALTERATION_COMPANY = "data_permission_alteration_company";
    public static final String DATA_PERMISSION_ALTERATION_PERSONAL = "data_permission_alteration_personal";

    //产品信息查询
    public static final String DATA_PERMISSION_PRODUCT_QUERY_SYSTEM = "data_permission_product_query_system";
    public static final String DATA_PERMISSION_PRODUCT_QUERY_COMPANY = "data_permission_product_query_company";
    public static final String DATA_PERMISSION_PRODUCT_QUERY_PERSONAL = "data_permission_product_query_personal";

    // 地址管理
    public static final String DATA_PERMISSION_ADDRESS_SYSTEM = "data_permission_system";
    public static final String DATA_PERMISSION_ADDRESS_COMPANY = "data_permission_company";
    public static final String DATA_PERMISSION_ADDRESS_PERSONAL = "data_permission_personal";

    // 大屏
    public static final String DATA_PERMISSION_SCREEN_ROOT_ALL = "data_permission_screen_root_all";
    public static final String DATA_PERMISSION_SCREEN_SECOND_ALL = "data_permission_screen_second_all";
    public static final String DATA_PERMISSION_SCREEN_SECOND_PART = "data_permission_screen_second_part";
    public static final String DATA_PERMISSION_SCREEN_PRODUCT_ALL = "data_permission_screen_product_all";
    public static final String DATA_PERMISSION_SCREEN_ALL_OVERVIEW = "data_permission_screen_all_overview";
    public static final String DATA_PERMISSION_SCREEN_OPERATIONAL_DATA = "data_permission_screen_operational_data";
    //保理融资还款
    public static final String DATA_PERMISSION_FINANCING_REPAYMENT_SYSTEM = "data_permission_financing_repayment_system";
    public static final String DATA_PERMISSION_FINANCING_REPAYMENT_COMPANY = "data_permission_financing_repayment_company";

    // 场景需求管理
    public static final String DATA_PERMISSION_SCENE_REQUIREMENT_SYSTEM = "data_permission_scene_requirement_system";
    public static final String DATA_PERMISSION_SCENE_REQUIREMENT_PERSONAL = "data_permission_scene_requirement_personal";

    /***************** 数据权限 *****************/

    //OS系统
    public static final String SYSTEM_OS = "os";
    //合作伙伴中心
    public static final String SYSTEM_PARTNER = "partner";
    //大屏
    public static final String SYSTEM_SCREEN = "screen";

    public static final String SYSTEM_MINI = "mini";
    //省业管员
    public static final String PROVINCE_MANAGEMENT = "province";
    //用户来源物联网（含外协）
    public static final String USER_FROM_IOT = "iot";
    //用户来源外部
    public static final String USER_FROM_OTHER = "other";


    /**
     * 导入导出卡相关信息
     */
    public static final String IMPORT_EXPORT_CARD = "import_export_card";

    /**
     * 订单接单
     */
    public static final String ORDER_GET_ORDER = "order_get_order";

    // 结算管理-代销硬件账单
    public static final String SETTLEMENT_MANAGEMENT_CONSIGNMENT_HARDWARE_MANAGEMENT= "settlement_management_consignment_hardware_management";

    /**
     * 超管特有权限，用于内部上线及其他不在前端页面展示的敏感接口
     */
    public static final String PERMISSION_SUPER_ADMIN_ONLY = "permission_super_admin_only";

    public static final String PARTNER_INSTALL_ROLE_ID = "1109180984878157824";

    public static final String PARTNER_BAOLI_ROLE_ID = "1109180984878157830";

    public static final String PARTNER_PRIMARY_ROLE_ID = "907998143042379777";

    public static final String PARTNER_FOLLOW_ROLE_ID = "907998143042379778";

    public static final String SYSTEM_USER_ROLE_ID = "907921766251245569";

    public static final String PARTNER_PROVINCE_ROLE_ID = "1226830885729095680";

    public static final String PROVINCE_ORDER_MANAGER_ROLE_ID = "1217482093586214913";


    /**
     * 千里眼charge_code
     */
    public static final String QLY_CHARGE_CODE = "C_IOT_411011";


    /**
     * TODO 待产品确认
     * 云视讯charge_code
     */
    public static final String YSX_CHARGE_CODE = "YSX_CHARGE_CODE";

    public static final String PRODUCT_SHELF_PROCESS ="product_shelf_process";

    public static final String PRODUCT_TAKEDOWN_PROCESS ="product_takedown_process";

    public static final String PRODUCT_CHANGE_PROCESS ="product_change_process";

    public static final String PRODUCT_MESSAGE_QUERY ="product_message_query";


    //开放能力主动接口
    public static final String OPEN_ABILITY_CONTENT_TPE_API = "api";

    //开放能力数据订阅
    public static final String OPEN_ABILITY_CONTENT_TPE_SUBSCRIBE = "subscribe";
}
