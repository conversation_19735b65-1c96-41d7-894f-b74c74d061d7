package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 开放能力部门配置
 *
 * <AUTHOR>
@Data
public class OpenAbilityOrganizationRO {
    /**
     * 主键
     */
    private String id;


    /**
     * 部门名称
     *
     */
    private String name;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 签名密钥
     */
    private String apiKey;

    /**
     * aes加密密钥
     */
    private String secret;

    /**
     * 是否可用，默认可用
     */
    private Boolean enable;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 部门所配置的字段列表
     */
    private List<OpenAbilityThemeFieldConfigRO> themeFieldConfigList;

    /**
     * 部门所配置的字内容表
     */
    private List<OpenAbilityThemeContentConfigRO> themeContentConfigList;

}