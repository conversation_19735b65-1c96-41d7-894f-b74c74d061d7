<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.operation.dao.ext.SmartOperationSmsTemplateMapperExt">
  <select id="getSmsTemplateList" resultType="com.chinamobile.operation.pojo.mapper.GetSmsTemplateListDO">
  SELECT
      sost.id id,
      sost.template_name templateName,
      sost.template_id templateId,
      sost.template_type templateType,
      sost.template_content templateContent,
      sost.description description,
      sost.create_time createTime,
      sost.update_time updateTime,
      us.name          userName
  FROM
      smart_operation_sms_template sost
      LEFT JOIN user_screen us ON sost.user_id = us.id
  where 1= 1
  <if test="param.templateName != null and param.templateName != ''">
      and sost.template_name like concat('%',#{param.templateName},'%')
  </if>
  ORDER BY sost.update_time DESC
  </select>
</mapper>