<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.operation.dao.SmartOperationDisturbConfigMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="frequency_switch" jdbcType="BIT" property="frequencySwitch" />
    <result column="frequency_day_limit" jdbcType="INTEGER" property="frequencyDayLimit" />
    <result column="frequency_count_limit" jdbcType="INTEGER" property="frequencyCountLimit" />
    <result column="disturb_switch" jdbcType="BIT" property="disturbSwitch" />
    <result column="disturb_start_time" jdbcType="VARCHAR" property="disturbStartTime" />
    <result column="disturb_end_time" jdbcType="VARCHAR" property="disturbEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, channel, frequency_switch, frequency_day_limit, frequency_count_limit, disturb_switch, 
    disturb_start_time, disturb_end_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_operation_disturb_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from smart_operation_disturb_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from smart_operation_disturb_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from smart_operation_disturb_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into smart_operation_disturb_config (id, channel, frequency_switch, 
      frequency_day_limit, frequency_count_limit, 
      disturb_switch, disturb_start_time, disturb_end_time, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{frequencySwitch,jdbcType=BIT}, 
      #{frequencyDayLimit,jdbcType=INTEGER}, #{frequencyCountLimit,jdbcType=INTEGER}, 
      #{disturbSwitch,jdbcType=BIT}, #{disturbStartTime,jdbcType=VARCHAR}, #{disturbEndTime,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into smart_operation_disturb_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="frequencySwitch != null">
        frequency_switch,
      </if>
      <if test="frequencyDayLimit != null">
        frequency_day_limit,
      </if>
      <if test="frequencyCountLimit != null">
        frequency_count_limit,
      </if>
      <if test="disturbSwitch != null">
        disturb_switch,
      </if>
      <if test="disturbStartTime != null">
        disturb_start_time,
      </if>
      <if test="disturbEndTime != null">
        disturb_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="frequencySwitch != null">
        #{frequencySwitch,jdbcType=BIT},
      </if>
      <if test="frequencyDayLimit != null">
        #{frequencyDayLimit,jdbcType=INTEGER},
      </if>
      <if test="frequencyCountLimit != null">
        #{frequencyCountLimit,jdbcType=INTEGER},
      </if>
      <if test="disturbSwitch != null">
        #{disturbSwitch,jdbcType=BIT},
      </if>
      <if test="disturbStartTime != null">
        #{disturbStartTime,jdbcType=VARCHAR},
      </if>
      <if test="disturbEndTime != null">
        #{disturbEndTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from smart_operation_disturb_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    update smart_operation_disturb_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.frequencySwitch != null">
        frequency_switch = #{record.frequencySwitch,jdbcType=BIT},
      </if>
      <if test="record.frequencyDayLimit != null">
        frequency_day_limit = #{record.frequencyDayLimit,jdbcType=INTEGER},
      </if>
      <if test="record.frequencyCountLimit != null">
        frequency_count_limit = #{record.frequencyCountLimit,jdbcType=INTEGER},
      </if>
      <if test="record.disturbSwitch != null">
        disturb_switch = #{record.disturbSwitch,jdbcType=BIT},
      </if>
      <if test="record.disturbStartTime != null">
        disturb_start_time = #{record.disturbStartTime,jdbcType=VARCHAR},
      </if>
      <if test="record.disturbEndTime != null">
        disturb_end_time = #{record.disturbEndTime,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    update smart_operation_disturb_config
    set id = #{record.id,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      frequency_switch = #{record.frequencySwitch,jdbcType=BIT},
      frequency_day_limit = #{record.frequencyDayLimit,jdbcType=INTEGER},
      frequency_count_limit = #{record.frequencyCountLimit,jdbcType=INTEGER},
      disturb_switch = #{record.disturbSwitch,jdbcType=BIT},
      disturb_start_time = #{record.disturbStartTime,jdbcType=VARCHAR},
      disturb_end_time = #{record.disturbEndTime,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    update smart_operation_disturb_config
    <set>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="frequencySwitch != null">
        frequency_switch = #{frequencySwitch,jdbcType=BIT},
      </if>
      <if test="frequencyDayLimit != null">
        frequency_day_limit = #{frequencyDayLimit,jdbcType=INTEGER},
      </if>
      <if test="frequencyCountLimit != null">
        frequency_count_limit = #{frequencyCountLimit,jdbcType=INTEGER},
      </if>
      <if test="disturbSwitch != null">
        disturb_switch = #{disturbSwitch,jdbcType=BIT},
      </if>
      <if test="disturbStartTime != null">
        disturb_start_time = #{disturbStartTime,jdbcType=VARCHAR},
      </if>
      <if test="disturbEndTime != null">
        disturb_end_time = #{disturbEndTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    update smart_operation_disturb_config
    set channel = #{channel,jdbcType=VARCHAR},
      frequency_switch = #{frequencySwitch,jdbcType=BIT},
      frequency_day_limit = #{frequencyDayLimit,jdbcType=INTEGER},
      frequency_count_limit = #{frequencyCountLimit,jdbcType=INTEGER},
      disturb_switch = #{disturbSwitch,jdbcType=BIT},
      disturb_start_time = #{disturbStartTime,jdbcType=VARCHAR},
      disturb_end_time = #{disturbEndTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into smart_operation_disturb_config
    (id, channel, frequency_switch, frequency_day_limit, frequency_count_limit, disturb_switch, 
      disturb_start_time, disturb_end_time, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.channel,jdbcType=VARCHAR}, #{item.frequencySwitch,jdbcType=BIT}, 
        #{item.frequencyDayLimit,jdbcType=INTEGER}, #{item.frequencyCountLimit,jdbcType=INTEGER}, 
        #{item.disturbSwitch,jdbcType=BIT}, #{item.disturbStartTime,jdbcType=VARCHAR}, 
        #{item.disturbEndTime,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 04 09:41:12 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into smart_operation_disturb_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=VARCHAR}
        </if>
        <if test="'frequency_switch'.toString() == column.value">
          #{item.frequencySwitch,jdbcType=BIT}
        </if>
        <if test="'frequency_day_limit'.toString() == column.value">
          #{item.frequencyDayLimit,jdbcType=INTEGER}
        </if>
        <if test="'frequency_count_limit'.toString() == column.value">
          #{item.frequencyCountLimit,jdbcType=INTEGER}
        </if>
        <if test="'disturb_switch'.toString() == column.value">
          #{item.disturbSwitch,jdbcType=BIT}
        </if>
        <if test="'disturb_start_time'.toString() == column.value">
          #{item.disturbStartTime,jdbcType=VARCHAR}
        </if>
        <if test="'disturb_end_time'.toString() == column.value">
          #{item.disturbEndTime,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>