package com.chinamobile.operation.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 智慧运营-活动与用户群组关联表
 *
 * <AUTHOR>
public class SmartOperationActivityGroup implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private String id;

    /**
     * 活动id
     *
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private String activityId;

    /**
     * 用户群组id
     *
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private String groupId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity_group.id
     *
     * @return the value of supply_chain..smart_operation_activity_group.id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public SmartOperationActivityGroup withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity_group.id
     *
     * @param id the value for supply_chain..smart_operation_activity_group.id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity_group.activity_id
     *
     * @return the value of supply_chain..smart_operation_activity_group.activity_id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public SmartOperationActivityGroup withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity_group.activity_id
     *
     * @param activityId the value for supply_chain..smart_operation_activity_group.activity_id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity_group.group_id
     *
     * @return the value of supply_chain..smart_operation_activity_group.group_id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public String getGroupId() {
        return groupId;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public SmartOperationActivityGroup withGroupId(String groupId) {
        this.setGroupId(groupId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity_group.group_id
     *
     * @param groupId the value for supply_chain..smart_operation_activity_group.group_id
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity_group.create_time
     *
     * @return the value of supply_chain..smart_operation_activity_group.create_time
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public SmartOperationActivityGroup withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity_group.create_time
     *
     * @param createTime the value for supply_chain..smart_operation_activity_group.create_time
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity_group.update_time
     *
     * @return the value of supply_chain..smart_operation_activity_group.update_time
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public SmartOperationActivityGroup withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity_group.update_time
     *
     * @param updateTime the value for supply_chain..smart_operation_activity_group.update_time
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", groupId=").append(groupId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SmartOperationActivityGroup other = (SmartOperationActivityGroup) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getGroupId() == null ? other.getGroupId() == null : this.getGroupId().equals(other.getGroupId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getGroupId() == null) ? 0 : getGroupId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        groupId("group_id", "groupId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Feb 24 15:24:34 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}