package com.chinamobile.operation.pojo.param;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/21 10:44
 */
@Data
public class SaveActivityParam {

    //id为空表示新增，否则表示编辑
    private String id;

    //活动名称
    @NotEmpty(message = "活动名称必填")
    private String name;

    //群组Id列表
    @NotEmpty(message = "群组必选")
    private List<String> groupIdList;

    //触达方式 1-短信 2-公众号 3-小程序
    @NotNull(message = "触达方式必选")
    private Integer type;

    //触发条件 1-单次触发 2-周期触发
    @NotNull(message = "触发条件必选")
    private Integer startCondition;

    //触发内容，对于短信是短信模板ID
    @NotNull(message = "触发内容必填")
    private String content;

    //开始时间，对于单次触发就是触发时间
    @NotNull(message = "开始时间必填")
    private Date startTime;

    //结束时间(单次触发没有结束时间)
    private Date endTime;

    //每次触发的具体时间,HH:mm
    @Pattern(regexp = "^(?:[01][0-9]|2[0-3]):[0-5][0-9]$",message = "时间格式错误,要求HH:mm")
    private String taskTime;

    //每周的哪几天触发，逗号分隔（如1,2代表每周一和周二触发，-1代表周的每一天；单次触发和每天触发没有值）
    private String weekDays;

    //每月的哪几天触发，逗号分隔（如1,2代表每月的1日和2日触发，-1代表月的每一天；单次触发和每天触发没有值）
    private String monthDays;

    //0-保存草稿 1-发起审批
    @NotNull
    @Min(value = 0,message = "status最小值为0")
    @Max(value = 1,message = "status最大值为1")
    private Integer status;



}
