package com.chinamobile.operation.pojo.mapper;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/2/21 10:45
 */
@Data
public class ActivityListDO {

    private String id;

    //活动名称
    private String name;

    //触达方式 1-短信 2-公众号 3-小程序
    private Integer type;

    //触发条件 1-单次触发 2-周期触发
    private Integer startCondition;

    //开始时间，对于单次触发就是触发时间
    private Date startTime;

    //结束时间(单次触发没有结束时间)
    private Date endTime;

    //创建人
    private String createUserName;

    //创建时间
    private Date createTime;

    //状态
    private Integer status;

    private List<String> groupIdList;

}
