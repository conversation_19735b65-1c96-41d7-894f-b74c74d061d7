package com.chinamobile.operation.enums;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/24 10:21
 */
public enum SmartOperationActivityStatusEnum {

    DESIGNING(0,"设计中"),
    AUDITING(1,"审批中"),
    REJECTED(2,"已驳回"),
    PASSED(3,"待发布"),
    RELEASED(4,"待开始"),
    STARTED(5,"已开始"),
    PAUSED(6,"暂停中"),
    END(7,"已结束"),
    CLOSED(8,"已关闭"),

    ;

    public Integer code;
    public String name;

    SmartOperationActivityStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Boolean contains(Integer code){
        SmartOperationActivityStatusEnum[] values = SmartOperationActivityStatusEnum.values();
        for (SmartOperationActivityStatusEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

}
