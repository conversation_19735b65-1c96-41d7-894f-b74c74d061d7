package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.SmartOperationSmsTemplate;
import com.chinamobile.operation.pojo.entity.SmartOperationSmsTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmartOperationSmsTemplateMapper {
    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    long countByExample(SmartOperationSmsTemplateExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int deleteByExample(SmartOperationSmsTemplateExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int insert(SmartOperationSmsTemplate record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int insertSelective(SmartOperationSmsTemplate record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    List<SmartOperationSmsTemplate> selectByExample(SmartOperationSmsTemplateExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    SmartOperationSmsTemplate selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int updateByExampleSelective(@Param("record") SmartOperationSmsTemplate record, @Param("example") SmartOperationSmsTemplateExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int updateByExample(@Param("record") SmartOperationSmsTemplate record, @Param("example") SmartOperationSmsTemplateExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int updateByPrimaryKeySelective(SmartOperationSmsTemplate record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int updateByPrimaryKey(SmartOperationSmsTemplate record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int batchInsert(@Param("list") List<SmartOperationSmsTemplate> list);

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SmartOperationSmsTemplate> list, @Param("selective") SmartOperationSmsTemplate.Column ... selective);
}