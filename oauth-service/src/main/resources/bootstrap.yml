#服务配置
server:
  port: 9798
  forward-headers-strategy: native
  servlet:
    context-path: /mallos/oauth
  # 开启优雅下线
  shutdown: graceful
spring:
  application:
    name: supply-chain-oauth-svc
  profiles:
    active: test
apollo:
#  meta: http://10.12.4.11:8080
  meta: http://10.12.57.1:8080
  autoUpdateInjectedSpringProperties: true
  bootstrap:
    enabled: true
    namespaces: application.yml
app:
  id: supply-chain-oauth
