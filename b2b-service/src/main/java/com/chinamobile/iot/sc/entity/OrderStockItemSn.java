package com.chinamobile.iot.sc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 订单备货产品串号信息
 *
 * <AUTHOR>
public class OrderStockItemSn implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private String id;

    /**
     * 订单号
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private String orderNum;

    /**
     * 备货单号
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private String stockNum;

    /**
     * 产品编码
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private String itemcode;

    /**
     * 串号
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private String sn;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private Date creationDate;

    /**
     * 最后更新时间
     *
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private Date lastUpdateDate;

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.id
     *
     * @return the value of supply_chain..order_stock_item_sn.id
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.id
     *
     * @param id the value for supply_chain..order_stock_item_sn.id
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.order_num
     *
     * @return the value of supply_chain..order_stock_item_sn.order_num
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public String getOrderNum() {
        return orderNum;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withOrderNum(String orderNum) {
        this.setOrderNum(orderNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.order_num
     *
     * @param orderNum the value for supply_chain..order_stock_item_sn.order_num
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.stock_num
     *
     * @return the value of supply_chain..order_stock_item_sn.stock_num
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public String getStockNum() {
        return stockNum;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withStockNum(String stockNum) {
        this.setStockNum(stockNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.stock_num
     *
     * @param stockNum the value for supply_chain..order_stock_item_sn.stock_num
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setStockNum(String stockNum) {
        this.stockNum = stockNum;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.itemCode
     *
     * @return the value of supply_chain..order_stock_item_sn.itemCode
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public String getItemcode() {
        return itemcode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withItemcode(String itemcode) {
        this.setItemcode(itemcode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.itemCode
     *
     * @param itemcode the value for supply_chain..order_stock_item_sn.itemCode
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.sn
     *
     * @return the value of supply_chain..order_stock_item_sn.sn
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public String getSn() {
        return sn;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withSn(String sn) {
        this.setSn(sn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.sn
     *
     * @param sn the value for supply_chain..order_stock_item_sn.sn
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.creation_date
     *
     * @return the value of supply_chain..order_stock_item_sn.creation_date
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withCreationDate(Date creationDate) {
        this.setCreationDate(creationDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.creation_date
     *
     * @param creationDate the value for supply_chain..order_stock_item_sn.creation_date
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * This method returns the value of the database column supply_chain..order_stock_item_sn.last_update_date
     *
     * @return the value of supply_chain..order_stock_item_sn.last_update_date
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public OrderStockItemSn withLastUpdateDate(Date lastUpdateDate) {
        this.setLastUpdateDate(lastUpdateDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_stock_item_sn.last_update_date
     *
     * @param lastUpdateDate the value for supply_chain..order_stock_item_sn.last_update_date
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderNum=").append(orderNum);
        sb.append(", stockNum=").append(stockNum);
        sb.append(", itemcode=").append(itemcode);
        sb.append(", sn=").append(sn);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", lastUpdateDate=").append(lastUpdateDate);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderStockItemSn other = (OrderStockItemSn) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderNum() == null ? other.getOrderNum() == null : this.getOrderNum().equals(other.getOrderNum()))
            && (this.getStockNum() == null ? other.getStockNum() == null : this.getStockNum().equals(other.getStockNum()))
            && (this.getItemcode() == null ? other.getItemcode() == null : this.getItemcode().equals(other.getItemcode()))
            && (this.getSn() == null ? other.getSn() == null : this.getSn().equals(other.getSn()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()))
            && (this.getLastUpdateDate() == null ? other.getLastUpdateDate() == null : this.getLastUpdateDate().equals(other.getLastUpdateDate()));
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderNum() == null) ? 0 : getOrderNum().hashCode());
        result = prime * result + ((getStockNum() == null) ? 0 : getStockNum().hashCode());
        result = prime * result + ((getItemcode() == null) ? 0 : getItemcode().hashCode());
        result = prime * result + ((getSn() == null) ? 0 : getSn().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        result = prime * result + ((getLastUpdateDate() == null) ? 0 : getLastUpdateDate().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 11:28:19 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderNum("order_num", "orderNum", "VARCHAR", false),
        stockNum("stock_num", "stockNum", "VARCHAR", false),
        itemcode("itemCode", "itemcode", "VARCHAR", false),
        sn("sn", "sn", "VARCHAR", false),
        creationDate("creation_date", "creationDate", "TIMESTAMP", false),
        lastUpdateDate("last_update_date", "lastUpdateDate", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Oct 24 11:28:19 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}