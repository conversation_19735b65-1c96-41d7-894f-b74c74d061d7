package com.chinamobile.iot.sc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data
@TableName("order_info")
public class OrderInfo extends Model<OrderInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 渠道ID
     */
    private String orgId;

    /**
     * 订单渠道类型
     */
    private String orgType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单类型(1订单创建（参数全部为必填），2退货（只需填订单号orderNum），3撤销（只需填订单号orderNum）)
     */
    private String orderType;

    /**
     * 订单状态: 1010:客户已付款 1020:商家待发货 1030:商家已发货 1040:客户已收货 1050:订单完成
     * 2010: 客户申请退货 2020: 商家退货处理中 2030:商家确认收货 2040: 退货完成 3010: 客户申请订单撤销 3020: 订单撤销商家处理中
     */
    private String status;

    /**
     * 订单同步状态, 1订单创建未同步，2订单创建同步
     */
    private String syncStatus;

    /**
     * 收货人姓名
     */
    private String rcvContact;

    /**
     * 电话号码
     */
    private String rcvContactPhone;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 收货地址
     */
    private String rcvContactAddress;

    /**
     * 创建时间
     */
    private Date creationDate;

    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;

    /**
     * 营销活动代码
     */
    private String activityCode;

    /**
     * 营销活动名称
     */
    private String activityName;

    /**
     * 预占流水号
     */
    private String bookId;

}
