package com.chinamobile.iot.sc.constant.enums;

public enum OrderReturnStatus {
    CREATE(1, "退货退款单创建"),
    MERCHANT_PROCESSING(2, "商家处理中"),
    RETURN_PROCESSING(3, "买家退货处理中"),
    RECEIPT_PROCESSING(4, "商家收货处理中"),
    REFUND_PROCESSING(5, "商城退款处理中"),
    REFUND_SUCCESS(6, "退款成功"),
    CANCEL(7, "退款取消");

    private Integer code;

    private String name;

    OrderReturnStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        OrderReturnStatus[] statuses = values();
        for (OrderReturnStatus status : statuses) {
            if (status.code == code) {
                return status.name;
            }
        }
        return null;
    }
}
