package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.constant.enums.OrderRefundReason;
import com.chinamobile.iot.sc.constant.enums.OrderRefundType;
import com.chinamobile.iot.sc.constant.enums.OrderRefuseReason;
import com.chinamobile.iot.sc.constant.enums.OrderReturnStatus;
import com.chinamobile.iot.sc.dto.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.mapper.DictOrderInfoMapperExt;
import com.chinamobile.iot.sc.params.OrderReturnListParam;
import com.chinamobile.iot.sc.service.DictOrderService;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.SignUtils;
import com.chinamobile.iot.sc.vo.OrderReturnVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 11:42
 * @description TODO
 */
@Service
public class DictOrderServiceImpl implements DictOrderService {

    @Resource
    private DictOrderInfoMapperExt orderInfoMapperExt;

    @Value("${supply.des.key}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public List<OrderReturnVO> getOrderReturnList(SyncCommonRequest syncCommonRequest) {
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        // sign验签
        SignUtils.checkSign(input, sign);
        OrderReturnListParam param = JSON.parseObject(input, OrderReturnListParam.class);
        List<OrderReturnVO> orderReturnVOS = orderInfoMapperExt.getOrderReturnList(param);
        if (CollectionUtils.isNotEmpty(orderReturnVOS)) {
            for (OrderReturnVO orderReturnVO : orderReturnVOS) {
                orderReturnVO.setStatus(OrderReturnStatus.getNameByCode(orderReturnVO.getStatusCode()));
                orderReturnVO.setRefundType(OrderRefundType.getNameByCode(orderReturnVO.getRefundTypeCode()));
                orderReturnVO.setRefundReason(OrderRefundReason.getNameByCode(orderReturnVO.getRefundReasonCode()));
                orderReturnVO.setRefuseReason(OrderRefuseReason.getNameByCode(orderReturnVO.getRefuseReasonCode()));
                orderReturnVO.setTotalPrice(IOTEncodeUtils.decryptSM4(orderReturnVO.getTotalPrice(), iotSm4Key,iotSm4Iv));
                orderReturnVO.setSpuOfferingClass(SPUOfferingClassEnum.getDescribe(orderReturnVO.getSpuOfferingClass()));
            }
        }
        return orderReturnVOS;
    }
}
