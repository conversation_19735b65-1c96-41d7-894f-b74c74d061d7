package com.chinamobile.iot.sc.mapper;

import com.chinamobile.iot.sc.entity.AftermarketOrderHistory;
import com.chinamobile.iot.sc.entity.AftermarketOrderHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AftermarketOrderHistoryMapper {
    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    long countByExample(AftermarketOrderHistoryExample example);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int deleteByExample(AftermarketOrderHistoryExample example);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int insert(AftermarketOrderHistory record);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int insertSelective(AftermarketOrderHistory record);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    List<AftermarketOrderHistory> selectByExample(AftermarketOrderHistoryExample example);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    AftermarketOrderHistory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int updateByExampleSelective(@Param("record") AftermarketOrderHistory record, @Param("example") AftermarketOrderHistoryExample example);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int updateByExample(@Param("record") AftermarketOrderHistory record, @Param("example") AftermarketOrderHistoryExample example);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int updateByPrimaryKeySelective(AftermarketOrderHistory record);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int updateByPrimaryKey(AftermarketOrderHistory record);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int batchInsert(@Param("list") List<AftermarketOrderHistory> list);

    /**
     *
     * @mbg.generated Thu Jan 05 16:02:31 CST 2023
     */
    int batchInsertSelective(@Param("list") List<AftermarketOrderHistory> list, @Param("selective") AftermarketOrderHistory.Column ... selective);
}