package com.chinamobile.iot.sc.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/18 10:29
 * @description 大音平台订单信息VO
 */
@Data
public class OrderReturnVO {
    /**
     * 退货退款订单号
     */
    private String orderId;

    /**
     * 退款订单状态。orderType= returnOrder退货退款时必填； 1：退货退款单创建（退货退款申请提交，同步本状态） 2：商家处理中（商家审核退换货退款单） 3：买家退货处理中（商家审核通过） 4：商家收货处理中（买家退货） 5：商城退款处理中 6：退款成功 7：退款取消
     */
    private String status;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer statusCode;

    /**
     * 下单时间。
     */
    private Date createTime;

    /**
     * 退款类型。returnOrderStatus=1：退货退款单创建必填； 1：仅退款 2：退货退款 3：换货（暂不启用）
     */
    private String refundType;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer refundTypeCode;

    /**
     * 退款原因。returnOrderStatus=1：退货退款单创建必填； 1：不喜欢/不想要 2：商品错发 3：收到商品与描述不符 4：商品质量问题 5：快递/物流一直未送到 6：其他 7：七天无理由退换货 8：商品信息拍错 9：地址/电话信息填写错误 10：拍多了 11：协商一致退款 12：缺货 13：发货速度不满意
     */
    private String refundReason;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer refundReasonCode;

    /**
     * 退款取消原因。returnOrderStatus =7：退款取消时必填； 1：合作伙伴退款申请审核不通过 2：合作伙伴退货验收不通过 3：审批超时拒绝 4：客户取消退款 5：买家超时未上传物流
     */
    private String refuseReason;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer refuseReasonCode;

    /**
     * 商家驳回原因。refuseReason=1：合作伙伴退款申请审核不通过或2：合作伙伴退货验收不通过时必填； 合作伙伴退货退款申请审核不通过原因或合作伙伴退货验收不通过原因
     */
    private String auditResultReason;

    /**
     * 订单总金额
     * 单位：厘
     */
    private String totalPrice;

    private String spuOfferingName;

    private String spuOfferingClass;

    private String skuOfferingName;

    private String skuOfferingCode;

    private String quantity;

    @JsonIgnore
    @JSONField(serialize = false)
    private Long skuQuantity;
}