package com.chinamobile.iot.sc.constant.enums;

public enum OrderRefundReason {
    DO_NOT_LIKE(1, "不喜欢/不想要"),
    WRONG_DELIVERY(2, "商品错发"),
    NOT_AS_DESCRIBED(3, "收到商品与描述不符"),
    QUALITY_PROBLEM(4, "商品质量问题"),
    NOT_DELIVERED(5, "快递/物流一直未送到"),
    OTHER(6, "其他"),
    SEVEN_DAYS_NO_REASON(7, "七天无理由退换货"),
    WRONG_ORDER(8, "商品信息拍错"),
    WRONG_ADRESS(9, "地址/电话信息填写错误"),
    ORDER_TOO_MANY(10, "拍多了"),
    REFUND_BY_CONSENSUS(11, "协商一致退款"),
    OUT_OF_STOCK(12, "缺货"),
    TOO_SLOW(13, "发货速度不满意");

    private Integer code;

    private String name;

    OrderRefundReason(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        OrderRefundReason[] reasons = values();
        for (OrderRefundReason reason : reasons) {
            if (reason.code == code) {
                return reason.name;
            }
        }
        return null;
    }
}
