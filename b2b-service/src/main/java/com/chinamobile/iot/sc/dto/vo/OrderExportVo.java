package com.chinamobile.iot.sc.dto.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;

/**
 * <AUTHOR> liang
 * @date : 2021/9/1
 *
 **/
@Data
@ExcelTarget("orderExportVo")
public class OrderExportVo {

    /**
     * 采购单号
     */
    @Excel(name = "采购单号")
    private String purchaseOrderNo;

    /**
     * 商城单号
     */
    @Excel(name = "商城单号")
    private String mallOrderNo;

    /**
     * 所属公司
     */
    @Excel(name = "所属公司")
    private String companyOfAffiliation;

    @Excel(name = "申请单位")
    private String  applyUnit;

    @Excel(name = "渠道类型")
    private String channelType;

    @Excel(name = "供应商")
    private String supplier;

    @Excel(name = "机型编码")
    private String modelCode;

    @Excel(name = "机型名称")
    private String typeName;

    @Excel(name = "颜色")
    private String color;

    @Excel(name = "终端类型")
    private String terminalType;

//    @Excel(name = "采购类型")
//    private String procurementType;
//
//    @Excel(name = "营销活动名称")
//    private String marketingActivityName;
//
//    @Excel(name = "营销活动编码")
//    private String marketingActivityCode;
//
//    @Excel(name = "项目")
//    private String project;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "订单数量")
    private String orderNumber;

    @Excel(name = "接收数量")
    private String receiveNumber;

    @Excel(name = "入库数量")
    private String putNumber;

    @Excel(name = "拒收数量")
    private String refuseNumber;

    @Excel(name = "单价(元)")
    private String unitPrice;

//    @Excel(name = "承诺日期")
//    private String commitmentDate;
//
//    @Excel(name = "预计到货日期")
//    private String estimatedDate;
//
//    @Excel(name = "预计到货批次")
//    private String estimatedNo;

    @Excel(name = "小计(元)")
    private String totalPrice;

    @Excel(name = "采购员")
    private String buyer;

    @Excel(name = "创建人")
    private String founder;

    @Excel(name = "创建日期")
    private String createDate;

    @Excel(name = "单据状态")
    private String documentsState;

    @Excel(name = "确认人")
    private String confirmPerson;

    @Excel(name = "确认日期")
    private String confirmDate;

    @Excel(name = "订单类型")
    private String orderType;

    @Excel(name = "结算模式")
    private String settlementPattern;

    @Excel(name = "是否赠机")
    private String zengJi;

//    @Excel(name = "系统登录人电话号码")
//    private String sysPhone;
    @Excel(name = "登录人电话")
    private String sysPhone;

    /**
     * 收货人姓名
     */
    @Excel(name = "收货人姓名")
    private String rcvContact;

    /**
     * 电话号码
     */
    @Excel(name = "收货人联系电话")
    private String rcvContactPhone;


    /**
     * 收货地址
     */
    @Excel(name = "收货人地址")
    private String rcvContactAddress;


//    @Excel(name = "收货人所在省份")
//    private String rcvProvinces;
//
//    @Excel(name = "收货人所在城市")
//    private String rcvCity;

    @Excel(name = "公司名称")
    private String companyName;

    @Excel(name = "支付公司")
    private String payCompany;

    @Excel(name = "支付订单号")
    private String payOrderNo;

    @Excel(name = "交易流水号")
    private String tradingNo;

    @Excel(name = "支付状态")
    private String payStatus;

    @Excel(name = "支付时间")
    private String payTime;

    @Excel(name = "二级分类")
    private String subCategory;

    @Excel(name = "产品标签")
    private String productLab;

}
