package com.chinamobile.iot.sc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 订单备货产品信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data
@TableName("order_stock_item")
public class OrderStockItem extends Model<OrderStockItem> {

    private static final long serialVersionUID=1L;

      private String id;

      /**
     * 订单号
     */
      private String orderNum;

      /**
     * 备货单号
     */
      private String stockNum;

      /**
     * 产品编码
     */
      @TableField("itemCode")
    private String itemCode;

      /**
     * 颜色
     */
      private String color;

      /**
     * 数量
     */
      private String qty;

      /**
     * 创建时间
     */
      private Date creationDate;

      /**
     * 最后更新时间
     */
      private Date lastUpdateDate;

    

}
