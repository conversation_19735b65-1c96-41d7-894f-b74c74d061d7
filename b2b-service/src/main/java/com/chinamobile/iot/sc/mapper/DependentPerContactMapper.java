package com.chinamobile.iot.sc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinamobile.iot.sc.pojo.DependentPerContact;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DependentPerContactMapper extends BaseMapper<DependentPerContact> {

    @Select("<script>" +
            " select contact_id,name,phone,email,company,province,province_name,create_time,update_time,creator " +
            " from dependent_per_contact a where 1=1 " +
            " <if test=' contactNameOrPro !=null and contactNameOrPro != &apos;&apos;'>" +
            " and (a.name like concat('%',#{contactNameOrPro},'%') or a.province_name like concat('%',#{contactNameOrPro},'%') )" +
            " </if>" +
            " order by update_time desc" +
            " limit ${pageIndex}, ${num}" +
            " </script>")
    List<DependentPerContact> page4DepPerContact(String contactNameOrPro, Integer pageIndex, Integer num);

    @Select("<script>" +
            " select count(*) from (select contact_id,name,phone,email,company,province,province_name,create_time,update_time,creator " +
            "  from dependent_per_contact a where 1=1 " +
            " <if test=' contactNameOrPro !=null and contactNameOrPro != &apos;&apos;'>" +
            " and (a.name like concat('%',#{contactNameOrPro},'%') or a.province like concat('%',#{contactNameOrPro},'%') )" +
            " </if>" +
            " ) tmp" +
            " </script>")
    Long pageCount4DepPerContact(String contactNameOrPro);

}
