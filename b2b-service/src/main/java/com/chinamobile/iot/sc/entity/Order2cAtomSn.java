package com.chinamobile.iot.sc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class Order2cAtomSn implements Serializable {
    /**
     * 原子订单ID
     *
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    private String atomOrderId;

    /**
     * 硬件SN号
     *
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    private String sn;

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_sn.atom_order_id
     *
     * @return the value of supply_chain..order_2c_atom_sn.atom_order_id
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public Order2cAtomSn withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_sn.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..order_2c_atom_sn.atom_order_id
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_sn.sn
     *
     * @return the value of supply_chain..order_2c_atom_sn.sn
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public String getSn() {
        return sn;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public Order2cAtomSn withSn(String sn) {
        this.setSn(sn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_sn.sn
     *
     * @param sn the value for supply_chain..order_2c_atom_sn.sn
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", sn=").append(sn);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAtomSn other = (Order2cAtomSn) that;
        return (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getSn() == null ? other.getSn() == null : this.getSn().equals(other.getSn()));
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getSn() == null) ? 0 : getSn().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:46 CST 2022
     */
    public enum Column {
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        sn("sn", "sn", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:46 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}