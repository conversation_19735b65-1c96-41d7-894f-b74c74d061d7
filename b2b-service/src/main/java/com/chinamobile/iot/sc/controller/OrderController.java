package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.dto.*;
import com.chinamobile.iot.sc.dto.vo.OrderBackTerminateDTO;
import com.chinamobile.iot.sc.dto.vo.OrderInfoVo;
import com.chinamobile.iot.sc.dto.vo.OrderStackVo;
import com.chinamobile.iot.sc.dto.vo.PageResponseVo;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.service.OrderService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR> liang
 * @date : 2021/8/27 9:59
 * @description: 订单类
 **/
@RequestMapping("/other")
@RestController
public class OrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 创建订单
     *
     * @return
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_IMPORT})
    @PostMapping("/orderCreate")
    public BaseAnswer<Void> createOrder(@RequestBody @Valid OrderDTO orderDTO) {
        orderService.createOrder(orderDTO);
        return new BaseAnswer<>();
    }

//        @Auth(authCode = {BaseConstant.ORDER_OTHER_IMPORT})
    @PostMapping("/orderCreateInternal")
    public BaseAnswer<Void> orderCreateInternal(@RequestBody @Valid OrderDTO orderDTO) {
        orderService.createOrderInternal(orderDTO);
        return new BaseAnswer<>();
    }

    /**
     * 终端公司发货后可查询
     */
    @PostMapping("/orderStock")
    @Deprecated
    public BaseAnswer<OrderStackVo> orderStock(@RequestBody @Valid OrderStackDTO orderNum) {
        OrderStackVo vo = orderService.orderStock(orderNum);
        return new BaseAnswer<OrderStackVo>().setData(vo);
    }

    /**
     * 客户已收货发送给终端公司
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/orderSaleSn")
    public BaseAnswer<Void> orderSaleSn(@RequestBody @Valid OrderSaleSnDTO orderSaleSnDTO,
                                        @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderService.orderSaleSn(orderSaleSnDTO,userId,ip);
        return new BaseAnswer<>();
    }

    /**
     * 客户已收货发送给终端公司IOT内部调用
     */
    @PostMapping("/orderSaleSnInternal")
    public BaseAnswer<Void> orderSaleSnInternal(@RequestBody @Valid OrderSaleSnDTO orderSaleSnDTO, LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return orderService.orderSaleSnInternal(orderSaleSnDTO,loginIfo4Redis.getUserId(),ip);
//        return new BaseAnswer<>();
    }

    /**
     * 订单结果页查询
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_QUERY})
    @PostMapping("/orderList")
    public BaseAnswer<PageResponseVo<OrderInfoVo>> orderList(@RequestBody OrderListDto orderListDto) {
        PageResponseVo<OrderInfoVo> page = orderService.orderList(orderListDto);
        return new BaseAnswer<PageResponseVo<OrderInfoVo>>().setData(page);
    }

    /**
     * 通过excel文件创建订单
     * @return
     *
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_IMPORT})
    @PostMapping("/uploadOrderExcel")
    public BaseAnswer<Void> orderExcelUpload(@RequestPart("file") MultipartFile file
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderService.orderExcelUpload(file,userId,ip);
        return new BaseAnswer<>();
    }

    /**
     * 导出解密Excel
     *
     */
    @PostMapping("/exportOrderExcel")
    public BaseAnswer<Void> exportOrderExcel(@RequestPart("file") MultipartFile file,HttpServletResponse response) {
        orderService.exportOrderExcel(file,response);
        return new BaseAnswer<>();
    }

    /**
     * 向终端公司发送退货或撤销订单请求
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/backOrCancelOrder")
    public BaseAnswer<Void> backOrCancelOrder(@RequestBody @Valid OrderBackOrCancelDTO orderBackOrCancelDTO
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderService.backOrCancelOrder(orderBackOrCancelDTO,userId,ip);
        return new BaseAnswer<>();
    }

    /**
     * 向终端公司发送退货或撤销订单请求 IOT调用
     */
    @PostMapping("/backOrCancelOrderInternal")
    public BaseAnswer<Void> backOrCancelOrderInternal(@RequestBody @Valid OrderBackOrCancelDTO orderBackOrCancelDTO,LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return orderService.backOrCancelOrderInternal(orderBackOrCancelDTO,loginIfo4Redis.getUserId(),ip);
//        return new BaseAnswer<>();
    }

    /**
     * 向终端公司发送修改订单请求内部os调用
     * @param orderBackOrCancelDTO
     * @param
     * @return
     */
    @PostMapping("/updateReceiveGoodsOrderInternal")
    public BaseAnswer<Void> updateReceiveGoodsOrderInternalInternal(@RequestBody @Valid OrderBackOrCancelDTO orderBackOrCancelDTO,
                                                                    @RequestParam(value = "rcvContactOld") String rcvContactOld,
                                                                    @RequestParam(value = "rcvContactPhoneOld") String rcvContactPhoneOld,
                                                                    @RequestParam(value = "rcvContactAddressOld") String rcvContactAddressOld,
                                                                    @RequestParam(value = "userId") String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        OrderUpdateDTO orderUpdateDTO = new OrderUpdateDTO();
        orderUpdateDTO.setRcvContactOld(rcvContactOld);
        orderUpdateDTO.setRcvContactPhoneOld(rcvContactPhoneOld);
        orderUpdateDTO.setRcvContactAddressOld(rcvContactAddressOld);

        return orderService.updateReceiveGoodsOrderInternal(orderBackOrCancelDTO,orderUpdateDTO,userId,ip);
    }


    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/confirmBackOrder")
    public BaseAnswer<Void> confirmBackOrder(@RequestBody @Valid OrderBackConfirmDTO orderBackConfirmDTO
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        orderService.confirmBackOrder(orderBackConfirmDTO);
        return new BaseAnswer<>();
    }


    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/terminateBackOrder")
    public BaseAnswer<Void> terminateBackOrder(@RequestBody @Valid OrderBackTerminateDTO orderBackTerminateDTO
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderService.terminateBackOrder(orderBackTerminateDTO,userId,ip);
        return new BaseAnswer<>();
    }


    /**
     * 向终端公司发送库存查询请求 IOT调用
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/queryInventoryInternal")
    public BaseAnswer<Qry3rdInventoryResp> queryThirdInventoryInternal(@RequestBody @Valid QueryInventoryDTO queryInventoryDto, String province){
        return orderService.queryInventoryInternal(queryInventoryDto, province);
    }


    /**
     * 向终端公司发送库存预占请求 IOT调用
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/reserveInventoryInternal")
    public BaseAnswer<Void> reserveInventoryInternal(ReserveInventoryDTO reserveDto, String province){
        return orderService.reserveInventoryInternal(reserveDto, province);
    }

    /**
     * 向终端公司发送库存释放请求 IOT调用
     */
    @Auth(authCode = {BaseConstant.ORDER_OTHER_HANDLE})
    @PostMapping("/releaseInventoryInternal")
    public BaseAnswer<Void> releaseInventoryInternal(String bookId, String province){
        return orderService.releaseInventoryInternal(bookId, province);
    }


    @PostMapping("/testhttps")
    public BaseAnswer testHttps(){
        return orderService.testHttps();
    }


    /**
     * 处理同步省侧失败接口
     */
    @PostMapping("/syncB2B")
    public BaseAnswer<Void> syncB2B(String input, String sign, String type){
        return orderService.syncB2B(input,sign, type);
    }


}
