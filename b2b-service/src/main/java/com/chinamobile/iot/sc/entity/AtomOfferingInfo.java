package com.chinamobile.iot.sc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 原子商品
offeringClass=A06时，必传;
 *
 * <AUTHOR>
public class AtomOfferingInfo implements Serializable {
    /**
     * 原子商品主键ID
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String id;

    /**
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String spuId;

    /**
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String spuCode;

    /**
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String skuId;

    /**
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String skuCode;

    /**
     * 原子商品编码
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String offeringCode;

    /**
     * 原子商品名称
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String offeringName;

    /**
     * 原子商品类型
     * S：软件功能费
     * H：硬件（代销) ， O：OneNET独立服务，D：（DICT）产品增值服务包
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String offeringClass;

    /**
     * 当前sku下配置的原子商品数量
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long quantity;

    /**
     * 平台软件编码
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String extSoftOfferingCode;

    /**
     * 终端物料编码
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String extHardOfferingCode;

    /**
     * 原子商品结算单价
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long settlePrice;

    /**
     * 颜色
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String color;

    /**
     * 型号
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String model;

    /**
     * 计量单位
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String unit;

    /**
     * 原子商品销售目录价
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long atomSalePrice;

    /**
     * 库存数量
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long inventory;

    /**
     * 预占库存
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long reserveInventory;

    /**
     * 是否配置库存
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Boolean isInventory;

    /**
     * 合作伙伴ID
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String cooperatorId;

    /**
     * 库存预警值
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Long inventoryThreshold;

    /**
     * 是否短信告警
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Boolean isNotice;

    /**
     * 配置合作伙伴时间
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Date configTime;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Date updateTime;

    /**
     * 是否已经发送短信通知 true 已发送 false未发送
     * 如果添加了库存则修改此状态为未发送
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Boolean notified;

    /**
     * 销售省市/区域
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private String offeringsaleregion;

    /**
     * 删除时间
     *
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private Date deleteTime;

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.id
     *
     * @return the value of supply_chain..atom_offering_info.id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.id
     *
     * @param id the value for supply_chain..atom_offering_info.id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.spu_id
     *
     * @return the value of supply_chain..atom_offering_info.spu_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withSpuId(String spuId) {
        this.setSpuId(spuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.spu_id
     *
     * @param spuId the value for supply_chain..atom_offering_info.spu_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.spu_code
     *
     * @return the value of supply_chain..atom_offering_info.spu_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.spu_code
     *
     * @param spuCode the value for supply_chain..atom_offering_info.spu_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.sku_id
     *
     * @return the value of supply_chain..atom_offering_info.sku_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withSkuId(String skuId) {
        this.setSkuId(skuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.sku_id
     *
     * @param skuId the value for supply_chain..atom_offering_info.sku_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.sku_code
     *
     * @return the value of supply_chain..atom_offering_info.sku_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.sku_code
     *
     * @param skuCode the value for supply_chain..atom_offering_info.sku_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.offering_code
     *
     * @return the value of supply_chain..atom_offering_info.offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getOfferingCode() {
        return offeringCode;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withOfferingCode(String offeringCode) {
        this.setOfferingCode(offeringCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.offering_code
     *
     * @param offeringCode the value for supply_chain..atom_offering_info.offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setOfferingCode(String offeringCode) {
        this.offeringCode = offeringCode;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.offering_name
     *
     * @return the value of supply_chain..atom_offering_info.offering_name
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getOfferingName() {
        return offeringName;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withOfferingName(String offeringName) {
        this.setOfferingName(offeringName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.offering_name
     *
     * @param offeringName the value for supply_chain..atom_offering_info.offering_name
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setOfferingName(String offeringName) {
        this.offeringName = offeringName;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.offering_class
     *
     * @return the value of supply_chain..atom_offering_info.offering_class
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getOfferingClass() {
        return offeringClass;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withOfferingClass(String offeringClass) {
        this.setOfferingClass(offeringClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.offering_class
     *
     * @param offeringClass the value for supply_chain..atom_offering_info.offering_class
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setOfferingClass(String offeringClass) {
        this.offeringClass = offeringClass;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.quantity
     *
     * @return the value of supply_chain..atom_offering_info.quantity
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.quantity
     *
     * @param quantity the value for supply_chain..atom_offering_info.quantity
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.ext_soft_offering_code
     *
     * @return the value of supply_chain..atom_offering_info.ext_soft_offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getExtSoftOfferingCode() {
        return extSoftOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withExtSoftOfferingCode(String extSoftOfferingCode) {
        this.setExtSoftOfferingCode(extSoftOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.ext_soft_offering_code
     *
     * @param extSoftOfferingCode the value for supply_chain..atom_offering_info.ext_soft_offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setExtSoftOfferingCode(String extSoftOfferingCode) {
        this.extSoftOfferingCode = extSoftOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.ext_hard_offering_code
     *
     * @return the value of supply_chain..atom_offering_info.ext_hard_offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getExtHardOfferingCode() {
        return extHardOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withExtHardOfferingCode(String extHardOfferingCode) {
        this.setExtHardOfferingCode(extHardOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.ext_hard_offering_code
     *
     * @param extHardOfferingCode the value for supply_chain..atom_offering_info.ext_hard_offering_code
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setExtHardOfferingCode(String extHardOfferingCode) {
        this.extHardOfferingCode = extHardOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.settle_price
     *
     * @return the value of supply_chain..atom_offering_info.settle_price
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getSettlePrice() {
        return settlePrice;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withSettlePrice(Long settlePrice) {
        this.setSettlePrice(settlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.settle_price
     *
     * @param settlePrice the value for supply_chain..atom_offering_info.settle_price
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setSettlePrice(Long settlePrice) {
        this.settlePrice = settlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.color
     *
     * @return the value of supply_chain..atom_offering_info.color
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getColor() {
        return color;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withColor(String color) {
        this.setColor(color);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.color
     *
     * @param color the value for supply_chain..atom_offering_info.color
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setColor(String color) {
        this.color = color;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.model
     *
     * @return the value of supply_chain..atom_offering_info.model
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.model
     *
     * @param model the value for supply_chain..atom_offering_info.model
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.unit
     *
     * @return the value of supply_chain..atom_offering_info.unit
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.unit
     *
     * @param unit the value for supply_chain..atom_offering_info.unit
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.atom_sale_price
     *
     * @return the value of supply_chain..atom_offering_info.atom_sale_price
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getAtomSalePrice() {
        return atomSalePrice;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withAtomSalePrice(Long atomSalePrice) {
        this.setAtomSalePrice(atomSalePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.atom_sale_price
     *
     * @param atomSalePrice the value for supply_chain..atom_offering_info.atom_sale_price
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setAtomSalePrice(Long atomSalePrice) {
        this.atomSalePrice = atomSalePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.inventory
     *
     * @return the value of supply_chain..atom_offering_info.inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getInventory() {
        return inventory;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withInventory(Long inventory) {
        this.setInventory(inventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.inventory
     *
     * @param inventory the value for supply_chain..atom_offering_info.inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setInventory(Long inventory) {
        this.inventory = inventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.reserve_inventory
     *
     * @return the value of supply_chain..atom_offering_info.reserve_inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getReserveInventory() {
        return reserveInventory;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withReserveInventory(Long reserveInventory) {
        this.setReserveInventory(reserveInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.reserve_inventory
     *
     * @param reserveInventory the value for supply_chain..atom_offering_info.reserve_inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setReserveInventory(Long reserveInventory) {
        this.reserveInventory = reserveInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.is_inventory
     *
     * @return the value of supply_chain..atom_offering_info.is_inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Boolean getIsInventory() {
        return isInventory;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withIsInventory(Boolean isInventory) {
        this.setIsInventory(isInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.is_inventory
     *
     * @param isInventory the value for supply_chain..atom_offering_info.is_inventory
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setIsInventory(Boolean isInventory) {
        this.isInventory = isInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.cooperator_id
     *
     * @return the value of supply_chain..atom_offering_info.cooperator_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..atom_offering_info.cooperator_id
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.inventory_threshold
     *
     * @return the value of supply_chain..atom_offering_info.inventory_threshold
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Long getInventoryThreshold() {
        return inventoryThreshold;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withInventoryThreshold(Long inventoryThreshold) {
        this.setInventoryThreshold(inventoryThreshold);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.inventory_threshold
     *
     * @param inventoryThreshold the value for supply_chain..atom_offering_info.inventory_threshold
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setInventoryThreshold(Long inventoryThreshold) {
        this.inventoryThreshold = inventoryThreshold;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.is_notice
     *
     * @return the value of supply_chain..atom_offering_info.is_notice
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Boolean getIsNotice() {
        return isNotice;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withIsNotice(Boolean isNotice) {
        this.setIsNotice(isNotice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.is_notice
     *
     * @param isNotice the value for supply_chain..atom_offering_info.is_notice
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setIsNotice(Boolean isNotice) {
        this.isNotice = isNotice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.config_time
     *
     * @return the value of supply_chain..atom_offering_info.config_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Date getConfigTime() {
        return configTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withConfigTime(Date configTime) {
        this.setConfigTime(configTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.config_time
     *
     * @param configTime the value for supply_chain..atom_offering_info.config_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setConfigTime(Date configTime) {
        this.configTime = configTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.create_time
     *
     * @return the value of supply_chain..atom_offering_info.create_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.create_time
     *
     * @param createTime the value for supply_chain..atom_offering_info.create_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.update_time
     *
     * @return the value of supply_chain..atom_offering_info.update_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.update_time
     *
     * @param updateTime the value for supply_chain..atom_offering_info.update_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.notified
     *
     * @return the value of supply_chain..atom_offering_info.notified
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Boolean getNotified() {
        return notified;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withNotified(Boolean notified) {
        this.setNotified(notified);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.notified
     *
     * @param notified the value for supply_chain..atom_offering_info.notified
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setNotified(Boolean notified) {
        this.notified = notified;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.offeringSaleRegion
     *
     * @return the value of supply_chain..atom_offering_info.offeringSaleRegion
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public String getOfferingsaleregion() {
        return offeringsaleregion;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withOfferingsaleregion(String offeringsaleregion) {
        this.setOfferingsaleregion(offeringsaleregion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.offeringSaleRegion
     *
     * @param offeringsaleregion the value for supply_chain..atom_offering_info.offeringSaleRegion
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setOfferingsaleregion(String offeringsaleregion) {
        this.offeringsaleregion = offeringsaleregion;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info.delete_time
     *
     * @return the value of supply_chain..atom_offering_info.delete_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public AtomOfferingInfo withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info.delete_time
     *
     * @param deleteTime the value for supply_chain..atom_offering_info.delete_time
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuId=").append(spuId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", skuId=").append(skuId);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", offeringCode=").append(offeringCode);
        sb.append(", offeringName=").append(offeringName);
        sb.append(", offeringClass=").append(offeringClass);
        sb.append(", quantity=").append(quantity);
        sb.append(", extSoftOfferingCode=").append(extSoftOfferingCode);
        sb.append(", extHardOfferingCode=").append(extHardOfferingCode);
        sb.append(", settlePrice=").append(settlePrice);
        sb.append(", color=").append(color);
        sb.append(", model=").append(model);
        sb.append(", unit=").append(unit);
        sb.append(", atomSalePrice=").append(atomSalePrice);
        sb.append(", inventory=").append(inventory);
        sb.append(", reserveInventory=").append(reserveInventory);
        sb.append(", isInventory=").append(isInventory);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", inventoryThreshold=").append(inventoryThreshold);
        sb.append(", isNotice=").append(isNotice);
        sb.append(", configTime=").append(configTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", notified=").append(notified);
        sb.append(", offeringsaleregion=").append(offeringsaleregion);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AtomOfferingInfo other = (AtomOfferingInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSkuId() == null ? other.getSkuId() == null : this.getSkuId().equals(other.getSkuId()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getOfferingCode() == null ? other.getOfferingCode() == null : this.getOfferingCode().equals(other.getOfferingCode()))
            && (this.getOfferingName() == null ? other.getOfferingName() == null : this.getOfferingName().equals(other.getOfferingName()))
            && (this.getOfferingClass() == null ? other.getOfferingClass() == null : this.getOfferingClass().equals(other.getOfferingClass()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getExtSoftOfferingCode() == null ? other.getExtSoftOfferingCode() == null : this.getExtSoftOfferingCode().equals(other.getExtSoftOfferingCode()))
            && (this.getExtHardOfferingCode() == null ? other.getExtHardOfferingCode() == null : this.getExtHardOfferingCode().equals(other.getExtHardOfferingCode()))
            && (this.getSettlePrice() == null ? other.getSettlePrice() == null : this.getSettlePrice().equals(other.getSettlePrice()))
            && (this.getColor() == null ? other.getColor() == null : this.getColor().equals(other.getColor()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getAtomSalePrice() == null ? other.getAtomSalePrice() == null : this.getAtomSalePrice().equals(other.getAtomSalePrice()))
            && (this.getInventory() == null ? other.getInventory() == null : this.getInventory().equals(other.getInventory()))
            && (this.getReserveInventory() == null ? other.getReserveInventory() == null : this.getReserveInventory().equals(other.getReserveInventory()))
            && (this.getIsInventory() == null ? other.getIsInventory() == null : this.getIsInventory().equals(other.getIsInventory()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getInventoryThreshold() == null ? other.getInventoryThreshold() == null : this.getInventoryThreshold().equals(other.getInventoryThreshold()))
            && (this.getIsNotice() == null ? other.getIsNotice() == null : this.getIsNotice().equals(other.getIsNotice()))
            && (this.getConfigTime() == null ? other.getConfigTime() == null : this.getConfigTime().equals(other.getConfigTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getNotified() == null ? other.getNotified() == null : this.getNotified().equals(other.getNotified()))
            && (this.getOfferingsaleregion() == null ? other.getOfferingsaleregion() == null : this.getOfferingsaleregion().equals(other.getOfferingsaleregion()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()));
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSkuId() == null) ? 0 : getSkuId().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getOfferingCode() == null) ? 0 : getOfferingCode().hashCode());
        result = prime * result + ((getOfferingName() == null) ? 0 : getOfferingName().hashCode());
        result = prime * result + ((getOfferingClass() == null) ? 0 : getOfferingClass().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getExtSoftOfferingCode() == null) ? 0 : getExtSoftOfferingCode().hashCode());
        result = prime * result + ((getExtHardOfferingCode() == null) ? 0 : getExtHardOfferingCode().hashCode());
        result = prime * result + ((getSettlePrice() == null) ? 0 : getSettlePrice().hashCode());
        result = prime * result + ((getColor() == null) ? 0 : getColor().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getAtomSalePrice() == null) ? 0 : getAtomSalePrice().hashCode());
        result = prime * result + ((getInventory() == null) ? 0 : getInventory().hashCode());
        result = prime * result + ((getReserveInventory() == null) ? 0 : getReserveInventory().hashCode());
        result = prime * result + ((getIsInventory() == null) ? 0 : getIsInventory().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getInventoryThreshold() == null) ? 0 : getInventoryThreshold().hashCode());
        result = prime * result + ((getIsNotice() == null) ? 0 : getIsNotice().hashCode());
        result = prime * result + ((getConfigTime() == null) ? 0 : getConfigTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getNotified() == null) ? 0 : getNotified().hashCode());
        result = prime * result + ((getOfferingsaleregion() == null) ? 0 : getOfferingsaleregion().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 15:59:34 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuId("spu_id", "spuId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        skuId("sku_id", "skuId", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        offeringCode("offering_code", "offeringCode", "VARCHAR", false),
        offeringName("offering_name", "offeringName", "VARCHAR", false),
        offeringClass("offering_class", "offeringClass", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        extSoftOfferingCode("ext_soft_offering_code", "extSoftOfferingCode", "VARCHAR", false),
        extHardOfferingCode("ext_hard_offering_code", "extHardOfferingCode", "VARCHAR", false),
        settlePrice("settle_price", "settlePrice", "BIGINT", false),
        color("color", "color", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        unit("unit", "unit", "VARCHAR", false),
        atomSalePrice("atom_sale_price", "atomSalePrice", "BIGINT", false),
        inventory("inventory", "inventory", "BIGINT", false),
        reserveInventory("reserve_inventory", "reserveInventory", "BIGINT", false),
        isInventory("is_inventory", "isInventory", "BIT", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        inventoryThreshold("inventory_threshold", "inventoryThreshold", "BIGINT", false),
        isNotice("is_notice", "isNotice", "BIT", false),
        configTime("config_time", "configTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        notified("notified", "notified", "BIT", false),
        offeringsaleregion("offeringSaleRegion", "offeringsaleregion", "VARCHAR", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Oct 19 15:59:34 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}