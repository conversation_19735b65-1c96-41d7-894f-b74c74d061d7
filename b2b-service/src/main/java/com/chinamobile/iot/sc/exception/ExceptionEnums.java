package com.chinamobile.iot.sc.exception;

import com.chinamobile.iot.sc.exceptions.ResponseCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ExceptionEnums {
    PARAMETER_CANNOT_BE_EMPTY(ResponseCode.PARAM_INVALID,"参数不能为空"),
    USER_NOT_EXISTS(ResponseCode.NOT_FOUND,"用户不存在"),
    TEAM_NOT_EXISTS(ResponseCode.NOT_FOUND,"团队不存在"),
    DATA_NOT_EXISTS(ResponseCode.NOT_FOUND,"没有查询到记录"),
    TASK_NOT_EXISTS(ResponseCode.NOT_FOUND,"没有查询到任务"),
    CAN_NOT_DELETE(ResponseCode.FAILURE,"不能删除该记录"),
    CAN_FOUND_LEADER(ResponseCode.FAILURE,"审批领导不存在"),
    APPROVE_FAILED(ResponseCode.FAILURE,"审核失败"),
    CANCEL_FAILED(ResponseCode.FAILURE,"取消失败"),
    NO_IS_REPEATED(ResponseCode.FAILURE,"合同编码已被占用，请重新设置"),
    NOT_AREA_MANAGER(ResponseCode.FAILURE,"仅大区经理可以申请"),
    SUBJECT_BE_FROZEN(ResponseCode.FAILURE,"标的物被冻结，不能申请"),
    STATUS_CAN_NOT_UPDATE(ResponseCode.FAILURE,"该状态不能更新"),
    STATUS_NOT_RIGHT(ResponseCode.FAILURE,"状态不正确"),
    DELETE_FAILED(ResponseCode.OPERATE_FAILED,"删除失败"),
    STATUS_CAN_NOT_BE_EMPTY(ResponseCode.OPERATE_FAILED,"请输入驳回意见"),
    REPLACE_TIME_TOO_EARLY(ResponseCode.PARAM_INVALID,"预设接替时间不能早于当前时间"),
    REPLACE_STATE_ERROR(ResponseCode.PARAM_INVALID,"原合同关联的产品不是在售或余量不足状态"),
    REPLACE_TIME_ARRVIED(ResponseCode.FAILURE,"预设时间已达到，不可更改或删除计划"),
    USER_NOT_MATCH(ResponseCode.PARAM_INVALID,"用户不匹配，请求失败"),
    DATE_FORMAT_ERROR(ResponseCode.PARAM_INVALID,"日期格式错误"),
    NOT_FOUND(ResponseCode.NOT_FOUND,"资源不存在"),
    CONTRACT_STATUS_ERROR(ResponseCode.FAILURE,"合同状态错误"),
    DATABASE_ERROR(ResponseCode.FAILURE,"数据库异常"),
    CONTRACT_FORMAT_ERROR(ResponseCode.FAILURE,"协议格式错误"),
    RECORD_EXISTS(ResponseCode.FAILURE,"记录已存在"),
    EXPORT_FAILED(ResponseCode.EXPORT_FAILED,"导出失败"),
    IMPORT_FAILED(ResponseCode.EXPORT_FAILED,"导入失败"),
    NO_DATA(ResponseCode.NO_DATA,"没有数据"),
    LOGIN_FIRST(ResponseCode.LOGIN_FIRST,"请先登录"),
    SERVICE_NOT_EXISTS(ResponseCode.SERVICE_NOT_EXISTS,"服务不存在"),
    ADD_FAILED(ResponseCode.ADD_FAILED,"添加失败"),
    UPDATE_FAILED(ResponseCode.UPDATE_FAILED,"更新失败"),
    CHECK_STATUS(ResponseCode.CHECK_STATUS,"请检查状态"),
    NO_LEADER(ResponseCode.PARAM_INVALID,"部门外订单必须选择部门领导"),
    AMOUNT_ERROR(ResponseCode.PARAM_INVALID,"申请金额不能大于含税销售金额"),
    FILE_NOT_UPLOAD(ResponseCode.FILE_NOT_UPLOAD,"未上传文件"),
    HAS_OVERDUE(ResponseCode.PARAM_INVALID,"该订单的市场经理有逾期未还款的记录"),
    AMOUT_OVERLIMIT(ResponseCode.PARAM_INVALID,"申请金额不能大于资金池剩余金额减去所有未付款的申请金额之和且不能大于500万元"),
    ORDER_STATUS_ERROR(ResponseCode.PARAM_INVALID,"当订单处于“回款结算中”状态，且K3出入库进度为“已出库”，才可申请供应链资金"),
    NO_POOL_INFO(ResponseCode.FAILURE,"请先设置供应链资金池"),
    POOL_INFO_ERROR(ResponseCode.FAILURE,"资金池异常"),
    POOL_AMOUNT_ERROR(ResponseCode.FAILURE,"总金额必须大于0且大于使用金额和冻结金额之和"),
    ORDER_PROJECT_NOT_MATCH(ResponseCode.PARAM_INVALID,"订单和项目不匹配"),
    CAN_NOT_CHANGE_PRICE(ResponseCode.CAN_NOT_CHANGE_PRICE,"非冻结状态不能改价"),
    PROJECT_REPLACE_NOT_MATCH(ResponseCode.PARAM_INVALID,"产品和采购合同接替计划不匹配"),
    CONTRACT_REPLACE_NOT_MATCH(ResponseCode.PARAM_INVALID,"合同和采购合同接替计划不匹配"),
    NO_TL(ResponseCode.FAILURE,"该用户在数据库中没有TL"),
    NO_GM_MASTER(ResponseCode.FAILURE,"开放平台部缺少总经理用户，请先设置"),
    RECEIVED_AMOUNT_ERROR(ResponseCode.PARAM_INVALID,"回款金额不能超过待回款金额"),
    DUPLICATED_PROJECT_NO(ResponseCode.PARAM_INVALID,"对于未撤销的供应链资金申请，项目编号不可重复"),
    APPYLY_USER_ERROR(ResponseCode.PARAM_INVALID,"资金申请人必须是当前订单的市场经理"),
    APPLY_AMOUNT_ERROR(ResponseCode.PARAM_INVALID,"申请金额必须等于订单项目的采购小计金额"),
    CONTRACT_NO_EXISTS(ResponseCode.FAILURE,"合同编码已存在"),
    ORDER_NOT_EXISTS(ResponseCode.NOT_FOUND,"订单不存在"),
    ASSOCIATED(ResponseCode.OPERATE_FAILED, "已被关联"),
    FILE_ASSOCIATED(ResponseCode.OPERATE_FAILED, "文件已被关联"),
    PERMISSION_DENIED(ResponseCode.AUTHOR_NEED, "没有操作权限"),
    FILE_TYPE_ERROR(ResponseCode.UPLOAD_FAILED,"文件类型错误"),
    ;
    private int code;
    private String msg;
}