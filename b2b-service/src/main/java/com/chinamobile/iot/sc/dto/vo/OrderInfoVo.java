package com.chinamobile.iot.sc.dto.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Author: liang
 * @Date: 2021/8/31 11:29
 */
@Data
public class OrderInfoVo {
    private String id;

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 渠道ID
     */
    private String orgId;

    /**
     * 订单渠道类型
     */
    private String orgType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;


    /**
     * 收货人姓名
     */
    private String rcvContact;

    /**
     * 电话号码
     */
    private String rcvContactPhone;


    /**
     * 收货地址
     */
    private String rcvContactAddress;
    /**
     * 营销活动代码
     */
    private String activityCode;

    /**
     * 营销活动名称
     */
    private String activityName;
    /**
     * color
     */
    private String sku;
    /**
     * 数量
     */
    private String qty;
    /**
     * 产品编码
     */
    private String itemCode;
    /**
     * 供应商
     */
    private String vendorId;
    /**
     * 订单状态
     */
    private String status;
    /**
     * 发货物流单号
     */
    private String logisticsOrder;
    /**
     *  退货物流单号
     */
    private String retLogisticsOrder;
    /**
     *  串号列表
     */
    private List<String> snList;
    /**
     * 省份
     */
    private String provinceCode;

}
