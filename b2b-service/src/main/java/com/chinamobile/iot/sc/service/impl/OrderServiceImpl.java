package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.OrderStatusConstant;
import com.chinamobile.iot.sc.constant.OrderSyncConstant;
import com.chinamobile.iot.sc.constant.OrgIdConstant;
import com.chinamobile.iot.sc.constant.enums.ProvinceEnum;
import com.chinamobile.iot.sc.dto.*;
import com.chinamobile.iot.sc.dto.excel.OrderDecryptionDTO;
import com.chinamobile.iot.sc.dto.excel.OrderExcel;
import com.chinamobile.iot.sc.dto.sync.OrderSyncDTO;
import com.chinamobile.iot.sc.dto.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.dto.sync.back.ReturnAffirmDTO;
import com.chinamobile.iot.sc.dto.vo.*;
import com.chinamobile.iot.sc.entity.*;
import com.chinamobile.iot.sc.entity.iot.ConfirmReturnOrder;
import com.chinamobile.iot.sc.entity.iot.LogisticsInfoRequest;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exception.ServicePowerException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mapper.*;
import com.chinamobile.iot.sc.pojo.DependentPerContact;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.OrderService;
import com.chinamobile.iot.sc.service.excel.OrderDecryptionExcelListener;
import com.chinamobile.iot.sc.service.excel.OrderExcelListener;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.utils.*;
import com.chinamobile.iot.sc.vo.B2BReturn;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.OrderTypeConstant.ORDER_CREATE;
import static com.chinamobile.iot.sc.constant.OrderTypeConstant.ORDER_RETURN;

/**
 * @version 1.0
 * @Author: liang
 * @Date: 2021/8/27 10:07
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, OrderInfo> implements OrderService {
    private static final String IOT_ORDER_PREFIX = "jkiot";
    @Value("${order.url.create}")
    private String orderCreateUrl;
    @Value("${order.url.saleSn}")
    private String orderSaleSnUrl;
    @Value("${order.url.stock}")
    private String orderStockUrl;
    @Value("${sms.order.createTempId:105708}")
    private String createOrderTempId;
    @Value("${sms.order.returnTempId:105748}")
    private String returnOrderTempId;
    @Value("${supply.sign.secret_key}")
    private String secretKey;
    @Resource
    OrderMapper orderMapperExt;
    @Resource
    SubOrderInfoMapper subOrderInfoMapper;
    @Resource
    OrderStockMapper orderStockMapper;
    @Resource
    OrderStockItemMapper orderStockItemMapper;
    @Resource
    OrderStockItemSnMapper orderStockItemSnMapper;
    /*@Resource
    private SnowFlakeKeyUtils snowFlakeKeyUtils;*/
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private SmsFeignClient smsFeignClient;
    @Resource
    private IotFeignClient iotFeignClient;
    @Resource
    private Environment evn;
    @Resource
    DependentPerContactMapper dependentPerContactMapper;

    @Resource
    private LogService logService;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));
    /**
     * 创建订单及子订单
     *
     * @param orderDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrder(OrderDTO orderDTO) {
        //对orderDTO敏感信息进行加密
        orderDTO.setRcvContact(AESUtils.getEncryptString(orderDTO.getRcvContact()));
        orderDTO.setRcvContactAddress(AESUtils.getEncryptString(orderDTO.getRcvContactAddress()));
        orderDTO.setRcvContactPhone(AESUtils.getEncryptString(orderDTO.getRcvContactPhone()));
        OrderInfo orderInfo = new OrderInfo();
//        String orderId = snowFlakeKeyUtils.getId();
        String orderId = BaseServiceUtils.getId();
        BeanUtils.copyProperties(orderDTO, orderInfo);
        orderInfo.setId(orderId);
        orderInfo.setOrderType(ORDER_CREATE);
        orderInfo.setStatus(OrderStatusConstant.ORDER_SEND);
        orderInfo.setSyncStatus(OrderSyncConstant.ORDER_CREATE_SYNC_YES);
        orderInfo.setProvinceCode(orderDTO.getProvinceCode());
//        orderInfo.setProvinceCode(HE_NAN_PROVINCE);
        orderInfo.setCreationDate(new Date());
        orderInfo.setLastUpdateDate(new Date());

        //对于青海、云南，先预占，预占成功了写库，然后再同步终端公司，预占不成功，直接返回错误，该条不写库
//        if (ProvinceEnum.Qinghai.getCode().equals(orderInfo.getProvinceCode())
//            ||ProvinceEnum.Yunnan.getCode().equals(orderInfo.getProvinceCode())) {
//            String bookId = IOT_ORDER_PREFIX + BaseServiceUtils.getId();
//            sendOrderBook(bookId, orderDTO);
//            orderInfo.setBookId(bookId);
//            orderDTO.setBookId(bookId);
//        }

        //有预占的先尝试预占
        String bookId = sendOrderBook(orderDTO);
        log.info("createOrder 预占 bookId = {}",bookId);
        if(!bookId.equals("")){
            orderInfo.setBookId(bookId);
            orderDTO.setBookId(bookId);
        }

        try {
            orderMapperExt.insert(orderInfo);
            log.info("after insert order info = {}", orderInfo);
        } catch (DuplicateKeyException e) {
            throw new BusinessException(BaseErrorConstant.DUPLICATE_KEY, "订单号重复,重复订单号为:" + orderDTO.getOrderNum().substring(5));
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<AddOrderProductDTO> addOrderProductDTOList = orderDTO.getSubOrder();
        for (AddOrderProductDTO dto : addOrderProductDTOList) {
            SubOrderInfo sub = new SubOrderInfo();
            BeanUtils.copyProperties(dto, sub);
//            sub.setId(snowFlakeKeyUtils.getId());
            sub.setId(BaseServiceUtils.getId());
            sub.setOrderNum(orderInfo.getOrderNum());
            sub.setOrderTid(orderId);
            sub.setQty(Integer.valueOf(dto.getQty()));
            sub.setCreationDate(new Date());
            sub.setLastUpdateDate(new Date());
            subOrderInfoMapper.insert(sub);
        }
        //同步给终端公司
//        sendOrderCreate(orderInfo, orderDTO);
        sendOrderCreate(orderDTO);
        // 根据省份发送短息给非独立履约联系人-zyj
        sendSms2DepContact(orderDTO.getOrderNum(), orderDTO.getProvinceCode(), createOrderTempId, ORDER_CREATE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderInternal(OrderDTO orderDTO) {
        //对orderDTO敏感信息进行加密
        orderDTO.setRcvContact(AESUtils.getEncryptString(orderDTO.getRcvContact()));
        orderDTO.setRcvContactAddress(AESUtils.getEncryptString(orderDTO.getRcvContactAddress()));
        orderDTO.setRcvContactPhone(AESUtils.getEncryptString(orderDTO.getRcvContactPhone()));
//        OrderInfo orderInfo = new OrderInfo();
//        String orderId = BaseServiceUtils.getId();
//        BeanUtils.copyProperties(orderDTO, orderInfo);
//        orderInfo.setId(orderId);
//        orderInfo.setOrderType(ORDER_CREATE);
//        orderInfo.setStatus(OrderStatusConstant.ORDER_SEND);
//        orderInfo.setSyncStatus(OrderSyncConstant.ORDER_CREATE_SYNC_YES);
//        orderInfo.setProvinceCode(orderDTO.getProvinceCode());
//        orderInfo.setCreationDate(new Date());
//        orderInfo.setLastUpdateDate(new Date());

        //对于青海，先预占，预占成功了写库，然后再同步终端公司，预占不成功，直接返回错误，该条不写库
//        if (ProvinceEnum.Qinghai.getCode().equals(orderInfo.getProvinceCode()) {
//            String bookId = IOT_ORDER_PREFIX + BaseServiceUtils.getId();
//            sendOrderBook(bookId, orderDTO);
//            orderInfo.setBookId(bookId);
//            orderDTO.setBookId(bookId);
//        }
        //同步给终端公司
//        sendOrderCreate(orderInfo, orderDTO);


        //云南第一期库存先配在我们公司，不用调用库存预占接口
//        String bookId = sendOrderBook(orderDTO);
//        log.info("createOrderInternal bookId = {}",bookId);
//        if(!bookId.equals("")){
//            orderDTO.setBookId(bookId);
//        }


        //同步给终端公司
        sendOrderCreate(orderDTO);

    }

    /**
     * 终端公司发货后可查询
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderStackVo orderStock(OrderStackDTO orderStackDTO) {
        String orderNum = orderStackDTO.getOrderNum();
        String msg = "";
        try {
            String inputParams = JSONObject.toJSONString(orderStackDTO);
            String answer = HttpClientUtil.post(orderStockUrl, inputParams);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            msg = (String) responseDetail.get("msg");
            //返回成功
            if (responseDetail.get("code") == "1") {
                log.info("收到终端公司响应");
                OrderStackVo result = (OrderStackVo) responseDetail.get("result");
                QueryWrapper<OrderStock> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_num", orderNum);
                List<OrderStock> userList = orderStockMapper.selectList(queryWrapper);
                if (userList != null && userList.size() > 0) {
                    return result;
                } else {
                    QueryWrapper<OrderInfo> queryOrderWrapper = new QueryWrapper<>();
                    queryOrderWrapper.eq("order_num", orderNum);
                    List<OrderInfo> orderInfos = orderMapperExt.selectList(queryOrderWrapper);
                    if (orderInfos != null && orderInfos.size() > 0) {
                        for (OrderInfo orderInfo : orderInfos) {
                            orderInfo.setStatus(OrderStatusConstant.ORDER_DELIVERED);
                            orderMapperExt.updateById(orderInfo);
                        }
                    }
                    OrderStock orderStock = new OrderStock();
                    String stockNum = result.getStockNum();
                    BeanUtils.copyProperties(result, orderStock);
//                    String OrderStockId = snowFlakeKeyUtils.getId();
                    String OrderStockId = BaseServiceUtils.getId();
                    orderStock.setId(OrderStockId);
                    String stockDate = result.getStockDate();
                    Date date = DateUtil.parseTime(stockDate);
                    orderStock.setStockDate(date);
                    orderStock.setCreationDate(new Date());
                    orderStock.setLastUpdateDate(new Date());
                    orderStockMapper.insert(orderStock);
                    List<OrderStackItemVo> stockItem = result.getStockItem();
                    if (stockItem != null && stockItem.size() > 0) {
                        for (OrderStackItemVo orderStackItemVo : stockItem) {
                            OrderStockItem orderStockItem = new OrderStockItem();
                            BeanUtils.copyProperties(orderStackItemVo, orderStockItem);
//                            orderStockItem.setId(snowFlakeKeyUtils.getId());
                            orderStockItem.setId(BaseServiceUtils.getId());
                            String itemCode = orderStockItem.getItemCode();
                            orderStockItem.setStockNum(stockNum);
                            orderStockItem.setOrderNum(orderNum);
                            orderStockItem.setCreationDate(new Date());
                            orderStockItem.setLastUpdateDate(new Date());
                            orderStockItemMapper.insert(orderStockItem);
                            List<OrderStackItemSnVo> snList = orderStackItemVo.getSnList();
                            if (snList != null && snList.size() > 0) {
                                for (OrderStackItemSnVo orderStackItemSnVo : snList) {
                                    OrderStockItemSn orderStockItemSn = new OrderStockItemSn();
//                                    orderStockItemSn.setId(snowFlakeKeyUtils.getId());
                                    orderStockItemSn.setId(BaseServiceUtils.getId());
                                    orderStockItemSn.setOrderNum(orderNum);
                                    orderStockItemSn.setItemcode(itemCode);
                                    orderStockItemSn.setSn(orderStackItemSnVo.getSn());
                                    orderStockItem.setCreationDate(new Date());
                                    orderStockItem.setLastUpdateDate(new Date());
                                    orderStockItemSnMapper.insert(orderStockItemSn);
                                }
                            }
                        }
                    }
                    return result;
                }
            } else {
                throw new BusinessException(BaseErrorConstant.ORDER_NOT_EXIST, msg);
            }
        } catch (BusinessException ex) {
            throw new BusinessException(BaseErrorConstant.ORDER_NOT_EXIST, msg);
        } catch (Exception e) {
            log.error("查询订单备货异常", e);
            throw new ServicePowerException(500, "查询终端公司备货信息失败:" + msg);
        }
    }

    /**
     * 客户已收货发送给终端公司（确认收货接口）
     *
     * @param orderSaleSnDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> orderSaleSnInternal(OrderSaleSnDTO orderSaleSnDTO,String userId,String ip) {
//注释入库操作，杨思陈 2022年2月16日10:02:27
//        String orderNum = orderSaleSnDTO.getOrderNum();
//        OrderInfo orderInfo = new OrderInfo();
//        QueryWrapper<OrderInfo> queryOrderWrapper = new QueryWrapper<>();
//        queryOrderWrapper.eq("order_num", orderNum);
//        List<OrderInfo> orderInfos = orderMapperExt.selectList(queryOrderWrapper);
//        if (orderInfos != null && orderInfos.size() > 0) {
//            orderInfo = orderInfos.get(0);
//        }
//        orderInfo.setStatus(OrderStatusConstant.ORDER_FINISH);
//        orderInfo.setLastUpdateDate(new Date());
//        orderMapperExt.updateById(orderInfo);
//注释入库操作，杨思陈 2022年2月16日10:02:27
        orderSaleSnDTO.setOptionType("1");
        String msg = "";
        try {
            orderSaleSnUrl = getUrl("sale", orderSaleSnDTO.getProvinceCode());
            orderSaleSnDTO.setProvinceCode(null);
            String inputParams = JSONObject.toJSONString(orderSaleSnDTO);

//            orderSaleSnUrl = getUrl("sale",orderInfo.getProvinceCode());
            log.info("orderSaleSnInternal 发送请求：{}",inputParams);
            String answer = HttpClientUtil.post(orderSaleSnUrl, inputParams);
            log.info("orderSaleSnInternal 请求返回：{}",answer);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            msg = (String) responseDetail.get("msg");
            if (1 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应, 请求客户已收货发送给终端公司接口成功");
                //记录日志
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO), LogResultEnum.LOG_SUCESS.code,null);
            } else if (-1 == (Integer) responseDetail.get("code") && ((String) responseDetail.get("msg")).contains("订单已经收货")) {
                log.info("该订单已收货,订单号:{}", orderSaleSnDTO.getOrderNum());
                //记录日志
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO),LogResultEnum.LOG_SUCESS.code,null);
            } else {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO), userId, ip, LogResultEnum.LOG_FAIL.code,"请求客户已收货发送给终端公司接口失败");
                });
                throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
            }
        } catch (Exception e) {
            log.error("请求客户已收货发送给终端公司接口失败", e);
            BaseAnswer<Void> answer = new BaseAnswer<>();
            answer.setStateCode(BaseErrorConstant.CONFIRM_REV_ERROR.getStateCode());
            answer.setMessage(msg);
            return answer;
//            throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
        }
        return new BaseAnswer<>();

    }

    /**
     * 客户已收货发送给终端公司（确认收货接口）
     *
     * @param orderSaleSnDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderSaleSn(OrderSaleSnDTO orderSaleSnDTO,String userId,String ip) {
        String orderNum = orderSaleSnDTO.getOrderNum();
        OrderInfo orderInfo = new OrderInfo();
        QueryWrapper<OrderInfo> queryOrderWrapper = new QueryWrapper<>();
        queryOrderWrapper.eq("order_num", orderNum);
        List<OrderInfo> orderInfos = orderMapperExt.selectList(queryOrderWrapper);
        if (orderInfos != null && orderInfos.size() > 0) {
            orderInfo = orderInfos.get(0);
        }
        orderInfo.setStatus(OrderStatusConstant.ORDER_FINISH);
        orderInfo.setLastUpdateDate(new Date());
        orderMapperExt.updateById(orderInfo);
        orderSaleSnDTO.setOptionType("1");
        String msg = "";
        try {
            String inputParams = JSONObject.toJSONString(orderSaleSnDTO);

            orderSaleSnUrl = getUrl("sale", orderInfo.getProvinceCode());

            String answer = HttpClientUtil.post(orderSaleSnUrl, inputParams);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            msg = (String) responseDetail.get("msg");
            if (1 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应, 请求客户已收货发送给终端公司接口成功");
                //记录日志
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO), LogResultEnum.LOG_SUCESS.code,null);
            } else if (-1 == (Integer) responseDetail.get("code") && ((String) responseDetail.get("msg")).contains("订单已经收货")) {
                log.info("该订单已收货,订单号:{}", orderSaleSnDTO.getOrderNum());
                //记录日志
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO),LogResultEnum.LOG_SUCESS.code,null);
            } else {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO), userId, ip, LogResultEnum.LOG_FAIL.code,"请求客户已收货发送给终端公司接口失败:");
                });
                throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
            }
        } catch (Exception e) {
            log.error("请求客户已收货发送给终端公司接口失败", e);
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersSaleSnContentFromOrders(orderSaleSnDTO), userId, ip, LogResultEnum.LOG_FAIL.code,"请求客户已收货发送给终端公司接口失败:");
            });
            throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
        }


    }

    /**
     * 订单结果页查询
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponseVo<OrderInfoVo> orderList(OrderListDto orderListDto) {
        PageResponseVo<OrderInfoVo> pageResponseVo = new PageResponseVo<>();
        Page<OrderInfoVo> page = new Page<>(orderListDto.getPage() != null ? orderListDto.getPage() : 1
                , orderListDto.getLimit() != null ? orderListDto.getLimit() : 10);
        // 若statusList为null或空，则设置为null
        if(ObjectUtils.isNotEmpty(orderListDto) && ObjectUtils.isEmpty(orderListDto.getStatusList())){
            orderListDto.setStatusList(null);
        }
        List<OrderInfoVo> list = orderMapperExt.pageList(page, orderListDto);
        //敏感信息解密
        if (list != null && list.size() > 0) {
            for (OrderInfoVo orderInfoVo : list) {
                String rcvContact = orderInfoVo.getRcvContact();
                if (StringUtils.isNotBlank(rcvContact)) {
                    orderInfoVo.setRcvContact(AESUtils.getDecryptString(rcvContact));
                }
                String rcvContactAddress = orderInfoVo.getRcvContactAddress();
                if (StringUtils.isNotBlank(rcvContactAddress)) {
                    orderInfoVo.setRcvContactAddress(AESUtils.getDecryptString(rcvContactAddress));
                }
                String rcvContactPhone = orderInfoVo.getRcvContactPhone();
                if (StringUtils.isNotBlank(rcvContactPhone)) {
                    orderInfoVo.setRcvContactPhone(AESUtils.getDecryptString(rcvContactPhone));
                }
                String provinceName = getProvinceName(orderInfoVo.getProvinceCode());
                orderInfoVo.setProvinceCode(provinceName);

                //封装sn串号集合
                List<String> snList = new ArrayList<>();
                //通过订单num查询sn串号集合
                String orderNum = orderInfoVo.getOrderNum();
                List<OrderStockItemSn> orderStockItemSns = orderStockItemSnMapper.selectByExample(
                    new OrderStockItemSnExample().createCriteria()
                        .andOrderNumEqualTo(orderNum)
                        .example()
                );
                if(ObjectUtils.isNotEmpty(orderStockItemSns)){
                    orderStockItemSns.forEach(orderStockItemSn -> {
                        snList.add(orderStockItemSn.getSn());
                    });
                }
                //设置sn集合
                orderInfoVo.setSnList(snList);
            }
        }
        pageResponseVo.setList(list);
        pageResponseVo.setTotal(page.getTotal());
        return pageResponseVo;
    }

    /**
     * 通过excel文件创建订单
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderExcelUpload(MultipartFile uploadFile,String userId,String ip) {
        if (uploadFile.isEmpty()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.FILE_NOT_EXIST.getMessage());
            });
            //返回选择文件提示
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        //获取原始的名字  original:最初的，起始的  方法是得到原来的文件名在客户机的文件系统名称
        String oldName = uploadFile.getOriginalFilename();
        if(oldName==null||(!oldName.endsWith(".xlsx")&&!oldName.endsWith(".xls"))){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.FILE_TYPE_ERROR.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        log.info("解析文件:{}", oldName);
        try {
            List<Object> list = EasyExcel.read(uploadFile.getInputStream(), OrderExcel.class, new OrderExcelListener()).sheet(0).headRowNumber(1).doReadSync();
            if(list.size()==0){
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.EMPTY_EXCEL.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
            List<String> orderIds = new ArrayList<>();
            for (Object o : list) {
                OrderExcel orderExcel = (OrderExcel) o;
                String importOrderNum = orderExcel.getOrderNum();
                //订单号添加iot标识前缀
                orderExcel.setOrderNum(IOT_ORDER_PREFIX + orderExcel.getOrderNum());
                //future:这里对写死的内容进行复制，后续可能会需要导入
                orderExcel.setOrgType("10");
                orderExcel.setVendorId("116555");
                //进行保存
                OrderDTO orderDTO = new OrderDTO();
                AddOrderProductDTO addOrderProductDTO = new AddOrderProductDTO();
                BeanUtils.copyProperties(orderExcel, orderDTO);
                BeanUtils.copyProperties(orderExcel, addOrderProductDTO);

//                orderDTO.setProvinceCode(getProvinceCode(orderExcel.getProvinceName()));
                orderDTO.setProvinceCode(getProvinceCode(orderExcel.getRcvContactAddress(),orderExcel.getProvinceName()));

                //这里添加渠道ID
                addOrderProductDTO.setSubOrderNum(orderDTO.getOrderNum());
                orderDTO.setOrgId(getOrgId(orderExcel.getRcvContactAddress()));
                List<AddOrderProductDTO> subOrder = new ArrayList<>();
                subOrder.add(addOrderProductDTO);
                orderDTO.setSubOrder(subOrder);
                createOrder(orderDTO);

                orderIds.add(importOrderNum);
            }

            //记录日志
            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                    B2BLogUtil.importOrdersContentFromOrders(orderIds),LogResultEnum.LOG_SUCESS.code, null);
        } catch (IOException ioe) {
            log.error("读取文件异常，文件名:{},异常描述:{}", oldName, ioe);
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.FILE_PARSE_ERROR.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.RULE_NOT_ALLOW.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        }catch (BusinessException e){
            throw e;
        }
        catch (Exception e){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        "【订单导入】", userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.FILE_PARSE_ERROR.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnAffirm(SyncCommonRequest syncCommonRequest) {
        String input = syncCommonRequest.getInput();
        ObjectMapper objectMapper = new ObjectMapper();
        String sign = syncCommonRequest.getSign();
        // sign验签
        checkSign(input, sign);
        String msg = "转换订单号失败";
        //json 转实体
        try {
            ReturnAffirmDTO addIotOrderDTO = objectMapper.readValue(input, ReturnAffirmDTO.class);
            String orderNum = addIotOrderDTO.getOrderNum();
            //青海流程新增,备用，兼容，null或“”的时候代表验收确认
            String status = addIotOrderDTO.getStatus();
            String remark = addIotOrderDTO.getRemark();
            log.info("returnAffirm orderNum = {}; status = {}; remark = {}",orderNum, status, remark);
            if (StringUtils.isBlank(orderNum)) {
                msg = "订单号不能为空";
                throw new ServicePowerException(500, msg);
            }
            QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("order_num", orderNum);
            List<OrderInfo> orderInfos = orderMapperExt.selectList(queryWrapper);
            if (null != orderInfos && orderInfos.size() > 0) {
                log.info("returnAffirm orderInfo != null 来自手工导入合同履约订单处理;");
                for (OrderInfo orderInfo : orderInfos) {
                    //青海退货流程(不通过)展示
                    if (null != status) {
                        orderInfo.setStatus(OrderStatusConstant.ORDER_BACK_INSECTFAILED);
                    } else {
                        orderInfo.setStatus(OrderStatusConstant.ORDER_BACK_RECEIVED);
                    }
                    orderInfo.setLastUpdateDate(new Date());
                    orderMapperExt.updateById(orderInfo);
                }
            } else {
                //增加对不存在的订单,可能来自IOT的同步订单。同步审核结果到IOT
                //删除前缀
                log.info("returnAffirm orderInfo = null 来自IOT的同步订单处理;");
                ConfirmReturnOrder confirmReturnOrder = new ConfirmReturnOrder();
                confirmReturnOrder.setOrderId(orderNum.substring(IOT_ORDER_PREFIX.length()));
                confirmReturnOrder.setReceiptReason(status == null ? null : remark);
                confirmReturnOrder.setReceiptResult(status == null ? "0" : "1");
                try {
                    BaseAnswer<Void> voidBaseAnswer = iotFeignClient.confirmReturnOrderInternal(confirmReturnOrder);
                    if (Objects.equals(voidBaseAnswer.getStateCode(), ExcepStatus.getSuccInstance().getStateCode())) {
                        log.info("记录IOT回调内容:{}", voidBaseAnswer);
                    } else {
                        log.error("验收同步到IOT失败，请求内容:{},失败返回结果:{}", confirmReturnOrder, voidBaseAnswer);
                        msg = "同步IOT异常";
                        throw new ServicePowerException(500, msg);
                    }

                    return;
                } catch (Exception e) {
                    log.error("验收同步到IOT失败，请求内容:{},失败描述:{}", confirmReturnOrder, e.toString());
                    msg = "同步IOT异常";
                    throw new ServicePowerException(500, msg);
                }
            }
            List<SubOrderInfo> subOrderInfos = subOrderInfoMapper.selectList(new QueryWrapper<SubOrderInfo>().lambda()
                    .eq(SubOrderInfo::getOrderNum, orderNum));
            if (ObjectUtils.isNotEmpty(subOrderInfos)) {
                for (SubOrderInfo subOrderInfo : subOrderInfos) {
                    if(null!=status){
                        subOrderInfo.setStatus(OrderStatusConstant.ORDER_BACK_INSECTFAILED);
                    }else{
                        subOrderInfo.setStatus(OrderStatusConstant.ORDER_BACK_RECEIVED);
                    }
                    subOrderInfo.setLastUpdateDate(new Date());
                    subOrderInfoMapper.updateById(subOrderInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServicePowerException(500, msg);
        }

    }

    /**
     * 终端公司发货后将数据推送给OS
     * 判断是否在订单中，没有则同步到IOT
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliveryAffirm(SyncCommonRequest syncCommonRequest) {
        String orderNum = null;
        String type = "1";
        String orderStatus = OrderStatusConstant.ORDER_DELIVERED;
        String input = syncCommonRequest.getInput();
        ObjectMapper objectMapper = new ObjectMapper();
        String sign = syncCommonRequest.getSign();
        log.info("deliveryAffirm input = {}",input);
        // sign验签
        checkSign(input, sign);

        //json 转实体
        try {
            OrderStackVo result = objectMapper.readValue(input, OrderStackVo.class);
            log.info("B2B终端公司同步订单数据为：{}", JSONObject.toJSONString(result));
            orderNum = result.getOrderNum();
            type = result.getType();
            if (StringUtils.isBlank(orderNum)) {
                throw new ServicePowerException(500, "订单号不能为空");
            }
            if (StringUtils.isBlank(result.getStockDate())) {
                throw new ServicePowerException(500, "备货时间不能为空");
            }
            //判断订单是否在订单表中，没有则同步给IOT
            QueryWrapper<OrderInfo> orderInfoQueryWrapper = new QueryWrapper<>();
            orderInfoQueryWrapper.eq("order_num", result.getOrderNum());
            if (orderMapperExt.selectOne(orderInfoQueryWrapper) == null) {
                LogisticsInfoRequest logisticsInfoRequest = new LogisticsInfoRequest();
                logisticsInfoRequest.setId("jkiot");
                //将订单前缀去掉
                logisticsInfoRequest.setOrderId(result.getOrderNum().substring(IOT_ORDER_PREFIX.length()));
                List<String> snList = new ArrayList<>();
                result.getStockItem().forEach(x -> x.getSnList().forEach(sn -> snList.add(sn.getSn())));
                logisticsInfoRequest.setSnList(snList);
                List<LogisticsInfoRequest.LogisticsMsg> logisticsMsgs = new ArrayList<>();
                LogisticsInfoRequest.LogisticsMsg logisticsMsg = new LogisticsInfoRequest.LogisticsMsg();
                logisticsMsg.setLogisCode(result.getLogisticsNum());
                logisticsMsg.setSupplierName(result.getSupplierName());
                logisticsMsgs.add(logisticsMsg);
                logisticsInfoRequest.setLogisticsMsgs(logisticsMsgs);
                try {
                    BaseAnswer<Void> voidBaseAnswer = iotFeignClient.logisticsSyncInternal(logisticsInfoRequest);
                    if (Objects.equals(voidBaseAnswer.getStateCode(), ExcepStatus.getSuccInstance().getStateCode()) || "30011".equals(voidBaseAnswer.getStateCode())) {
                        log.info("记录IOT回调内容:{}", voidBaseAnswer);
                        return;
                    } else {
                        log.error("发货同步到IOT失败，请求内容:{},失败返回结果:{}", logisticsInfoRequest, voidBaseAnswer);
                        String msg = voidBaseAnswer.getMessage();
                        throw new ServicePowerException(500, msg);
                    }
                } catch (Exception e) {
                    log.error("b2b 同步 IOT 失败,失败原因:{}", e.toString());
                    String msg = "同步IOT异常";
                    if (e instanceof ServicePowerException) {
                        throw e;
                    }
                    throw new ServicePowerException(500, msg);

                }
            }
            QueryWrapper<OrderStock> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("order_num", orderNum);
            List<OrderStock> userList = orderStockMapper.selectList(queryWrapper);
            if (userList != null && userList.size() > 0 && !"2".equals(type)) {
                //已存在信息
                log.info("订单号：{}，备货信息已存在！", orderNum);
            } else {
                if ("2".equals(type)) {
                    orderStatus = OrderStatusConstant.ORDER_BACK_DELIVERED;
                } else {
                    //换货流程没有，暂不考虑
                    orderStatus = OrderStatusConstant.ORDER_DELIVERED;
                }
                QueryWrapper<OrderInfo> queryOrderWrapper = new QueryWrapper<>();
                queryOrderWrapper.eq("order_num", orderNum);
                List<OrderInfo> orderInfos = orderMapperExt.selectList(queryOrderWrapper);
                log.info("查询到的订单信息为：{}", JSONObject.toJSONString(orderInfos));
                if (orderInfos != null && orderInfos.size() > 0) {
                    for (OrderInfo orderInfo : orderInfos) {
                        orderInfo.setStatus(orderStatus);
                        orderInfo.setLastUpdateDate(new Date());
                        orderMapperExt.updateById(orderInfo);
                    }
                }
                // 更新子订单物流单号
                List<SubOrderInfo> subOrderInfos = subOrderInfoMapper.selectList(new QueryWrapper<SubOrderInfo>()
                        .lambda()
                        .eq(SubOrderInfo::getOrderNum, orderNum));
                if (ObjectUtils.isNotEmpty(subOrderInfos)) {
                    for (SubOrderInfo subOrderInfo : subOrderInfos) {
                        subOrderInfo.setLogisticsOrder(result.getLogisticsNum());
                        subOrderInfo.setStatus(orderStatus);
                        subOrderInfo.setLastUpdateDate(new Date());
                        subOrderInfoMapper.updateById(subOrderInfo);
                    }
                }
                if(!"2".equals(type)){
                    //保持原有逻辑
                    OrderStock orderStock = new OrderStock();
                    String stockNum = result.getStockNum();
                    BeanUtils.copyProperties(result, orderStock);
//                String OrderStockId = snowFlakeKeyUtils.getId();
                    String OrderStockId = BaseServiceUtils.getId();
                    orderStock.setId(OrderStockId);
                    String stockDate = result.getStockDate();
                    Date date = DateUtil.parseTime(stockDate);
                    orderStock.setStockDate(date);
                    orderStock.setCreationDate(new Date());
                    orderStock.setLastUpdateDate(new Date());
                    orderStockMapper.insert(orderStock);
                    List<OrderStackItemVo> stockItem = result.getStockItem();
                    if (stockItem != null && stockItem.size() > 0) {
                        for (OrderStackItemVo orderStackItemVo : stockItem) {
                            OrderStockItem orderStockItem = new OrderStockItem();
                            BeanUtils.copyProperties(orderStackItemVo, orderStockItem);
//                        orderStockItem.setId(snowFlakeKeyUtils.getId());
                            orderStockItem.setId(BaseServiceUtils.getId());
                            String itemCode = orderStockItem.getItemCode();
                            orderStockItem.setStockNum(stockNum);
                            orderStockItem.setOrderNum(orderNum);
                            orderStockItem.setCreationDate(new Date());
                            orderStockItem.setLastUpdateDate(new Date());
                            orderStockItemMapper.insert(orderStockItem);
                            List<OrderStackItemSnVo> snList = orderStackItemVo.getSnList();
                            if (snList != null && snList.size() > 0) {
                                for (OrderStackItemSnVo orderStackItemSnVo : snList) {
                                    OrderStockItemSn orderStockItemSn = new OrderStockItemSn();
//                                orderStockItemSn.setId(snowFlakeKeyUtils.getId());
                                    orderStockItemSn.setId(BaseServiceUtils.getId());
                                    orderStockItemSn.setOrderNum(orderNum);
                                    orderStockItemSn.setItemcode(itemCode);
                                    orderStockItemSn.setSn(orderStackItemSnVo.getSn());
                                    orderStockItem.setCreationDate(new Date());
                                    orderStockItem.setLastUpdateDate(new Date());
                                    orderStockItemSnMapper.insert(orderStockItemSn);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("B2B同步发货信息失败！订单号为：{}，错误信息：{}", orderNum, e.getMessage());
            throw new ServicePowerException(500, "接收发货信息失败");
        }
    }

    /**
     * 向终端公司发送退货或撤销订单请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> backOrCancelOrderInternal(OrderBackOrCancelDTO orderBackOrCancelDTO,String userId,String ip) {
        String provinceCode = orderBackOrCancelDTO.getProvinceCode();
        orderBackOrCancelDTO.setProvinceCode(null);
        String type = orderBackOrCancelDTO.getType();
        orderCreateUrl = getUrl("order", provinceCode);
        //退货
        if ("2".equals(StringUtils.trim(type))) {
            if (StringUtils.isBlank(orderBackOrCancelDTO.getRetLogisticsOrder())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.RET_CODE_IS_NULL.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.RET_CODE_IS_NULL);
            }
        }
        String msg = "";
        try {
            String inputParams = JSONObject.toJSONString(orderBackOrCancelDTO);
            String answer = HttpClientUtil.post(orderCreateUrl, inputParams);
            log.info("请求终端公司同步退货订单接口, 响应信息{}", answer);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            if (1 == (Integer) responseDetail.get("code") || 200 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应, 同步退货订单信息成功");
            } else {
                msg = (String) responseDetail.get("msg");
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.SYNC_B2b_FAILED.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步退货订单到终端公司失败:" + msg);
            }
            log.info("向终端公司同步退货订单完成");
            //记录日志
            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                    B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO),LogResultEnum.LOG_SUCESS.code, null);
        } catch (Exception e) {
            log.error("同步退货订单到终端公司异常", e);
//            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步订单到终端公司失败:" + msg);
            BaseAnswer<Void> answer = new BaseAnswer<>();
            answer.setStateCode(BaseErrorConstant.SYNC_B2b_FAILED.getStateCode());
            answer.setMessage(msg);
            return answer;
        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Void> updateReceiveGoodsOrderInternal(OrderBackOrCancelDTO orderBackOrCancelDTO,OrderUpdateDTO orderUpdateDTO, String userId, String ip) {
        String provinceCode = orderBackOrCancelDTO.getProvinceCode();
        orderBackOrCancelDTO.setProvinceCode(null);
        String type = orderBackOrCancelDTO.getType();
        log.info("updateReceiveGoodsOrderInternal userId:{}",userId);
        orderCreateUrl = getUrl("order", provinceCode);
        //修改
        if ("6".equals(StringUtils.trim(type))) {
            if (StringUtils.isBlank(orderBackOrCancelDTO.getRcvContact()) && StringUtils.isBlank(orderBackOrCancelDTO.getRcvContactPhone())
           && StringUtils.isBlank(orderBackOrCancelDTO.getRcvContactAddress())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.GOODS_ORDER_MAIN.code,
                            B2BLogUtil.OrderUpdateReceiveGoodsContentFromOrders(orderBackOrCancelDTO,orderUpdateDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.RET_CODE_IS_NULL.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.RET_CODE_IS_NULL,"修改信息参数不能为空");
            }
        }else {
            throw new BusinessException(BaseErrorConstant.RET_CODE_IS_NULL,"操作类型参数错误");
        }
        String msg = "";
        try {
            String inputParams = JSONObject.toJSONString(orderBackOrCancelDTO);
            String answer = HttpClientUtil.post(orderCreateUrl, inputParams);
            log.info("请求终端公司同步orderReal修改订单接口, 响应信息{}", answer);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            if (1 == (Integer) responseDetail.get("code") || 200 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应,orderReal 同步修改订单信息成功");
            } else {
                msg = (String) responseDetail.get("msg");
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.GOODS_ORDER_MAIN.code,
                            B2BLogUtil.OrderUpdateReceiveGoodsContentFromOrders(orderBackOrCancelDTO,orderUpdateDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.SYNC_B2b_FAILED.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步修改订单orderReal到终端公司失败:" + msg);
            }
            log.info("向终端公司orderReal同步修改订单完成");
            //记录日志
    /*        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.GOODS_ORDER_MAIN.code,
                    B2BLogUtil.OrderUpdateReceiveGoodsContentFromOrders(orderBackOrCancelDTO,orderUpdateDTO),LogResultEnum.LOG_SUCESS.code, null);*/
        } catch (Exception e) {
            log.error("同步orderReal修改单到终端公司异常", e);
            BaseAnswer<Void> answer = new BaseAnswer<>();
            answer.setStateCode(BaseErrorConstant.SYNC_B2b_FAILED.getStateCode());
            answer.setMessage(msg);
            return answer;
        }
        return new BaseAnswer<>();
    }

    /**
     * 向终端公司发送退货或撤销订单请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void backOrCancelOrder(OrderBackOrCancelDTO orderBackOrCancelDTO,String userId,String ip) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        String orderNum = orderBackOrCancelDTO.getOrderNum();
        String type = orderBackOrCancelDTO.getType();
        queryWrapper.eq("order_num", orderNum);
        QueryWrapper<SubOrderInfo> subQueryWrapper = new QueryWrapper<>();
        subQueryWrapper.eq("order_num", orderNum);
        List<OrderInfo> userList = orderMapperExt.selectList(queryWrapper);
        List<SubOrderInfo> subOrderInfos = subOrderInfoMapper.selectList(subQueryWrapper);
        if (null == userList || userList.size() == 0) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.ORDER_NOT_FIND.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.ORDER_NOT_FIND);
        }
        //这里实际订单号唯一就应该是第一个就行了 开始设计就应该把订单号作为唯一主键
        OrderInfo orderInfo = userList.get(0);

        orderCreateUrl = getUrl("order", orderInfo.getProvinceCode());

        //退货
        if ("2".equals(StringUtils.trim(type))) {
            if (StringUtils.isBlank(orderBackOrCancelDTO.getRetLogisticsOrder())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.RET_CODE_IS_NULL.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.RET_CODE_IS_NULL);
            }
            //退货必须在确认收获时才能点击
            //目前商城没有用户确认操作按钮，os点了确认收货才会弹出退货按钮，所以这里去掉不影响
//            if(!orderInfo.getStatus().equals(OrderStatusConstant.ORDER_FINISH)){
//                throw new BusinessException(BaseErrorConstant.RET_STATUS_ERROR);
//            }
            orderInfo.setStatus(OrderStatusConstant.APPLY_BACK);
            orderInfo.setLastUpdateDate(new Date());
            orderMapperExt.updateById(orderInfo);
            subOrderInfos.forEach(x -> {
                x.setRetLogisticsOrder(orderBackOrCancelDTO.getRetLogisticsOrder());
                subOrderInfoMapper.updateById(x);
            });
        }
        //撤销
        if ("3".equals(StringUtils.trim(type))) {
            if (!orderInfo.getStatus().equals(OrderStatusConstant.ORDER_SEND)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.CANCEL_STATUS_ERROR.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.CANCEL_STATUS_ERROR);
            }
            orderInfo.setStatus(OrderStatusConstant.APPLY_CANCEL);
            orderInfo.setLastUpdateDate(new Date());
            orderMapperExt.updateById(orderInfo);
        }
        String msg = "";
        try {
            String inputParams = JSONObject.toJSONString(orderBackOrCancelDTO);
            String answer = HttpClientUtil.post(orderCreateUrl, inputParams);
            log.info("请求终端公司同步订单接口, 响应信息{}", answer);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            if (1 == (Integer) responseDetail.get("code") || 200 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应, 同步订单信息成功");
                //退货
                if ("2".equals(StringUtils.trim(type))) {
                    orderInfo.setStatus(OrderStatusConstant.BACK_DEAL);
                    orderInfo.setLastUpdateDate(new Date());
                    orderMapperExt.updateById(orderInfo);
                }
                //撤销
                if ("3".equals(StringUtils.trim(type))) {
                    orderInfo.setStatus(OrderStatusConstant.CANCEL_DEAL);
                    orderInfo.setLastUpdateDate(new Date());
                    orderMapperExt.updateById(orderInfo);
                }
                // 退货或撤销提醒短信-zyj
                sendSms2DepContact(orderInfo.getOrderNum(), orderInfo.getProvinceCode(), returnOrderTempId, ORDER_RETURN);
            } else {
                msg = (String) responseDetail.get("msg");
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.SYNC_B2b_FAILED.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步订单到终端公司失败:" + msg);
            }
            log.info("向终端公司同步订单完成");

            //记录日志
            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                    B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO),LogResultEnum.LOG_SUCESS.code, null);
        } catch (Exception e) {
            log.error("同步订单到终端公司异常", e);
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersBackOrCancelContentFromOrders(orderBackOrCancelDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.SYNC_B2b_FAILED.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步订单到终端公司失败:" + msg);
        }

    }


    /**
     * 文件解密
     *
     * @param uploadFile
     * @param response
     */
    @Override
    public void exportOrderExcel(MultipartFile uploadFile, HttpServletResponse response) {
        if (uploadFile.isEmpty()) {
            //返回选择文件提示
            throw new ServicePowerException(500, "请选择上传文件");
        }

        String oldName = uploadFile.getOriginalFilename();
        if(oldName==null||(!oldName.endsWith(".xlsx")&&!oldName.endsWith(".xls"))){
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        log.info("-----------文件原始的名字【" + oldName + "】-----------");
        List<OrderExportVo> listVo = new ArrayList<>();
        try {
            List<Object> list = EasyExcel.read(uploadFile.getInputStream(), OrderDecryptionDTO.class, new OrderDecryptionExcelListener()).sheet(0).headRowNumber(1).doReadSync();
            for (Object o : list) {
                OrderDecryptionDTO dto = (OrderDecryptionDTO) o;
                OrderExportVo vo = new OrderExportVo();
                BeanUtils.copyProperties(dto, vo);
                String rcvContact = vo.getRcvContact();
                if (StringUtils.isNotBlank(rcvContact)) {
                    String decryptString = AESUtils.getDecryptString(rcvContact);
                    vo.setRcvContact(decryptString);
                }
                String rcvContactAddress = vo.getRcvContactAddress();
                if (StringUtils.isNotBlank(rcvContactAddress)) {
                    String decryptString = AESUtils.getDecryptString(rcvContactAddress);
                    vo.setRcvContactAddress(decryptString);
                }
                String rcvContactPhone = vo.getRcvContactPhone();
                if (StringUtils.isNotBlank(rcvContactPhone)) {
                    String decryptString = AESUtils.getDecryptString(rcvContactPhone);
                    vo.setRcvContactPhone(decryptString);
                }
                //进行保存
                listVo.add(vo);
            }
        } catch (Exception ioe) {
            log.error("读取解析文件失败,文件名:{}", oldName);
            throw new BusinessException(BaseErrorConstant.DECRYPT_FAILED);
        }
        try {
            ExcelUtils.exportExcel(listVo, "IOT订单收货信息解密",
                    "",
                    OrderExportVo.class,
                    "order-export-",
                    response);
        } catch (Exception e) {
            throw new BusinessException(BaseErrorConstant.DECRYPT_FAILED);
            // e.printStackTrace();
        }
    }

    @Override
    public void confirmBackOrder(OrderBackConfirmDTO orderBackConfirmDTO) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        String orderNum = orderBackConfirmDTO.getOrderNum();
        queryWrapper.eq("order_num", orderNum);
        QueryWrapper<SubOrderInfo> subQueryWrapper = new QueryWrapper<>();
        subQueryWrapper.eq("order_num", orderNum);
        List<OrderInfo> userList = orderMapperExt.selectList(queryWrapper);
        if (null == userList || userList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.ORDER_NOT_FIND);
        }
        //这里订单号是唯一的所以第一条数据即查询结果
        OrderInfo orderInfo=userList.get(0);
        if(!orderInfo.getStatus().equals(OrderStatusConstant.ORDER_BACK_RECEIVED)){
            throw new BusinessException(BaseErrorConstant.ERROR_STATUS_BACK);
        }
        orderInfo.setStatus(OrderStatusConstant.ORDER_BACK_FINISH);
        orderInfo.setLastUpdateDate(new Date());
        orderMapperExt.updateById(orderInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateBackOrder(OrderBackTerminateDTO orderBackTerminateDTO,String userId,String ip) {
        //对于青海流程，循环退货时，人工点击退货按钮结束：1、订单串号销售接口，同步至省侧，更改订单发货中状态；2、更新自己数据库
        String msg = "";
        String orderNum = orderBackTerminateDTO.getOrderNum();
        OrderSaleSnDTO orderSaleSnDTO = new OrderSaleSnDTO();
        orderSaleSnDTO.setOrderNum(orderNum);
        orderSaleSnDTO.setOptionType("1");
        try {
            String inputParams = JSONObject.toJSONString(orderSaleSnDTO);
            //目前只有青海
            orderSaleSnUrl = getUrl("sale",ProvinceEnum.Qinghai.getCode());
            String answer = HttpClientUtil.post(orderSaleSnUrl, inputParams);
            JSONObject responseDetail = JSONObject.parseObject(answer);
            msg = (String) responseDetail.get("msg");
            if (1 == (Integer) responseDetail.get("code") || 0 == (Integer) responseDetail.get("code")) {
                log.info("收到终端公司响应, 请求客户已收货发送给终端公司接口成功");
            } else if (-1==(Integer) responseDetail.get("code") && ((String) responseDetail.get("msg")).contains("订单已经收货")) {
                log.info("该订单已收货,订单号:{}", orderSaleSnDTO.getOrderNum());
            } else {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                            B2BLogUtil.ordersBackTerminateContentFromOrders(orderBackTerminateDTO), userId, ip, LogResultEnum.LOG_FAIL.code,"请求客户已收货发送给终端公司接口失败");
                });
                throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
            }
        } catch (Exception e) {
            log.error("请求客户已收货发送给终端公司接口失败", e);
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersBackTerminateContentFromOrders(orderBackTerminateDTO), userId, ip, LogResultEnum.LOG_FAIL.code,"请求客户已收货发送给终端公司接口失败");
            });
            throw new BusinessException(BaseErrorConstant.CONFIRM_REV_ERROR, "请求客户已收货发送给终端公司接口失败:" + msg);
        }

        //更新数据库订单状态
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_num", orderNum);
        QueryWrapper<SubOrderInfo> subQueryWrapper = new QueryWrapper<>();
        subQueryWrapper.eq("order_num", orderNum);
        List<OrderInfo> userList = orderMapperExt.selectList(queryWrapper);
        if (null == userList || userList.size() == 0) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                        B2BLogUtil.ordersBackTerminateContentFromOrders(orderBackTerminateDTO), userId, ip, LogResultEnum.LOG_FAIL.code,BaseErrorConstant.ORDER_NOT_FIND.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.ORDER_NOT_FIND);
        }
        //这里订单号是唯一的所以第一条数据即查询结果
        OrderInfo orderInfo=userList.get(0);
//        if(!orderInfo.getStatus().equals(OrderStatusConstant.ORDER_BACK_RECEIVED)){
//            throw new BusinessException(BaseErrorConstant.ERROR_STATUS_BACK);
//        }
        //这里不用判断，强制更新状态
        orderInfo.setStatus(OrderStatusConstant.ORDER_BACK_FINISH);
        orderInfo.setLastUpdateDate(new Date());
        orderMapperExt.updateById(orderInfo);

        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                OrderManageOperateEnum.CONTRACT_PERFORMANCE_ORDER.code,
                B2BLogUtil.ordersBackTerminateContentFromOrders(orderBackTerminateDTO),LogResultEnum.LOG_SUCESS.code, null);
    }

    /**
     * 发送终端公司创建订单同步请求
     *
     * @param orderDTO
     */
    private void sendOrderCreate(OrderDTO orderDTO) {
        String msg = "";
        try {
            // 构造请求参数
            //Thread.sleep(3000);
            OrderSyncDTO orderSyncDTO = new OrderSyncDTO();
            orderSyncDTO.setType("1");
            BeanUtils.copyProperties(orderDTO, orderSyncDTO);

            ObjectMapper objectMapper = new ObjectMapper();
            String inputParams = objectMapper.writeValueAsString(orderSyncDTO);
            HashMap<String, String> requestMap = Maps.newHashMap();
            requestMap.put("input", inputParams);
            requestMap.put("version", "1.0");
            requestMap.put("subscriber", "IOT");
            requestMap.put("sign", SignUtils.getSign(inputParams));
            B2BReturn b2BReturn;
            orderCreateUrl = getUrl("order", orderDTO.getProvinceCode());
            try {
                //log.info("加密sign-secretKey：{}", secretKey);
                String res = HttpClientUtil.post(orderCreateUrl, inputParams);
                log.info("同步订单到省侧 返回 res = {}",res);
                //解析B2B返回结果
                b2BReturn = JSONObject.parseObject(res, B2BReturn.class);
                log.info("调用B2B订单创建接口，返回结果：{}", JSONObject.toJSONString(b2BReturn));
            } catch (Exception e) {
                log.error("调用B2B订单创建接口失败，请求url：{}，请求参数：{}，返回错误：{}", orderCreateUrl, JSONObject.toJSONString(requestMap), e);
                throw new BusinessException(BaseErrorConstant.REQUEST_B2B_FAILED);
            }
            if (ObjectUtils.isNotEmpty(b2BReturn) && (0 == b2BReturn.getCode() || 1 == b2BReturn.getCode())) {
                log.info("向终端公司同步订单完成");
            } else if (ObjectUtils.isNotEmpty(b2BReturn) && -2 == b2BReturn.getCode() && b2BReturn.getMsg().contains("已存在")) {
                log.info("订单已同步过，订单号:{}", orderSyncDTO.getOrderNum());
            } else {
                msg = b2BReturn.getMsg();
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步订单到终端公司失败:" + msg);
            }
        } catch (Exception e) {
            log.error("同步订单到终端公司异常", e);
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "同步订单到终端公司失败:" + msg);
        }
    }


    /**
     * 青海流程：发送终端公司预占库存
     *
     * @param orderDTO
     */
    private String sendOrderBook(OrderDTO orderDTO) {
        log.info("ORDER DTO = {}",orderDTO);
        String msg = "";
        String bookUrl = "";
        //new add
        String bookId = IOT_ORDER_PREFIX + BaseServiceUtils.getId();
        switch(orderDTO.getProvinceCode()){
            case "Qinghai":
                bookUrl = evn.getProperty("order.url.addBookQinghai");
                break;
            case "Yunnan":
                bookUrl = evn.getProperty("order.url.bookStockYunnan");
                break;
            default:
                break;
        }
        log.info("add book url = {}",bookUrl);
        if(bookUrl==null||bookUrl.equals("")){
            return "";
        }
        //new add end

        try {
            // 构造请求参数
            BookProductDTO bookProductDTO = new BookProductDTO();
            bookProductDTO.setBookId(bookId);
            bookProductDTO.setQuantity(orderDTO.getSubOrder().get(0).getQty());
            bookProductDTO.setMaterialNum(orderDTO.getSubOrder().get(0).getItemCode());

            ObjectMapper objectMapper = new ObjectMapper();
            String inputParams = objectMapper.writeValueAsString(bookProductDTO);

            HashMap<String, Object> requestMap = Maps.newHashMap();
            requestMap.put("input", inputParams);
            requestMap.put("version", "1.0");
            requestMap.put("subscriber", "IOT");
            requestMap.put("sign", SignUtils.getSign(inputParams));
            log.info("add book requestMap = {}",requestMap);
            B2BReturn b2BReturn;


//            String bookUrl = evn.getProperty("order.url.addBookQinghai");

            log.info("inputParams = {}",inputParams);
            try {
                String res = HttpClientUtil.post(bookUrl, inputParams);
                log.info("return res = {}",res);
                //解析B2B返回结果
                b2BReturn = JSONObject.parseObject(res, B2BReturn.class);
                log.info("调用B2B预占库存接口，返回结果：{}", JSONObject.toJSONString(b2BReturn));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("调用B2B预占库存接口失败，请求url：{}，请求参数：{}", bookUrl, JSONObject.toJSONString(requestMap));
                throw new BusinessException(BaseErrorConstant.REQUEST_B2B_FAILED);
            }
            if (ObjectUtils.isNotEmpty(b2BReturn) && (0 == b2BReturn.getCode() || 1 == b2BReturn.getCode())) {
                log.info("向终端公司预占订单完成");
            } else {
                msg = b2BReturn.getMsg();
                throw new BusinessException(BaseErrorConstant.BOOK_B2B_FAILED, "预占库存失败:" + msg);
            }
        } catch (Exception e) {
            log.error("预占库存流程异常", e);
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "预占库存失败:" + msg);
        }
        return bookId;
    }






    /**
     * 根据收获地址推导渠道ID
     *
     * @param rcvAddress 收获地址
     * @return
     */
    private static String getOrgId(String rcvAddress) {
        String city = rcvAddress.substring(rcvAddress.indexOf("省") + 1, rcvAddress.indexOf("市"));
        String orgId=OrgIdConstant.orgIdMap.get(city);
        String province = rcvAddress.substring(0,rcvAddress.indexOf("省"));
        if(ProvinceEnum.Henan.getName().equals(province)){
            if(StringUtils.isBlank(orgId)){
                throw new BusinessException(BaseErrorConstant.PARSE_CITY_ERROR);
            }
        }else{
            orgId = OrgIdConstant.defOrgId;
        }
        return orgId;
    }

    /**
     * 省份名称转代码入库
     *
     * @param standerAddr 收货地址
     * @param pName 表格填写的省份
     * @return
     */

    private String getProvinceCode(String standerAddr,String pName){
        String provinceName = pName;
        int index = standerAddr.indexOf("省");
        if(index!=-1){
            provinceName = standerAddr.substring(0,index);
        }else{
            provinceName = pName;
        }
        if(!provinceName.equals(pName)){
            throw new BusinessException(BaseErrorConstant.IMPORT_CONFLICT_PROVINCE);
        }
        if(provinceName.contains(ProvinceEnum.Qinghai.getName())){
            return ProvinceEnum.Qinghai.getCode();
        }else if(provinceName.contains(ProvinceEnum.Henan.getName())){
            return ProvinceEnum.Henan.getCode();
        }
        throw new BusinessException(BaseErrorConstant.IMPORT_UNSUPPORT_PROVINCE);
    }


    /**
     * 根据省代码获取省分名称
     *
     * @param provinceCode 省份代码
     * @return
     */
    private String getProvinceName(String provinceCode){
        if(ProvinceEnum.Henan.getCode().equals(provinceCode)){
            return ProvinceEnum.Henan.getName();
        }else if(ProvinceEnum.Qinghai.getCode().equals(provinceCode)){
            return ProvinceEnum.Qinghai.getName();
        }
        return ProvinceEnum.Unknown.getName();
    }


    /**
     * 获取不同省相应url
     *
     * @param type url类型
     * @param code 省份代码
     * @return
     */
    private String getUrl(String type, String code){
        log.info("b2bService 获取请求 url type = {}， code = {}",type, code);
        String url = "";
        if(ProvinceEnum.Henan.getCode().equals(code)){
            if("order".equals(type)){
                url = evn.getProperty("order.url.create");
            }else if("sale".equals(type)){
                url = evn.getProperty("order.url.saleSn");
            }
        }else if(ProvinceEnum.Qinghai.getCode().equals(code)){
            if("order".equals(type)){
                url = evn.getProperty("order.url.createQinghai");
            }else if("sale".equals(type)){
                url = evn.getProperty("order.url.saleSnQinghai");
            }
        }else if(ProvinceEnum.Yunnan.getCode().equals(code)){
            if("order".equals(type)){
                url = evn.getProperty("order.url.createYunnan");
            }else if("sale".equals(type)){
                url = evn.getProperty("order.url.saleSnYunnan");
            }
        }
        return url;
    }



    /**
     *
     * @param paramStr 内容
     * @param reqSign 鉴权签名
     */
    private void checkSign(String paramStr, String reqSign) {
        if (StringUtils.isBlank(paramStr)) {
            throw new ServicePowerException(500, "input输入信息不能为空");
        }
        if (StringUtils.isBlank(reqSign)) {
            throw new ServicePowerException(500, "sign签名信息不能为空");
        }
        //根据内容获得 鉴权签名
        String sign = SignUtils.getSign(paramStr);
        if (!sign.equals(reqSign)) {
            throw new ServicePowerException(500, "签名检验失败");
        }
        //md5 加密校验通过
    }
    /**
     *@Description: 发送导入订单提醒短信
     *@return: void
     *@Author: zyj
     */
    public void sendSms2DepContact(String orderNum, String provinceCode, String templateId, String operType){
        Msg4Request request = new Msg4Request();
        List<String> mobiles = new ArrayList<>();
        try {
            // 根据省份查询履约联系人
            List<DependentPerContact> dependentPerContacts = dependentPerContactMapper.selectList(new QueryWrapper<DependentPerContact>().lambda()
                    .eq(DependentPerContact::getProvince, provinceCode));
            // 履约联系人判空处理
            if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(dependentPerContacts)){
                dependentPerContacts.forEach( contact ->{
                    mobiles.add(contact.getPhone());
                });
                List<String> phones = mobiles.stream().distinct().collect(Collectors.toList());
                Map<String,String> message = new HashMap<>();
                message.put("orderId",orderNum);
                request.setMobiles(phones);
                request.setMessage(message);
                request.setTemplateId(templateId);
                BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
                log.info("远程调用短信服务，返回结果：{}", JSON.toJSONString(messageAnswer));
            }
            log.info("发送订单提醒短信-订单号：{}，操作类型：{}，省份code：{}，发送手机号：{}",orderNum, operType, provinceCode
                    , JSONObject.toJSONString(mobiles));
        }catch (Exception e){
            log.error("发送短信服务异常！异常描述:{}", e.getMessage());
        }
    }


    @Override
    public BaseAnswer<Qry3rdInventoryResp> queryInventoryInternal(QueryInventoryDTO qryInventorydto, String province) {
        B2BReturn b2BReturn;
        BaseAnswer<Qry3rdInventoryResp> baseAnswer = new BaseAnswer<Qry3rdInventoryResp>();
        String msg = "";
        try {
            // 构造请求参数
//            ReserveInventoryDTO reserveDto = new ReserveInventoryDTO();
//            reserveDto.setBookId(bookId);
//            reserveDto.setMaterials(materials);


            ObjectMapper objectMapper = new ObjectMapper();
            String inputParams = objectMapper.writeValueAsString(qryInventorydto);

            HashMap<String, Object> requestMap = Maps.newHashMap();
            requestMap.put("input", inputParams);
            requestMap.put("version", "1.0");
            requestMap.put("subscriber", "IOT");
            requestMap.put("sign", SignUtils.getSign(inputParams));
            log.info("reserveInventoryInternal requestMap = {}",requestMap);


            String url = "";
            switch(province){
                case "Qinghai":
                    url = evn.getProperty("order.url.queryStockYnnan");
                    break;
                case "Yunnan":
                    url = evn.getProperty("order.url.queryStockYnnan");
                    break;
                default:
                    break;
            }


            log.info("inputParams = {}",inputParams);
            try {
                String res = HttpClientUtil.post(url, inputParams);
                log.info("return res = {}",res);
                //解析B2B返回结果
                b2BReturn = JSONObject.parseObject(res, B2BReturn.class);
                log.info("调用B2B预占库存接口，返回结果：{}", JSONObject.toJSONString(b2BReturn));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("调用B2B预占库存接口失败，请求url：{}，请求参数：{}", url, JSONObject.toJSONString(requestMap));
                throw new BusinessException(BaseErrorConstant.REQUEST_B2B_FAILED);
            }
            if (ObjectUtils.isNotEmpty(b2BReturn) && (0 == b2BReturn.getCode() || 1 == b2BReturn.getCode())) {
                log.info("向终端公司预占订单完成");
                Object rob = b2BReturn.getResult();
                log.info("queryInventoryInternal 接口返回 rob = {}",rob.toString());
                Qry3rdInventoryResp qryResp = JSONObject.parseObject(JSONObject.toJSON(rob).toString(), Qry3rdInventoryResp.class);
                baseAnswer.setData(qryResp);

            } else {
                msg = b2BReturn.getMsg();
                throw new BusinessException(BaseErrorConstant.BOOK_B2B_FAILED, "预占库存失败:" + msg);
            }
        } catch (Exception e) {
            log.error("预占库存流程异常", e);
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "预占库存失败:" + msg);
        }



//        Qry3rdInventoryResp resp = new Qry3rdInventoryResp();
        return baseAnswer;

    }


    @Override
    public BaseAnswer<Void> reserveInventoryInternal(ReserveInventoryDTO reserveInventoryDTO, String province) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<Void>();
        B2BReturn b2BReturn;
        String msg = "";
        try {
            // 构造请求参数
//            ReserveInventoryDTO reserveDto = new ReserveInventoryDTO();
//            reserveDto.setBookId(bookId);
//            reserveDto.setMaterials(materials);


            ObjectMapper objectMapper = new ObjectMapper();
            String inputParams = objectMapper.writeValueAsString(reserveInventoryDTO);

            HashMap<String, Object> requestMap = Maps.newHashMap();
            requestMap.put("input", inputParams);
            requestMap.put("version", "1.0");
            requestMap.put("subscriber", "IOT");
            requestMap.put("sign", SignUtils.getSign(inputParams));
            log.info("reserveInventoryInternal requestMap = {}",requestMap);


            String url = "";
            switch(province){
                case "Qinghai":
                    url = evn.getProperty("order.url.addBookQinghai");
                    break;
                case "Yunnan":
                    url = evn.getProperty("order.url.bookStockYunnan");
                    break;
                default:
                    break;
            }


            log.info("inputParams = {}",inputParams);
            try {
                String res = HttpClientUtil.post(url, inputParams);
                log.info("return res = {}",res);
                //解析B2B返回结果
                b2BReturn = JSONObject.parseObject(res, B2BReturn.class);
                log.info("调用B2B预占库存接口，返回结果：{}", JSONObject.toJSONString(b2BReturn));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("调用B2B预占库存接口失败，请求url：{}，请求参数：{}", url, JSONObject.toJSONString(requestMap));
                throw new BusinessException(BaseErrorConstant.REQUEST_B2B_FAILED);
            }
            if (ObjectUtils.isNotEmpty(b2BReturn) && (0 == b2BReturn.getCode() || 1 == b2BReturn.getCode())) {
                log.info("向终端公司预占订单完成");
            } else {
                msg = b2BReturn.getMsg();
                throw new BusinessException(BaseErrorConstant.BOOK_B2B_FAILED, "预占库存失败:" + msg);
            }
        } catch (Exception e) {
            log.error("预占库存流程异常", e);
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "预占库存失败:" + msg);
        }
        return baseAnswer;
    }

    @Override
    public BaseAnswer<Void> releaseInventoryInternal(String bookId, String province) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<Void>();
        String url = "";
        String msg = "";
        B2BReturn b2BReturn;

        try{

            switch(province){
                case "Qinghai":
                    url = evn.getProperty("order.url.releaseStockQinghai");
                    break;
                case "Yunnan":
                    url = evn.getProperty("order.url.releaseStockYunnan");
                    break;
                default:
                    break;
            }

            ReleaseInventoryDTO releaseDTO = new ReleaseInventoryDTO();
            releaseDTO.setBookId(bookId);

            ObjectMapper objectMapper = new ObjectMapper();
            String inputParams = objectMapper.writeValueAsString(releaseDTO);

            HashMap<String, Object> requestMap = Maps.newHashMap();
            requestMap.put("input", inputParams);
            requestMap.put("version", "1.0");
            requestMap.put("subscriber", "IOT");
            requestMap.put("sign", SignUtils.getSign(inputParams));
            log.info("reserveInventoryInternal requestMap = {}",requestMap);

            log.info("inputParams = {}",inputParams);
            try {
                String res = HttpClientUtil.post(url, inputParams);
                log.info("return res = {}",res);
                //解析B2B返回结果
                b2BReturn = JSONObject.parseObject(res, B2BReturn.class);
                log.info("调用B2B预占库存接口，返回结果：{}", JSONObject.toJSONString(b2BReturn));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("调用B2B预占库存接口失败，请求url：{}，请求参数：{}", url, JSONObject.toJSONString(requestMap));
                throw new BusinessException(BaseErrorConstant.REQUEST_B2B_FAILED);
            }
            if (ObjectUtils.isNotEmpty(b2BReturn) && (0 == b2BReturn.getCode() || 1 == b2BReturn.getCode())) {
                log.info("向终端公司预占订单完成");
            } else {
                msg = b2BReturn.getMsg();
                throw new BusinessException(BaseErrorConstant.BOOK_B2B_FAILED, "预占库存失败:" + msg);
            }

        }catch (Exception e) {
            log.error("释放库存流程异常", e);
            throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "释放库存流程异常:" + msg);
        }
        return baseAnswer;
    }

    @Override
    public BaseAnswer<Void> testHttps() {
        String url = "https://yn.10086.cn/mall/mallapi/card/sales/check.do";
        try{
            String res = HttpClientUtil.postTest(url);
            log.info("test Https return res = {}",res);
        }catch(Exception e){
            e.printStackTrace();
        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Void> syncB2B(String input, String sign, String type) {
        ObjectMapper objectMapper = new ObjectMapper();
        if("0".equals(type)){
            //订单接口
            OrderDTO orderDTO;
            try{
                orderDTO = objectMapper.readValue(input, OrderDTO.class);
            }catch(Exception e){
                e.printStackTrace();
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "参数转换错误");
            }
            log.info("syncB2B orderDTO = {}",orderDTO.toString());
//            createOrderInternal(orderDTO);
        }else if("1".equals(type)){
            //订单核销接口
            OrderSaleSnDTO orderSaleSnDTO;
            ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            String ip = requestAttr.getRequest().getHeader(Constant.IP);
            String userId = requestAttr.getRequest().getHeader(Constant.HEADER_KEY_USER_ID);
            try{
                orderSaleSnDTO = objectMapper.readValue(input, OrderSaleSnDTO.class);
            }catch(Exception e){
                e.printStackTrace();
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "参数转换错误");
            }
            log.info("syncB2B orderSaleSnDTO = {}",orderSaleSnDTO.toString());
//            orderSaleSnInternal(orderSaleSnDTO, userId, ip);
        }else if("2".equals(type)){
            OrderBackOrCancelDTO orderBackOrCancelDTO;
            ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            String ip = requestAttr.getRequest().getHeader(Constant.IP);
            String userId = requestAttr.getRequest().getHeader(Constant.HEADER_KEY_USER_ID);
            try{
                orderBackOrCancelDTO = objectMapper.readValue(input, OrderBackOrCancelDTO.class);
            }catch(Exception e){
                e.printStackTrace();
                throw new BusinessException(BaseErrorConstant.SYNC_B2b_FAILED, "参数转换错误");
            }
            log.info("syncB2B orderBackOrCancelDTO = {}",orderBackOrCancelDTO.toString());
//            backOrCancelOrderInternal(orderBackOrCancelDTO, userId, ip);
        }
        return new BaseAnswer<>();
    }


}
