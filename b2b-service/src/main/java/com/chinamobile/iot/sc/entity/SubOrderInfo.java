package com.chinamobile.iot.sc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 子订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data
@TableName("sub_order_info")
public class SubOrderInfo extends Model<SubOrderInfo> {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 主订单号
     */
    private String orderNum;

    /**
     * 主订单表id
     */
    private String orderTid;

    /**
     *  订单类型 1创建2退货3撤销 预留字段
     */
    private String orderType;

    /**
     * 订单行id
     */
    @TableField("subOrderNum")
    private String subOrderNum;

    /**
     * 供应商
     */
    @TableField("vendorId")
    private String vendorId;

    /**
     * 产品编码
     */
    @TableField("itemCode")
    private String itemCode;

    /**
     * 颜色
     */
    private String sku;

    /**
     * 单价
     */
    @TableField("unitPrice")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 发货物流单号
     */
    @TableField("logisticsOrder")
    private String logisticsOrder;

    /**
     * 退货物流单号
     */
    @TableField("retLogisticsOrder")
    private String retLogisticsOrder;

    /**
     * 预留字段 方便后期可能出现单个子订单退货状态
     * 子订单状态: 1010:客户已付款 1020:商家待发货 1030:商家已发货 1040:客户已收货 1050:订单完成
     * 2010: 客户申请退货 2020: 商家退货处理中 2030:商家确认收货 2040: 退货完成 3010: 客户申请订单撤销 3020: 订单撤销商家处理中
     */
    private String status;

    /**
     * 创建时间
     */
    private Date creationDate;

    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;


}
