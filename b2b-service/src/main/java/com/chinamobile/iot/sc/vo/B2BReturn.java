package com.chinamobile.iot.sc.vo;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @package: com.chinamobile.iot.sc.vo
 * @ClassName: B2BReturn
 * @description: B2B终端调用返回结果
 * @author: zyj
 * @create: 2021/9/7 11:46
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class B2BReturn {
    //返回状态码
    private Integer code;
    //返回信息
    private String msg;
    //返回结果
    private Object result;

}
