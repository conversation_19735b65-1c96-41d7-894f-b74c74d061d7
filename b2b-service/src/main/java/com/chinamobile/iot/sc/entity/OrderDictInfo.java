package com.chinamobile.iot.sc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * DICT、OneNET和OnePark商品退货退款订单信息
 *
 * <AUTHOR>
public class OrderDictInfo implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String id;

    /**
     * 订单类型 businessOrder：订购类订单returnOrder：退货退款订单
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String orderType;

    /**
     * 退货退款订单号
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String orderId;

    /**
     * 退货退款单关联的订购订单号。orderType=returnOrder退货退款时必填，传值为退货退款单关联的订购订单；
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String associatedOrder;

    /**
     * 订购订单状态。orderType= businessOrder订购时必填； 1：待付款 2：待发货 3：待收货  4、交易完成（订单确认收货后，同步本状态） 5：交易关闭（付款取消或订单退款完成，同步本状态） 6：已付款 7：订单计收（订单确认收货7天）
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Integer businessOrderStatus;

    /**
     * 退款订单状态。orderType= returnOrder退货退款时必填； 1：退货退款单创建（退货退款申请提交，同步本状态） 2：商家处理中（商家审核退换货退款单） 3：买家退货处理中（商家审核通过） 4：商家收货处理中（买家退货） 5：商城退款处理中 6：退款成功 7：退款取消
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Integer returnOrderStatus;

    /**
     * 订单状态变更时间。格式：yyyyMMddhhmmss； 时间为IOT商城内部状态变更时间，与实际操作可能会有少许误差，如：已付款为客户客户付款成功，IOT商城更新订单的时间，非客户付款发起时间
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Date orderStatusTime;

    /**
     * 退款类型。returnOrderStatus=1：退货退款单创建必填； 1：仅退款 2：退货退款 3：换货（暂不启用）
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Integer refundType;

    /**
     * 退款原因。returnOrderStatus=1：退货退款单创建必填； 1：不喜欢/不想要 2：商品错发 3：收到商品与描述不符 4：商品质量问题 5：快递/物流一直未送到 6：其他 7：七天无理由退换货 8：商品信息拍错 9：地址/电话信息填写错误 10：拍多了 11：协商一致退款 12：缺货 13：发货速度不满意
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Integer refundReason;

    /**
     * 退款取消原因。returnOrderStatus =7：退款取消时必填； 1：合作伙伴退款申请审核不通过 2：合作伙伴退货验收不通过 3：审批超时拒绝 4：客户取消退款 5：买家超时未上传物流
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private Integer refuseReason;

    /**
     * 物流信息。businessOrderStatus=3：待收货时必填，即商家发货时录入的物流； returnOrderStatus=4：商家收货处理中必填，即客户退货退款时录入的物流； 格式：物流供应商编码#物流单号，若存在多笔物流以&分隔，OS可拆分解析入库， V256为单笔物流长度；  物流供应商编码枚举值如下： yuantong:圆通速递	 yunda:韵达快递	 zhongtong:中通快递	 shentong:申通快递	 huitongkuaidi:百世快递 shunfeng:顺丰速运	 youzhengguonei:邮政快递包裹 jd:京东物流		 debangwuliu:德邦 zhaijisong:宅急送 youshuwuliu:优速快递 kuayue:跨越速运	 suer:速尔快递		 tiantian:天天快递	 guotongkuaidi:国通快递 kuaijiesudi:快捷速递	 quanyikuaidi:全一快递 ganzhongnengda:能达速递 quanfengkuaidi:全峰快递 rufengda:如风达	 hre:高铁速递	 99:其他
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String ordersLogisInfo;

    /**
     * 商家驳回原因。refuseReason=1：合作伙伴退款申请审核不通过或2：合作伙伴退货验收不通过时必填； 合作伙伴退货退款申请审核不通过原因或合作伙伴退货验收不通过原因
     *
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private String auditResultReason;

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.id
     *
     * @return the value of supply_chain..order_dict_info.id
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.id
     *
     * @param id the value for supply_chain..order_dict_info.id
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.order_type
     *
     * @return the value of supply_chain..order_dict_info.order_type
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.order_type
     *
     * @param orderType the value for supply_chain..order_dict_info.order_type
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.order_id
     *
     * @return the value of supply_chain..order_dict_info.order_id
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.order_id
     *
     * @param orderId the value for supply_chain..order_dict_info.order_id
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.associated_order
     *
     * @return the value of supply_chain..order_dict_info.associated_order
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getAssociatedOrder() {
        return associatedOrder;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withAssociatedOrder(String associatedOrder) {
        this.setAssociatedOrder(associatedOrder);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.associated_order
     *
     * @param associatedOrder the value for supply_chain..order_dict_info.associated_order
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setAssociatedOrder(String associatedOrder) {
        this.associatedOrder = associatedOrder;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.business_order_status
     *
     * @return the value of supply_chain..order_dict_info.business_order_status
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Integer getBusinessOrderStatus() {
        return businessOrderStatus;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withBusinessOrderStatus(Integer businessOrderStatus) {
        this.setBusinessOrderStatus(businessOrderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.business_order_status
     *
     * @param businessOrderStatus the value for supply_chain..order_dict_info.business_order_status
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setBusinessOrderStatus(Integer businessOrderStatus) {
        this.businessOrderStatus = businessOrderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.return_order_status
     *
     * @return the value of supply_chain..order_dict_info.return_order_status
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Integer getReturnOrderStatus() {
        return returnOrderStatus;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withReturnOrderStatus(Integer returnOrderStatus) {
        this.setReturnOrderStatus(returnOrderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.return_order_status
     *
     * @param returnOrderStatus the value for supply_chain..order_dict_info.return_order_status
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setReturnOrderStatus(Integer returnOrderStatus) {
        this.returnOrderStatus = returnOrderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.order_status_time
     *
     * @return the value of supply_chain..order_dict_info.order_status_time
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Date getOrderStatusTime() {
        return orderStatusTime;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withOrderStatusTime(Date orderStatusTime) {
        this.setOrderStatusTime(orderStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.order_status_time
     *
     * @param orderStatusTime the value for supply_chain..order_dict_info.order_status_time
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setOrderStatusTime(Date orderStatusTime) {
        this.orderStatusTime = orderStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.refund_type
     *
     * @return the value of supply_chain..order_dict_info.refund_type
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Integer getRefundType() {
        return refundType;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withRefundType(Integer refundType) {
        this.setRefundType(refundType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.refund_type
     *
     * @param refundType the value for supply_chain..order_dict_info.refund_type
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.refund_reason
     *
     * @return the value of supply_chain..order_dict_info.refund_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Integer getRefundReason() {
        return refundReason;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withRefundReason(Integer refundReason) {
        this.setRefundReason(refundReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.refund_reason
     *
     * @param refundReason the value for supply_chain..order_dict_info.refund_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setRefundReason(Integer refundReason) {
        this.refundReason = refundReason;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.refuse_reason
     *
     * @return the value of supply_chain..order_dict_info.refuse_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public Integer getRefuseReason() {
        return refuseReason;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withRefuseReason(Integer refuseReason) {
        this.setRefuseReason(refuseReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.refuse_reason
     *
     * @param refuseReason the value for supply_chain..order_dict_info.refuse_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setRefuseReason(Integer refuseReason) {
        this.refuseReason = refuseReason;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.orders_logis_info
     *
     * @return the value of supply_chain..order_dict_info.orders_logis_info
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getOrdersLogisInfo() {
        return ordersLogisInfo;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withOrdersLogisInfo(String ordersLogisInfo) {
        this.setOrdersLogisInfo(ordersLogisInfo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.orders_logis_info
     *
     * @param ordersLogisInfo the value for supply_chain..order_dict_info.orders_logis_info
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setOrdersLogisInfo(String ordersLogisInfo) {
        this.ordersLogisInfo = ordersLogisInfo;
    }

    /**
     * This method returns the value of the database column supply_chain..order_dict_info.audit_result_reason
     *
     * @return the value of supply_chain..order_dict_info.audit_result_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public String getAuditResultReason() {
        return auditResultReason;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public OrderDictInfo withAuditResultReason(String auditResultReason) {
        this.setAuditResultReason(auditResultReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_dict_info.audit_result_reason
     *
     * @param auditResultReason the value for supply_chain..order_dict_info.audit_result_reason
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public void setAuditResultReason(String auditResultReason) {
        this.auditResultReason = auditResultReason;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderType=").append(orderType);
        sb.append(", orderId=").append(orderId);
        sb.append(", associatedOrder=").append(associatedOrder);
        sb.append(", businessOrderStatus=").append(businessOrderStatus);
        sb.append(", returnOrderStatus=").append(returnOrderStatus);
        sb.append(", orderStatusTime=").append(orderStatusTime);
        sb.append(", refundType=").append(refundType);
        sb.append(", refundReason=").append(refundReason);
        sb.append(", refuseReason=").append(refuseReason);
        sb.append(", ordersLogisInfo=").append(ordersLogisInfo);
        sb.append(", auditResultReason=").append(auditResultReason);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderDictInfo other = (OrderDictInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAssociatedOrder() == null ? other.getAssociatedOrder() == null : this.getAssociatedOrder().equals(other.getAssociatedOrder()))
            && (this.getBusinessOrderStatus() == null ? other.getBusinessOrderStatus() == null : this.getBusinessOrderStatus().equals(other.getBusinessOrderStatus()))
            && (this.getReturnOrderStatus() == null ? other.getReturnOrderStatus() == null : this.getReturnOrderStatus().equals(other.getReturnOrderStatus()))
            && (this.getOrderStatusTime() == null ? other.getOrderStatusTime() == null : this.getOrderStatusTime().equals(other.getOrderStatusTime()))
            && (this.getRefundType() == null ? other.getRefundType() == null : this.getRefundType().equals(other.getRefundType()))
            && (this.getRefundReason() == null ? other.getRefundReason() == null : this.getRefundReason().equals(other.getRefundReason()))
            && (this.getRefuseReason() == null ? other.getRefuseReason() == null : this.getRefuseReason().equals(other.getRefuseReason()))
            && (this.getOrdersLogisInfo() == null ? other.getOrdersLogisInfo() == null : this.getOrdersLogisInfo().equals(other.getOrdersLogisInfo()))
            && (this.getAuditResultReason() == null ? other.getAuditResultReason() == null : this.getAuditResultReason().equals(other.getAuditResultReason()));
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAssociatedOrder() == null) ? 0 : getAssociatedOrder().hashCode());
        result = prime * result + ((getBusinessOrderStatus() == null) ? 0 : getBusinessOrderStatus().hashCode());
        result = prime * result + ((getReturnOrderStatus() == null) ? 0 : getReturnOrderStatus().hashCode());
        result = prime * result + ((getOrderStatusTime() == null) ? 0 : getOrderStatusTime().hashCode());
        result = prime * result + ((getRefundType() == null) ? 0 : getRefundType().hashCode());
        result = prime * result + ((getRefundReason() == null) ? 0 : getRefundReason().hashCode());
        result = prime * result + ((getRefuseReason() == null) ? 0 : getRefuseReason().hashCode());
        result = prime * result + ((getOrdersLogisInfo() == null) ? 0 : getOrdersLogisInfo().hashCode());
        result = prime * result + ((getAuditResultReason() == null) ? 0 : getAuditResultReason().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Nov 18 11:20:12 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        associatedOrder("associated_order", "associatedOrder", "VARCHAR", false),
        businessOrderStatus("business_order_status", "businessOrderStatus", "INTEGER", false),
        returnOrderStatus("return_order_status", "returnOrderStatus", "INTEGER", false),
        orderStatusTime("order_status_time", "orderStatusTime", "TIMESTAMP", false),
        refundType("refund_type", "refundType", "INTEGER", false),
        refundReason("refund_reason", "refundReason", "INTEGER", false),
        refuseReason("refuse_reason", "refuseReason", "INTEGER", false),
        ordersLogisInfo("orders_logis_info", "ordersLogisInfo", "VARCHAR", false),
        auditResultReason("audit_result_reason", "auditResultReason", "LONGVARCHAR", false);

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Nov 18 11:20:12 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}