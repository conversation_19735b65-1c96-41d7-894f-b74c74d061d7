package com.chinamobile.iot.sc.params;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 11:48
 * @description TODO
 */
@Data
public class QueryOrderHenanZwParam {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 派发起始时间
     */
    private Date timeFrom;

    /**
     * 派发结束时间
     */
    private Date timeTo;

    /**
     * 状态 0-待派发，1-已派发，2-已受理，3-开通成功，4-开通失败
     */
    private Integer status;

    private Integer page;

    private Integer size;
}
