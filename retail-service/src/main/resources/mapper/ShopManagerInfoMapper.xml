<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ShopManagerInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.ShopManagerInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_oper_code" jdbcType="VARCHAR" property="createOperCode" />
    <result column="create_oper_phone" jdbcType="VARCHAR" property="createOperPhone" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="customer_manager_name" jdbcType="VARCHAR" property="customerManagerName" />
    <result column="employee_num" jdbcType="VARCHAR" property="employeeNum" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="mrg_status" jdbcType="VARCHAR" property="mrgStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="gridding_name" jdbcType="VARCHAR" property="griddingName" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="manager_category" jdbcType="VARCHAR" property="managerCategory" />
    <result column="rinse_time" jdbcType="TIMESTAMP" property="rinseTime" />
    <result column="is_rinse" jdbcType="BIT" property="isRinse" />
    <result column="is_merchant" jdbcType="BIT" property="isMerchant" />
    <result column="account_duty" jdbcType="VARCHAR" property="accountDuty" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, create_oper_code, create_oper_phone, user_id, customer_manager_name, employee_num, 
    register_date, be_id, province_name, location, city_name, region_id, region_name, 
    mrg_status, create_time, update_time, gridding_name, channel_type, manager_category, 
    rinse_time, is_rinse, is_merchant, account_duty
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_manager_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from shop_manager_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from shop_manager_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from shop_manager_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_manager_info (id, create_oper_code, create_oper_phone, 
      user_id, customer_manager_name, employee_num, 
      register_date, be_id, province_name, 
      location, city_name, region_id, 
      region_name, mrg_status, create_time, 
      update_time, gridding_name, channel_type, 
      manager_category, rinse_time, is_rinse, 
      is_merchant, account_duty)
    values (#{id,jdbcType=VARCHAR}, #{createOperCode,jdbcType=VARCHAR}, #{createOperPhone,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{customerManagerName,jdbcType=VARCHAR}, #{employeeNum,jdbcType=VARCHAR}, 
      #{registerDate,jdbcType=TIMESTAMP}, #{beId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, 
      #{regionName,jdbcType=VARCHAR}, #{mrgStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{griddingName,jdbcType=VARCHAR}, #{channelType,jdbcType=VARCHAR}, 
      #{managerCategory,jdbcType=VARCHAR}, #{rinseTime,jdbcType=TIMESTAMP}, #{isRinse,jdbcType=BIT}, 
      #{isMerchant,jdbcType=BIT}, #{accountDuty,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_manager_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createOperCode != null">
        create_oper_code,
      </if>
      <if test="createOperPhone != null">
        create_oper_phone,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="customerManagerName != null">
        customer_manager_name,
      </if>
      <if test="employeeNum != null">
        employee_num,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="mrgStatus != null">
        mrg_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="griddingName != null">
        gridding_name,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="managerCategory != null">
        manager_category,
      </if>
      <if test="rinseTime != null">
        rinse_time,
      </if>
      <if test="isRinse != null">
        is_rinse,
      </if>
      <if test="isMerchant != null">
        is_merchant,
      </if>
      <if test="accountDuty != null">
        account_duty,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="createOperCode != null">
        #{createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperPhone != null">
        #{createOperPhone,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="customerManagerName != null">
        #{customerManagerName,jdbcType=VARCHAR},
      </if>
      <if test="employeeNum != null">
        #{employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="mrgStatus != null">
        #{mrgStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="griddingName != null">
        #{griddingName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="managerCategory != null">
        #{managerCategory,jdbcType=VARCHAR},
      </if>
      <if test="rinseTime != null">
        #{rinseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRinse != null">
        #{isRinse,jdbcType=BIT},
      </if>
      <if test="isMerchant != null">
        #{isMerchant,jdbcType=BIT},
      </if>
      <if test="accountDuty != null">
        #{accountDuty,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from shop_manager_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_manager_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.createOperCode != null">
        create_oper_code = #{record.createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createOperPhone != null">
        create_oper_phone = #{record.createOperPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerManagerName != null">
        customer_manager_name = #{record.customerManagerName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeNum != null">
        employee_num = #{record.employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null">
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.mrgStatus != null">
        mrg_status = #{record.mrgStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.griddingName != null">
        gridding_name = #{record.griddingName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelType != null">
        channel_type = #{record.channelType,jdbcType=VARCHAR},
      </if>
      <if test="record.managerCategory != null">
        manager_category = #{record.managerCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.rinseTime != null">
        rinse_time = #{record.rinseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isRinse != null">
        is_rinse = #{record.isRinse,jdbcType=BIT},
      </if>
      <if test="record.isMerchant != null">
        is_merchant = #{record.isMerchant,jdbcType=BIT},
      </if>
      <if test="record.accountDuty != null">
        account_duty = #{record.accountDuty,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_manager_info
    set id = #{record.id,jdbcType=VARCHAR},
      create_oper_code = #{record.createOperCode,jdbcType=VARCHAR},
      create_oper_phone = #{record.createOperPhone,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      customer_manager_name = #{record.customerManagerName,jdbcType=VARCHAR},
      employee_num = #{record.employeeNum,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      mrg_status = #{record.mrgStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      gridding_name = #{record.griddingName,jdbcType=VARCHAR},
      channel_type = #{record.channelType,jdbcType=VARCHAR},
      manager_category = #{record.managerCategory,jdbcType=VARCHAR},
      rinse_time = #{record.rinseTime,jdbcType=TIMESTAMP},
      is_rinse = #{record.isRinse,jdbcType=BIT},
      is_merchant = #{record.isMerchant,jdbcType=BIT},
      account_duty = #{record.accountDuty,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_manager_info
    <set>
      <if test="createOperCode != null">
        create_oper_code = #{createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperPhone != null">
        create_oper_phone = #{createOperPhone,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="customerManagerName != null">
        customer_manager_name = #{customerManagerName,jdbcType=VARCHAR},
      </if>
      <if test="employeeNum != null">
        employee_num = #{employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="mrgStatus != null">
        mrg_status = #{mrgStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="griddingName != null">
        gridding_name = #{griddingName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="managerCategory != null">
        manager_category = #{managerCategory,jdbcType=VARCHAR},
      </if>
      <if test="rinseTime != null">
        rinse_time = #{rinseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRinse != null">
        is_rinse = #{isRinse,jdbcType=BIT},
      </if>
      <if test="isMerchant != null">
        is_merchant = #{isMerchant,jdbcType=BIT},
      </if>
      <if test="accountDuty != null">
        account_duty = #{accountDuty,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.ShopManagerInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_manager_info
    set create_oper_code = #{createOperCode,jdbcType=VARCHAR},
      create_oper_phone = #{createOperPhone,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      customer_manager_name = #{customerManagerName,jdbcType=VARCHAR},
      employee_num = #{employeeNum,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=VARCHAR},
      mrg_status = #{mrgStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      gridding_name = #{griddingName,jdbcType=VARCHAR},
      channel_type = #{channelType,jdbcType=VARCHAR},
      manager_category = #{managerCategory,jdbcType=VARCHAR},
      rinse_time = #{rinseTime,jdbcType=TIMESTAMP},
      is_rinse = #{isRinse,jdbcType=BIT},
      is_merchant = #{isMerchant,jdbcType=BIT},
      account_duty = #{accountDuty,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_manager_info
    (id, create_oper_code, create_oper_phone, user_id, customer_manager_name, employee_num, 
      register_date, be_id, province_name, location, city_name, region_id, region_name, 
      mrg_status, create_time, update_time, gridding_name, channel_type, manager_category, 
      rinse_time, is_rinse, is_merchant, account_duty)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.createOperCode,jdbcType=VARCHAR}, #{item.createOperPhone,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.customerManagerName,jdbcType=VARCHAR}, 
        #{item.employeeNum,jdbcType=VARCHAR}, #{item.registerDate,jdbcType=TIMESTAMP}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, 
        #{item.cityName,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.regionName,jdbcType=VARCHAR}, 
        #{item.mrgStatus,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.griddingName,jdbcType=VARCHAR}, #{item.channelType,jdbcType=VARCHAR}, #{item.managerCategory,jdbcType=VARCHAR}, 
        #{item.rinseTime,jdbcType=TIMESTAMP}, #{item.isRinse,jdbcType=BIT}, #{item.isMerchant,jdbcType=BIT}, 
        #{item.accountDuty,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 11 11:01:52 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_manager_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'create_oper_code'.toString() == column.value">
          #{item.createOperCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_oper_phone'.toString() == column.value">
          #{item.createOperPhone,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'customer_manager_name'.toString() == column.value">
          #{item.customerManagerName,jdbcType=VARCHAR}
        </if>
        <if test="'employee_num'.toString() == column.value">
          #{item.employeeNum,jdbcType=VARCHAR}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'region_name'.toString() == column.value">
          #{item.regionName,jdbcType=VARCHAR}
        </if>
        <if test="'mrg_status'.toString() == column.value">
          #{item.mrgStatus,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'gridding_name'.toString() == column.value">
          #{item.griddingName,jdbcType=VARCHAR}
        </if>
        <if test="'channel_type'.toString() == column.value">
          #{item.channelType,jdbcType=VARCHAR}
        </if>
        <if test="'manager_category'.toString() == column.value">
          #{item.managerCategory,jdbcType=VARCHAR}
        </if>
        <if test="'rinse_time'.toString() == column.value">
          #{item.rinseTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_rinse'.toString() == column.value">
          #{item.isRinse,jdbcType=BIT}
        </if>
        <if test="'is_merchant'.toString() == column.value">
          #{item.isMerchant,jdbcType=BIT}
        </if>
        <if test="'account_duty'.toString() == column.value">
          #{item.accountDuty,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>