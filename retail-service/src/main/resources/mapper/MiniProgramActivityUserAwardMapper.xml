<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramActivityUserAwardMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAward">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="award_id" jdbcType="VARCHAR" property="awardId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="ranking" jdbcType="INTEGER" property="ranking" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="total" jdbcType="INTEGER" property="total" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="address_id" jdbcType="VARCHAR" property="addressId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="logistics_code" jdbcType="VARCHAR" property="logisticsCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, activity_id, award_id, user_id, ranking, amount, total, create_time, update_time, 
    address_id, status, logistics_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAwardExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_activity_user_award
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_activity_user_award
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity_user_award
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAwardExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_activity_user_award
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAward">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_user_award (id, activity_id, award_id, 
      user_id, ranking, amount, 
      total, create_time, update_time, 
      address_id, status, logistics_code
      )
    values (#{id,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{awardId,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{ranking,jdbcType=INTEGER}, #{amount,jdbcType=BIGINT}, 
      #{total,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{addressId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{logisticsCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAward">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_user_award
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="awardId != null">
        award_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="ranking != null">
        ranking,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="addressId != null">
        address_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="logisticsCode != null">
        logistics_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="awardId != null">
        #{awardId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="ranking != null">
        #{ranking,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="total != null">
        #{total,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="logisticsCode != null">
        #{logisticsCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAwardExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_activity_user_award
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_user_award
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.awardId != null">
        award_id = #{record.awardId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.ranking != null">
        ranking = #{record.ranking,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.total != null">
        total = #{record.total,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addressId != null">
        address_id = #{record.addressId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsCode != null">
        logistics_code = #{record.logisticsCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_user_award
    set id = #{record.id,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      award_id = #{record.awardId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      ranking = #{record.ranking,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=BIGINT},
      total = #{record.total,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      address_id = #{record.addressId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      logistics_code = #{record.logisticsCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAward">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_user_award
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="awardId != null">
        award_id = #{awardId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="ranking != null">
        ranking = #{ranking,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addressId != null">
        address_id = #{addressId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="logisticsCode != null">
        logistics_code = #{logisticsCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUserAward">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_activity_user_award
    set activity_id = #{activityId,jdbcType=VARCHAR},
      award_id = #{awardId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      ranking = #{ranking,jdbcType=INTEGER},
      amount = #{amount,jdbcType=BIGINT},
      total = #{total,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      address_id = #{addressId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      logistics_code = #{logisticsCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_user_award
    (id, activity_id, award_id, user_id, ranking, amount, total, create_time, update_time, 
      address_id, status, logistics_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, #{item.awardId,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.ranking,jdbcType=INTEGER}, #{item.amount,jdbcType=BIGINT}, 
        #{item.total,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.addressId,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.logisticsCode,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jul 26 16:05:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_activity_user_award (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'award_id'.toString() == column.value">
          #{item.awardId,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'ranking'.toString() == column.value">
          #{item.ranking,jdbcType=INTEGER}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=BIGINT}
        </if>
        <if test="'total'.toString() == column.value">
          #{item.total,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'address_id'.toString() == column.value">
          #{item.addressId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'logistics_code'.toString() == column.value">
          #{item.logisticsCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>