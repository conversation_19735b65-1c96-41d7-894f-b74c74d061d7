<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.AfterMarketStdServiceMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.AfterMarketStdService">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="after_market_code" jdbcType="VARCHAR" property="afterMarketCode" />
    <result column="std_service_id" jdbcType="VARCHAR" property="stdServiceId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, after_market_code, std_service_id, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdServiceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from after_market_std_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from after_market_std_service
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from after_market_std_service
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdServiceExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from after_market_std_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdService">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into after_market_std_service (id, after_market_code, std_service_id, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{afterMarketCode,jdbcType=VARCHAR}, #{stdServiceId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdService">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into after_market_std_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="afterMarketCode != null">
        after_market_code,
      </if>
      <if test="stdServiceId != null">
        std_service_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketCode != null">
        #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="stdServiceId != null">
        #{stdServiceId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdServiceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from after_market_std_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    update after_market_std_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketCode != null">
        after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.stdServiceId != null">
        std_service_id = #{record.stdServiceId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    update after_market_std_service
    set id = #{record.id,jdbcType=VARCHAR},
      after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      std_service_id = #{record.stdServiceId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdService">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    update after_market_std_service
    <set>
      <if test="afterMarketCode != null">
        after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="stdServiceId != null">
        std_service_id = #{stdServiceId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.AfterMarketStdService">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    update after_market_std_service
    set after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      std_service_id = #{stdServiceId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into after_market_std_service
    (id, after_market_code, std_service_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.afterMarketCode,jdbcType=VARCHAR}, #{item.stdServiceId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 01 15:27:22 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into after_market_std_service (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_code'.toString() == column.value">
          #{item.afterMarketCode,jdbcType=VARCHAR}
        </if>
        <if test="'std_service_id'.toString() == column.value">
          #{item.stdServiceId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>