<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramSceneRequirementsAnswerMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswer">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="requirement_id" jdbcType="VARCHAR" property="requirementId" />
    <result column="question_id" jdbcType="VARCHAR" property="questionId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, requirement_id, question_id, answer
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswerExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_scene_requirements_answer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_scene_requirements_answer
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_scene_requirements_answer
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswerExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_scene_requirements_answer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswer">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_scene_requirements_answer (id, requirement_id, question_id, 
      answer)
    values (#{id,jdbcType=VARCHAR}, #{requirementId,jdbcType=VARCHAR}, #{questionId,jdbcType=VARCHAR}, 
      #{answer,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswer">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_scene_requirements_answer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="requirementId != null">
        requirement_id,
      </if>
      <if test="questionId != null">
        question_id,
      </if>
      <if test="answer != null">
        answer,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="requirementId != null">
        #{requirementId,jdbcType=VARCHAR},
      </if>
      <if test="questionId != null">
        #{questionId,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswerExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_scene_requirements_answer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_scene_requirements_answer
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.requirementId != null">
        requirement_id = #{record.requirementId,jdbcType=VARCHAR},
      </if>
      <if test="record.questionId != null">
        question_id = #{record.questionId,jdbcType=VARCHAR},
      </if>
      <if test="record.answer != null">
        answer = #{record.answer,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_scene_requirements_answer
    set id = #{record.id,jdbcType=VARCHAR},
      requirement_id = #{record.requirementId,jdbcType=VARCHAR},
      question_id = #{record.questionId,jdbcType=VARCHAR},
      answer = #{record.answer,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswer">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_scene_requirements_answer
    <set>
      <if test="requirementId != null">
        requirement_id = #{requirementId,jdbcType=VARCHAR},
      </if>
      <if test="questionId != null">
        question_id = #{questionId,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        answer = #{answer,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsAnswer">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_scene_requirements_answer
    set requirement_id = #{requirementId,jdbcType=VARCHAR},
      question_id = #{questionId,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_scene_requirements_answer
    (id, requirement_id, question_id, answer)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.requirementId,jdbcType=VARCHAR}, #{item.questionId,jdbcType=VARCHAR}, 
        #{item.answer,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 10:38:41 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_scene_requirements_answer (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'requirement_id'.toString() == column.value">
          #{item.requirementId,jdbcType=VARCHAR}
        </if>
        <if test="'question_id'.toString() == column.value">
          #{item.questionId,jdbcType=VARCHAR}
        </if>
        <if test="'answer'.toString() == column.value">
          #{item.answer,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>