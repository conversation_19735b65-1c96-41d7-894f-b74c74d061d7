<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.EstewardDirectoryMapperExt">

  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.EstewardDirectory">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 14 16:14:43 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="DirectoryWithChildrenResultMap" type="com.chinamobile.retail.pojo.vo.EStewardDirectoryWithProductVO">
    <id property="directoryId" column="directoryId"/>
    <result property="directoryName" column="directoryName"/>
    <result property="imgUrl" column="imgUrl"/>
    <collection property="children" ofType="com.chinamobile.retail.pojo.vo.EStewardDirectoryWithProductVO">
      <id property="directoryId" column="childDirectoryId"/>
      <result property="directoryName" column="childDirectoryName"/>
      <result property="imgUrl" column="childImgUrl"/>
    </collection>
  </resultMap>


  <resultMap id="ProductResultMap" type="com.chinamobile.retail.pojo.vo.EStewardDirectoryWithProductVO">
    <id property="productId" column="productId"/>
    <result property="productName" column="productName"/>
    <result property="productUrl" column="productUrl"/>
    <result property="headUrl" column="headUrl"/>
    <result property="productType" column="productType"/>
    <result property="spuCode" column="spuCode"/>
    <result property="skuCount" column="skuCount"/>
    <result property="directoryId" column="directoryId"/>
  </resultMap>

  <select id="selectDirectoriesWithChildren" resultMap="DirectoryWithChildrenResultMap">
    SELECT
      d1.id AS directoryId,
      d1.name AS directoryName,
      d1.parent_id AS parentId,
      d1.img_url imgUrl,
      d1.sort AS sort,
      d2.id AS childDirectoryId,
      d2.name AS childDirectoryName,
      d2.parent_id AS childParentId,
      d2.sort AS childSort,
      d2.img_url childImgUrl
    FROM
      esteward_directory d1
        LEFT JOIN
      esteward_directory d2 ON d1.id = d2.parent_id
    WHERE
      d1.parent_id = #{parentId}
    ORDER BY
      d1.sort, d2.sort
  </select>


  <select id="selectMaxSortByParentId" resultType="Integer">
    SELECT IFNULL(MAX(sort),0) AS maxSort
    FROM esteward_directory
    WHERE parent_id = #{parentId}
  </select>

  <select id="selectAllDirectories" resultMap="BaseResultMap">
    SELECT *
    FROM esteward_directory
    ORDER BY sort ASC
  </select>

  <select id="selectProductsByDirectoryIds" resultMap="ProductResultMap">
    SELECT
    p.id AS productId,
    p.product_name productName,
    case when p.show_type = 1 then p.product_link
         when p.show_type = 2 then spu.url
    end productUrl,
    p.head_url headUrl,
    p.spu_code AS spuCode,
    p.show_type AS productType,
    pd.directory_id AS directoryId,
    (select count(*) from sku_offering_info
    where spu_code = p.spu_code
    and offering_status != '2'
    and delete_time is null) skuCount
    FROM
    esteward_product_directory pd
    JOIN
    esteward_product p ON pd.product_id = p.id and p.delete_time is null and p.status = 4
    LEFT JOIN
    spu_offering_info spu ON p.spu_code = spu.offering_code and spu.offering_status != '2' and spu.delete_time is null
    WHERE
    pd.directory_id IN
    <foreach item="directoryId" index="index" collection="directoryIdList" open="(" separator="," close=")">
      #{directoryId}
    </foreach>
    AND pd.is_bottom = true
    AND (
    (p.show_type = 1)
    OR
    (p.show_type = 2 AND (
    select count(*) from sku_offering_info
    where spu_code = p.spu_code
    and offering_status != '2'
    and delete_time is null
    ) > 0)
    )
    ORDER BY p.id
  </select>


</mapper>