<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramActivityMapperExt">

    <resultMap id="region_map" type="com.chinamobile.retail.pojo.vo.miniprogram.RegionVO">
        <result property="provinceCode" column="provinceCode"/>
        <result property="provinceName" column="provinceName"/>
        <result property="cityCode" column="cityCode"/>
        <result property="cityName" column="cityName"/>
    </resultMap>

    <resultMap id="page_activity_map" type="com.chinamobile.retail.pojo.vo.miniprogram.PageActivityVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="activityType" column="activityType"/>
        <result property="target" column="target"/>
        <result property="startTime" column="startTime"/>
        <result property="stopTime" column="stopTime"/>
        <result property="auditStatus" column="auditStatus"/>
        <result property="createUserName" column="createUserName"/>
        <result property="createTime" column="createTime"/>
        <result property="status" column="status"/>
        <result property="active" column="active"/>
        <collection property="regions" ofType="com.chinamobile.retail.pojo.vo.miniprogram.RegionVO"
                    resultMap="region_map"/>
    </resultMap>

    <!-- 管理后台分页查询小程序活动列表 -->
    <select id="pageActivityList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageActivityListParam"
            resultMap="page_activity_map">
        select
        a.id as id,
        a.name as name,
        a.activity_type as activityType,
        a.target as target,
        a.start_time as startTime,
        a.stop_time as stopTime,
        a.audit_status as auditStatus,
        a.createUserName as createUserName,
        a.create_time as createTime,
        a.status as status,
        a.active as active,
        r.province_code as provinceCode,
        p.mall_name as provinceName,
        r.city_code as cityCode,
        c.mall_name as cityName
        from (
        select
        a1.id,
        a1.name,
        a1.activity_type,
        a1.target,
        a1.start_time,
        a1.stop_time,
        a1.audit_status,
        u1.name as createUserName,
        a1.create_time,
        a1.update_time,
        a1.status,
        a1.active
        from mini_program_activity a1
        left join user u1 on u1.user_id = a1.create_uid
        left join mini_program_activity_region r1 on r1.activity_id = a1.id
        left join contract_province_info p1 on p1.mall_code = r1.province_code
        left join contract_city_info c1 on c1.mall_code = r1.city_code
        <where>
            and a1.is_delete=0
            <if test="status != null">
                and a1.status = #{status}
            </if>
            <if test="auditStatus != null and auditStatus != 4">
                and a1.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus != null and auditStatus == 4">
                and (a1.audit_status = 2 or a1.audit_status = 3)
            </if>
            <if test="activityName != null and activityName != ''">
                and a1.name like concat(#{activityName}, '%')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and u1.name like concat(#{createUserName}, '%')
            </if>
            <if test="regions != null and !regions.isEmpty()">
                and (r1.province_code, coalesce(r1.city_code, '')) in
                <foreach item="item" index="index" collection="regions" open="(" separator="," close=")">
                    (#{item.provinceCode}, ifnull(#{item.cityCode}, ''))
                </foreach>
            </if>
            <if test="active != null">
                and a1.active = #{active}
            </if>
            <if test="draft != null and draft == false">
                and a1.status > 0
            </if>
        </where>
        <choose>
            <when test="draft == null or draft == true">
                group by a1.id, a1.create_time
                order by a1.id desc, a1.create_time desc
            </when>
            <otherwise>
                group by a1.id, a1.update_time
                order by a1.id desc, a1.update_time desc
            </otherwise>
        </choose>
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
        ) a
        left join mini_program_activity_region r on r.activity_id = a.id
        left join contract_province_info p on p.mall_code = r.province_code
        left join contract_city_info c on c.mall_code = r.city_code
    </select>

    <!-- 管理后台查询小程序活动总数 -->
    <select id="countActivityList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageActivityListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select a.id
        from mini_program_activity a
        left join user u on u.user_id = a.create_uid
        left join mini_program_activity_region r on r.activity_id = a.id
        <where>
            and a.is_delete=0
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="auditStatus != null and auditStatus != 4">
                and a.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus != null and auditStatus == 4">
                and (a.audit_status = 2 or a.audit_status = 3)
            </if>
            <if test="activityName != null and activityName != ''">
                and a.name like concat(#{activityName}, '%')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and u.name like concat(#{createUserName}, '%')
            </if>
            <if test="regions != null and !regions.isEmpty()">
                and (r.province_code, coalesce(r.city_code, '')) in
                <foreach item="item" index="index" collection="regions" open="(" separator="," close=")">
                    (#{item.provinceCode}, ifnull(#{item.cityCode}, ''))
                </foreach>
            </if>
            <if test="active != null">
                and a.active = #{active}
            </if>
            <if test="draft != null and draft == false">
                and a.audit_status > 0
            </if>
        </where>
        group by a.id
        ) t
    </select>

    <select id="getTotalNum" resultType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityStatisticsVO">
        select
        sum(ifnull(award.total, 0)) as atomOrderCount,
        sum(ifnull(award.amount, 0)) as atomOrderAmount
        from mini_program_activity_user_award award
        where award.activity_id = #{activityId}
        <if test="userId != null and userId != ''">
            and award.user_id = #{userId}
        </if>

    </select>

    <select id="getActivityPersonNum" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
        count(distinct mpau.user_id)
        from mini_program_activity_user mpau
        left join mini_program_activity_user_award mpaua on (mpau.activity_id = mpaua.activity_id and mpau.user_id=
        mpaua.user_id)
        where 1=1
        and mpaua.total is not null
        and mpaua.total != 0
        <if test="activityId != null">
            and mpau.activity_id = #{activityId}
        </if>
    </select>
    <select id="getActivityPersonNumSignUp" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
        count(distinct mpau.user_id)
        from mini_program_activity_user mpau
        where 1=1
        <if test="activityId != null">
            and mpau.activity_id = #{activityId}
        </if>
    </select>
    <select id="getPersonNum" resultType="java.lang.Long">
        SELECT
        count(distinct user_id) personCount
        from user_mini_program uip
        where 1=1
        and status=1
        <if test="roleTypeList != null">
            and uip.role_type in
            <foreach item="item" index="index" collection="roleTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="regions != null and !regions.isEmpty()">
            and (uip.be_id, coalesce(uip.location, '')) in
            <foreach item="item" index="index" collection="regions" open="(" separator="," close=")">
                (#{item.provinceCode}, IF(uip.location is null, '', ifnull(#{item.cityCode}, ''))
                )
            </foreach>
        </if>
    </select>
    <!-- 小程序前台查询活动列表 -->
    <select id="getListFrontPage"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageActivityVO">
        select a.id as id,
        a.name as name,
        a.start_time as startTime,
        a.stop_time as stopTime,
        a.audit_status as auditStatus,
        a.create_time as createTime,
        a.status as status,
        a.active as active,
        a.list_img as listImg,
        a.activity_type as activityType,
        case
        when mpau.id is not null then true
        else false
        end as isParticipate
        from mini_program_activity a
        join mini_program_activity_region r on r.activity_id = a.id
        <if test="location != null and location!='' ">
            and r.province_code=#{beId} and r.city_code=#{location}
        </if>
        <if test="location == null ">
            and r.province_code=#{beId}
        </if>
        left join mini_program_activity_user mpau on mpau.activity_id = a.id and mpau.user_id = #{userId}
        where a.audit_status = 2 and a.active= 1
        <if test="isParticipate != null and isParticipate == true ">
            and mpau.id is not null
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="activityType != null">
            and a.activity_type = #{activityType}
        </if>
        group by a.id, a.create_time
        order by a.create_time desc
    </select>

    <select id="selectActivityUsers"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.SelectActivityUsersParam"
            resultType="com.chinamobile.retail.pojo.entity.MiniProgramActivityUser">
        select
        user.user_id as userId,
        user.phone as phone,
        user.role_type as roleType
        from user_mini_program user
        left join mini_program_user_order_info oi on oi.user_id = user.user_id
        left join order_2c_info o2i on o2i.order_id = oi.order_id
        where user.user_id is not null
        and user.status=1
        <if test="userIds != null and !userIds.isEmpty()">
            and user.user_id in
            <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roleTypes != null and !roleTypes.isEmpty()">
            and user.role_type in
            <foreach item="item" index="index" collection="roleTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="regions != null and !regions.isEmpty()">
            and (user.be_id, coalesce(user.location, '')) in
            <foreach item="item" index="index" collection="regions" open="(" separator="," close=")">
                (#{item.provinceCode}, IF(user.location is null, '', ifnull(#{item.cityCode}, ''))
                )
            </foreach>
        </if>
        <if test="offeringClasses != null and !offeringClasses.isEmpty()">
            and o2i.spu_offering_class in
            <foreach item="item" index="index" collection="offeringClasses" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuCodes != null and !spuCodes.isEmpty()">
            and o2i.spu_offering_code in
            <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by user.user_id
    </select>

    <!-- 获取满足条件的订单数量 -->
    <select id="getOrderNum"
            resultType="java.lang.Integer">
        SELECT
        count(o2s.order_id) orderNum
        FROM
        (
        select
        oa.order_id as order_id
        from mini_program_user_order_info mpuoi
        left join order_2c_atom_info oa on oa.id = mpuoi.atom_order_id
        left join order_2c_info oi on oa.order_id=oi.order_id
        where oa.order_status not in (8)
        and mpuoi.user_id=#{userId}
        <if test="startTime != null">
            and mpuoi.order_create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null ">
            and mpuoi.order_create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="offeringClassList != null and !offeringClassList.isEmpty()">
            and oi.spu_offering_class in
            <foreach item="item" index="index" collection="offeringClassList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuCodeList != null and !spuCodeList.isEmpty()">
            and oi.spu_offering_code in
            <foreach item="item" index="index" collection="spuCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY
        mpuoi.order_id ) o2s;
    </select>
    <!-- 获取用户获得的奖品 -->
    <select id="selectActivityAwardList"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityAwardListVO">
        SELECT mna.id AS activityId,
        mna.name AS name,
        mna.start_time AS startTime,
        mna.stop_time AS stopTime,
        COALESCE(ra.id, fa.id) AS awardId,
        COALESCE(ra.award_name, fa.award_name) AS awardName,
        mpaua.create_time AS awardTime,
        COALESCE(ra.product_img, fa.product_img) AS productImg,
        COALESCE(ra.type, fa.type) AS awardType,
        COALESCE(ra.points, fa.points) AS points,
        COALESCE(ra.supplier_id, fa.supplier_id) AS supplierId,
        mpaua.status AS awardStatus,
        mpaua.logistics_code AS logisticsCode,
        umpa.id AS addressId,
        umpa.addr1 AS addr1,
        umpa.addr2 AS addr2,
        umpa.addr3 AS addr3,
        umpa.usaddr AS usaddr,
        umpa.name AS addressName,
        phone
        FROM mini_program_activity_user_award mpaua
        LEFT JOIN mini_program_activity mna ON mna.id = mpaua.activity_id
        LEFT JOIN mini_program_activity_rank_award ra
        ON mna.activity_type = 1 AND ra.activity_id = mpaua.activity_id AND ra.id = mpaua.award_id
        LEFT JOIN mini_program_activity_weekly_fun_award fa
        ON mna.activity_type = 2 AND fa.activity_id = mpaua.activity_id AND fa.id = mpaua.award_id
        LEFT JOIN user_mini_program_address umpa on mpaua.address_id = umpa.id
        where 1=1
        and COALESCE(ra.id, fa.id) is not null
        and ( (mna.activity_type=1 and mna.status in (3,4,8)) || mna.activity_type=2 )
        <if test="userId != null and userId != ''">
            and mpaua.user_id = #{userId}
        </if>
        order by mpaua.create_time desc
    </select>
    <select id="getNotReceivedAwardNum"
            resultType="java.lang.Integer">
        select count(m.id) as count
        from
        (SELECT
        mpaua.id
        FROM mini_program_activity_user_award mpaua
        LEFT JOIN mini_program_activity mna ON mna.id = mpaua.activity_id
        LEFT JOIN mini_program_activity_rank_award ra
        ON mna.activity_type = 1 AND ra.activity_id = mpaua.activity_id AND ra.id = mpaua.award_id
        LEFT JOIN mini_program_activity_weekly_fun_award fa
        ON mna.activity_type = 2 AND fa.activity_id = mpaua.activity_id AND fa.id = mpaua.award_id
        LEFT JOIN user_mini_program_address umpa on mpaua.address_id = umpa.id
        where 1=1
        and mpaua.status=0
        and COALESCE(ra.type, fa.type)=2
        and mna.stop_time <![CDATA[ < ]]> NOW()
        <if test="userId != null and userId != ''">
            and mpaua.user_id = #{userId}
        </if>
        group by mpaua.id
        ) m


    </select>
    <!-- 获取数据看板排名列表 -->
    <select id="getDataRankList"
            resultType="com.chinamobile.retail.pojo.dto.ActivityDataRankDTO">
        select
        mpaua.id,
        mpaua.user_id as userId,
        mpa.activity_type as activityType,
        ranking,
        ump.name,
        ump.phone,
        ump.province_name as provinceName,
        ump.city_name as cityName,
        COALESCE(ra.id, fa.id) as awardId,
        COALESCE(ra.award_name, fa.award_name) as awardName,
        COALESCE(ra.type, fa.type) AS awardType,
        COALESCE(ra.product, fa.product) AS product,
        COALESCE(ra.points, fa.points) AS points,
        COALESCE(ra.supplier_id, fa.supplier_id) AS supplierId,
        mpaua.create_time as createTime,
        addr1,
        addr2,
        addr3,
        mpaua.amount as orderAmount,
        mpaua.total as orderCount,
        mpa.stop_time as stopTime,
        usaddr,
        mpaua.logistics_code as logisticsCode,
        case when pep.id is null then 0
        else pep.status end isExchange
        from mini_program_activity_user_award mpaua
        left join mini_program_activity mpa on mpaua.activity_id = mpa.id
        left join user_mini_program_address umpa on umpa.id = mpaua.address_id
        left join user_mini_program ump on ump.user_id = mpaua.user_id
        LEFT JOIN mini_program_activity_rank_award ra
        ON mpa.activity_type = 1 AND ra.activity_id = mpaua.activity_id AND ra.id = mpaua.award_id
        LEFT JOIN mini_program_activity_weekly_fun_award fa
        ON mpa.activity_type = 2 AND fa.activity_id = mpaua.activity_id AND fa.id = mpaua.award_id
        LEFT JOIN point_exchange_partner pep on mpaua.activity_id = pep.activity_id and mpaua.user_id=pep.partner_id
        where 1=1
        AND (mpa.activity_type != 2 OR fa.id IS NOT NULL)
        and ((mpa.activity_type = 1 and mpaua.total>0) or mpa.activity_type = 2 )
        <if test="activityId != null and activityId != ''">
            and mpaua.activity_id = #{activityId}
        </if>
        <if test="name != null and name != ''">
            and ump.name like CONCAT('%',#{name},'%')
        </if>

        ORDER BY
        CASE
        WHEN mpa.activity_type = 1 THEN ranking
        END ASC,
        CASE
        WHEN mpa.activity_type = 2 THEN mpaua.create_time
        END DESC
    </select>
    <!-- 删除排名后，更新后面的排名 -->
    <select id="updateDataRank">
        UPDATE mini_program_activity_user_award t1
            INNER JOIN (
            SELECT t2.ranking, t3.award_id AS new_award_id,t3.ranking as new_ranking
            FROM mini_program_activity_user_award t2
            LEFT JOIN mini_program_activity_user_award t3 ON t2.ranking = t3.ranking + 1
            WHERE t2.ranking > #{ranking}
            ) t2
        ON t1.ranking = t2.ranking
            SET t1.award_id = t2.new_award_id, t1.ranking = t2.new_ranking;
    </select>

    <select id="selectRankActivityUserAwards"
            parameterType="com.chinamobile.retail.pojo.mapper.SelectRankActivityUserAwardDTO"
            resultType="com.chinamobile.retail.pojo.dto.MiniProgramActivityUserRankingDTO">
        select
        t.userId as userId,
        t.name as name,
        @row:=@row+1 as row,
        @row as ranking,
        t.amount as amount,
        t.total as total
        from (
        select
        u.user_id as userId,
        u.name as name,
        ifnull(t.amount, 0) as amount,
        maxCreateTime,
        ifnull(t.total, 0) as total
        from mini_program_activity_user mu
        inner join user_mini_program u on u.user_id = mu.user_id
        left join (
        select
        mu1.user_id,
        case
        when oai.order_status = 9 then sum((oai.sku_quantity - oi.special_after_refunds_number) * oai.atom_price *
        oai.atom_quantity)
        else sum(oai.sku_quantity * oai.atom_price * oai.atom_quantity)
        end as amount,
        max(oai.create_time) as maxCreateTime,
        count(distinct moi.order_id) as total
        from mini_program_activity_user mu1
        inner join mini_program_user_order_info moi
        on mu1.user_id = moi.user_id
        and moi.order_create_time  <![CDATA[ >= ]]> #{startTime} and moi.order_create_time <![CDATA[ <= ]]> #{stopTime}
        inner join order_2c_atom_info oai
        on oai.id = moi.atom_order_id
        and oai.order_status not in (8)
        inner join order_2c_info oi on oi.order_id = moi.order_id
        where mu1.activity_id =#{activityId}
        <if test="offeringClasses != null and !offeringClasses.isEmpty()">
            and oi.spu_offering_class in
            <foreach item="item" index="index" collection="offeringClasses" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuCodes != null and !spuCodes.isEmpty()">
            and oi.spu_offering_code in
            <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderTypes != null and !orderTypes.isEmpty()">
            and oi.order_type in
            <foreach item="item" index="index" collection="orderTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessCodes != null and !businessCodes.isEmpty()">
            and oi.business_code in
            <foreach item="item" index="index" collection="businessCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by mu1.user_id
        ) t on t.user_id = mu.user_id
        where mu.activity_id = #{activityId}

        <if test="sortType != null and sortType==0">
            order by amount desc,total desc,maxCreateTime asc
        </if>
        <if test="sortType != null and sortType==1">
            order by total desc,amount desc,maxCreateTime asc
        </if>
        ) t, (select @row:=0) r
    </select>

    <resultMap id="orderInfoHandleMap" type="com.chinamobile.retail.pojo.handel.OrderInfoHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="skuQuantity" jdbcType="INTEGER" property="skuQuantity"/>
        <result column="skuPrice" jdbcType="BIGINT" property="skuPrice"/>
        <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="sync_k3_id" jdbcType="VARCHAR" property="syncK3Id"/>
        <result column="special_after_market_handle" jdbcType="INTEGER" property="specialAfterMarketHandle"/>
        <result column="special_after_status" jdbcType="VARCHAR" property="specialAfterStatus"/>
        <result column="special_after_latest_time" jdbcType="VARCHAR" property="specialAfterLatestTime"/>
        <result column="imgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="carOpenStatus" jdbcType="VARCHAR" property="carOpenStatus"/>
        <result column="extSoftOfferingCode" jdbcType="VARCHAR" property="extSoftOfferingCode"/>
        <result column="kxRefundStatus" jdbcType="VARCHAR" property="kxRefundStatus"/>
        <result column="allowOrderStatus" jdbcType="INTEGER" property="allowOrderStatus"/>
        <result column="totalPrice" jdbcType="BIGINT" property="totalPrice"/>
        <result column="softServiceOpenStatus" jdbcType="INTEGER" property="softServiceOpenStatus"/>
        <result column="softServiceRetailStatus" jdbcType="INTEGER" property="softServiceRetailStatus"/>
        <result column="softServiceUseTime" jdbcType="TIMESTAMP" property="softServiceUseTime"/>
    </resultMap>
    <select id="getDataRankOrderInfo" resultMap="orderInfoHandleMap">
        select oa.id as id,
        oa.create_time as create_time,
        oa.order_id as order_id,
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        oa.sku_offering_name as sku_offering_name,
        oa.sku_offering_code as sku_offering_code,
        oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_class as atom_offering_class,
        oa.supplier_name as supplier_name,
        oa.model as model,
        oa.color as color,
        oa.atom_quantity*oa.sku_quantity as quantity,
        oa.atom_price as atom_price,
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.order_status as order_status,
        cp.cooperator_id as cooperator_id,
        cp.user_name cooperator_name,
        cp.partner_name as partner_name,
        cph.cooperator_id as finish_cooperator_id,
        cph.user_name finish_cooperator_name,
        oi.spu_offering_class as spu_offering_class,
        oi.sync_k3_id,
        oi.special_after_market_handle,
        oi.special_after_status,
        oi.special_after_latest_time,
        oi.addr1 addr1,
        oi.order_type orderType,
        oi.qly_status qlyStatus,
        si.url,
        si.img_url imgUrl,
        oi.ysx_status ysxStatus,
        oa.car_open_status carOpenStatus,
        aoi.ext_soft_offering_code extSoftOfferingCode,
        oi.kx_refund_status kxRefundStatus,
        oa.allow_order_status allowOrderStatus,
        oa.sku_quantity*sku_price totalPrice,
        oi.special_after_refunds_number specialAfterRefundsNumber,
        oa.soft_service_status softServiceStatus
        from mini_program_user_order_info mpuoi
        left join order_2c_atom_info oa on oa.id = mpuoi.atom_order_id
        left join order_2c_info oi on oa.order_id=oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version =
        oa.spu_offering_version
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_2c_atom_info o2ai
        inner join order_cooperator_relation ocr on o2ai.id = ocr.atom_order_id and o2ai.order_id = ocr.order_id
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by o2ai.id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner join order_2c_atom_info o2ai on o2ai.id = ocrh.atom_order_id and o2ai.order_id = ocrh.order_id
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by o2ai.id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id
        where oa.order_status not in (8)
        and mpuoi.user_id=#{userId}

        <if test="offeringClassList != null and !offeringClassList.isEmpty()">
            and oi.spu_offering_class in
            <foreach item="item" index="index" collection="offeringClassList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="spuCodeList != null and !spuCodeList.isEmpty()">
            and oi.spu_offering_code in
            <foreach item="item" index="index" collection="spuCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderType != null and orderType!=''">
            and oi.order_type = #{orderType}

        </if>
        <if test="businessCodes != null and !businessCodes.isEmpty()">
            and oi.business_code in
            <foreach item="item" index="index" collection="businessCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null and startTime!=''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime!=''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        ORDER BY oa.update_time DESC
    </select>
    <!-- 获取访问人数 -->
    <select id="getDataRankVisitCount" resultType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityAccessVO">
        SELECT access_time, count(user_id) count
        from mini_program_activity_access mpaa
        where mpaa.activity_id=#{activityId}
        GROUP BY access_time;
    </select>

    <resultMap id="city_result_map" type="com.chinamobile.retail.pojo.vo.miniprogram.ActivityCityVO">
        <id property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
    </resultMap>

    <resultMap id="province_result_map" type="com.chinamobile.retail.pojo.vo.miniprogram.ActivityProvinceVO">
        <id property="provinceCode" column="province_mall_code"/>
        <result property="provinceName" column="province_mall_name"/>
        <collection property="cities" ofType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityCityVO"
                    resultMap="city_result_map"/>
    </resultMap>

    <select id="getAllRegions" resultMap="province_result_map">
        SELECT p.mall_code as province_mall_code,
               p.mall_name as province_mall_name,
               c.mall_code as city_code,
               c.mall_name as city_name
        FROM contract_province_info p
                 LEFT JOIN contract_city_info c ON p.mall_code = c.province_mall_code
    </select>
    <select id="searchSpuByCode" resultType="com.chinamobile.retail.pojo.entity.SpuOfferingInfo">
        select *
        from spu_offering_info spu
        join sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND sku.delete_time
        is null
        WHERE
        spu.offering_status = 1
        AND spu.delete_time is null
        <if test="spuCode != null and spuCode !='' ">
            AND (spu.offering_code like concat('%',#{spuCode},'%') or spu.offering_name like concat('%',#{spuCode},'%'))
        </if>
        group by spu.offering_code
    </select>
    <select id="searchSpuAllByCode" resultType="com.chinamobile.retail.pojo.entity.SpuOfferingInfo">
        select *
        from spu_offering_info spu
        join sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status !='2' and sku.delete_time is null
        WHERE
         spu.offering_status != '2' and
         spu.delete_time is null
        <if test="spuCode != null and spuCode !='' ">
            AND (spu.offering_code like concat('%',#{spuCode},'%') or spu.offering_name like concat('%',#{spuCode},'%'))
        </if>
        group by spu.offering_code
    </select>
    <select id="getActivityExtraRankAward"
            parameterType="java.lang.String"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityExtraRankAwardVO">
        SELECT a.id           AS id,
               a.activity_id  AS activityId,
               a.type         AS type,
               a.points       AS points,
               a.product      AS product,
               a.product_img  AS productImg,
               a.supplier_id  AS supplierId,
               ps.full_name   AS supplierName,
               a.ranking_from AS rankingFrom,
               a.ranking_to   AS rankingTo,
               a.award_name   AS awardName
        FROM mini_program_activity_rank_award a
                 LEFT JOIN point_supplier ps ON ps.id = a.supplier_id
        WHERE a.activity_id = #{activityId}
        ORDER BY a.id
    </select>

    <select id="getActivityExtraWeeklyAward"
            parameterType="java.lang.String"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.ActivityExtraWeeklyAwardVO">
        SELECT a.id               AS id,
               a.activity_id      AS activityId,
               a.type             AS type,
               a.points           AS points,
               a.product          AS product,
               a.product_img      AS productImg,
               a.supplier_id      AS supplierId,
               ps.full_name       AS supplierName,
               a.probability      AS probability,
               a.award_name       AS awardName,
               a.max_awards       AS maxAwards,
               a.max_awards_daily AS maxAwardsDaily
        FROM mini_program_activity_weekly_fun_award a
                 LEFT JOIN point_supplier ps ON ps.id = a.supplier_id
        WHERE a.activity_id = #{activityId}
        ORDER BY a.id
    </select>

    <!-- 小程序前台查询活动列表 -->
    <select id="searchMiniActivity"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageActivityVO">
        select a.id as id,
        a.name as name,
        a.start_time as startTime,
        a.stop_time as stopTime,
        a.audit_status as auditStatus,
        a.create_time as createTime,
        a.status as status,
        a.active as active,
        a.list_img as listImg,
        a.activity_type as activityType
        from mini_program_activity a
        left join mini_program_activity_region r on r.activity_id = a.id
        where 1 = 1
        and a.audit_status = 2
        and a.is_delete= 0
        <if test="keyWord != null and keyWord !='' ">
            and a.name like CONCAT('%',#{keyWord},'%')
        </if>

        <if test="provinceCode != null and provinceCode !='' ">
            and r.province_code=#{provinceCode}
        </if>
        group by a.id, a.create_time
        order by a.create_time desc
    </select>

    <select id="homeSearchMiniActivity"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.HomeSearchVO">
        select a.id as activityId,
        a.name as activityName,
        a.start_time as startTime,
        a.stop_time as stopTime,
        a.status as status,
        a.list_img as listImg,
        a.activity_type as activityType
        from mini_program_activity a
        left join mini_program_activity_region r on r.activity_id = a.id
        where 1 = 1
        and a.audit_status = 2
        and a.is_delete= 0
        <if test="keyWord != null and keyWord !='' ">
            and (a.name like CONCAT('%',#{keyWord},'%')
                or
                 (
                     case
                         when a.activity_type = 1 then exists(select 1 from mini_program_activity_rank where description like  CONCAT('%',#{keyWord},'%') and activity_id = a.id)
                         when a.activity_type = 2 then exists(select 1 from mini_program_activity_weekly_fun where description like  CONCAT('%',#{keyWord},'%') and activity_id = a.id)
                     END
            )
                 )
        </if>
        <if test="provinceCode != null and provinceCode !='' ">
            and r.province_code = #{provinceCode}
        </if>
          <if test="cityCode != null and cityCode !='' ">
            and r.city_code = #{cityCode}
        </if>
        group by a.id, a.create_time
        order by a.create_time desc
    </select>

    <select id="getActivityWeeklyFunAwardCount"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ActivityWeeklyFunAwardCountParam"
            resultType="com.chinamobile.retail.pojo.dto.ActivityWeeklyFunAwardCountDTO">
        select
        a.award_id as awardId,
        count(a.id) as today
        from mini_program_activity_user_award a
        where 1=1
        <if test="awardIds != null and !awardIds.isEmpty()">
            and a.award_id in
            <foreach item="item" index="index" collection="awardIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="begin != null ">
            and a.create_time <![CDATA[ > ]]> #{begin}
        </if>
        <if test="end != null ">
            and a.create_time <![CDATA[ < ]]> #{end}
        </if>
        group by a.award_id
    </select>
</mapper>
