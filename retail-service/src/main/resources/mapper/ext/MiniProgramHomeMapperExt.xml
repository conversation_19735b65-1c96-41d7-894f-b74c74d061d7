<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramHomeMapperExt">

    <!-- 分页查询小程序首页列表 -->
    <select id="pageHomeList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageHomeListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.HomeVO">
        select
        a.id as id,
        a.province_code as provinceCode,
        p.mall_name as provinceName,
        u.name as createUserName,
        a.create_time as createTime,
        a.audit_status as auditStatus,
        a.status as status
        from mini_program_home a
        left join user u on u.user_id = a.creator_id
        left join contract_province_info p on p.mall_code = a.province_code

        <where>
            and a.is_delete = 0
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="auditStatus != null ">
                and a.audit_status = #{auditStatus}
            </if>
            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and a.audit_status in
                <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>

            <if test="provinceCode != null and provinceCode != ''">
                and a.province_code = #{provinceCode}
            </if>


            <if test="createUserName != null and createUserName != ''">
                and u.name like concat('%',#{createUserName}, '%')
            </if>


        </where>
        order by a.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序首页总数 -->
    <select id="countHomeList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageHomeListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select a.id
        from mini_program_home a
        left join user u on u.user_id = a.creator_id
        <where>
            and a.is_delete = 0
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="auditStatus != null ">
                and a.audit_status = #{auditStatus}
            </if>

            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and a.audit_status in
                <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>

            <if test="provinceCode != null and provinceCode != ''">
                and a.province_code = #{provinceCode}
            </if>


            <if test="createUserName != null and createUserName != ''">
                and u.name like concat('%',#{createUserName}, '%')
            </if>

        </where>

        ) t
    </select>


    <select id="getUserName" parameterType="String" resultType="String">
        select u.name
        from user u
        where u.user_id = #{userId}
    </select>

    <select id="getProvinceName" parameterType="String" resultType="String">
        select c.mall_name
        from contract_province_info c
        where c.mall_code = #{provinceCode}
    </select>

    <select id="getHomeProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO"
            parameterType="String">

        SELECT spu.offering_code                  AS spuCode,
               spu.offering_name                  AS spuName,
               spu.img_url                        AS image,
               IFNULL(MIN(sku.price), 0)          AS price,
               spu.tag                            AS saleTag,
               spu.product_description            AS spuRemark,
               sc.core_component_name             AS coreComponentName,
               sc.core_component_img              AS coreComponentImg,
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel
        FROM mini_program_home_spu ms
                 INNER JOIN spu_offering_info spu ON spu.offering_code = ms.spu_code
                 INNER JOIN sku_offering_info sku
                            ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND sku.delete_time is null
                 LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component sc ON sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        WHERE ms.home_id = #{homeId}
          AND spu.offering_status = 1
          AND spu.delete_time is null
        GROUP BY ms.spu_code, ms.sort
        ORDER BY ms.sort
    </select>

    <!-- 分页查询小程序资讯列表 -->
    <select id="getHomeInfo"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO">
        select
        a.id as id,
        a.name as name,
        a.category as category,
        a.info_type as infoType,
        a.content_type as contentType,
        a.audit_status as auditStatus,
        a.head_img_url_1 as headImgUrl1,
        a.head_img_url_2 as headImgUrl2,
        a.create_time as createTime,
        a.status as status
        from mini_program_home_info mi
        left join mini_program_info a on a.id = mi.info_id
        where mi.home_id = #{homeId} and mi.content_type = #{contentType}
    </select>

</mapper>
