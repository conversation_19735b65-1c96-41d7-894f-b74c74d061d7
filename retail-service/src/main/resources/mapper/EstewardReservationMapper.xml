<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.EstewardReservationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.EstewardReservation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="reservation_order_id" jdbcType="VARCHAR" property="reservationOrderId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_count" jdbcType="INTEGER" property="productCount" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="user_addr" jdbcType="VARCHAR" property="userAddr" />
    <result column="user_addr_detail" jdbcType="VARCHAR" property="userAddrDetail" />
    <result column="is_hidden" jdbcType="VARCHAR" property="isHidden" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reservation_time" jdbcType="TIMESTAMP" property="reservationTime" />
    <result column="reservation_wf_id" jdbcType="VARCHAR" property="reservationWfId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, reservation_order_id, spu_code, product_id, product_count, user_id, user_name, 
    user_phone, user_addr, user_addr_detail, is_hidden, create_time, update_time, reservation_time, 
    reservation_wf_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from esteward_reservation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from esteward_reservation
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from esteward_reservation
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from esteward_reservation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_reservation (id, reservation_order_id, spu_code, 
      product_id, product_count, user_id, 
      user_name, user_phone, user_addr, 
      user_addr_detail, is_hidden, create_time, 
      update_time, reservation_time, reservation_wf_id
      )
    values (#{id,jdbcType=VARCHAR}, #{reservationOrderId,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, 
      #{productId,jdbcType=VARCHAR}, #{productCount,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, 
      #{userName,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, #{userAddr,jdbcType=VARCHAR}, 
      #{userAddrDetail,jdbcType=VARCHAR}, #{isHidden,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{reservationTime,jdbcType=TIMESTAMP}, #{reservationWfId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_reservation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reservationOrderId != null">
        reservation_order_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productCount != null">
        product_count,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="userAddr != null">
        user_addr,
      </if>
      <if test="userAddrDetail != null">
        user_addr_detail,
      </if>
      <if test="isHidden != null">
        is_hidden,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reservationTime != null">
        reservation_time,
      </if>
      <if test="reservationWfId != null">
        reservation_wf_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="reservationOrderId != null">
        #{reservationOrderId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        #{productCount,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userAddr != null">
        #{userAddr,jdbcType=VARCHAR},
      </if>
      <if test="userAddrDetail != null">
        #{userAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="isHidden != null">
        #{isHidden,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationTime != null">
        #{reservationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationWfId != null">
        #{reservationWfId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from esteward_reservation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_reservation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.reservationOrderId != null">
        reservation_order_id = #{record.reservationOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.productCount != null">
        product_count = #{record.productCount,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.userPhone != null">
        user_phone = #{record.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.userAddr != null">
        user_addr = #{record.userAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.userAddrDetail != null">
        user_addr_detail = #{record.userAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.isHidden != null">
        is_hidden = #{record.isHidden,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reservationTime != null">
        reservation_time = #{record.reservationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reservationWfId != null">
        reservation_wf_id = #{record.reservationWfId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_reservation
    set id = #{record.id,jdbcType=VARCHAR},
      reservation_order_id = #{record.reservationOrderId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=VARCHAR},
      product_count = #{record.productCount,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      user_addr = #{record.userAddr,jdbcType=VARCHAR},
      user_addr_detail = #{record.userAddrDetail,jdbcType=VARCHAR},
      is_hidden = #{record.isHidden,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      reservation_time = #{record.reservationTime,jdbcType=TIMESTAMP},
      reservation_wf_id = #{record.reservationWfId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_reservation
    <set>
      <if test="reservationOrderId != null">
        reservation_order_id = #{reservationOrderId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        product_count = #{productCount,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userAddr != null">
        user_addr = #{userAddr,jdbcType=VARCHAR},
      </if>
      <if test="userAddrDetail != null">
        user_addr_detail = #{userAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="isHidden != null">
        is_hidden = #{isHidden,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationTime != null">
        reservation_time = #{reservationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationWfId != null">
        reservation_wf_id = #{reservationWfId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.EstewardReservation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_reservation
    set reservation_order_id = #{reservationOrderId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      product_count = #{productCount,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      user_addr = #{userAddr,jdbcType=VARCHAR},
      user_addr_detail = #{userAddrDetail,jdbcType=VARCHAR},
      is_hidden = #{isHidden,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reservation_time = #{reservationTime,jdbcType=TIMESTAMP},
      reservation_wf_id = #{reservationWfId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_reservation
    (id, reservation_order_id, spu_code, product_id, product_count, user_id, user_name, 
      user_phone, user_addr, user_addr_detail, is_hidden, create_time, update_time, reservation_time, 
      reservation_wf_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.reservationOrderId,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, 
        #{item.productId,jdbcType=VARCHAR}, #{item.productCount,jdbcType=INTEGER}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.userName,jdbcType=VARCHAR}, #{item.userPhone,jdbcType=VARCHAR}, #{item.userAddr,jdbcType=VARCHAR}, 
        #{item.userAddrDetail,jdbcType=VARCHAR}, #{item.isHidden,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.reservationTime,jdbcType=TIMESTAMP}, 
        #{item.reservationWfId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 24 09:35:08 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_reservation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'reservation_order_id'.toString() == column.value">
          #{item.reservationOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=VARCHAR}
        </if>
        <if test="'product_count'.toString() == column.value">
          #{item.productCount,jdbcType=INTEGER}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'user_phone'.toString() == column.value">
          #{item.userPhone,jdbcType=VARCHAR}
        </if>
        <if test="'user_addr'.toString() == column.value">
          #{item.userAddr,jdbcType=VARCHAR}
        </if>
        <if test="'user_addr_detail'.toString() == column.value">
          #{item.userAddrDetail,jdbcType=VARCHAR}
        </if>
        <if test="'is_hidden'.toString() == column.value">
          #{item.isHidden,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'reservation_time'.toString() == column.value">
          #{item.reservationTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'reservation_wf_id'.toString() == column.value">
          #{item.reservationWfId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>