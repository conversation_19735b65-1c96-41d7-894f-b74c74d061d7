<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.SpuCoreComponentMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.SpuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="core_component_name" jdbcType="VARCHAR" property="coreComponentName" />
    <result column="core_component_img" jdbcType="VARCHAR" property="coreComponentImg" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="create_uid" jdbcType="VARCHAR" property="createUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_code, core_component_name, core_component_img, status, audit_status, is_delete, 
    create_uid, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from spu_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from spu_core_component
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_core_component
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponentExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_core_component (id, spu_code, core_component_name, 
      core_component_img, status, audit_status, 
      is_delete, create_uid, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{coreComponentName,jdbcType=VARCHAR}, 
      #{coreComponentImg,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{auditStatus,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=BIT}, #{createUid,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_core_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="coreComponentName != null">
        core_component_name,
      </if>
      <if test="coreComponentImg != null">
        core_component_img,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createUid != null">
        create_uid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentName != null">
        #{coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentImg != null">
        #{coreComponentImg,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="createUid != null">
        #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from spu_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_core_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.coreComponentName != null">
        core_component_name = #{record.coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="record.coreComponentImg != null">
        core_component_img = #{record.coreComponentImg,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.createUid != null">
        create_uid = #{record.createUid,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_core_component
    set id = #{record.id,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      core_component_name = #{record.coreComponentName,jdbcType=VARCHAR},
      core_component_img = #{record.coreComponentImg,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      is_delete = #{record.isDelete,jdbcType=BIT},
      create_uid = #{record.createUid,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_core_component
    <set>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentName != null">
        core_component_name = #{coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentImg != null">
        core_component_img = #{coreComponentImg,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="createUid != null">
        create_uid = #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.SpuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_core_component
    set spu_code = #{spuCode,jdbcType=VARCHAR},
      core_component_name = #{coreComponentName,jdbcType=VARCHAR},
      core_component_img = #{coreComponentImg,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=BIT},
      create_uid = #{createUid,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_core_component
    (id, spu_code, core_component_name, core_component_img, status, audit_status, is_delete, 
      create_uid, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.coreComponentName,jdbcType=VARCHAR}, 
        #{item.coreComponentImg,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.auditStatus,jdbcType=INTEGER}, 
        #{item.isDelete,jdbcType=BIT}, #{item.createUid,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:58:23 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_core_component (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'core_component_name'.toString() == column.value">
          #{item.coreComponentName,jdbcType=VARCHAR}
        </if>
        <if test="'core_component_img'.toString() == column.value">
          #{item.coreComponentImg,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="'is_delete'.toString() == column.value">
          #{item.isDelete,jdbcType=BIT}
        </if>
        <if test="'create_uid'.toString() == column.value">
          #{item.createUid,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>