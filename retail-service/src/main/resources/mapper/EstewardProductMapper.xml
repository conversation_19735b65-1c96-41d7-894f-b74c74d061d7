<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.EstewardProductMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.EstewardProduct">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="head_url" jdbcType="VARCHAR" property="headUrl" />
    <result column="product_link" jdbcType="VARCHAR" property="productLink" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="business_category" jdbcType="INTEGER" property="businessCategory" />
    <result column="sub_business_type" jdbcType="INTEGER" property="subBusinessType" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="show_detail" jdbcType="INTEGER" property="showDetail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, show_type, product_name, head_url, product_link, business_type, channel, business_category, 
    sub_business_type, spu_code, create_user_id, status, audit_status, delete_time, create_time, 
    update_time, show_detail
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardProductExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from esteward_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from esteward_product
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from esteward_product
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardProductExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from esteward_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.EstewardProduct">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_product (id, show_type, product_name, 
      head_url, product_link, business_type, 
      channel, business_category, sub_business_type, 
      spu_code, create_user_id, status, 
      audit_status, delete_time, create_time, 
      update_time, show_detail)
    values (#{id,jdbcType=VARCHAR}, #{showType,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, 
      #{headUrl,jdbcType=VARCHAR}, #{productLink,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, 
      #{channel,jdbcType=INTEGER}, #{businessCategory,jdbcType=INTEGER}, #{subBusinessType,jdbcType=INTEGER}, 
      #{spuCode,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{deleteTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{showDetail,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.EstewardProduct">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="showType != null">
        show_type,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="headUrl != null">
        head_url,
      </if>
      <if test="productLink != null">
        product_link,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="businessCategory != null">
        business_category,
      </if>
      <if test="subBusinessType != null">
        sub_business_type,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="showDetail != null">
        show_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        #{showType,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="headUrl != null">
        #{headUrl,jdbcType=VARCHAR},
      </if>
      <if test="productLink != null">
        #{productLink,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="businessCategory != null">
        #{businessCategory,jdbcType=INTEGER},
      </if>
      <if test="subBusinessType != null">
        #{subBusinessType,jdbcType=INTEGER},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showDetail != null">
        #{showDetail,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.EstewardProductExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from esteward_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_product
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.showType != null">
        show_type = #{record.showType,jdbcType=INTEGER},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.headUrl != null">
        head_url = #{record.headUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.productLink != null">
        product_link = #{record.productLink,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.businessCategory != null">
        business_category = #{record.businessCategory,jdbcType=INTEGER},
      </if>
      <if test="record.subBusinessType != null">
        sub_business_type = #{record.subBusinessType,jdbcType=INTEGER},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.showDetail != null">
        show_detail = #{record.showDetail,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_product
    set id = #{record.id,jdbcType=VARCHAR},
      show_type = #{record.showType,jdbcType=INTEGER},
      product_name = #{record.productName,jdbcType=VARCHAR},
      head_url = #{record.headUrl,jdbcType=VARCHAR},
      product_link = #{record.productLink,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=INTEGER},
      business_category = #{record.businessCategory,jdbcType=INTEGER},
      sub_business_type = #{record.subBusinessType,jdbcType=INTEGER},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      show_detail = #{record.showDetail,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.EstewardProduct">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_product
    <set>
      <if test="showType != null">
        show_type = #{showType,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="headUrl != null">
        head_url = #{headUrl,jdbcType=VARCHAR},
      </if>
      <if test="productLink != null">
        product_link = #{productLink,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="businessCategory != null">
        business_category = #{businessCategory,jdbcType=INTEGER},
      </if>
      <if test="subBusinessType != null">
        sub_business_type = #{subBusinessType,jdbcType=INTEGER},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showDetail != null">
        show_detail = #{showDetail,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.EstewardProduct">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update esteward_product
    set show_type = #{showType,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      head_url = #{headUrl,jdbcType=VARCHAR},
      product_link = #{productLink,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      channel = #{channel,jdbcType=INTEGER},
      business_category = #{businessCategory,jdbcType=INTEGER},
      sub_business_type = #{subBusinessType,jdbcType=INTEGER},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      show_detail = #{showDetail,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_product
    (id, show_type, product_name, head_url, product_link, business_type, channel, business_category, 
      sub_business_type, spu_code, create_user_id, status, audit_status, delete_time, 
      create_time, update_time, show_detail)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.showType,jdbcType=INTEGER}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.headUrl,jdbcType=VARCHAR}, #{item.productLink,jdbcType=VARCHAR}, #{item.businessType,jdbcType=INTEGER}, 
        #{item.channel,jdbcType=INTEGER}, #{item.businessCategory,jdbcType=INTEGER}, #{item.subBusinessType,jdbcType=INTEGER}, 
        #{item.spuCode,jdbcType=VARCHAR}, #{item.createUserId,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.auditStatus,jdbcType=INTEGER}, #{item.deleteTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.showDetail,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 22 09:02:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into esteward_product (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'show_type'.toString() == column.value">
          #{item.showType,jdbcType=INTEGER}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'head_url'.toString() == column.value">
          #{item.headUrl,jdbcType=VARCHAR}
        </if>
        <if test="'product_link'.toString() == column.value">
          #{item.productLink,jdbcType=VARCHAR}
        </if>
        <if test="'business_type'.toString() == column.value">
          #{item.businessType,jdbcType=INTEGER}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=INTEGER}
        </if>
        <if test="'business_category'.toString() == column.value">
          #{item.businessCategory,jdbcType=INTEGER}
        </if>
        <if test="'sub_business_type'.toString() == column.value">
          #{item.subBusinessType,jdbcType=INTEGER}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'show_detail'.toString() == column.value">
          #{item.showDetail,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>