#spring:
#  cloud:
#    nacos:
#      discovery:
#        namespace: test
#        server-addr: 10.12.57.2:8848
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    hikari:
#      idle-timeout: 180000
#      max-lifetime: 30000
#      maximum-pool-size: 8
#      minimum-idle: 4
#    password: app_!QAZxsw2
#    url: jdbc:mysql://**********:3306/supply_chain?useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true
#    username: supply
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#  redis:
#    cluster:
#      nodes: **********:6381,**********:6381,**********:6381
#    password: app_!QAZxsw2
#    pool:
#      max-active: 5
#      max-idle: 5
#      max-wait: -1
#      min-idle: 0
#    timeout: 10000
#  servlet:
#    multipart:
#      max-file-size: 10MB
#      max-request-size: 10MB
#product:
#  shareUrl:
#    suffix: sf=%d&st=a&share=y&u=%s
#
##短信模板id
#sms:
#  validCodeTempId: 106035 #105689
#  retailCodeLoginTempId: 106236 #105784
#  retailCodeRegTempId: 106236 #
#
##日志打印
#logging:
#  level:
#    org.mybatis: debug
#    org.apache.ibatis: debug
#    com.chinamobile.retail.dao.ext: debug
#
##解密密钥
#iot:
#  secretKey: 82E4FE7FE78FE293
#  encodeKey: 3D88F1C1AAE7
#
##分销大厅链接
#distribution:
#  #  link: https://iotbase.cmcconenet.com:1005/mallos/distribution/jump
#  #  mglink: https://iotbase.cmcconenet.com:1005/mallos/distribution/error
#  link: https://************:1005/mallos/distribution/jump
#  mglink: https://************:1005/mallos/distribution/error
#lakala:
#  url: https://wopen.dshytest.com/w/openapi/proxypay
#  signSupplierUid: ff03f5a1eeee490e
#  notify:
#    url: https://**************:1005/mallos/api/retail/partner/point/exchangeResult
#  main:
#    id: 9
#  public:
#    key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBkjQADiGzay1O8Ar3q3ky18dp0OdmkEaRnuARblzNZS0v5eEbl5MVgJWrRsT7kK7cMXdxsriAcvicSLiVFpP3oYLizbeTj7NiVH+J6MwggVYqnXZul3T6yVh5Jq0TJGXzyWHbtmMNLDxL/iWO4C8a1tNIhd07rVKF/SrMZ6jxpUyrUc8Y8cr59tND0IQpwVRcFevyMOLVZQPEtlGAq4rbHZ6JMTb6/I1f+k6MLqcnvXQ6Hd6MNwV9uOjmVDEtt0mgUeuSoUX1hXzMBZR7NO2OVXrJSWgFkP5TzTXXNoNIdURnWhUuJ6teJliMvEHYyFaPXVQmWvygzbSdPg2sX8RwIDAQAB
#private:
#  key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDTyrPZuAjILMsqTPxkkh3Rjp4/Cl94mw+8HP2lyN71m4GvOiAQQiT2JgsT/G//6XfcDqwPnc7VTIY7+3NN2dTR/D40dE9IHhLTBjMyuBAU4vJ9iwzBHv0dPfY17DTBNvd/MP7Rsp+j+YGaOhWaGlsgm6TVtlQey7QsmvWAV0YXGlyAf07ssmh1/M2bfxE95YrnpyUhjDZQzA+rq51kS7EAuPE/UNSOgDklwOKLi5guAhvI1Q6rLjtufdC+JqYrIPmxjRhjW1RFE2UNCw7LqBfByb9n8mxgP4LKBjiCLYAfEMfzieIQUNpLSj+a3xo1S4rpMGiR7TYxFLBlzdcxif6NAgMBAAECggEAWfSyxzq/oCZGdMUWPrFBMQzecfA59MQHvuKhaZUT/kX6oy6RB9bQVCx8cBS8jXngivtAYbGpdDd4nGmE5AAtwLLeyPDN69e6Lx9nB5feXMC4NlKlLDG5WH5E4UpebeKm5MMuuGqiG8eSIKujGT0wj30MWimDOUFUWc3HkKeBAbZOo7xRs28cSHk81G7mpiYmCQUJVyOCbdDOwGMkE51t07iVGNswXRqYaavVI85eEB4QRuPAdCGOZff/ll6rVrJPEBj8ch03oDEszdQb+0B39pUi3sfex2AGvLISSJTSy9cab43MCcGYZdL4leX7Eq+x3exZO3fmVPTm3dtJyhiPEQKBgQD0VWXMofRAHPl8cxf/lq1PAcalffBUo24Fv1DfsD9yNxagM8s5PzChsrTpi4BxEUdMZeeQrUtlrHgxWF9sBp21LczObkkqKQ9/b0M3rXIS91bqsQsFWS2OsGFzLo+voxtGip5KK9ORI34cwSaXxuJ5kwC5hAXnrG8I9Ef7IyWLewKBgQDd54gdKsR80HIuRLsirQZP/cKbHgcjQY1OATtk8q6b/rtvO9t0wSOeCCRMuv/EuKGk7POtqyuvwutqg1WKNxMy1bY9ty4bRXd44ii4aCYGDOAl8NFtJCSMWr++t6XzNHbwClbLSbY/tOGGIVumg1p+udu+btOb8lpHg7/WyHBblwKBgQC+3D6rPQ/JTVjuCBIFC7TR9Lcx61DjLM7zGmGYetr042d/OTZUv7HDfg+oJ9rrd+3UFf5vm488Gx/AnCHeBsUHFIHZ93ibwHtktosxYQGtIxz4M9hCE0ltHwbgrMx9DNJvpjTEB7w6shj/aTo2cZvUECsOv7zFHoOV4QyhdvELJQKBgBkluvwrM5c9fCMYMOjuGNAJ3vr7PS3WqO/VHekDw5v0E0O40db6aFHpdEupyYB+t/rby4W75ziE627nsVL3iNpy87MsxjHa/n4ZiynSy7RV0zUZhHJM7UNmqWIwp8LXCD+NvGZPVTMFCaXMs/k7246O+4MqRhrfLTH7kUsC7hDDAoGBAMs+rMVstr9udOIsKLmADBXPGK+UuWbtJAVCSyX61EuKz8gJL8KyXOlc3Dnokf4Qhh5ZMtslM6VPeSwGkHCH0CA66t/Aq56TCkbQi1IrJbc7gCL/CuX1jly8pOQs8widY/U7UrVxsJ87POVVXsf3I35FD198uYrqXzklLNGX7X6l
#public:
#  key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA08qz2bgIyCzLKkz8ZJId0Y6ePwpfeJsPvBz9pcje9ZuBrzogEEIk9iYLE/xv/+l33A6sD53O1UyGO/tzTdnU0fw+NHRPSB4S0wYzMrgQFOLyfYsMwR79HT32New0wTb3fzD+0bKfo/mBmjoVmhpbIJuk1bZUHsu0LJr1gFdGFxpcgH9O7LJodfzNm38RPeWK56clIYw2UMwPq6udZEuxALjxP1DUjoA5JcDii4uYLgIbyNUOqy47bn3QviamKyD5sY0YY1tURRNlDQsOy6gXwcm/Z/JsYD+CygY4gi2AHxDH84niEFDaS0o/mt8aNUuK6TBoke02MRSwZc3XMYn+jQIDAQAB
#feign:
#  client:
#    config:
#      default:
#        connectTimeout: 10000 #单位毫秒
#        readTimeout: 10000 #单位毫秒
#weixin:
#  appId: wx5c7c94a53b783f39
#  appSecret: b42b36a664dbd90d0b91e56044d2296b
#  application:
#    appId: wx6e135d2f5762cca4
#    appSecret: ed04ff6d12e4df707b81eb4a59f4f282
#  getShareCode: https://api.weixin.qq.com/wxa/getwxacodeunlimit
#  getShareUrl: https://api.weixin.qq.com/wxa/generate_urllink
#  weixinShareCode:
#    appId: wx3807b6eab2476749
#    appSecret: a9c2a3e9a9eeed78fe27672e13dca28c
#whitelist:
#  visitor: 18523046327,18053846686,13648047960,13594253053,13896987253,17383062157,18223126515
#    ,13752954000,13340260645,15823524720,13658312520,15213091924,13668497025,13996250880,13896119290
#    ,18716643042,13594176527
#    ,17896205585,18741060066,17847909190
#    ,18841087789,13804100200,15542977211,13941073338,13654103777,13841005577,13841082388,13941037234,15041070199,18241013231,15941088699,15841003029
#    ,18341016817,13591015678,18841017955,13941076911,13704900818,15041095655,18841051789,15004102133,13704909994,18841087789,
#    18742202873,13504109273,15241049897,15042002525,13470169997,15141082666,18841042609,13614106767,18524816777,15941019876,18604905725,18341099555,13464482865,13904906445,13941029587,15941061201,15941070901,15184110666,18340196967,18340129229,13704901112,18741009492,15941081777,13841029699,15041013336,13841069956,13841065755,13841069229,13591059908,13504108473,13804104608,13500400566,13514104646,15841000883,18204101860,18242620345,13610908000,15898061902,18841009923,13804107898,18341065055,15804100100,15241009793,18341016060,15724351131,13704105040,15941036649,18340168111,18841011789,13654102017,13941060240,17741535555,15041009686,18904107777,18341099988,15141887711,18341049333,15904109168,13941042288,15104104588,15941031113,18741073777,15214247777,13470158866,13941041559,13941042288,13614903666,19904950666,15241706562,13700100855,13704106577,13591003229,18041028888,18241015667,13478359898,15841057299,18341036686,18241011988,18241011196,15042071877,13841043677,13464134656,13591097797,15141010102,13591021151,15042022006,13591626762,13591014178,13464185288,18741016161,13464484548,13591024295,15141039136,15724357877,15542071877,15841074888,15241010203,13514108850,18241001788,13591626888,15241055959,13470127789,13898225200,13470106888,18841099992,13941016242,18841006230,15144443455,15214202222,13654106666,13704107752,13591097727,15898085111,13804105933,15714036668,15041048688,15714032999,18804100543,15184138666,18242639988,15898077979,13464133377,13464130051,13591034288,13841052688,18841080811,18841002255,13504103320,13941031771,18841049777,18241086678,15164143737,15104252211,13674108666,15841074888,13470147527,13464132555,13464156116,13470116663,18604903307,18341049333,15241056555,15941050006,13941070488,15104256088,15141883888,18241056888,13464130009,13644108111,13941047929,13464133030,15164123466,18241027788,13614102422,13464078878,13591014949,15184124666,15042097654,15141877337,18241031262,13591058520,15898000575,15898038288,15141496969,18241072227,13081794009,18242635554,13941022944,15898091977,15542054495,13154109040,15241076782,13841027101,15141009992,15141038889,15141865988,15841033188,15841061677,15241700488,13941042588,18241077888,18241039039,15941091558,13464178777,15941012656,15841029668,18841045789,15141067678,18340127666,15898098186,13464086000,18341031155,13841031186,18841046678,15214269988,13470164988,13841031444,13674104469,15141898883,13941009036,13614105688,15141078000,15041070199,13591060505,18241006687,15898039345,15898046127,18341038849,18741086011,13841094599,15241098123,15042088855,15184123457,13941048881,15141000977,18241018889,15214278358,15724650070,15042011210,14741001110,15904101166,13841082156,18241069662,15841024618,13514103331,13514104785,15042039966,15798312678,15841066363,18741020789,13464100155,13464171888,13654103999,18341059477,15941076591,15214278840,15241039620,18524814190,18741058370,13591077979,13591068898,13624102226,15714037778,13841017176,18841062604,18340162898,18241091097,15724352492,13700107191,15184128567,15941057920,13591079264,15141485789,15241080009,15241022321,13941096019,13464121008,13464131008,13941091001,15042029997,13464173986,15214254666,13464176678,13941080517,13941090517,13941069456,15141060888,13841030045,18841019641,13841053000,15141054168,13464079996,18841055566,18741011161,15941071711,13514102288,13898591978,15241713555,13464071999,19804258599,18241056663,18241038999,18341044010,15904091555,13470171978,15041036678,13591058507,13358995877,15164127911,13840551753,13904103021,13464072888,13841050091,13029276123,15841057222,15041028102,15141011678,15841010155,18341059666,18741022345,18241071888
#
#
##对象存储
#storage:
#  platform: OneNet #目前支持 Qiniu、OneNet平台
#
##oneNET 对象存储
#onenet-storage:
#  queryHttpInner: http://172.19.12.163:9092/mallos/oss/ #访问代理地址
#  queryHttpOuter: http://10.12.57.1/mallos/oss/
#  endpoint: http://s3-qos.iot-st-armtest.qiniu-solutions.com
#  bucketName: mallos-test
#  accessKey: W1aowUWsredwHbsuCeLUbI_wXI8_eNJtSWelhbxD
#  secretKey: zht2qc8vrIdCrL50PB5EdFrZSNAApdlOxQ7wZIFD
#
#org:
#  quartz:
#    dataSource:
#      sgs_quartz:
#        URL: jdbc:mysql://**********:3306/sc_quartz?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=GMT%2B8&useSSL=false
#        driver: com.mysql.cj.jdbc.Driver
#        maxConnections: 30
#        password: app_!QAZxsw2
#        user: supply
#        validateOnCheckout: true
#        validationQuery: select 1
#    jobStore:
#      class: org.quartz.impl.jdbcjobstore.JobStoreTX
#      clusterCheckinInterval: 20000
#      dataSource: sgs_quartz
#      driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#      isClustered: true
#      maxMisfiresToHandleAtATime: 1
#      misfireThreshold: 120000
#      selectWithLockSQL: SELECT * FROM {0}LOCKS WHERE LOCK_NAME = ? FOR UPDATE
#      tablePrefix: qrtz_
#      txIsolationLevelSerializable: true
#      useProperties: true
#    plugin:
#      shutdownhook:
#        class: org.quartz.plugins.management.ShutdownHookPlugin
#        cleanShutdown: true
#      triggHistory:
#        class: org.quartz.plugins.history.LoggingJobHistoryPlugin
#    scheduler:
#      instanceId: AUTO
#      instanceName: retailScheduler
#      skipUpdateCheck: true
#    threadPool:
#      class: org.quartz.simpl.SimpleThreadPool
#      threadCount: 10
#      threadPriority: 5
#      threadsInheritContextClassLoaderOfInitializingThread: true
