#服务配置
server:
  port: 9898
  # 开启优雅下线
  shutdown: graceful
  servlet:
    context-path: /retail
spring:
  application:
    name: supply-chain-retail-svc
  profiles:
    active: test
apollo:
#  meta: http://10.12.4.11:8080
  meta: http://10.12.57.1:8080
  autoUpdateInjectedSpringProperties: true
  bootstrap:
    enabled: true
    namespaces: application.yml
app:
  id: supply-chain-retail
mybatis:
  mapperLocations: classpath*:mapper/*.xml,classpath*:/mapper/ext/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
# 暴露 shutdown和prometheus 接口
management:
  server:
    port: 8090
  endpoint:
    shutdown:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    export:
      prometheus:
        enabled: true