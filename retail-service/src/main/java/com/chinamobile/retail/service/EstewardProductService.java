package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.EstewardProductParam;
import com.chinamobile.retail.pojo.vo.EstewardProductVO;

import java.util.List;

/**
 * EstewardProductService 接口定义了与管家产品相关的业务逻辑操作。
 * 包括产品的查询、新增、更新、审核、上下线、发布以及删除等功能。
 */
public interface EstewardProductService {

    /**
     * 分页获取管家产品列表。
     *
     * @param param 查询参数，包含分页信息和筛选条件
     * @return 包含管家产品信息的分页数据
     */
    PageData<EstewardProductVO> getList(EstewardProductParam param);

    /**
     * 新增一个管家产品。
     *
     * @param record 产品信息参数
     * @param userId 操作用户ID
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer insertOne(EstewardProductParam record, String userId);

    /**
     * 更新一个管家产品。
     *
     * @param record 产品信息参数
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer updateOne(EstewardProductParam record);

    /**
     * 审核多个管家产品。
     *
     * @param records 待审核的产品信息列表
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer audit(List<EstewardProductParam> records);

    /**
     * 下线一个管家产品。
     *
     * @param record 产品信息参数
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer offlineEstewardProduct(EstewardProductParam record);

    /**
     * 发布一个管家产品。
     *
     * @param record 产品信息参数
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer publishEstewardProduct(EstewardProductParam record);

    /**
     * 删除一个管家产品。
     *
     * @param id 产品ID
     * @return 操作结果，包含成功或失败的状态信息
     */
    BaseAnswer deleteOne(String id);
}