package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.constant.*;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.MiniProgramInfoMapperExt;
import com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.MiniProgramInfoRequestOnlineParam;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO;
import com.chinamobile.retail.pojo.vo.miniprogram.KnowledgeHomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import com.chinamobile.retail.service.IMiniInfoService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Slf4j
@Service
public class MiniInfoServiceImpl implements IMiniInfoService {
    private static final int TARGET_MASK_MANAGER = 0x01;
    private static final int TARGET_MASK_DISTRIBUTOR = 0x02;
    private static final int TARGET_MASK_CHANNEL = 0x04;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LogService logService;

    @Resource
    private MiniProgramInfoMapperExt miniProgramInfoMapperExt;

    @Resource
    private MiniProgramInfoMapper miniProgramInfoMapper;
    @Resource
    MiniProgramInfoActivityMapper miniProgramInfoActivityMapper;

    @Resource
    private MiniProgramInfoSpuCodeMapper miniProgramInfoSpuCodeMapper;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private SpuOfferingInfoMapperExt spuOfferingInfoMapperExt;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private MiniProgramInfoRequestOnlineMapper miniProgramInfoRequestOnlineMapper;


    @Override
    @DS("query")
    public InfoDetailVO getInfoDetail(String infoId, String userId, String provinceCode,LoginIfo4Redis loginIfo4Redis) {
        String locationKey = "";
        UserMiniProgram userMiniProgram = null;
        if (StringUtils.isNotBlank(userId)) {
            userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId,
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(userId)
            );
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.USER_NOT_FOUNT);
            }
            locationKey = StringUtils.defaultString(userMiniProgram.getBeId(),"") + userMiniProgram.getLocation();
        } else if (StringUtils.isNotBlank(provinceCode)) {
            locationKey = provinceCode;
        }

        final UserMiniProgram user = userMiniProgram;
        InfoDetailVO result = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_INFO_ID + infoId + locationKey,
                RedisLockConstant.LOCK_INFO_ID + infoId + locationKey, 2, TimeUnit.DAYS, () -> {
                    MiniProgramInfo info = miniProgramInfoMapper.selectByPrimaryKey(infoId);
                    if (ObjectUtils.isEmpty(info)) {
                        return null;
                    }
                    InfoDetailVO vo = new InfoDetailVO();
                    BeanUtils.copyProperties(info, vo);
                    Integer target = info.getCategory();
                    if (target != null) {
                        List<Integer> targetLst = new ArrayList<>();
                        if ((target & TARGET_MASK_MANAGER) != 0) {
                            targetLst.add(TARGET_MASK_MANAGER);

                        }
                        if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
                            targetLst.add(TARGET_MASK_DISTRIBUTOR);

                        }
                        if ((target & TARGET_MASK_CHANNEL) != 0) {
                            targetLst.add(TARGET_MASK_CHANNEL);
                        }

                        vo.setCategoryString(StringUtils.join(targetLst,","));
                    }
                    vo.setCreateUserName(miniProgramInfoMapperExt.getUserName(info.getCreateUid()));
                    if (user != null) {
                        InfoSpuListParam param = new InfoSpuListParam();
                        param.setInfoId(infoId);
                        param.setProvinceCode(user.getBeId());
                        param.setCityCode(user.getLocation());
                        param.setRoleType(user.getRoleType());
                        List<InfoSpuItemVO> infoSpuList = miniProgramInfoMapperExt.getInfoSpuList(param);
                        setLabelList(infoSpuList);
                        vo.setSpuItems(infoSpuList);
                        vo.setActivityList(miniProgramInfoMapperExt.getInfoActivityList(param));
                    } else if (StringUtils.isNotBlank(provinceCode)){
                        InfoSpuListParam param = new InfoSpuListParam();
                        param.setInfoId(infoId);
                        param.setProvinceCode(provinceCode);
                        List<InfoSpuItemVO> infoSpuList = miniProgramInfoMapperExt.getInfoSpuList(param);
                        setLabelList(infoSpuList);
                        vo.setSpuItems(infoSpuList);
                        vo.setActivityList(miniProgramInfoMapperExt.getInfoActivityList(param));
                    }
                    else {
                        List<InfoSpuItemVO> listForWeb = miniProgramInfoMapperExt.getInfoSpuListForWeb(infoId);
                        setLabelList(listForWeb);
                        vo.setSpuItems(listForWeb);
                        vo.setActivityList(miniProgramInfoActivityMapper.selectByExample(new MiniProgramInfoActivityExample()
                                .createCriteria().andInfoIdEqualTo(infoId).example()));
                    }
                    vo.setHasProduct(CollectionUtils.isNotEmpty(miniProgramInfoSpuCodeMapper.selectByExample(
                            new MiniProgramInfoSpuCodeExample().createCriteria().andInfoIdEqualTo(infoId).example())));
                    return vo;
                });
        if (ObjectUtils.isEmpty(result)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }
        if ((user != null || StringUtils.isNotBlank(provinceCode)) && !InfoStatusEnum.PUBLISHED.getStatus().equals(result.getStatus())) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }

        if (loginIfo4Redis != null) {
            String content = "【查看素材】\n" + "素材主题 " + result.getName();
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, content);
        }

        return result;
    }

    private void setLabelList(List<InfoSpuItemVO> infoSpuList) {
        for (InfoSpuItemVO spuItemVO : infoSpuList) {
            if (StringUtils.isNotEmpty(spuItemVO.getSubSaleLabel())) {
                spuItemVO.setSubLabelList(Arrays.asList(spuItemVO.getSubSaleLabel()));
            }
            if (StringUtils.isNotEmpty(spuItemVO.getMainSaleLabel())) {
                spuItemVO.setMainLabelList(Arrays.asList(spuItemVO.getMainSaleLabel()));
            }
        }
    }

    @Override
    @DS("query")
    public PageData<PageInfoVO> pageInfoList(PageInfoListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(PARAM_ERROR, "页码和每页数量必须大于0");
        }
        List<Integer> categoryListInt = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getCategory())) {
            for (String category : param.getCategory()) {
                categoryListInt.add(Integer.valueOf(category));
            }
        }
        int target = 0;
        for (Integer t : categoryListInt) {
            target |= t;
        }
        param.setCategoryNum(target == 0 ? null : target);
        PageData<PageInfoVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = miniProgramInfoMapperExt.countInfoList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<PageInfoVO> list = miniProgramInfoMapperExt.pageInfoList(param);
            list.forEach(item -> {
                Integer target1 = item.getCategory();
                if (target1 != null) {
                    StringBuilder targets = new StringBuilder();
                    if ((target1 & TARGET_MASK_MANAGER) != 0) {

                        targets.append('1');
                    }
                    if ((target1 & TARGET_MASK_DISTRIBUTOR) != 0) {
                        if (targets.length() > 0) {
                            targets.append(',');
                        }
                        targets.append('2');

                    }
                    if ((target1 & TARGET_MASK_CHANNEL) != 0) {
                        if (targets.length() > 0) {
                            targets.append(',');
                        }
                        targets.append('4');
                    }

                    item.setCategoryString(targets.toString());
                }
            });
            pageData.setData(list);
        }
        return pageData;
    }

    @Override
    @DS("query")
    public PageData<PageInfoVO> pageInfoListMini(PageInfoListParam param) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(PARAM_ERROR, "页码和每页数量必须大于0");
        }
        String jsonKey = JSON.toJSONString(param);
        PageData<PageInfoVO> redisVO = (PageData<PageInfoVO>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_MINI_INFO_LIST_PARAM + jsonKey);
        if (redisVO != null) {
            return redisVO;
        }
        return redisUtil.smartLock(RedisLockConstant.LOCK_INFO_LIST_PARAM + jsonKey, () -> {
            PageData<PageInfoVO> redisVO1 = (PageData<PageInfoVO>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_MINI_INFO_LIST_PARAM + jsonKey);
            if (redisVO1 != null) {
                return redisVO1;
            }
            PageData<PageInfoVO> pageData = new PageData<>();
            pageData.setPage(pageNum);
            List<Integer> categoryListInt = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(param.getCategory())) {
                for (String category : param.getCategory()) {
                    categoryListInt.add(Integer.valueOf(category));
                }
            }
            int target = 0;
            for (Integer t : categoryListInt) {
                target |= t;
            }
            param.setCategoryNum(target == 0 ? null : target);
            Long total = miniProgramInfoMapperExt.countInfoList(param);
            pageData.setCount(total != null ? total : 0);
            if (pageData.getCount() > 0) {
                List<PageInfoVO> list = miniProgramInfoMapperExt.pageInfoList(param);
                list.forEach(item -> {
                    Integer target1 = item.getCategory();
                    if (target1 != null) {
                        StringBuilder targets = new StringBuilder();
                        if ((target1 & TARGET_MASK_MANAGER) != 0) {

                            targets.append('1');
                        }
                        if ((target1 & TARGET_MASK_DISTRIBUTOR) != 0) {
                            if (targets.length() > 0) {
                                targets.append(',');
                            }
                            targets.append('2');

                        }
                        if ((target1 & TARGET_MASK_CHANNEL) != 0) {
                            if (targets.length() > 0) {
                                targets.append(',');
                            }
                            targets.append('4');
                        }

                        item.setCategoryString(targets.toString());
                    }
                });
                pageData.setData(list);
            }
            redisTemplate.opsForValue().set(Constant.REDIS_KEY_MINI_INFO_LIST_PARAM + jsonKey, pageData, MiniRedisExpireTimeConstant.MINI_INFO_LIST_EXPIRE_TIME);
            return pageData;
        });

    }

    @Override
    @DS("query")
    public KnowledgeHomeVO pageKnowledgeHome() {
        KnowledgeHomeVO redisVO = (KnowledgeHomeVO) redisTemplate.opsForValue().get(Constant.REDIS_KEY_MINI_KNOWLEDGE_HOME_LIST);
        if (redisVO != null) {
            return redisVO;
        }
        return redisUtil.smartLock(RedisLockConstant.LOCK_KNOWLEDGE_HOME_LIST, () -> {
            //加锁之后二次判断，其他请求是否已经添加缓存
            KnowledgeHomeVO homeVO = (KnowledgeHomeVO) redisTemplate.opsForValue().get(Constant.REDIS_KEY_MINI_KNOWLEDGE_HOME_LIST);
            if (homeVO != null) {
                return homeVO;
            }
            KnowledgeHomeVO knowledgeHomeVO = new KnowledgeHomeVO();
            //获取产品资料  视频
            KnowledgeHomeParam param = new KnowledgeHomeParam();
            param.setKnowledgeType(KnowledgeTypeEnum.PRODUCT_DESC.getType());
            param.setContentType(InfoContentTypeEnum.VIDEO.getType());
            param.setIsPopular(1);
            param.setLimit(4);
            List<PageInfoVO> productInformationList = miniProgramInfoMapperExt.getKnowlegeInfoList(param);
            if (CollectionUtils.isNotEmpty(productInformationList)) {
                knowledgeHomeVO.setProductInformationList(productInformationList);
            }
            //获取操作指南 视频
            KnowledgeHomeParam param1 = new KnowledgeHomeParam();
            param1.setKnowledgeType(KnowledgeTypeEnum.FLOW_HANDLE.getType());
            param1.setContentType(InfoContentTypeEnum.VIDEO.getType());
            param.setIsPopular(1);
            param1.setLimit(4);
            List<PageInfoVO> operationsGuideList = miniProgramInfoMapperExt.getKnowlegeInfoList(param1);
            if (CollectionUtils.isNotEmpty(operationsGuideList)) {
                knowledgeHomeVO.setOperationsGuideList(operationsGuideList);
            }

            //获取热门问答
            KnowledgeHomeParam param2 = new KnowledgeHomeParam();
            param2.setKnowledgeType(KnowledgeTypeEnum.FAQ.getType());
            param2.setIsPopular(1);
            param2.setLimit(5);
            List<PageInfoVO> hotQuestionsAnswersList = miniProgramInfoMapperExt.getKnowlegeInfoList(param2);
            if (CollectionUtils.isNotEmpty(hotQuestionsAnswersList)) {
                knowledgeHomeVO.setHotQuestionsAnswersList(hotQuestionsAnswersList);
            }

            redisTemplate.opsForValue().set(Constant.REDIS_KEY_MINI_KNOWLEDGE_HOME_LIST, knowledgeHomeVO, MiniRedisExpireTimeConstant.MINI_INFO_EXPIRE_TIME);
            return knowledgeHomeVO;
        });


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void create(InfoParam param, String userId) {
        Date now = new Date();
        MiniProgramInfo info = new MiniProgramInfo();
        BeanUtils.copyProperties(param, info);
        info.setId(BaseServiceUtils.getId());
        info.setCreateUid(userId);
        info.setCreateTime(now);
        info.setUpdateTime(now);
        info.setWordType(param.getWordType());
        info.setStatus(InfoStatusEnum.DRAFT.getStatus());
        info.setAuditStatus(InfoAuditStatusEnum.DRAFT.getStatus());
        //info.setAuditStatus(param.getAction() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.IN_PROGRESS.getStatus());
        miniProgramInfoMapper.insertSelective(info);
        if (CollectionUtils.isNotEmpty(param.getSpuCodes())) {
            List<MiniProgramInfoSpuCode> spuCodes = param.getSpuCodes().stream().map(x -> {
                MiniProgramInfoSpuCode code = new MiniProgramInfoSpuCode();
                code.setId(BaseServiceUtils.getId());
                code.setInfoId(info.getId());
                code.setSpuOfferingCode(x);
                return code;
            }).collect(Collectors.toList());

            miniProgramInfoSpuCodeMapper.batchInsert(spuCodes);
        }
        if(CollectionUtils.isNotEmpty(param.getActivityList())){
            List<MiniProgramInfoActivity> activityList = param.getActivityList().stream().map(x -> {
                MiniProgramInfoActivity code = new MiniProgramInfoActivity();
                BeanUtils.copyProperties(x, code);
                code.setId(BaseServiceUtils.getId());
                code.setInfoId(info.getId());

                return code;
            }).collect(Collectors.toList());

            miniProgramInfoActivityMapper.batchInsert(activityList);
        }

        String content = "【创建素材】\n" + "素材主题 " + info.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, content);
        invalidInfoCache(info.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void edit(InfoParam param, String userId) {
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(PARAM_ERROR, "素材ID为空");
        }
        Date now = new Date();
        MiniProgramInfo info = miniProgramInfoMapper.selectByPrimaryKey(param.getId());
        if (ObjectUtils.isEmpty(info) || info.getIsDelete().equals(1)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }

        if (!InfoStatusEnum.DRAFT.getStatus().equals(info.getStatus()) && !InfoStatusEnum.REJECTED.getStatus().equals(info.getStatus())
                && !InfoStatusEnum.OFFLINE.getStatus().equals(info.getStatus())
        ) {
            // 只有已上传,已驳回,已下线状态的素材才可以编辑
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_CANNOT_EDIT);
        }

        MiniProgramInfo old = new MiniProgramInfo();
        List<MiniProgramInfoSpuCode> oldSpuCodes = miniProgramInfoSpuCodeMapper.selectByExample(new MiniProgramInfoSpuCodeExample().createCriteria()
                .andInfoIdEqualTo(info.getId()).example());
        List<MiniProgramInfoActivity> oldActivityList = miniProgramInfoActivityMapper.selectByExample(new MiniProgramInfoActivityExample().createCriteria()
                .andInfoIdEqualTo(info.getId()).example());
        BeanUtils.copyProperties(info, old);
        BeanUtils.copyProperties(param, info);
        info.setUpdateTime(now);
        // 充值素材状态和审核状态
        info.setStatus(InfoStatusEnum.DRAFT.getStatus());
        info.setAuditStatus(InfoAuditStatusEnum.DRAFT.getStatus());
        //info.setAuditStatus(param.getAction() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.IN_PROGRESS.getStatus());
        miniProgramInfoMapper.updateByPrimaryKey(info);
        miniProgramInfoSpuCodeMapper.deleteByExample(new MiniProgramInfoSpuCodeExample().createCriteria()
                .andInfoIdEqualTo(info.getId()).example());
        miniProgramInfoActivityMapper.deleteByExample(new MiniProgramInfoActivityExample().createCriteria()
                .andInfoIdEqualTo(info.getId()).example());
        List<MiniProgramInfoSpuCode> spuCodes = new ArrayList<>();
        List<MiniProgramInfoActivity> activityList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSpuCodes())) {
            spuCodes = param.getSpuCodes().stream().map(x -> {
                MiniProgramInfoSpuCode code = new MiniProgramInfoSpuCode();
                code.setId(BaseServiceUtils.getId());
                code.setInfoId(info.getId());
                code.setSpuOfferingCode(x);
                return code;
            }).collect(Collectors.toList());

            miniProgramInfoSpuCodeMapper.batchInsert(spuCodes);
        }
        if(CollectionUtils.isNotEmpty(param.getActivityList())){
             activityList = param.getActivityList().stream().map(x -> {
                MiniProgramInfoActivity code = new MiniProgramInfoActivity();
                 BeanUtils.copyProperties(x, code);
                code.setId(BaseServiceUtils.getId());
                code.setInfoId(info.getId());
             
                return code;
            }).collect(Collectors.toList());

            miniProgramInfoActivityMapper.batchInsert(activityList);
        }

        String log = getEditContent(old, info, oldSpuCodes, spuCodes,oldActivityList, activityList);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, log);
        }
        invalidInfoCache(info.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void setPopular(String id, Integer isPopular) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException(PARAM_ERROR, "素材ID为空");
        }
        Date now = new Date();
        MiniProgramInfo info = miniProgramInfoMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(info) || info.getIsDelete().equals(1)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }

        //判断同类型个数是否超限
        if (isPopular == 1) {
            //视频
            if (Objects.equals(info.getContentType(), InfoContentTypeEnum.VIDEO.getType())) {
                //判断是否为产品介绍
                if (Objects.equals(info.getKnowledgeType(), KnowledgeTypeEnum.PRODUCT_DESC.getType())) {
                    List<MiniProgramInfo> miniProgramInfos = miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                            .andKnowledgeTypeEqualTo(KnowledgeTypeEnum.PRODUCT_DESC.getType())
                            .andContentTypeEqualTo(InfoContentTypeEnum.VIDEO.getType())
                            .andIsPopularEqualTo(1)
                            .andStatusEqualTo(InfoStatusEnum.PUBLISHED.getStatus())
                            .example()
                    );
                    //默认为4个
                    if (miniProgramInfos.size() >= 4) {
                        throw new BusinessException(StatusContant.MINI_PROGRAM_POPULAR_MORE);
                    }


                } else if (Objects.equals(info.getKnowledgeType(), KnowledgeTypeEnum.FLOW_HANDLE.getType())) {
                    //判断是否为产品介绍
                    if (Objects.equals(info.getKnowledgeType(), KnowledgeTypeEnum.FLOW_HANDLE.getType())) {
                        List<MiniProgramInfo> miniProgramInfos = miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                                .andKnowledgeTypeEqualTo(KnowledgeTypeEnum.FLOW_HANDLE.getType())
                                .andContentTypeEqualTo(InfoContentTypeEnum.VIDEO.getType())
                                .andIsPopularEqualTo(1)
                                .example()
                        );
                        //默认为4个
                        if (miniProgramInfos.size() >= 4) {
                            throw new BusinessException(StatusContant.MINI_PROGRAM_POPULAR_MORE);
                        }
                    } else {
                        throw new BusinessException(PARAM_ERROR, "设置为首页的咨询类型不正确");
                    }

                } else if (Objects.equals(info.getContentType(), InfoContentTypeEnum.IMAGE_TEXT.getType()) && Objects.equals(info.getKnowledgeType(), KnowledgeTypeEnum.FAQ.getType())) {
                    List<MiniProgramInfo> miniProgramInfos = miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                            .andKnowledgeTypeEqualTo(KnowledgeTypeEnum.FAQ.getType())
                            .andContentTypeEqualTo(InfoContentTypeEnum.IMAGE_TEXT.getType())
                            .andIsPopularEqualTo(1)
                            .example()
                    );
                    //默认为4个
                    if (miniProgramInfos.size() >= 5) {
                        throw new BusinessException(StatusContant.MINI_PROGRAM_POPULAR_MORE);
                    }
                } else {
                    throw new BusinessException(PARAM_ERROR, "设置为首页的咨询类型不正确");
                }
            }

        }
        info.setIsPopular(isPopular);
        info.setUpdateTime(now);
        miniProgramInfoMapper.updateByPrimaryKey(info);
        String header = isPopular == 1 ? "【设置首页】" : "【取消首页】";
        StringBuilder sb = new StringBuilder();
        sb.append(header);
        sb.append("\n").append("素材主题").append(info.getName()).append(isPopular == 1 ? "设置为首页显示" : "取消首页显示");
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, sb.toString());
        invalidInfoCache(id);
    }

    private String getEditContent(MiniProgramInfo old, MiniProgramInfo
            info, List<MiniProgramInfoSpuCode> oldSpuCodes, List<MiniProgramInfoSpuCode> spuCodes,
                                  List<MiniProgramInfoActivity> oldActivitys, List<MiniProgramInfoActivity> activitys ) {
        boolean writeLog = false;
        StringBuilder content = new StringBuilder();

        //资讯名称
        if (!StringUtils.equals(old.getName(), info.getName())) {
            writeLog = true;
            content.append("\n").append("素材主题").append("由").append(old.getName()).append("修改为").append(info.getName());
        }


        if (!Objects.equals(old.getCategory(), info.getCategory())) {
            Integer target = old.getCategory();
            String oldType = "",
                    newType = "";


            if (target != null) {
                List<Integer> targets = new ArrayList<>();
                if ((target & TARGET_MASK_MANAGER) != 0) {
                    targets.add(TARGET_MASK_MANAGER);
                }
                if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
                    targets.add(TARGET_MASK_DISTRIBUTOR);
                }
                if ((target & TARGET_MASK_CHANNEL) != 0) {
                    targets.add(TARGET_MASK_CHANNEL);
                }
                for (Integer t : targets) {
                    oldType += InfoCategoryEnum.getName(String.valueOf(t)) + ",";

                }
            }
            Integer target1 = info.getCategory();


            if (target1 != null) {
                List<Integer> targets = new ArrayList<>();
                if ((target1 & TARGET_MASK_MANAGER) != 0) {
                    targets.add(TARGET_MASK_MANAGER);
                }
                if ((target1 & TARGET_MASK_DISTRIBUTOR) != 0) {
                    targets.add(TARGET_MASK_DISTRIBUTOR);
                }
                if ((target1 & TARGET_MASK_CHANNEL) != 0) {
                    targets.add(TARGET_MASK_CHANNEL);
                }
                for (Integer t : targets) {
                    newType += InfoCategoryEnum.getName(String.valueOf(t)) + ",";

                }
            }
            //发布区域
            content.append("\n").append("发布区域").append("由").append(oldType).append("修改为").append(newType);
        }

        //资讯类型
        if (!StringUtils.equals(old.getInfoType(), info.getInfoType())) {
            writeLog = true;

            String oldTypeInfo = JSON.toJSONString(Arrays.stream(old.getInfoType().split(","))
                    .map(InfoTypeEnum::getName).collect(Collectors.toList()));
            String newTypeInfo = JSON.toJSONString(Arrays.stream(info.getInfoType().split(","))
                    .map(InfoTypeEnum::getName).collect(Collectors.toList()));
            content.append("\n").append("素材类型").append("由").append(oldTypeInfo).append("修改为").append(newTypeInfo);
        }

        // 素材类型
        if (!Objects.equals(old.getContentType(), info.getContentType())) {
            writeLog = true;
            content.append("\n").append("素材类型").append("由").append(old.getName()).append("修改为").append(info.getName());
        }

        //资讯头图url
        if (!StringUtils.equals(old.getHeadImgUrl1(), info.getHeadImgUrl1())) {
            writeLog = true;
            content.append("\n").append("资讯头图").append("由").append(old.getHeadImgUrl1()).append("修改为").append(info.getHeadImgUrl1());
        }

        //营销素材头图url
        if (!StringUtils.equals(old.getHeadImgUrl2(), info.getHeadImgUrl2())) {
            writeLog = true;
            content.append("\n").append("营销素材头图").append("由").append(old.getHeadImgUrl2()).append("修改为").append(info.getHeadImgUrl2());
        }

        //资讯内容
        if (!StringUtils.equals(old.getContent(), info.getContent())) {
            writeLog = true;
            content.append("\n").append("素材内容").append("由").append(old.getContent()).append("修改为").append(info.getContent());
        }

        //资讯关键词
        if (!StringUtils.equals(old.getKeyWords(), info.getKeyWords())) {
            writeLog = true;
            content.append("\n").append("素材关键词").append("由").append(old.getKeyWords()).append("修改为").append(info.getKeyWords());
        }

//        if(!StringUtils.equals(old.getDescription(),info.getDescription())) {
//            content.append("\n").append("素材描述").append("由").append(old.getDescription()).append("修改为").append(info.getDescription());
//        }

        List<String> oldSpuCodesList = oldSpuCodes.stream().map(MiniProgramInfoSpuCode::getSpuOfferingCode).collect(Collectors.toList());
        List<String> spuCodesList = spuCodes.stream().map(MiniProgramInfoSpuCode::getSpuOfferingCode).collect(Collectors.toList());
        //资讯关键词
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldSpuCodesList, spuCodesList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(spuCodesList, oldSpuCodesList))) {
            writeLog = true;
            String oldSpuStr = "空";
            if (CollectionUtils.isNotEmpty(oldSpuCodes)) {
                oldSpuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(oldSpuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }

            String spuStr = "空";
            if (CollectionUtils.isNotEmpty(spuCodes)) {
                spuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(spuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }
            content.append("\n").append("产品清单").append("由").append(oldSpuStr).append("修改为").append(spuStr);
        }

        //资讯关键词
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldSpuCodesList, spuCodesList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(spuCodesList, oldSpuCodesList))) {
            writeLog = true;
            String oldSpuStr = "空";
            if (CollectionUtils.isNotEmpty(oldSpuCodes)) {
                oldSpuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(oldSpuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }

            String spuStr = "空";
            if (CollectionUtils.isNotEmpty(spuCodes)) {
                spuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(spuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }
            content.append("\n").append("产品清单").append("由").append(oldSpuStr).append("修改为").append(spuStr);
        }
        //专区名称变更
        if (!StringUtils.equals(old.getActivityName(), info.getActivityName())) {
            writeLog = true;
            content.append("\n").append("专区名称").append("由").append(old.getActivityName()).append("修改为").append(info.getActivityName());
        }
        //专区列表是否变更
        List<String> oldActivityList = oldActivitys.stream().map(MiniProgramInfoActivity::getActivityId).collect(Collectors.toList());
        List<String> activityList = activitys.stream().map(MiniProgramInfoActivity::getActivityId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldActivityList, activityList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(activityList, oldActivityList))){
            writeLog = true;
            content.append("\n").append("专区列表").append("由").append(JSON.toJSONString(oldActivitys.stream().map(MiniProgramInfoActivity::getActivityName).collect(Collectors.toList()))).append("修改为").append(JSON.toJSONString(activitys.stream().map(MiniProgramInfoActivity::getActivityName).collect(Collectors.toList())));

        }
        if (writeLog) {
            if (StringUtils.equals(old.getName(), info.getName())) {
                content.insert(0, "\n素材主题：" + old.getName());
            }
            content.insert(0, "【编辑素材】");
        }
        return content.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis) {

        List<MiniProgramInfo> infos = miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(0).example());
        if (infos.size() != param.getIds().size()) {
            throw new BusinessException(PARAM_ERROR, "所审核的素材"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    infos.stream().map(MiniProgramInfo::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramInfo> notInProcess = infos.stream().filter(x -> !x.getAuditStatus().equals(InfoAuditStatusEnum.AUDITING.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInProcess)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所审核的素材"
                    + JSON.toJSONString(notInProcess.stream().map(MiniProgramInfo::getId).collect(Collectors.toList()))
                    + "不可以被审核");
        }

        Date now = new Date();
        infos.forEach(x -> {
            x.setUpdateTime(now);
            x.setAuditStatus(param.getApprove() ? InfoAuditStatusEnum.PASSED.getStatus() : InfoAuditStatusEnum.DENIED.getStatus());
            x.setStatus(param.getApprove() ? InfoStatusEnum.PUBLISHED.getStatus() : InfoStatusEnum.REJECTED.getStatus());
            if (!param.getApprove()) {
                x.setCategory(null);
            }
            miniProgramInfoMapper.updateByPrimaryKeySelective(x);
        });
        String header = null;
        if (param.getApprove()) {
            header = param.getIds().size() > 1 ? "【批量同意】" : "【同意】";
        } else {
            header = param.getIds().size() > 1 ? "【批量驳回】" : "【驳回】";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(header);
        infos.forEach(x -> sb.append("\n").append("素材主题 ").append(x.getName()));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, sb.toString());
        invalidInfoCache(infos.stream().map(MiniProgramInfo::getId).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramInfo> infos = miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(0).example());
        if (infos.size() != param.getIds().size()) {
            throw new BusinessException(PARAM_ERROR, "所下线的素材"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    infos.stream().map(MiniProgramInfo::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramInfo> notPublished = infos.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.PUBLISHED.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notPublished)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所下线的素材"
                    + JSON.toJSONString(notPublished.stream().map(MiniProgramInfo::getId).collect(Collectors.toList()))
                    + "不可以被下线");
        }

        Date now = new Date();
        infos.forEach(x -> {
            x.setUpdateTime(now);
            x.setStatus(InfoStatusEnum.OFFLINE.getStatus());
            miniProgramInfoMapper.updateByPrimaryKeySelective(x);
        });

        StringBuilder sb = new StringBuilder("【下线素材】");
        infos.forEach(x -> sb.append("\n").append("素材主题 ").append(x.getName()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, sb.toString());
        invalidInfoCache(infos.stream().map(MiniProgramInfo::getId).collect(Collectors.toList()));
    }

    @Override
    @DS("query")
    public Boolean judgeProduct(String spuCode, String userId,String provinceCode) {
        String cityCode = null;
        String roleType = null;
        if (StringUtils.isNotBlank(userId)) {
            UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
            if (userMiniProgram == null) {
                return false;
            }
            provinceCode = userMiniProgram.getBeId();
            cityCode = userMiniProgram.getLocation();
            roleType = userMiniProgram.getRoleType();
        } else if (StringUtils.isBlank(provinceCode)) {
            return false;
        }
        return spuOfferingInfoMapperExt.countSkuByProvinceAndCity(spuCode, provinceCode, cityCode,roleType) > 0;
    }

    @Override
    @DS("save")
    public void publish(PublishInfoParam param) {
        MiniProgramInfo miniProgramInfo = miniProgramInfoMapper.selectByPrimaryKey(param.getId());
        if (null == miniProgramInfo || miniProgramInfo.getIsDelete().equals(1)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_NOT_EXIST);
        }
        if (!InfoAuditStatusEnum.DRAFT.getStatus().equals(miniProgramInfo.getAuditStatus()) && !InfoAuditStatusEnum.DENIED.getStatus().equals(miniProgramInfo.getAuditStatus())) {
            // 只能发布待审核的素材
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_PUBLISH_WRONG_STATUS);
        }
        String[] categoryList = param.getCategory().split(",");
        List<Integer> categoryListInt = new ArrayList<>();
        for (String c : categoryList) {
            categoryListInt.add(Integer.valueOf(c));
        }

        int target = 0;
        for (Integer t : categoryListInt) {
            target |= t;
        }
        miniProgramInfo.setCategory(target == 0 ? null : target);
        miniProgramInfo.setInfoType(param.getInfoType());
        miniProgramInfo.setHeadImgUrl1(param.getHeadImgUrl1());
        miniProgramInfo.setHeadImgUrl2(param.getHeadImgUrl2());
        miniProgramInfo.setKnowledgeType(param.getKnowledgeType());
        miniProgramInfo.setWordType(param.getWordType());
        // 发布变审核中
        miniProgramInfo.setStatus(InfoStatusEnum.AUDITING.getStatus());
        miniProgramInfo.setAuditStatus(InfoAuditStatusEnum.AUDITING.getStatus());
        miniProgramInfoMapper.updateByPrimaryKeySelective(miniProgramInfo);

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, "【发布素材】" + "\n" + "素材主题 " + miniProgramInfo.getName());
        invalidInfoCache(miniProgramInfo.getId());
    }

    @Override
    @DS("query")
    public List<PageInfoVO> searchInfo(String keyWord, Integer contentType, List<Integer> categoryList) {
        Integer category = 0;
        for (Integer c : categoryList){
            category |= c;
        }
        return miniProgramInfoMapperExt.searchInfo(category, keyWord, contentType);
    }

    private void invalidInfoCache(String infoId) {
        List<String> infoIds = new ArrayList<>();
        infoIds.add(infoId);
        invalidInfoCache(infoIds);
    }

    private void invalidInfoCache(List<String> infoIds) {
        infoIds.forEach(x -> {
            Set<String> keys = redisTemplate.keys(Constant.REDIS_KEY_MINI_INFO_ID + x + "*");
            redisTemplate.delete(keys);
        });

        redisTemplate.delete(Constant.REDIS_KEY_MINI_KNOWLEDGE_HOME_LIST);
        Set<String> keys = redisTemplate.keys(Constant.REDIS_KEY_MINI_INFO_LIST_PARAM + "*");
        redisTemplate.delete(keys);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MiniProgramInfo miniProgramInfo = miniProgramInfoMapper.selectByPrimaryKey(id);
        if (miniProgramInfo == null || miniProgramInfo.getIsDelete().equals(1)) {
            throw new BusinessException(PARAM_ERROR, "咨询不存在");
        }
        if (!(
                miniProgramInfo.getStatus().equals(InfoStatusEnum.DRAFT.getStatus()) ||
                        miniProgramInfo.getStatus().equals(InfoStatusEnum.OFFLINE.getStatus())
                        || miniProgramInfo.getStatus().equals(InfoStatusEnum.REJECTED.getStatus())
        )) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_DEACTIVATE_STATUS_WRONG);
        }
        miniProgramInfo.setIsDelete(1);
        miniProgramInfo.setUpdateTime(new Date());
        miniProgramInfoMapper.updateByPrimaryKeySelective(miniProgramInfo);
        invalidInfoCache(id);
        StringBuilder sb = new StringBuilder();
        sb.append("【删除素材】").append("/n").append("素材主题").append(miniProgramInfo.getName());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code,
                sb.toString(), null, 0, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void requestOnline(MiniProgramInfoRequestOnlineParam param, LoginIfo4Redis loginIfo4Redis) {
        MiniProgramInfo miniProgramInfo = miniProgramInfoMapper.selectByPrimaryKey(param.getInfoId());
        if (miniProgramInfo == null || miniProgramInfo.getIsDelete().equals(1)) {
            throw new BusinessException(PARAM_ERROR, "素材不存在");
        }

        List<MiniProgramInfoRequestOnline> requestOnlineList = miniProgramInfoRequestOnlineMapper.selectByExample(
                new MiniProgramInfoRequestOnlineExample().createCriteria().andInfoIdEqualTo(param.getInfoId())
                        .andUserIdEqualTo(loginIfo4Redis.getMallUserId()).example());
        if (CollectionUtils.isNotEmpty(requestOnlineList)) {
            throw new BusinessException(PARAM_ERROR, "用户已经提交上架申请");
        }

        MiniProgramInfoRequestOnline online = new MiniProgramInfoRequestOnline();
        online.setInfoId(param.getInfoId());
        online.setId(BaseServiceUtils.getId());
        online.setUserId(loginIfo4Redis.getMallUserId());
        online.setPhone(loginIfo4Redis.getPhone());
        online.setProvinceCode(loginIfo4Redis.getBeId());
        online.setCityCode(loginIfo4Redis.getLocation());
        Date now = new Date();
        online.setCreateTime(now);
        online.setUpdateTime(now);

        miniProgramInfoRequestOnlineMapper.insertSelective(online);
    }
}
