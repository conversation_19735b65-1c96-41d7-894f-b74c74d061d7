package com.chinamobile.retail.request.esteward;

import lombok.Data;

@Data
public class OppfRequest {

    private Params params;

    @Data
    public static class Params {
        //用户id
        private String serviceNumber;
        private String submitTime;
        //手机号
        private String submitServNumber;
        //商品名称
        private String busiType;
        //预约渠道——200020011
        private String busiChannel;
        //业务大类——GOVERNMENT_AND_ENTERPRISE/HOME_BROADBAND
        private String businessId;
        //业务类型——NG_KHYY/H_CPBL
        private String businessType;
        //业务子类型——00030020002663308/00030020002664437
        private String subBusinessType;


        //预约档次——2
        private String appoimentLevel;

        //预约备注——地址
        private String appoimentRemark;
    }
}