package com.chinamobile.retail.controller.esteward;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.*;
import com.chinamobile.retail.pojo.vo.EstewardReservationListVO;
import com.chinamobile.retail.service.EstewardReservationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/esteward/reservation")
public class EstewardReservationController {

    @Resource
    private EstewardReservationService estewardReservationService;

    @PostMapping("/addProduct")
    public BaseAnswer<Void> addProduct(@RequestBody EstewardAddProductParam param) {
        estewardReservationService.addProduct(param);
        return BaseAnswer.success(null);
    }

    @PostMapping("/submit")
    public BaseAnswer<Void> submitReservation(@RequestBody EstewardSubmitReservationParam param) {
        estewardReservationService.submitReservation(param);
        return BaseAnswer.success(null);
    }

    @PostMapping("/submit/immediately")
    public BaseAnswer<Void> submitReservationImmediately(@RequestBody EstewardSubmitReservationImmediatelyParam param) {
        estewardReservationService.submitReservationImmediately(param);
        return BaseAnswer.success(null);
    }

    @PostMapping("/list")
    public BaseAnswer<PageData<EstewardReservationListVO>> listReservations(@RequestBody EstewardReservationListParam param) {
        PageData<EstewardReservationListVO> pageData = estewardReservationService.listReservations(param);
        return new BaseAnswer<PageData<EstewardReservationListVO>>().setData(pageData);
    }

    @PostMapping("/list/group")
    public BaseAnswer<PageData<List<EstewardReservationListVO>>> listReservationGroup(@RequestBody EstewardReservationListParam param) {
        PageData<List<EstewardReservationListVO>> pageData = estewardReservationService.listReservationGroup(param);
        return new BaseAnswer<PageData<List<EstewardReservationListVO>>>().setData(pageData);
    }

    @GetMapping("/hide")
    public BaseAnswer<Void> hideReservation(@RequestParam String reservationId) {
        estewardReservationService.hideReservation(reservationId);
        return BaseAnswer.success(null);
    }

    @PostMapping("/deleteProduct")
    public BaseAnswer<Void> deleteProduct(@RequestBody EstewardSwitchReservationParam param) {
        estewardReservationService.deleteProduct(param.getReservationId());
        return BaseAnswer.success(null);
    }

    @PostMapping("/switchSKU")
    public BaseAnswer<Void> switchSKU(@RequestBody EstewardSwitchReservationParam param) {
        estewardReservationService.switchSKU(param);
        return BaseAnswer.success(null);
    }

//    @GetMapping("/spuProducts")
//    public BaseAnswer<List<EstewardProduct>> getSpuProducts(@RequestParam String spuCode) {
//        List<EstewardProduct> products = estewardReservationService.getSpuProducts(spuCode);
//        return new BaseAnswer<List<EstewardProduct>>().setData(products);
//    }
}