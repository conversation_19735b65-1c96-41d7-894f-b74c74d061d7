package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MaterialListVO;
import com.chinamobile.retail.pojo.vo.MaterialVO;
import com.chinamobile.retail.service.IMiniProgramMaterialService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/miniprogram/material")
public class MiniProgramMaterialController {

    @Resource
    private IMiniProgramMaterialService materialService;

    /**
     * 搜索查询素材或文件夹
     * @param param
     * @return
     */
    @GetMapping("/search")
    public BaseAnswer<List<MaterialVO>> searchMaterials(@Validated @ModelAttribute SearchMaterialParam param) {
        return materialService.searchMaterials(param);
    }

    /**
     * 查询素材列表
     * @param param
     * @return
     */
    @GetMapping("/searchList")
    public BaseAnswer<List<MaterialListVO>> searchMaterialList(@Validated @ModelAttribute SearchMaterialListParam param) {
        return materialService.searchMaterialList(param);
    }

    /**
     * 新建文件或文件夹
     * @param param
     * @return
     */
    @PostMapping("/createFolder")
    public BaseAnswer createFolder(@Validated @RequestBody CreateFolderParam param) {
        return materialService.createFolder(param);
    }

    /**
     * 重命名文件夹或素材
     * @param param
     * @return
     */
    @PostMapping("/rename")
    public BaseAnswer renameMaterial(@Validated @RequestBody RenameMaterialParam param) {
        return materialService.renameMaterial(param);
    }

    /**
     * 删除文件夹或素材
     * @param param
     * @return
     */
    @PostMapping("/delete")
    public BaseAnswer deleteMaterial(@Validated @RequestBody DeleteMaterialParam param) {
        return materialService.deleteMaterial(param);
    }

    /**
     * 批量删除文件夹或素材
     * @param param
     * @return
     */
    @PostMapping("/batchDelete")
    public BaseAnswer batchDeleteMaterial(@Validated @RequestBody BatchDeleteMaterialParam param) {
        return materialService.batchDeleteMaterial(param);
    }
}