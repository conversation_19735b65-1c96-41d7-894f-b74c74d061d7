package com.chinamobile.retail.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CodeValidMark {
    /**
     * 验证验证码
     * @return
     */
    boolean validCode() default true;

    /**
     * 验证滑动验证码
     * @return
     */
    boolean validSlideCode() default false;

    /**
     * 验证短信验证码
     * @return
     */
    boolean validSmsCode() default false;

    /**
     * 校验新手机验证码
     * @return
     */
    boolean validNewSmsCode() default false;

    /**
     * 是否加密
     * @return
     */
    boolean encrypted() default false;

    String  nextStepType() default "";

}
