package com.chinamobile.retail.quartz;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.quartz.Job;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 16:16
 * @description TODO
 */
@Data
public class GeneralJobData<T> {

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务分组
     */
    private String group;

    /**
     * 任务
     */
    @JSONField(serialize = false)
    private Class<? extends Job> job;

    /**
     * 数据
     */
    private T data;

    /**
     * cron表达式
     */
    private String cronExpression;

}
