package com.chinamobile.retail.util;

import com.chinamobile.retail.exception.ServicePowerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SignUtils {
    private static String secretKey;

    @Value("${supply.sign.secret_key}")
    public void setSecretKey(String value) {
        secretKey = value;
    }

    public static String getSign(String paramStr) {
        log.debug("SignUtils secretKey:{}", secretKey);
        if (StringUtils.isEmpty(paramStr)) {
            return DigestUtils.md5DigestAsHex(secretKey.getBytes(StandardCharsets.UTF_8));
        } else {
            //md5 加密
            log.info("md5加密:{}", DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8)));
            return DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8));
        }
    }
    public static String getSign(String paramStr,String secretKeyOut) {
        log.debug("SignUtils secretKey:{}", secretKeyOut);
        if (StringUtils.isEmpty(paramStr)) {
            return DigestUtils.md5DigestAsHex(secretKeyOut.getBytes(StandardCharsets.UTF_8));
        } else {
            //md5 加密
            log.info("md5加密:{}", DigestUtils.md5DigestAsHex((paramStr + secretKeyOut).getBytes(StandardCharsets.UTF_8)));
            return DigestUtils.md5DigestAsHex((paramStr + secretKeyOut).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 校验签名
     *
     * @param paramStr
     * @param reqSign
     */
    public static void checkSign(String paramStr, String reqSign) throws ServicePowerException {
        if (org.apache.commons.lang3.StringUtils.isBlank(paramStr)) {
            throw new ServicePowerException(500, "input输入信息不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(reqSign)) {
            throw new ServicePowerException(500, "sign签名信息不能为空");
        }
        //根据内容获得 鉴权签名
        String sign = SignUtils.getSign(paramStr);
        if (!sign.equals(reqSign)) {
            throw new ServicePowerException(500, "签名检验失败");
        }
        //md5 加密校验通过
    }
    public static void checkSign(String paramStr, String reqSign,String secretKeyOut) throws ServicePowerException {
        if (org.apache.commons.lang3.StringUtils.isBlank(paramStr)) {
            throw new ServicePowerException(500, "input输入信息不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(reqSign)) {
            throw new ServicePowerException(500, "sign签名信息不能为空");
        }
        //根据内容获得 鉴权签名
        String sign = SignUtils.getSign(paramStr,secretKeyOut);
        if (!sign.equals(reqSign)) {
            throw new ServicePowerException(500, "签名检验失败");
        }
        //md5 加密校验通过
    }

    public static void main(String[] args) {
        String signed = getSignTest("{\"orderNum\":\"jkiot740100000001592001\",\"optionType\":\"1\"}", "b244ff421031fde652f9bb66d1486576");
//        String signed = getSignTest("{\\\"linkType\\\":\\\"1\\\",\\\"loginRole\\\":\\\"2\\\",\\\"userInfo\\\":[{\\\"userCode\\\":\\\"FXY25855\\\",\\\"userPhone\\\":\\\"MmYyYWU1Njc0ODMxMTAwODE0YjI0MmU4ZmE1MGQzMDE=\\\",\\\"userProvince\\\":\\\"531\\\",\\\"userCity\\\":\\\"5310\\\",\\\"userID\\\":\\\"1661947044770903038\\\"}]}", "E4FE7FE78FE2933D");
        System.out.println(signed);

    }

    public static String getSignTest(String paramStr, String secretKey) {
//        log.info("SignUtils secretKey:{}", secretKey);
        if (StringUtils.isEmpty(paramStr)) {
            return DigestUtils.md5DigestAsHex(secretKey.getBytes(StandardCharsets.UTF_8));
        } else {
            //md5 加密
            log.info("md5加密:{}", DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8)));
            return DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8));
        }
    }

}
