package com.chinamobile.retail.pojo.ro;

import com.chinamobile.retail.pojo.vo.miniprogram.ActivityExtraRankVO;
import com.chinamobile.retail.pojo.vo.miniprogram.ActivityExtraWeeklyVO;
import com.chinamobile.retail.pojo.vo.miniprogram.RegionVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动redis存储对象
 */
@Data
public class ActivityRO {
    /**
     * 活动id
     */
    private String id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动对象 客户经理1，分销员2，渠道商4
     */
    private List<Integer> targetList;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date stopTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 奖品确认时间，该时间之后开始发放奖品
     */
    private Date confirmTime;

    /**
     * 结算时间
     */
    private Date settlementTime;

    /**
     * 活动类型 1-排位赛，2-即客周周乐
     */
    private Integer activityType;

    /**
     * 活动状态 0-待发布，1-待开始，2-进行中，3-结算中，4-已结束
     */
    private Integer status;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-审核不通过
     */
    private Integer auditStatus;

    /**
     * true-启用，false-停用
     */
    private Boolean active;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 活动列表图片
     */
    private String listImg;

    /**
     * true-存草稿，false-发布审核，默认false
     */
    private Boolean draft;

    /**
     * 活动省份
     */
    private List<RegionVO> regions;

    /**
     * 产品范式
     */
    private List<String> offeringClasses;

    /**
     * SPU编码
     */
    private List<String> spuCodes;

    /**
     * 已经参与的用户ID
     */
    private List<String> participatedUserIds;

    /**
     * 活动额外信息-排位赛
     */
    private ActivityExtraRankVO extraRank;

    /**
     * 活动额外信息-周周乐
     */
    private ActivityExtraWeeklyVO extraWeekly;
}
