package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 10:43
 * @description TODO
 */
@Data
public class ActivityActivateParam {

    /**
     * 小程序活动id
     */
    @NotBlank(message = "活动id不能为空")
    private String id;

    /**
     * 启用/停用状态，true-启用，false-停用
     */
    @NotNull(message = "启用/停用状态不能为空")
    private Boolean active;
}
