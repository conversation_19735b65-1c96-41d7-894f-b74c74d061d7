package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Order2cInfoExample {
    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        Order2cInfoExample example = new Order2cInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public Order2cInfoExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIsNull() {
            addCriterion("business_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIsNotNull() {
            addCriterion("business_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeEqualTo(String value) {
            addCriterion("business_code =", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotEqualTo(String value) {
            addCriterion("business_code <>", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThan(String value) {
            addCriterion("business_code >", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_code >=", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThan(String value) {
            addCriterion("business_code <", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThanOrEqualTo(String value) {
            addCriterion("business_code <=", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("business_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLike(String value) {
            addCriterion("business_code like", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotLike(String value) {
            addCriterion("business_code not like", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIn(List<String> values) {
            addCriterion("business_code in", values, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotIn(List<String> values) {
            addCriterion("business_code not in", values, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeBetween(String value1, String value2) {
            addCriterion("business_code between", value1, value2, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotBetween(String value1, String value2) {
            addCriterion("business_code not between", value1, value2, "businessCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNull() {
            addCriterion("create_oper_code is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNotNull() {
            addCriterion("create_oper_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualTo(String value) {
            addCriterion("create_oper_code =", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualTo(String value) {
            addCriterion("create_oper_code <>", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThan(String value) {
            addCriterion("create_oper_code >", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_code >=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThan(String value) {
            addCriterion("create_oper_code <", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualTo(String value) {
            addCriterion("create_oper_code <=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLike(String value) {
            addCriterion("create_oper_code like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotLike(String value) {
            addCriterion("create_oper_code not like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIn(List<String> values) {
            addCriterion("create_oper_code in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotIn(List<String> values) {
            addCriterion("create_oper_code not in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeBetween(String value1, String value2) {
            addCriterion("create_oper_code between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotBetween(String value1, String value2) {
            addCriterion("create_oper_code not between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNull() {
            addCriterion("employee_num is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNotNull() {
            addCriterion("employee_num is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualTo(String value) {
            addCriterion("employee_num =", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualTo(String value) {
            addCriterion("employee_num <>", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThan(String value) {
            addCriterion("employee_num >", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualTo(String value) {
            addCriterion("employee_num >=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThan(String value) {
            addCriterion("employee_num <", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualTo(String value) {
            addCriterion("employee_num <=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("employee_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLike(String value) {
            addCriterion("employee_num like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotLike(String value) {
            addCriterion("employee_num not like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIn(List<String> values) {
            addCriterion("employee_num in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotIn(List<String> values) {
            addCriterion("employee_num not in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumBetween(String value1, String value2) {
            addCriterion("employee_num between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotBetween(String value1, String value2) {
            addCriterion("employee_num not between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andCustMgNameIsNull() {
            addCriterion("cust_mg_name is null");
            return (Criteria) this;
        }

        public Criteria andCustMgNameIsNotNull() {
            addCriterion("cust_mg_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustMgNameEqualTo(String value) {
            addCriterion("cust_mg_name =", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameNotEqualTo(String value) {
            addCriterion("cust_mg_name <>", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameGreaterThan(String value) {
            addCriterion("cust_mg_name >", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_mg_name >=", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameLessThan(String value) {
            addCriterion("cust_mg_name <", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameLessThanOrEqualTo(String value) {
            addCriterion("cust_mg_name <=", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgNameLike(String value) {
            addCriterion("cust_mg_name like", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameNotLike(String value) {
            addCriterion("cust_mg_name not like", value, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameIn(List<String> values) {
            addCriterion("cust_mg_name in", values, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameNotIn(List<String> values) {
            addCriterion("cust_mg_name not in", values, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameBetween(String value1, String value2) {
            addCriterion("cust_mg_name between", value1, value2, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgNameNotBetween(String value1, String value2) {
            addCriterion("cust_mg_name not between", value1, value2, "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneIsNull() {
            addCriterion("cust_mg_phone is null");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneIsNotNull() {
            addCriterion("cust_mg_phone is not null");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneEqualTo(String value) {
            addCriterion("cust_mg_phone =", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneNotEqualTo(String value) {
            addCriterion("cust_mg_phone <>", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneGreaterThan(String value) {
            addCriterion("cust_mg_phone >", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("cust_mg_phone >=", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLessThan(String value) {
            addCriterion("cust_mg_phone <", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLessThanOrEqualTo(String value) {
            addCriterion("cust_mg_phone <=", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_mg_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLike(String value) {
            addCriterion("cust_mg_phone like", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneNotLike(String value) {
            addCriterion("cust_mg_phone not like", value, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneIn(List<String> values) {
            addCriterion("cust_mg_phone in", values, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneNotIn(List<String> values) {
            addCriterion("cust_mg_phone not in", values, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneBetween(String value1, String value2) {
            addCriterion("cust_mg_phone between", value1, value2, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneNotBetween(String value1, String value2) {
            addCriterion("cust_mg_phone not between", value1, value2, "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIsNull() {
            addCriterion("order_status_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIsNotNull() {
            addCriterion("order_status_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeEqualTo(Date value) {
            addCriterion("order_status_time =", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotEqualTo(Date value) {
            addCriterion("order_status_time <>", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThan(Date value) {
            addCriterion("order_status_time >", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_status_time >=", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThan(Date value) {
            addCriterion("order_status_time <", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_status_time <=", value, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeIn(List<Date> values) {
            addCriterion("order_status_time in", values, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotIn(List<Date> values) {
            addCriterion("order_status_time not in", values, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeBetween(Date value1, Date value2) {
            addCriterion("order_status_time between", value1, value2, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_status_time not between", value1, value2, "orderStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_ID is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_ID =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_ID <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_ID >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_ID >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_ID <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_ID <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("region_ID <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_ID like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_ID not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_ID in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_ID not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_ID between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_ID not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeIsNull() {
            addCriterion("order_org_biz_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeIsNotNull() {
            addCriterion("order_org_biz_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeEqualTo(String value) {
            addCriterion("order_org_biz_code =", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeNotEqualTo(String value) {
            addCriterion("order_org_biz_code <>", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeGreaterThan(String value) {
            addCriterion("order_org_biz_code >", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_org_biz_code >=", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLessThan(String value) {
            addCriterion("order_org_biz_code <", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLessThanOrEqualTo(String value) {
            addCriterion("order_org_biz_code <=", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_org_biz_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLike(String value) {
            addCriterion("order_org_biz_code like", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeNotLike(String value) {
            addCriterion("order_org_biz_code not like", value, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeIn(List<String> values) {
            addCriterion("order_org_biz_code in", values, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeNotIn(List<String> values) {
            addCriterion("order_org_biz_code not in", values, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeBetween(String value1, String value2) {
            addCriterion("order_org_biz_code between", value1, value2, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeNotBetween(String value1, String value2) {
            addCriterion("order_org_biz_code not between", value1, value2, "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrgLevelIsNull() {
            addCriterion("org_level is null");
            return (Criteria) this;
        }

        public Criteria andOrgLevelIsNotNull() {
            addCriterion("org_level is not null");
            return (Criteria) this;
        }

        public Criteria andOrgLevelEqualTo(String value) {
            addCriterion("org_level =", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelNotEqualTo(String value) {
            addCriterion("org_level <>", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelGreaterThan(String value) {
            addCriterion("org_level >", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelGreaterThanOrEqualTo(String value) {
            addCriterion("org_level >=", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelLessThan(String value) {
            addCriterion("org_level <", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelLessThanOrEqualTo(String value) {
            addCriterion("org_level <=", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_level <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgLevelLike(String value) {
            addCriterion("org_level like", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelNotLike(String value) {
            addCriterion("org_level not like", value, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelIn(List<String> values) {
            addCriterion("org_level in", values, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelNotIn(List<String> values) {
            addCriterion("org_level not in", values, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelBetween(String value1, String value2) {
            addCriterion("org_level between", value1, value2, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgLevelNotBetween(String value1, String value2) {
            addCriterion("org_level not between", value1, value2, "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("org_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameIsNull() {
            addCriterion("province_org_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameIsNotNull() {
            addCriterion("province_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameEqualTo(String value) {
            addCriterion("province_org_name =", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameNotEqualTo(String value) {
            addCriterion("province_org_name <>", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameGreaterThan(String value) {
            addCriterion("province_org_name >", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_org_name >=", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLessThan(String value) {
            addCriterion("province_org_name <", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLessThanOrEqualTo(String value) {
            addCriterion("province_org_name <=", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("province_org_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLike(String value) {
            addCriterion("province_org_name like", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameNotLike(String value) {
            addCriterion("province_org_name not like", value, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameIn(List<String> values) {
            addCriterion("province_org_name in", values, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameNotIn(List<String> values) {
            addCriterion("province_org_name not in", values, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameBetween(String value1, String value2) {
            addCriterion("province_org_name between", value1, value2, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameNotBetween(String value1, String value2) {
            addCriterion("province_org_name not between", value1, value2, "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("remarks <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andBookidIsNull() {
            addCriterion("bookId is null");
            return (Criteria) this;
        }

        public Criteria andBookidIsNotNull() {
            addCriterion("bookId is not null");
            return (Criteria) this;
        }

        public Criteria andBookidEqualTo(String value) {
            addCriterion("bookId =", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidNotEqualTo(String value) {
            addCriterion("bookId <>", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidGreaterThan(String value) {
            addCriterion("bookId >", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidGreaterThanOrEqualTo(String value) {
            addCriterion("bookId >=", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidLessThan(String value) {
            addCriterion("bookId <", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidLessThanOrEqualTo(String value) {
            addCriterion("bookId <=", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bookId <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBookidLike(String value) {
            addCriterion("bookId like", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidNotLike(String value) {
            addCriterion("bookId not like", value, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidIn(List<String> values) {
            addCriterion("bookId in", values, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidNotIn(List<String> values) {
            addCriterion("bookId not in", values, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidBetween(String value1, String value2) {
            addCriterion("bookId between", value1, value2, "bookid");
            return (Criteria) this;
        }

        public Criteria andBookidNotBetween(String value1, String value2) {
            addCriterion("bookId not between", value1, value2, "bookid");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(String value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(String value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(String value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(String value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(String value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(String value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLike(String value) {
            addCriterion("total_price like", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotLike(String value) {
            addCriterion("total_price not like", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<String> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<String> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(String value1, String value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(String value1, String value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(String value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(String value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<String> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(String value1, String value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameIsNull() {
            addCriterion("contact_person_name is null");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameIsNotNull() {
            addCriterion("contact_person_name is not null");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameEqualTo(String value) {
            addCriterion("contact_person_name =", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameNotEqualTo(String value) {
            addCriterion("contact_person_name <>", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameGreaterThan(String value) {
            addCriterion("contact_person_name >", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameGreaterThanOrEqualTo(String value) {
            addCriterion("contact_person_name >=", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLessThan(String value) {
            addCriterion("contact_person_name <", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLessThanOrEqualTo(String value) {
            addCriterion("contact_person_name <=", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_person_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLike(String value) {
            addCriterion("contact_person_name like", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameNotLike(String value) {
            addCriterion("contact_person_name not like", value, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameIn(List<String> values) {
            addCriterion("contact_person_name in", values, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameNotIn(List<String> values) {
            addCriterion("contact_person_name not in", values, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameBetween(String value1, String value2) {
            addCriterion("contact_person_name between", value1, value2, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameNotBetween(String value1, String value2) {
            addCriterion("contact_person_name not between", value1, value2, "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("contact_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andAddr1IsNull() {
            addCriterion("addr1 is null");
            return (Criteria) this;
        }

        public Criteria andAddr1IsNotNull() {
            addCriterion("addr1 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr1EqualTo(String value) {
            addCriterion("addr1 =", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1EqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1NotEqualTo(String value) {
            addCriterion("addr1 <>", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThan(String value) {
            addCriterion("addr1 >", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanOrEqualTo(String value) {
            addCriterion("addr1 >=", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1GreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1LessThan(String value) {
            addCriterion("addr1 <", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanOrEqualTo(String value) {
            addCriterion("addr1 <=", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1LessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr1 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr1Like(String value) {
            addCriterion("addr1 like", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotLike(String value) {
            addCriterion("addr1 not like", value, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1In(List<String> values) {
            addCriterion("addr1 in", values, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotIn(List<String> values) {
            addCriterion("addr1 not in", values, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1Between(String value1, String value2) {
            addCriterion("addr1 between", value1, value2, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr1NotBetween(String value1, String value2) {
            addCriterion("addr1 not between", value1, value2, "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr2IsNull() {
            addCriterion("addr2 is null");
            return (Criteria) this;
        }

        public Criteria andAddr2IsNotNull() {
            addCriterion("addr2 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr2EqualTo(String value) {
            addCriterion("addr2 =", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2EqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2NotEqualTo(String value) {
            addCriterion("addr2 <>", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThan(String value) {
            addCriterion("addr2 >", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanOrEqualTo(String value) {
            addCriterion("addr2 >=", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2GreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2LessThan(String value) {
            addCriterion("addr2 <", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanOrEqualTo(String value) {
            addCriterion("addr2 <=", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2LessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr2 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr2Like(String value) {
            addCriterion("addr2 like", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotLike(String value) {
            addCriterion("addr2 not like", value, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2In(List<String> values) {
            addCriterion("addr2 in", values, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotIn(List<String> values) {
            addCriterion("addr2 not in", values, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2Between(String value1, String value2) {
            addCriterion("addr2 between", value1, value2, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr2NotBetween(String value1, String value2) {
            addCriterion("addr2 not between", value1, value2, "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr3IsNull() {
            addCriterion("addr3 is null");
            return (Criteria) this;
        }

        public Criteria andAddr3IsNotNull() {
            addCriterion("addr3 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr3EqualTo(String value) {
            addCriterion("addr3 =", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3EqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3NotEqualTo(String value) {
            addCriterion("addr3 <>", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThan(String value) {
            addCriterion("addr3 >", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanOrEqualTo(String value) {
            addCriterion("addr3 >=", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3GreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3LessThan(String value) {
            addCriterion("addr3 <", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanOrEqualTo(String value) {
            addCriterion("addr3 <=", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3LessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr3 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr3Like(String value) {
            addCriterion("addr3 like", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotLike(String value) {
            addCriterion("addr3 not like", value, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3In(List<String> values) {
            addCriterion("addr3 in", values, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotIn(List<String> values) {
            addCriterion("addr3 not in", values, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3Between(String value1, String value2) {
            addCriterion("addr3 between", value1, value2, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr3NotBetween(String value1, String value2) {
            addCriterion("addr3 not between", value1, value2, "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr4IsNull() {
            addCriterion("addr4 is null");
            return (Criteria) this;
        }

        public Criteria andAddr4IsNotNull() {
            addCriterion("addr4 is not null");
            return (Criteria) this;
        }

        public Criteria andAddr4EqualTo(String value) {
            addCriterion("addr4 =", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4EqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4NotEqualTo(String value) {
            addCriterion("addr4 <>", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThan(String value) {
            addCriterion("addr4 >", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanOrEqualTo(String value) {
            addCriterion("addr4 >=", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4GreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4LessThan(String value) {
            addCriterion("addr4 <", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanOrEqualTo(String value) {
            addCriterion("addr4 <=", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4LessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("addr4 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddr4Like(String value) {
            addCriterion("addr4 like", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotLike(String value) {
            addCriterion("addr4 not like", value, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4In(List<String> values) {
            addCriterion("addr4 in", values, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotIn(List<String> values) {
            addCriterion("addr4 not in", values, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4Between(String value1, String value2) {
            addCriterion("addr4 between", value1, value2, "addr4");
            return (Criteria) this;
        }

        public Criteria andAddr4NotBetween(String value1, String value2) {
            addCriterion("addr4 not between", value1, value2, "addr4");
            return (Criteria) this;
        }

        public Criteria andUsaddrIsNull() {
            addCriterion("usaddr is null");
            return (Criteria) this;
        }

        public Criteria andUsaddrIsNotNull() {
            addCriterion("usaddr is not null");
            return (Criteria) this;
        }

        public Criteria andUsaddrEqualTo(String value) {
            addCriterion("usaddr =", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrNotEqualTo(String value) {
            addCriterion("usaddr <>", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThan(String value) {
            addCriterion("usaddr >", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanOrEqualTo(String value) {
            addCriterion("usaddr >=", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThan(String value) {
            addCriterion("usaddr <", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanOrEqualTo(String value) {
            addCriterion("usaddr <=", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("usaddr <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsaddrLike(String value) {
            addCriterion("usaddr like", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotLike(String value) {
            addCriterion("usaddr not like", value, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrIn(List<String> values) {
            addCriterion("usaddr in", values, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotIn(List<String> values) {
            addCriterion("usaddr not in", values, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrBetween(String value1, String value2) {
            addCriterion("usaddr between", value1, value2, "usaddr");
            return (Criteria) this;
        }

        public Criteria andUsaddrNotBetween(String value1, String value2) {
            addCriterion("usaddr not between", value1, value2, "usaddr");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIsNull() {
            addCriterion("spu_offering_class is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIsNotNull() {
            addCriterion("spu_offering_class is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassEqualTo(String value) {
            addCriterion("spu_offering_class =", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotEqualTo(String value) {
            addCriterion("spu_offering_class <>", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThan(String value) {
            addCriterion("spu_offering_class >", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_class >=", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThan(String value) {
            addCriterion("spu_offering_class <", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_class <=", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLike(String value) {
            addCriterion("spu_offering_class like", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotLike(String value) {
            addCriterion("spu_offering_class not like", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIn(List<String> values) {
            addCriterion("spu_offering_class in", values, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotIn(List<String> values) {
            addCriterion("spu_offering_class not in", values, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassBetween(String value1, String value2) {
            addCriterion("spu_offering_class between", value1, value2, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotBetween(String value1, String value2) {
            addCriterion("spu_offering_class not between", value1, value2, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNull() {
            addCriterion("spu_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNotNull() {
            addCriterion("spu_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualTo(String value) {
            addCriterion("spu_offering_code =", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualTo(String value) {
            addCriterion("spu_offering_code <>", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThan(String value) {
            addCriterion("spu_offering_code >", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_code >=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThan(String value) {
            addCriterion("spu_offering_code <", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_code <=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLike(String value) {
            addCriterion("spu_offering_code like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotLike(String value) {
            addCriterion("spu_offering_code not like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIn(List<String> values) {
            addCriterion("spu_offering_code in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotIn(List<String> values) {
            addCriterion("spu_offering_code not in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeBetween(String value1, String value2) {
            addCriterion("spu_offering_code between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("spu_offering_code not between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNull() {
            addCriterion("supplier_code is null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNotNull() {
            addCriterion("supplier_code is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeEqualTo(String value) {
            addCriterion("supplier_code =", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotEqualTo(String value) {
            addCriterion("supplier_code <>", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThan(String value) {
            addCriterion("supplier_code >", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_code >=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThan(String value) {
            addCriterion("supplier_code <", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanOrEqualTo(String value) {
            addCriterion("supplier_code <=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("supplier_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLike(String value) {
            addCriterion("supplier_code like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotLike(String value) {
            addCriterion("supplier_code not like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIn(List<String> values) {
            addCriterion("supplier_code in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotIn(List<String> values) {
            addCriterion("supplier_code not in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeBetween(String value1, String value2) {
            addCriterion("supplier_code between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotBetween(String value1, String value2) {
            addCriterion("supplier_code not between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNull() {
            addCriterion("deduct_price is null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNotNull() {
            addCriterion("deduct_price is not null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualTo(String value) {
            addCriterion("deduct_price =", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualTo(String value) {
            addCriterion("deduct_price <>", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThan(String value) {
            addCriterion("deduct_price >", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualTo(String value) {
            addCriterion("deduct_price >=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThan(String value) {
            addCriterion("deduct_price <", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualTo(String value) {
            addCriterion("deduct_price <=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLike(String value) {
            addCriterion("deduct_price like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotLike(String value) {
            addCriterion("deduct_price not like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIn(List<String> values) {
            addCriterion("deduct_price in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotIn(List<String> values) {
            addCriterion("deduct_price not in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceBetween(String value1, String value2) {
            addCriterion("deduct_price between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotBetween(String value1, String value2) {
            addCriterion("deduct_price not between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceIsNull() {
            addCriterion("ordering_channel_source is null");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceIsNotNull() {
            addCriterion("ordering_channel_source is not null");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceEqualTo(String value) {
            addCriterion("ordering_channel_source =", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceNotEqualTo(String value) {
            addCriterion("ordering_channel_source <>", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceGreaterThan(String value) {
            addCriterion("ordering_channel_source >", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceGreaterThanOrEqualTo(String value) {
            addCriterion("ordering_channel_source >=", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLessThan(String value) {
            addCriterion("ordering_channel_source <", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLessThanOrEqualTo(String value) {
            addCriterion("ordering_channel_source <=", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_source <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLike(String value) {
            addCriterion("ordering_channel_source like", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceNotLike(String value) {
            addCriterion("ordering_channel_source not like", value, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceIn(List<String> values) {
            addCriterion("ordering_channel_source in", values, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceNotIn(List<String> values) {
            addCriterion("ordering_channel_source not in", values, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceBetween(String value1, String value2) {
            addCriterion("ordering_channel_source between", value1, value2, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceNotBetween(String value1, String value2) {
            addCriterion("ordering_channel_source not between", value1, value2, "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameIsNull() {
            addCriterion("ordering_channel_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameIsNotNull() {
            addCriterion("ordering_channel_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameEqualTo(String value) {
            addCriterion("ordering_channel_name =", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameNotEqualTo(String value) {
            addCriterion("ordering_channel_name <>", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameGreaterThan(String value) {
            addCriterion("ordering_channel_name >", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameGreaterThanOrEqualTo(String value) {
            addCriterion("ordering_channel_name >=", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLessThan(String value) {
            addCriterion("ordering_channel_name <", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLessThanOrEqualTo(String value) {
            addCriterion("ordering_channel_name <=", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ordering_channel_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLike(String value) {
            addCriterion("ordering_channel_name like", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameNotLike(String value) {
            addCriterion("ordering_channel_name not like", value, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameIn(List<String> values) {
            addCriterion("ordering_channel_name in", values, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameNotIn(List<String> values) {
            addCriterion("ordering_channel_name not in", values, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameBetween(String value1, String value2) {
            addCriterion("ordering_channel_name between", value1, value2, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameNotBetween(String value1, String value2) {
            addCriterion("ordering_channel_name not between", value1, value2, "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeIsNull() {
            addCriterion("sso_terminal_type is null");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeIsNotNull() {
            addCriterion("sso_terminal_type is not null");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeEqualTo(String value) {
            addCriterion("sso_terminal_type =", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeNotEqualTo(String value) {
            addCriterion("sso_terminal_type <>", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeGreaterThan(String value) {
            addCriterion("sso_terminal_type >", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sso_terminal_type >=", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLessThan(String value) {
            addCriterion("sso_terminal_type <", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLessThanOrEqualTo(String value) {
            addCriterion("sso_terminal_type <=", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sso_terminal_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLike(String value) {
            addCriterion("sso_terminal_type like", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeNotLike(String value) {
            addCriterion("sso_terminal_type not like", value, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeIn(List<String> values) {
            addCriterion("sso_terminal_type in", values, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeNotIn(List<String> values) {
            addCriterion("sso_terminal_type not in", values, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeBetween(String value1, String value2) {
            addCriterion("sso_terminal_type between", value1, value2, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeNotBetween(String value1, String value2) {
            addCriterion("sso_terminal_type not between", value1, value2, "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andToK3IsNull() {
            addCriterion("to_k3 is null");
            return (Criteria) this;
        }

        public Criteria andToK3IsNotNull() {
            addCriterion("to_k3 is not null");
            return (Criteria) this;
        }

        public Criteria andToK3EqualTo(Integer value) {
            addCriterion("to_k3 =", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3EqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3NotEqualTo(Integer value) {
            addCriterion("to_k3 <>", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3NotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3GreaterThan(Integer value) {
            addCriterion("to_k3 >", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3GreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3GreaterThanOrEqualTo(Integer value) {
            addCriterion("to_k3 >=", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3GreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3LessThan(Integer value) {
            addCriterion("to_k3 <", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3LessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3LessThanOrEqualTo(Integer value) {
            addCriterion("to_k3 <=", value, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3LessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("to_k3 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andToK3In(List<Integer> values) {
            addCriterion("to_k3 in", values, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3NotIn(List<Integer> values) {
            addCriterion("to_k3 not in", values, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3Between(Integer value1, Integer value2) {
            addCriterion("to_k3 between", value1, value2, "toK3");
            return (Criteria) this;
        }

        public Criteria andToK3NotBetween(Integer value1, Integer value2) {
            addCriterion("to_k3 not between", value1, value2, "toK3");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleIsNull() {
            addCriterion("special_after_market_handle is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleIsNotNull() {
            addCriterion("special_after_market_handle is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleEqualTo(Integer value) {
            addCriterion("special_after_market_handle =", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleNotEqualTo(Integer value) {
            addCriterion("special_after_market_handle <>", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleGreaterThan(Integer value) {
            addCriterion("special_after_market_handle >", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleGreaterThanOrEqualTo(Integer value) {
            addCriterion("special_after_market_handle >=", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleLessThan(Integer value) {
            addCriterion("special_after_market_handle <", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleLessThanOrEqualTo(Integer value) {
            addCriterion("special_after_market_handle <=", value, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_market_handle <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleIn(List<Integer> values) {
            addCriterion("special_after_market_handle in", values, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleNotIn(List<Integer> values) {
            addCriterion("special_after_market_handle not in", values, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleBetween(Integer value1, Integer value2) {
            addCriterion("special_after_market_handle between", value1, value2, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterMarketHandleNotBetween(Integer value1, Integer value2) {
            addCriterion("special_after_market_handle not between", value1, value2, "specialAfterMarketHandle");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusIsNull() {
            addCriterion("special_after_status is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusIsNotNull() {
            addCriterion("special_after_status is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusEqualTo(String value) {
            addCriterion("special_after_status =", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusNotEqualTo(String value) {
            addCriterion("special_after_status <>", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusGreaterThan(String value) {
            addCriterion("special_after_status >", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusGreaterThanOrEqualTo(String value) {
            addCriterion("special_after_status >=", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLessThan(String value) {
            addCriterion("special_after_status <", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLessThanOrEqualTo(String value) {
            addCriterion("special_after_status <=", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLike(String value) {
            addCriterion("special_after_status like", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusNotLike(String value) {
            addCriterion("special_after_status not like", value, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusIn(List<String> values) {
            addCriterion("special_after_status in", values, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusNotIn(List<String> values) {
            addCriterion("special_after_status not in", values, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusBetween(String value1, String value2) {
            addCriterion("special_after_status between", value1, value2, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusNotBetween(String value1, String value2) {
            addCriterion("special_after_status not between", value1, value2, "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeIsNull() {
            addCriterion("special_after_status_time is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeIsNotNull() {
            addCriterion("special_after_status_time is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeEqualTo(String value) {
            addCriterion("special_after_status_time =", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeNotEqualTo(String value) {
            addCriterion("special_after_status_time <>", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeGreaterThan(String value) {
            addCriterion("special_after_status_time >", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeGreaterThanOrEqualTo(String value) {
            addCriterion("special_after_status_time >=", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLessThan(String value) {
            addCriterion("special_after_status_time <", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLessThanOrEqualTo(String value) {
            addCriterion("special_after_status_time <=", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLike(String value) {
            addCriterion("special_after_status_time like", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeNotLike(String value) {
            addCriterion("special_after_status_time not like", value, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeIn(List<String> values) {
            addCriterion("special_after_status_time in", values, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeNotIn(List<String> values) {
            addCriterion("special_after_status_time not in", values, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeBetween(String value1, String value2) {
            addCriterion("special_after_status_time between", value1, value2, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeNotBetween(String value1, String value2) {
            addCriterion("special_after_status_time not between", value1, value2, "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeIsNull() {
            addCriterion("special_after_latest_time is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeIsNotNull() {
            addCriterion("special_after_latest_time is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeEqualTo(String value) {
            addCriterion("special_after_latest_time =", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeNotEqualTo(String value) {
            addCriterion("special_after_latest_time <>", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeGreaterThan(String value) {
            addCriterion("special_after_latest_time >", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeGreaterThanOrEqualTo(String value) {
            addCriterion("special_after_latest_time >=", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLessThan(String value) {
            addCriterion("special_after_latest_time <", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLessThanOrEqualTo(String value) {
            addCriterion("special_after_latest_time <=", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_latest_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLike(String value) {
            addCriterion("special_after_latest_time like", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeNotLike(String value) {
            addCriterion("special_after_latest_time not like", value, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeIn(List<String> values) {
            addCriterion("special_after_latest_time in", values, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeNotIn(List<String> values) {
            addCriterion("special_after_latest_time not in", values, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeBetween(String value1, String value2) {
            addCriterion("special_after_latest_time between", value1, value2, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeNotBetween(String value1, String value2) {
            addCriterion("special_after_latest_time not between", value1, value2, "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdIsNull() {
            addCriterion("sync_k3_id is null");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdIsNotNull() {
            addCriterion("sync_k3_id is not null");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdEqualTo(String value) {
            addCriterion("sync_k3_id =", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdNotEqualTo(String value) {
            addCriterion("sync_k3_id <>", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdGreaterThan(String value) {
            addCriterion("sync_k3_id >", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdGreaterThanOrEqualTo(String value) {
            addCriterion("sync_k3_id >=", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLessThan(String value) {
            addCriterion("sync_k3_id <", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLessThanOrEqualTo(String value) {
            addCriterion("sync_k3_id <=", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("sync_k3_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLike(String value) {
            addCriterion("sync_k3_id like", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdNotLike(String value) {
            addCriterion("sync_k3_id not like", value, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdIn(List<String> values) {
            addCriterion("sync_k3_id in", values, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdNotIn(List<String> values) {
            addCriterion("sync_k3_id not in", values, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdBetween(String value1, String value2) {
            addCriterion("sync_k3_id between", value1, value2, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdNotBetween(String value1, String value2) {
            addCriterion("sync_k3_id not between", value1, value2, "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNull() {
            addCriterion("pay_time is null");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNotNull() {
            addCriterion("pay_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualTo(Date value) {
            addCriterion("pay_time =", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualTo(Date value) {
            addCriterion("pay_time <>", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThan(Date value) {
            addCriterion("pay_time >", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_time >=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThan(Date value) {
            addCriterion("pay_time <", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualTo(Date value) {
            addCriterion("pay_time <=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("pay_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayTimeIn(List<Date> values) {
            addCriterion("pay_time in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotIn(List<Date> values) {
            addCriterion("pay_time not in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeBetween(Date value1, Date value2) {
            addCriterion("pay_time between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotBetween(Date value1, Date value2) {
            addCriterion("pay_time not between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeIsNull() {
            addCriterion("refund_time is null");
            return (Criteria) this;
        }

        public Criteria andRefundTimeIsNotNull() {
            addCriterion("refund_time is not null");
            return (Criteria) this;
        }

        public Criteria andRefundTimeEqualTo(Date value) {
            addCriterion("refund_time =", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotEqualTo(Date value) {
            addCriterion("refund_time <>", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThan(Date value) {
            addCriterion("refund_time >", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("refund_time >=", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThan(Date value) {
            addCriterion("refund_time <", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThanOrEqualTo(Date value) {
            addCriterion("refund_time <=", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("refund_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRefundTimeIn(List<Date> values) {
            addCriterion("refund_time in", values, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotIn(List<Date> values) {
            addCriterion("refund_time not in", values, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeBetween(Date value1, Date value2) {
            addCriterion("refund_time between", value1, value2, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotBetween(Date value1, Date value2) {
            addCriterion("refund_time not between", value1, value2, "refundTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIsNull() {
            addCriterion("valet_order_complete_time is null");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIsNotNull() {
            addCriterion("valet_order_complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeEqualTo(String value) {
            addCriterion("valet_order_complete_time =", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotEqualTo(String value) {
            addCriterion("valet_order_complete_time <>", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThan(String value) {
            addCriterion("valet_order_complete_time >", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanOrEqualTo(String value) {
            addCriterion("valet_order_complete_time >=", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThan(String value) {
            addCriterion("valet_order_complete_time <", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanOrEqualTo(String value) {
            addCriterion("valet_order_complete_time <=", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLike(String value) {
            addCriterion("valet_order_complete_time like", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotLike(String value) {
            addCriterion("valet_order_complete_time not like", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIn(List<String> values) {
            addCriterion("valet_order_complete_time in", values, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotIn(List<String> values) {
            addCriterion("valet_order_complete_time not in", values, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeBetween(String value1, String value2) {
            addCriterion("valet_order_complete_time between", value1, value2, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotBetween(String value1, String value2) {
            addCriterion("valet_order_complete_time not between", value1, value2, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeIsNull() {
            addCriterion("bill_ladder_type is null");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeIsNotNull() {
            addCriterion("bill_ladder_type is not null");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeEqualTo(String value) {
            addCriterion("bill_ladder_type =", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeNotEqualTo(String value) {
            addCriterion("bill_ladder_type <>", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeGreaterThan(String value) {
            addCriterion("bill_ladder_type >", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_ladder_type >=", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLessThan(String value) {
            addCriterion("bill_ladder_type <", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLessThanOrEqualTo(String value) {
            addCriterion("bill_ladder_type <=", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("bill_ladder_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLike(String value) {
            addCriterion("bill_ladder_type like", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeNotLike(String value) {
            addCriterion("bill_ladder_type not like", value, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeIn(List<String> values) {
            addCriterion("bill_ladder_type in", values, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeNotIn(List<String> values) {
            addCriterion("bill_ladder_type not in", values, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeBetween(String value1, String value2) {
            addCriterion("bill_ladder_type between", value1, value2, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeNotBetween(String value1, String value2) {
            addCriterion("bill_ladder_type not between", value1, value2, "billLadderType");
            return (Criteria) this;
        }

        public Criteria andQlyStatusIsNull() {
            addCriterion("qly_status is null");
            return (Criteria) this;
        }

        public Criteria andQlyStatusIsNotNull() {
            addCriterion("qly_status is not null");
            return (Criteria) this;
        }

        public Criteria andQlyStatusEqualTo(Integer value) {
            addCriterion("qly_status =", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotEqualTo(Integer value) {
            addCriterion("qly_status <>", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThan(Integer value) {
            addCriterion("qly_status >", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("qly_status >=", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThan(Integer value) {
            addCriterion("qly_status <", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("qly_status <=", value, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("qly_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQlyStatusIn(List<Integer> values) {
            addCriterion("qly_status in", values, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotIn(List<Integer> values) {
            addCriterion("qly_status not in", values, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusBetween(Integer value1, Integer value2) {
            addCriterion("qly_status between", value1, value2, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andQlyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("qly_status not between", value1, value2, "qlyStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusIsNull() {
            addCriterion("ysx_status is null");
            return (Criteria) this;
        }

        public Criteria andYsxStatusIsNotNull() {
            addCriterion("ysx_status is not null");
            return (Criteria) this;
        }

        public Criteria andYsxStatusEqualTo(Integer value) {
            addCriterion("ysx_status =", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusNotEqualTo(Integer value) {
            addCriterion("ysx_status <>", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusGreaterThan(Integer value) {
            addCriterion("ysx_status >", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("ysx_status >=", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusLessThan(Integer value) {
            addCriterion("ysx_status <", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusLessThanOrEqualTo(Integer value) {
            addCriterion("ysx_status <=", value, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("ysx_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYsxStatusIn(List<Integer> values) {
            addCriterion("ysx_status in", values, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusNotIn(List<Integer> values) {
            addCriterion("ysx_status not in", values, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusBetween(Integer value1, Integer value2) {
            addCriterion("ysx_status between", value1, value2, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andYsxStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("ysx_status not between", value1, value2, "ysxStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusIsNull() {
            addCriterion("kx_refund_status is null");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusIsNotNull() {
            addCriterion("kx_refund_status is not null");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusEqualTo(Integer value) {
            addCriterion("kx_refund_status =", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusNotEqualTo(Integer value) {
            addCriterion("kx_refund_status <>", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusGreaterThan(Integer value) {
            addCriterion("kx_refund_status >", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("kx_refund_status >=", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusLessThan(Integer value) {
            addCriterion("kx_refund_status <", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusLessThanOrEqualTo(Integer value) {
            addCriterion("kx_refund_status <=", value, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusIn(List<Integer> values) {
            addCriterion("kx_refund_status in", values, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusNotIn(List<Integer> values) {
            addCriterion("kx_refund_status not in", values, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusBetween(Integer value1, Integer value2) {
            addCriterion("kx_refund_status between", value1, value2, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("kx_refund_status not between", value1, value2, "kxRefundStatus");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonIsNull() {
            addCriterion("kx_refund_reason is null");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonIsNotNull() {
            addCriterion("kx_refund_reason is not null");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonEqualTo(String value) {
            addCriterion("kx_refund_reason =", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonNotEqualTo(String value) {
            addCriterion("kx_refund_reason <>", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonGreaterThan(String value) {
            addCriterion("kx_refund_reason >", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonGreaterThanOrEqualTo(String value) {
            addCriterion("kx_refund_reason >=", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLessThan(String value) {
            addCriterion("kx_refund_reason <", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLessThanOrEqualTo(String value) {
            addCriterion("kx_refund_reason <=", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("kx_refund_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLike(String value) {
            addCriterion("kx_refund_reason like", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonNotLike(String value) {
            addCriterion("kx_refund_reason not like", value, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonIn(List<String> values) {
            addCriterion("kx_refund_reason in", values, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonNotIn(List<String> values) {
            addCriterion("kx_refund_reason not in", values, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonBetween(String value1, String value2) {
            addCriterion("kx_refund_reason between", value1, value2, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonNotBetween(String value1, String value2) {
            addCriterion("kx_refund_reason not between", value1, value2, "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNull() {
            addCriterion("spu_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNotNull() {
            addCriterion("spu_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualTo(String value) {
            addCriterion("spu_offering_version =", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualTo(String value) {
            addCriterion("spu_offering_version <>", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThan(String value) {
            addCriterion("spu_offering_version >", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_version >=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThan(String value) {
            addCriterion("spu_offering_version <", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_version <=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLike(String value) {
            addCriterion("spu_offering_version like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotLike(String value) {
            addCriterion("spu_offering_version not like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIn(List<String> values) {
            addCriterion("spu_offering_version in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotIn(List<String> values) {
            addCriterion("spu_offering_version not in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionBetween(String value1, String value2) {
            addCriterion("spu_offering_version between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("spu_offering_version not between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdIsNull() {
            addCriterion("reserve_be_id is null");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdIsNotNull() {
            addCriterion("reserve_be_id is not null");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdEqualTo(String value) {
            addCriterion("reserve_be_id =", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdNotEqualTo(String value) {
            addCriterion("reserve_be_id <>", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdGreaterThan(String value) {
            addCriterion("reserve_be_id >", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("reserve_be_id >=", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLessThan(String value) {
            addCriterion("reserve_be_id <", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLessThanOrEqualTo(String value) {
            addCriterion("reserve_be_id <=", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLike(String value) {
            addCriterion("reserve_be_id like", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdNotLike(String value) {
            addCriterion("reserve_be_id not like", value, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdIn(List<String> values) {
            addCriterion("reserve_be_id in", values, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdNotIn(List<String> values) {
            addCriterion("reserve_be_id not in", values, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdBetween(String value1, String value2) {
            addCriterion("reserve_be_id between", value1, value2, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdNotBetween(String value1, String value2) {
            addCriterion("reserve_be_id not between", value1, value2, "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveLocationIsNull() {
            addCriterion("reserve_location is null");
            return (Criteria) this;
        }

        public Criteria andReserveLocationIsNotNull() {
            addCriterion("reserve_location is not null");
            return (Criteria) this;
        }

        public Criteria andReserveLocationEqualTo(String value) {
            addCriterion("reserve_location =", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationNotEqualTo(String value) {
            addCriterion("reserve_location <>", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationGreaterThan(String value) {
            addCriterion("reserve_location >", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationGreaterThanOrEqualTo(String value) {
            addCriterion("reserve_location >=", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationLessThan(String value) {
            addCriterion("reserve_location <", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationLessThanOrEqualTo(String value) {
            addCriterion("reserve_location <=", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("reserve_location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveLocationLike(String value) {
            addCriterion("reserve_location like", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationNotLike(String value) {
            addCriterion("reserve_location not like", value, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationIn(List<String> values) {
            addCriterion("reserve_location in", values, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationNotIn(List<String> values) {
            addCriterion("reserve_location not in", values, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationBetween(String value1, String value2) {
            addCriterion("reserve_location between", value1, value2, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andReserveLocationNotBetween(String value1, String value2) {
            addCriterion("reserve_location not between", value1, value2, "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberIsNull() {
            addCriterion("special_after_refunds_number is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberIsNotNull() {
            addCriterion("special_after_refunds_number is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberEqualTo(String value) {
            addCriterion("special_after_refunds_number =", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberNotEqualTo(String value) {
            addCriterion("special_after_refunds_number <>", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberGreaterThan(String value) {
            addCriterion("special_after_refunds_number >", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberGreaterThanOrEqualTo(String value) {
            addCriterion("special_after_refunds_number >=", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLessThan(String value) {
            addCriterion("special_after_refunds_number <", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLessThanOrEqualTo(String value) {
            addCriterion("special_after_refunds_number <=", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("special_after_refunds_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLike(String value) {
            addCriterion("special_after_refunds_number like", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberNotLike(String value) {
            addCriterion("special_after_refunds_number not like", value, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberIn(List<String> values) {
            addCriterion("special_after_refunds_number in", values, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberNotIn(List<String> values) {
            addCriterion("special_after_refunds_number not in", values, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberBetween(String value1, String value2) {
            addCriterion("special_after_refunds_number between", value1, value2, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberNotBetween(String value1, String value2) {
            addCriterion("special_after_refunds_number not between", value1, value2, "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformIsNull() {
            addCriterion("spu_list_platform is null");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformIsNotNull() {
            addCriterion("spu_list_platform is not null");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformEqualTo(String value) {
            addCriterion("spu_list_platform =", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformNotEqualTo(String value) {
            addCriterion("spu_list_platform <>", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformNotEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformGreaterThan(String value) {
            addCriterion("spu_list_platform >", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformGreaterThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("spu_list_platform >=", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformGreaterThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLessThan(String value) {
            addCriterion("spu_list_platform <", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLessThanColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLessThanOrEqualTo(String value) {
            addCriterion("spu_list_platform <=", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLessThanOrEqualToColumn(Order2cInfo.Column column) {
            addCriterion(new StringBuilder("spu_list_platform <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLike(String value) {
            addCriterion("spu_list_platform like", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformNotLike(String value) {
            addCriterion("spu_list_platform not like", value, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformIn(List<String> values) {
            addCriterion("spu_list_platform in", values, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformNotIn(List<String> values) {
            addCriterion("spu_list_platform not in", values, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformBetween(String value1, String value2) {
            addCriterion("spu_list_platform between", value1, value2, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformNotBetween(String value1, String value2) {
            addCriterion("spu_list_platform not between", value1, value2, "spuListPlatform");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLikeInsensitive(String value) {
            addCriterion("upper(order_type) like", value.toUpperCase(), "orderType");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLikeInsensitive(String value) {
            addCriterion("upper(business_code) like", value.toUpperCase(), "businessCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLikeInsensitive(String value) {
            addCriterion("upper(create_oper_code) like", value.toUpperCase(), "createOperCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLikeInsensitive(String value) {
            addCriterion("upper(employee_num) like", value.toUpperCase(), "employeeNum");
            return (Criteria) this;
        }

        public Criteria andCustMgNameLikeInsensitive(String value) {
            addCriterion("upper(cust_mg_name) like", value.toUpperCase(), "custMgName");
            return (Criteria) this;
        }

        public Criteria andCustMgPhoneLikeInsensitive(String value) {
            addCriterion("upper(cust_mg_phone) like", value.toUpperCase(), "custMgPhone");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andLocationLikeInsensitive(String value) {
            addCriterion("upper(location) like", value.toUpperCase(), "location");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_ID) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andOrderOrgBizCodeLikeInsensitive(String value) {
            addCriterion("upper(order_org_biz_code) like", value.toUpperCase(), "orderOrgBizCode");
            return (Criteria) this;
        }

        public Criteria andOrgLevelLikeInsensitive(String value) {
            addCriterion("upper(org_level) like", value.toUpperCase(), "orgLevel");
            return (Criteria) this;
        }

        public Criteria andOrgNameLikeInsensitive(String value) {
            addCriterion("upper(org_name) like", value.toUpperCase(), "orgName");
            return (Criteria) this;
        }

        public Criteria andProvinceOrgNameLikeInsensitive(String value) {
            addCriterion("upper(province_org_name) like", value.toUpperCase(), "provinceOrgName");
            return (Criteria) this;
        }

        public Criteria andRemarksLikeInsensitive(String value) {
            addCriterion("upper(remarks) like", value.toUpperCase(), "remarks");
            return (Criteria) this;
        }

        public Criteria andBookidLikeInsensitive(String value) {
            addCriterion("upper(bookId) like", value.toUpperCase(), "bookid");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLikeInsensitive(String value) {
            addCriterion("upper(total_price) like", value.toUpperCase(), "totalPrice");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLikeInsensitive(String value) {
            addCriterion("upper(create_time) like", value.toUpperCase(), "createTime");
            return (Criteria) this;
        }

        public Criteria andContactPersonNameLikeInsensitive(String value) {
            addCriterion("upper(contact_person_name) like", value.toUpperCase(), "contactPersonName");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLikeInsensitive(String value) {
            addCriterion("upper(contact_phone) like", value.toUpperCase(), "contactPhone");
            return (Criteria) this;
        }

        public Criteria andAddr1LikeInsensitive(String value) {
            addCriterion("upper(addr1) like", value.toUpperCase(), "addr1");
            return (Criteria) this;
        }

        public Criteria andAddr2LikeInsensitive(String value) {
            addCriterion("upper(addr2) like", value.toUpperCase(), "addr2");
            return (Criteria) this;
        }

        public Criteria andAddr3LikeInsensitive(String value) {
            addCriterion("upper(addr3) like", value.toUpperCase(), "addr3");
            return (Criteria) this;
        }

        public Criteria andAddr4LikeInsensitive(String value) {
            addCriterion("upper(addr4) like", value.toUpperCase(), "addr4");
            return (Criteria) this;
        }

        public Criteria andUsaddrLikeInsensitive(String value) {
            addCriterion("upper(usaddr) like", value.toUpperCase(), "usaddr");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_class) like", value.toUpperCase(), "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_code) like", value.toUpperCase(), "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLikeInsensitive(String value) {
            addCriterion("upper(supplier_code) like", value.toUpperCase(), "supplierCode");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLikeInsensitive(String value) {
            addCriterion("upper(deduct_price) like", value.toUpperCase(), "deductPrice");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelSourceLikeInsensitive(String value) {
            addCriterion("upper(ordering_channel_source) like", value.toUpperCase(), "orderingChannelSource");
            return (Criteria) this;
        }

        public Criteria andOrderingChannelNameLikeInsensitive(String value) {
            addCriterion("upper(ordering_channel_name) like", value.toUpperCase(), "orderingChannelName");
            return (Criteria) this;
        }

        public Criteria andSsoTerminalTypeLikeInsensitive(String value) {
            addCriterion("upper(sso_terminal_type) like", value.toUpperCase(), "ssoTerminalType");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusLikeInsensitive(String value) {
            addCriterion("upper(special_after_status) like", value.toUpperCase(), "specialAfterStatus");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterStatusTimeLikeInsensitive(String value) {
            addCriterion("upper(special_after_status_time) like", value.toUpperCase(), "specialAfterStatusTime");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterLatestTimeLikeInsensitive(String value) {
            addCriterion("upper(special_after_latest_time) like", value.toUpperCase(), "specialAfterLatestTime");
            return (Criteria) this;
        }

        public Criteria andSyncK3IdLikeInsensitive(String value) {
            addCriterion("upper(sync_k3_id) like", value.toUpperCase(), "syncK3Id");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLikeInsensitive(String value) {
            addCriterion("upper(valet_order_complete_time) like", value.toUpperCase(), "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andBillLadderTypeLikeInsensitive(String value) {
            addCriterion("upper(bill_ladder_type) like", value.toUpperCase(), "billLadderType");
            return (Criteria) this;
        }

        public Criteria andKxRefundReasonLikeInsensitive(String value) {
            addCriterion("upper(kx_refund_reason) like", value.toUpperCase(), "kxRefundReason");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_version) like", value.toUpperCase(), "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andReserveBeIdLikeInsensitive(String value) {
            addCriterion("upper(reserve_be_id) like", value.toUpperCase(), "reserveBeId");
            return (Criteria) this;
        }

        public Criteria andReserveLocationLikeInsensitive(String value) {
            addCriterion("upper(reserve_location) like", value.toUpperCase(), "reserveLocation");
            return (Criteria) this;
        }

        public Criteria andSpecialAfterRefundsNumberLikeInsensitive(String value) {
            addCriterion("upper(special_after_refunds_number) like", value.toUpperCase(), "specialAfterRefundsNumber");
            return (Criteria) this;
        }

        public Criteria andSpuListPlatformLikeInsensitive(String value) {
            addCriterion("upper(spu_list_platform) like", value.toUpperCase(), "spuListPlatform");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Fri Jul 26 10:24:02 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        private Order2cInfoExample example;

        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        protected Criteria(Order2cInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        public Order2cInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri Jul 26 10:24:02 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri Jul 26 10:24:02 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.Order2cInfoExample example);
    }
}