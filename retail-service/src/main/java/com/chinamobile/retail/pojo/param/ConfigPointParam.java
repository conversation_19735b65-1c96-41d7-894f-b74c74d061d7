package com.chinamobile.retail.pojo.param;

import com.chinamobile.retail.pojo.dto.SkuPartnerRoleDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * created by l<PERSON><PERSON>ng on 2022/8/31 16:12
 */
@Data
public class ConfigPointParam {

    @NotEmpty(message = "勾选项不能为空")
    @Valid
    private List<SkuPartnerRoleDTO> itemList;

}
