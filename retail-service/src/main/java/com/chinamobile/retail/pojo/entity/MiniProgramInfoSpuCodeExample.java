package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class MiniProgramInfoSpuCodeExample {
    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCodeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCodeExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCodeExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramInfoSpuCodeExample example = new MiniProgramInfoSpuCodeExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCodeExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public MiniProgramInfoSpuCodeExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInfoIdIsNull() {
            addCriterion("info_id is null");
            return (Criteria) this;
        }

        public Criteria andInfoIdIsNotNull() {
            addCriterion("info_id is not null");
            return (Criteria) this;
        }

        public Criteria andInfoIdEqualTo(String value) {
            addCriterion("info_id =", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdNotEqualTo(String value) {
            addCriterion("info_id <>", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdNotEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdGreaterThan(String value) {
            addCriterion("info_id >", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdGreaterThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdGreaterThanOrEqualTo(String value) {
            addCriterion("info_id >=", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdGreaterThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdLessThan(String value) {
            addCriterion("info_id <", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdLessThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdLessThanOrEqualTo(String value) {
            addCriterion("info_id <=", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdLessThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("info_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInfoIdLike(String value) {
            addCriterion("info_id like", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdNotLike(String value) {
            addCriterion("info_id not like", value, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdIn(List<String> values) {
            addCriterion("info_id in", values, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdNotIn(List<String> values) {
            addCriterion("info_id not in", values, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdBetween(String value1, String value2) {
            addCriterion("info_id between", value1, value2, "infoId");
            return (Criteria) this;
        }

        public Criteria andInfoIdNotBetween(String value1, String value2) {
            addCriterion("info_id not between", value1, value2, "infoId");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNull() {
            addCriterion("spu_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNotNull() {
            addCriterion("spu_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualTo(String value) {
            addCriterion("spu_offering_code =", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualTo(String value) {
            addCriterion("spu_offering_code <>", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThan(String value) {
            addCriterion("spu_offering_code >", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_code >=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThan(String value) {
            addCriterion("spu_offering_code <", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_code <=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualToColumn(MiniProgramInfoSpuCode.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLike(String value) {
            addCriterion("spu_offering_code like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotLike(String value) {
            addCriterion("spu_offering_code not like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIn(List<String> values) {
            addCriterion("spu_offering_code in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotIn(List<String> values) {
            addCriterion("spu_offering_code not in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeBetween(String value1, String value2) {
            addCriterion("spu_offering_code between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("spu_offering_code not between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andInfoIdLikeInsensitive(String value) {
            addCriterion("upper(info_id) like", value.toUpperCase(), "infoId");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_code) like", value.toUpperCase(), "spuOfferingCode");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Aug 07 16:52:41 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        private MiniProgramInfoSpuCodeExample example;

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        protected Criteria(MiniProgramInfoSpuCodeExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public MiniProgramInfoSpuCodeExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Aug 07 16:52:41 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Aug 07 16:52:41 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Aug 07 16:52:41 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramInfoSpuCodeExample example);
    }
}