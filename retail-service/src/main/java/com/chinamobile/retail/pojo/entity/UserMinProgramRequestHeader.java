package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class UserMinProgramRequestHeader implements Serializable {
    private String id;

    private String userRetailId;

    private String hearderUrl;

    private String fileKey;

    private Integer auditStatus;

    private String auditUserId;

    private String auditReason;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public UserMinProgramRequestHeader withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUserRetailId() {
        return userRetailId;
    }

    public UserMinProgramRequestHeader withUserRetailId(String userRetailId) {
        this.setUserRetailId(userRetailId);
        return this;
    }

    public void setUserRetailId(String userRetailId) {
        this.userRetailId = userRetailId == null ? null : userRetailId.trim();
    }

    public String getHearderUrl() {
        return hearderUrl;
    }

    public UserMinProgramRequestHeader withHearderUrl(String hearderUrl) {
        this.setHearderUrl(hearderUrl);
        return this;
    }

    public void setHearderUrl(String hearderUrl) {
        this.hearderUrl = hearderUrl == null ? null : hearderUrl.trim();
    }

    public String getFileKey() {
        return fileKey;
    }

    public UserMinProgramRequestHeader withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey == null ? null : fileKey.trim();
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public UserMinProgramRequestHeader withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public UserMinProgramRequestHeader withAuditUserId(String auditUserId) {
        this.setAuditUserId(auditUserId);
        return this;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId == null ? null : auditUserId.trim();
    }

    public String getAuditReason() {
        return auditReason;
    }

    public UserMinProgramRequestHeader withAuditReason(String auditReason) {
        this.setAuditReason(auditReason);
        return this;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason == null ? null : auditReason.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public UserMinProgramRequestHeader withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public UserMinProgramRequestHeader withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userRetailId=").append(userRetailId);
        sb.append(", hearderUrl=").append(hearderUrl);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", auditUserId=").append(auditUserId);
        sb.append(", auditReason=").append(auditReason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserMinProgramRequestHeader other = (UserMinProgramRequestHeader) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserRetailId() == null ? other.getUserRetailId() == null : this.getUserRetailId().equals(other.getUserRetailId()))
            && (this.getHearderUrl() == null ? other.getHearderUrl() == null : this.getHearderUrl().equals(other.getHearderUrl()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getAuditUserId() == null ? other.getAuditUserId() == null : this.getAuditUserId().equals(other.getAuditUserId()))
            && (this.getAuditReason() == null ? other.getAuditReason() == null : this.getAuditReason().equals(other.getAuditReason()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserRetailId() == null) ? 0 : getUserRetailId().hashCode());
        result = prime * result + ((getHearderUrl() == null) ? 0 : getHearderUrl().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getAuditUserId() == null) ? 0 : getAuditUserId().hashCode());
        result = prime * result + ((getAuditReason() == null) ? 0 : getAuditReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        userRetailId("user_retail_id", "userRetailId", "VARCHAR", false),
        hearderUrl("hearder_url", "hearderUrl", "VARCHAR", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        auditUserId("audit_user_id", "auditUserId", "VARCHAR", false),
        auditReason("audit_reason", "auditReason", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}