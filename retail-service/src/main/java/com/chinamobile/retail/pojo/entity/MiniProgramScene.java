package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景
 *
 * <AUTHOR>
public class MiniProgramScene implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String id;

    /**
     * 场景解决方案名称
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String name;

    /**
     * 一级目录id
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String firstDirectoryId;

    /**
     * 二级目录id
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String secondDirectoryId;

    /**
     * 解决方案产品编码
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String spuCode;

    /**
     * 场景头图url
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String headImageUrl;

    /**
     * 场景图片url
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String imageUrl;

    /**
     * 需求模板ID
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String templateId;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private String createUid;

    /**
     * 状态,0:已上传、1:已发布、2:已下线、3:审核中,4:已驳回
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private Integer status;

    /**
     * 审核状态，0-待审核（审核列表不可见）、1-待审核（审核列表可见）、2-已通过、3-已驳回
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private Integer auditStatus;

    /**
     * 是否已删除，0-否，1-是
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private Boolean deleted;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.id
     *
     * @return the value of supply_chain..mini_program_scene.id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.id
     *
     * @param id the value for supply_chain..mini_program_scene.id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.name
     *
     * @return the value of supply_chain..mini_program_scene.name
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.name
     *
     * @param name the value for supply_chain..mini_program_scene.name
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.first_directory_id
     *
     * @return the value of supply_chain..mini_program_scene.first_directory_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getFirstDirectoryId() {
        return firstDirectoryId;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withFirstDirectoryId(String firstDirectoryId) {
        this.setFirstDirectoryId(firstDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.first_directory_id
     *
     * @param firstDirectoryId the value for supply_chain..mini_program_scene.first_directory_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setFirstDirectoryId(String firstDirectoryId) {
        this.firstDirectoryId = firstDirectoryId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.second_directory_id
     *
     * @return the value of supply_chain..mini_program_scene.second_directory_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getSecondDirectoryId() {
        return secondDirectoryId;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withSecondDirectoryId(String secondDirectoryId) {
        this.setSecondDirectoryId(secondDirectoryId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.second_directory_id
     *
     * @param secondDirectoryId the value for supply_chain..mini_program_scene.second_directory_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setSecondDirectoryId(String secondDirectoryId) {
        this.secondDirectoryId = secondDirectoryId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.spu_code
     *
     * @return the value of supply_chain..mini_program_scene.spu_code
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.spu_code
     *
     * @param spuCode the value for supply_chain..mini_program_scene.spu_code
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.head_image_url
     *
     * @return the value of supply_chain..mini_program_scene.head_image_url
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getHeadImageUrl() {
        return headImageUrl;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withHeadImageUrl(String headImageUrl) {
        this.setHeadImageUrl(headImageUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.head_image_url
     *
     * @param headImageUrl the value for supply_chain..mini_program_scene.head_image_url
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setHeadImageUrl(String headImageUrl) {
        this.headImageUrl = headImageUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.image_url
     *
     * @return the value of supply_chain..mini_program_scene.image_url
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withImageUrl(String imageUrl) {
        this.setImageUrl(imageUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.image_url
     *
     * @param imageUrl the value for supply_chain..mini_program_scene.image_url
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.template_id
     *
     * @return the value of supply_chain..mini_program_scene.template_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.template_id
     *
     * @param templateId the value for supply_chain..mini_program_scene.template_id
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.create_uid
     *
     * @return the value of supply_chain..mini_program_scene.create_uid
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public String getCreateUid() {
        return createUid;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withCreateUid(String createUid) {
        this.setCreateUid(createUid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.create_uid
     *
     * @param createUid the value for supply_chain..mini_program_scene.create_uid
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setCreateUid(String createUid) {
        this.createUid = createUid;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.status
     *
     * @return the value of supply_chain..mini_program_scene.status
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.status
     *
     * @param status the value for supply_chain..mini_program_scene.status
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.audit_status
     *
     * @return the value of supply_chain..mini_program_scene.audit_status
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.audit_status
     *
     * @param auditStatus the value for supply_chain..mini_program_scene.audit_status
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.deleted
     *
     * @return the value of supply_chain..mini_program_scene.deleted
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withDeleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.deleted
     *
     * @param deleted the value for supply_chain..mini_program_scene.deleted
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.create_time
     *
     * @return the value of supply_chain..mini_program_scene.create_time
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.create_time
     *
     * @param createTime the value for supply_chain..mini_program_scene.create_time
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene.update_time
     *
     * @return the value of supply_chain..mini_program_scene.update_time
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public MiniProgramScene withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_scene.update_time
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", firstDirectoryId=").append(firstDirectoryId);
        sb.append(", secondDirectoryId=").append(secondDirectoryId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", headImageUrl=").append(headImageUrl);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", templateId=").append(templateId);
        sb.append(", createUid=").append(createUid);
        sb.append(", status=").append(status);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramScene other = (MiniProgramScene) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getFirstDirectoryId() == null ? other.getFirstDirectoryId() == null : this.getFirstDirectoryId().equals(other.getFirstDirectoryId()))
            && (this.getSecondDirectoryId() == null ? other.getSecondDirectoryId() == null : this.getSecondDirectoryId().equals(other.getSecondDirectoryId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getHeadImageUrl() == null ? other.getHeadImageUrl() == null : this.getHeadImageUrl().equals(other.getHeadImageUrl()))
            && (this.getImageUrl() == null ? other.getImageUrl() == null : this.getImageUrl().equals(other.getImageUrl()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getCreateUid() == null ? other.getCreateUid() == null : this.getCreateUid().equals(other.getCreateUid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getFirstDirectoryId() == null) ? 0 : getFirstDirectoryId().hashCode());
        result = prime * result + ((getSecondDirectoryId() == null) ? 0 : getSecondDirectoryId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getHeadImageUrl() == null) ? 0 : getHeadImageUrl().hashCode());
        result = prime * result + ((getImageUrl() == null) ? 0 : getImageUrl().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getCreateUid() == null) ? 0 : getCreateUid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        firstDirectoryId("first_directory_id", "firstDirectoryId", "VARCHAR", false),
        secondDirectoryId("second_directory_id", "secondDirectoryId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        headImageUrl("head_image_url", "headImageUrl", "VARCHAR", false),
        imageUrl("image_url", "imageUrl", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        createUid("create_uid", "createUid", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        deleted("deleted", "deleted", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Dec 17 10:41:46 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}