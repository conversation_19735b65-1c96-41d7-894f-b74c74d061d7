package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * sku核心部件配置
 *
 * <AUTHOR>
public class SkuCoreComponent implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private String id;

    /**
     * spu编码
     *
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private String spuCode;

    /**
     * sku核心部件名称
     *
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private String coreComponentName;

    /**
     * sku编码
     *
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private String skuCode;

    /**
     * 是否删除 0-未删除，1-已删除
     *
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private Boolean isDelete;

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..sku_core_component.id
     *
     * @return the value of supply_chain..sku_core_component.id
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public SkuCoreComponent withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_core_component.id
     *
     * @param id the value for supply_chain..sku_core_component.id
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_core_component.spu_code
     *
     * @return the value of supply_chain..sku_core_component.spu_code
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public SkuCoreComponent withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_core_component.spu_code
     *
     * @param spuCode the value for supply_chain..sku_core_component.spu_code
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_core_component.core_component_name
     *
     * @return the value of supply_chain..sku_core_component.core_component_name
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public String getCoreComponentName() {
        return coreComponentName;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public SkuCoreComponent withCoreComponentName(String coreComponentName) {
        this.setCoreComponentName(coreComponentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_core_component.core_component_name
     *
     * @param coreComponentName the value for supply_chain..sku_core_component.core_component_name
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public void setCoreComponentName(String coreComponentName) {
        this.coreComponentName = coreComponentName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_core_component.sku_code
     *
     * @return the value of supply_chain..sku_core_component.sku_code
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public SkuCoreComponent withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_core_component.sku_code
     *
     * @param skuCode the value for supply_chain..sku_core_component.sku_code
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_core_component.is_delete
     *
     * @return the value of supply_chain..sku_core_component.is_delete
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public SkuCoreComponent withIsDelete(Boolean isDelete) {
        this.setIsDelete(isDelete);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_core_component.is_delete
     *
     * @param isDelete the value for supply_chain..sku_core_component.is_delete
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", coreComponentName=").append(coreComponentName);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SkuCoreComponent other = (SkuCoreComponent) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getCoreComponentName() == null ? other.getCoreComponentName() == null : this.getCoreComponentName().equals(other.getCoreComponentName()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getCoreComponentName() == null) ? 0 : getCoreComponentName().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 16:38:37 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        coreComponentName("core_component_name", "coreComponentName", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        isDelete("is_delete", "isDelete", "BIT", false);

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Feb 25 16:38:37 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}