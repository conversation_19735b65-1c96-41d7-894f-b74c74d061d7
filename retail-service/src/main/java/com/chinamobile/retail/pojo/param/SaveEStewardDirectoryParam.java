package com.chinamobile.retail.pojo.param;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class SaveEStewardDirectoryParam {

    //目录id，编辑时有值
    private String id;

    //目录级别
    @NotNull
    @Range(min = 1,max = 3,message = "目录级别只能是1,2,3")
    private Integer level;

    //一级目录id
    private String firstDirectoryId;

    //二级目录id
    private String secondDirectoryId;

    //目录名称
    @NotEmpty(message = "目录名称不能为空")
    private String name;

    //目录图片链接
    @NotEmpty(message = "目录图片链接不能为空")
    private String imgUrl;
}
