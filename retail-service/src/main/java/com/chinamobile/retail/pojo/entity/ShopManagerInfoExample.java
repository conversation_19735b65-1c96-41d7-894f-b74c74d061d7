package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ShopManagerInfoExample {
    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ShopManagerInfoExample example = new ShopManagerInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public ShopManagerInfoExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNull() {
            addCriterion("create_oper_code is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNotNull() {
            addCriterion("create_oper_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualTo(String value) {
            addCriterion("create_oper_code =", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualTo(String value) {
            addCriterion("create_oper_code <>", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThan(String value) {
            addCriterion("create_oper_code >", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_code >=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThan(String value) {
            addCriterion("create_oper_code <", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualTo(String value) {
            addCriterion("create_oper_code <=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLike(String value) {
            addCriterion("create_oper_code like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotLike(String value) {
            addCriterion("create_oper_code not like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIn(List<String> values) {
            addCriterion("create_oper_code in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotIn(List<String> values) {
            addCriterion("create_oper_code not in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeBetween(String value1, String value2) {
            addCriterion("create_oper_code between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotBetween(String value1, String value2) {
            addCriterion("create_oper_code not between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneIsNull() {
            addCriterion("create_oper_phone is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneIsNotNull() {
            addCriterion("create_oper_phone is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneEqualTo(String value) {
            addCriterion("create_oper_phone =", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneNotEqualTo(String value) {
            addCriterion("create_oper_phone <>", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneGreaterThan(String value) {
            addCriterion("create_oper_phone >", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_phone >=", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLessThan(String value) {
            addCriterion("create_oper_phone <", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLessThanOrEqualTo(String value) {
            addCriterion("create_oper_phone <=", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_oper_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLike(String value) {
            addCriterion("create_oper_phone like", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneNotLike(String value) {
            addCriterion("create_oper_phone not like", value, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneIn(List<String> values) {
            addCriterion("create_oper_phone in", values, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneNotIn(List<String> values) {
            addCriterion("create_oper_phone not in", values, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneBetween(String value1, String value2) {
            addCriterion("create_oper_phone between", value1, value2, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneNotBetween(String value1, String value2) {
            addCriterion("create_oper_phone not between", value1, value2, "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameIsNull() {
            addCriterion("customer_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameIsNotNull() {
            addCriterion("customer_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameEqualTo(String value) {
            addCriterion("customer_manager_name =", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameNotEqualTo(String value) {
            addCriterion("customer_manager_name <>", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameGreaterThan(String value) {
            addCriterion("customer_manager_name >", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_manager_name >=", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLessThan(String value) {
            addCriterion("customer_manager_name <", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_manager_name <=", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("customer_manager_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLike(String value) {
            addCriterion("customer_manager_name like", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameNotLike(String value) {
            addCriterion("customer_manager_name not like", value, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameIn(List<String> values) {
            addCriterion("customer_manager_name in", values, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameNotIn(List<String> values) {
            addCriterion("customer_manager_name not in", values, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameBetween(String value1, String value2) {
            addCriterion("customer_manager_name between", value1, value2, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameNotBetween(String value1, String value2) {
            addCriterion("customer_manager_name not between", value1, value2, "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNull() {
            addCriterion("employee_num is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNotNull() {
            addCriterion("employee_num is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualTo(String value) {
            addCriterion("employee_num =", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualTo(String value) {
            addCriterion("employee_num <>", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThan(String value) {
            addCriterion("employee_num >", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualTo(String value) {
            addCriterion("employee_num >=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThan(String value) {
            addCriterion("employee_num <", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualTo(String value) {
            addCriterion("employee_num <=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("employee_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLike(String value) {
            addCriterion("employee_num like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotLike(String value) {
            addCriterion("employee_num not like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIn(List<String> values) {
            addCriterion("employee_num in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotIn(List<String> values) {
            addCriterion("employee_num not in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumBetween(String value1, String value2) {
            addCriterion("employee_num between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotBetween(String value1, String value2) {
            addCriterion("employee_num not between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(Date value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(Date value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(Date value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(Date value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(Date value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(Date value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("register_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<Date> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<Date> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(Date value1, Date value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(Date value1, Date value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("region_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLike(String value) {
            addCriterion("region_name like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotLike(String value) {
            addCriterion("region_name not like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andMrgStatusIsNull() {
            addCriterion("mrg_status is null");
            return (Criteria) this;
        }

        public Criteria andMrgStatusIsNotNull() {
            addCriterion("mrg_status is not null");
            return (Criteria) this;
        }

        public Criteria andMrgStatusEqualTo(String value) {
            addCriterion("mrg_status =", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusNotEqualTo(String value) {
            addCriterion("mrg_status <>", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusGreaterThan(String value) {
            addCriterion("mrg_status >", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusGreaterThanOrEqualTo(String value) {
            addCriterion("mrg_status >=", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusLessThan(String value) {
            addCriterion("mrg_status <", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusLessThanOrEqualTo(String value) {
            addCriterion("mrg_status <=", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("mrg_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMrgStatusLike(String value) {
            addCriterion("mrg_status like", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusNotLike(String value) {
            addCriterion("mrg_status not like", value, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusIn(List<String> values) {
            addCriterion("mrg_status in", values, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusNotIn(List<String> values) {
            addCriterion("mrg_status not in", values, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusBetween(String value1, String value2) {
            addCriterion("mrg_status between", value1, value2, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andMrgStatusNotBetween(String value1, String value2) {
            addCriterion("mrg_status not between", value1, value2, "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andGriddingNameIsNull() {
            addCriterion("gridding_name is null");
            return (Criteria) this;
        }

        public Criteria andGriddingNameIsNotNull() {
            addCriterion("gridding_name is not null");
            return (Criteria) this;
        }

        public Criteria andGriddingNameEqualTo(String value) {
            addCriterion("gridding_name =", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameNotEqualTo(String value) {
            addCriterion("gridding_name <>", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameGreaterThan(String value) {
            addCriterion("gridding_name >", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameGreaterThanOrEqualTo(String value) {
            addCriterion("gridding_name >=", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameLessThan(String value) {
            addCriterion("gridding_name <", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameLessThanOrEqualTo(String value) {
            addCriterion("gridding_name <=", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("gridding_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGriddingNameLike(String value) {
            addCriterion("gridding_name like", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameNotLike(String value) {
            addCriterion("gridding_name not like", value, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameIn(List<String> values) {
            addCriterion("gridding_name in", values, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameNotIn(List<String> values) {
            addCriterion("gridding_name not in", values, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameBetween(String value1, String value2) {
            addCriterion("gridding_name between", value1, value2, "griddingName");
            return (Criteria) this;
        }

        public Criteria andGriddingNameNotBetween(String value1, String value2) {
            addCriterion("gridding_name not between", value1, value2, "griddingName");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNull() {
            addCriterion("channel_type is null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNotNull() {
            addCriterion("channel_type is not null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeEqualTo(String value) {
            addCriterion("channel_type =", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotEqualTo(String value) {
            addCriterion("channel_type <>", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThan(String value) {
            addCriterion("channel_type >", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_type >=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThan(String value) {
            addCriterion("channel_type <", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanOrEqualTo(String value) {
            addCriterion("channel_type <=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLike(String value) {
            addCriterion("channel_type like", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotLike(String value) {
            addCriterion("channel_type not like", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIn(List<String> values) {
            addCriterion("channel_type in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotIn(List<String> values) {
            addCriterion("channel_type not in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeBetween(String value1, String value2) {
            addCriterion("channel_type between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotBetween(String value1, String value2) {
            addCriterion("channel_type not between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryIsNull() {
            addCriterion("manager_category is null");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryIsNotNull() {
            addCriterion("manager_category is not null");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryEqualTo(String value) {
            addCriterion("manager_category =", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryNotEqualTo(String value) {
            addCriterion("manager_category <>", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryGreaterThan(String value) {
            addCriterion("manager_category >", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("manager_category >=", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLessThan(String value) {
            addCriterion("manager_category <", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLessThanOrEqualTo(String value) {
            addCriterion("manager_category <=", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("manager_category <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLike(String value) {
            addCriterion("manager_category like", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryNotLike(String value) {
            addCriterion("manager_category not like", value, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryIn(List<String> values) {
            addCriterion("manager_category in", values, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryNotIn(List<String> values) {
            addCriterion("manager_category not in", values, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryBetween(String value1, String value2) {
            addCriterion("manager_category between", value1, value2, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryNotBetween(String value1, String value2) {
            addCriterion("manager_category not between", value1, value2, "managerCategory");
            return (Criteria) this;
        }

        public Criteria andRinseTimeIsNull() {
            addCriterion("rinse_time is null");
            return (Criteria) this;
        }

        public Criteria andRinseTimeIsNotNull() {
            addCriterion("rinse_time is not null");
            return (Criteria) this;
        }

        public Criteria andRinseTimeEqualTo(Date value) {
            addCriterion("rinse_time =", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotEqualTo(Date value) {
            addCriterion("rinse_time <>", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThan(Date value) {
            addCriterion("rinse_time >", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("rinse_time >=", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThan(Date value) {
            addCriterion("rinse_time <", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanOrEqualTo(Date value) {
            addCriterion("rinse_time <=", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeIn(List<Date> values) {
            addCriterion("rinse_time in", values, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotIn(List<Date> values) {
            addCriterion("rinse_time not in", values, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeBetween(Date value1, Date value2) {
            addCriterion("rinse_time between", value1, value2, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotBetween(Date value1, Date value2) {
            addCriterion("rinse_time not between", value1, value2, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andIsRinseIsNull() {
            addCriterion("is_rinse is null");
            return (Criteria) this;
        }

        public Criteria andIsRinseIsNotNull() {
            addCriterion("is_rinse is not null");
            return (Criteria) this;
        }

        public Criteria andIsRinseEqualTo(Boolean value) {
            addCriterion("is_rinse =", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseNotEqualTo(Boolean value) {
            addCriterion("is_rinse <>", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThan(Boolean value) {
            addCriterion("is_rinse >", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_rinse >=", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThan(Boolean value) {
            addCriterion("is_rinse <", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanOrEqualTo(Boolean value) {
            addCriterion("is_rinse <=", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseIn(List<Boolean> values) {
            addCriterion("is_rinse in", values, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotIn(List<Boolean> values) {
            addCriterion("is_rinse not in", values, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseBetween(Boolean value1, Boolean value2) {
            addCriterion("is_rinse between", value1, value2, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_rinse not between", value1, value2, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsMerchantIsNull() {
            addCriterion("is_merchant is null");
            return (Criteria) this;
        }

        public Criteria andIsMerchantIsNotNull() {
            addCriterion("is_merchant is not null");
            return (Criteria) this;
        }

        public Criteria andIsMerchantEqualTo(Boolean value) {
            addCriterion("is_merchant =", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantNotEqualTo(Boolean value) {
            addCriterion("is_merchant <>", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantGreaterThan(Boolean value) {
            addCriterion("is_merchant >", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_merchant >=", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantLessThan(Boolean value) {
            addCriterion("is_merchant <", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantLessThanOrEqualTo(Boolean value) {
            addCriterion("is_merchant <=", value, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("is_merchant <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsMerchantIn(List<Boolean> values) {
            addCriterion("is_merchant in", values, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantNotIn(List<Boolean> values) {
            addCriterion("is_merchant not in", values, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantBetween(Boolean value1, Boolean value2) {
            addCriterion("is_merchant between", value1, value2, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andIsMerchantNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_merchant not between", value1, value2, "isMerchant");
            return (Criteria) this;
        }

        public Criteria andAccountDutyIsNull() {
            addCriterion("account_duty is null");
            return (Criteria) this;
        }

        public Criteria andAccountDutyIsNotNull() {
            addCriterion("account_duty is not null");
            return (Criteria) this;
        }

        public Criteria andAccountDutyEqualTo(String value) {
            addCriterion("account_duty =", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyNotEqualTo(String value) {
            addCriterion("account_duty <>", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyNotEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyGreaterThan(String value) {
            addCriterion("account_duty >", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyGreaterThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyGreaterThanOrEqualTo(String value) {
            addCriterion("account_duty >=", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyGreaterThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyLessThan(String value) {
            addCriterion("account_duty <", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyLessThanColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyLessThanOrEqualTo(String value) {
            addCriterion("account_duty <=", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyLessThanOrEqualToColumn(ShopManagerInfo.Column column) {
            addCriterion(new StringBuilder("account_duty <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountDutyLike(String value) {
            addCriterion("account_duty like", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyNotLike(String value) {
            addCriterion("account_duty not like", value, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyIn(List<String> values) {
            addCriterion("account_duty in", values, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyNotIn(List<String> values) {
            addCriterion("account_duty not in", values, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyBetween(String value1, String value2) {
            addCriterion("account_duty between", value1, value2, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andAccountDutyNotBetween(String value1, String value2) {
            addCriterion("account_duty not between", value1, value2, "accountDuty");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLikeInsensitive(String value) {
            addCriterion("upper(create_oper_code) like", value.toUpperCase(), "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperPhoneLikeInsensitive(String value) {
            addCriterion("upper(create_oper_phone) like", value.toUpperCase(), "createOperPhone");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andCustomerManagerNameLikeInsensitive(String value) {
            addCriterion("upper(customer_manager_name) like", value.toUpperCase(), "customerManagerName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLikeInsensitive(String value) {
            addCriterion("upper(employee_num) like", value.toUpperCase(), "employeeNum");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationLikeInsensitive(String value) {
            addCriterion("upper(location) like", value.toUpperCase(), "location");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameLikeInsensitive(String value) {
            addCriterion("upper(region_name) like", value.toUpperCase(), "regionName");
            return (Criteria) this;
        }

        public Criteria andMrgStatusLikeInsensitive(String value) {
            addCriterion("upper(mrg_status) like", value.toUpperCase(), "mrgStatus");
            return (Criteria) this;
        }

        public Criteria andGriddingNameLikeInsensitive(String value) {
            addCriterion("upper(gridding_name) like", value.toUpperCase(), "griddingName");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLikeInsensitive(String value) {
            addCriterion("upper(channel_type) like", value.toUpperCase(), "channelType");
            return (Criteria) this;
        }

        public Criteria andManagerCategoryLikeInsensitive(String value) {
            addCriterion("upper(manager_category) like", value.toUpperCase(), "managerCategory");
            return (Criteria) this;
        }

        public Criteria andAccountDutyLikeInsensitive(String value) {
            addCriterion("upper(account_duty) like", value.toUpperCase(), "accountDuty");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 11 11:01:52 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        private ShopManagerInfoExample example;

        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        protected Criteria(ShopManagerInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        public ShopManagerInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Jun 11 11:01:52 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Jun 11 11:01:52 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.ShopManagerInfoExample example);
    }
}