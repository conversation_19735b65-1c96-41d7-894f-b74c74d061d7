package com.chinamobile.retail.pojo.vo.miniprogram;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * 小程序销售年度报告
 *
 */
@Data
public class SaleYearReportVO implements Serializable {
    /**
     * 主键
     *

     */
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 注册顺序（第多少位用户）
     *
     */
    private Long registerIndex;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 用户角色，0：普通用户  1：一级分销员  2：二级分销员 3:渠道商 4:客户经理
     */
    private String roleType;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 第一次订单时间
     */
    private Date firstOrderTime;

    /**
     * 本年度最后一次订单时间
     */
    private Date yearLastOrderTime;

    /**
     * 第一笔订单spu编码
     */
    private String firstOrderSpuCode;

    /**
     * 第一笔订单spu名称
     */
    private String firstOrderSpuName;

    /**
     * 年度销售订单金额
     */
    private Long orderTotalPrice;

    /**
     * 年度销售订单笔数
     *
     */
    private Long orderTotalCount;

    /**
     * 年度预付费订单数量
     */
    private Long orderTotalCountPrepay;

    /**
     * 年度后付费订单数量
     */
    private Long orderTotalCountAfterpay;

    /**
     * 年度融合营销订单数量
     */
    private Long orderTotalCountMix;

    /**
     * 年度获得推广积分
     */
    private Long orderTotalPoint;

    /**
     * 年度推广商品款数
     */
    private Long spuTotalCount;

    /**
     * 年度销售排名（超过X%）
     */
    private Long saleRanking;

    /**
     * 最擅长销售商品spu编码
     */
    private String bestSaleSpuCode;

    /**
     * 最擅长销售商品spu名称
     *
     */
    private String bestSaleSpuName;

    /**
     * 最擅长销售商品订单次数
     */
    private Long bestSaleSpuOrderCount;

    /**
     * 集团客户购买的最多产品编码
     */
    private String bestSaleSpuCodeGroup;

    /**
     * 集团客户购买的最多产品名称
     */
    private String bestSaleSpuNameGroup;

    /**
     * 集团客户购买的最多产品次数
     */
    private Long bestSaleSpuCountGroup;

    /**
     * 个人客户购买的最多产品编码
     */
    private String bestSaleSpuCodeIndividual;

    /**
     * 个人客户购买的最多产品名称
     */
    private String bestSaleSpuNameIndividual;

    /**
     * 个人客户购买的最多产品次数
     */
    private Long bestSaleSpuCountIndividual;

    /**
     * 最擅长销售商品提示语
     *
     */
    private String bestSaleSpuPrompt;

    /**
     * 最晚一笔订单成交时间
     *
     */
    private Date yearLatestOrderTime;

    /**
     * 参与过商城活动次数
     */
    private Integer joinMallActivityCount;

    /**
     * 活动中奖次数
     */
    private Integer joinMallActivityRewardCount;

    /**
     * 最好成绩的活动名称
     *
     */
    private String joinMallBestActivityName;

    /**
     * 最好成绩的活动排名
     *
     */
    private Long joinMallBestActivityRanking;

    /**
     * 年度发展客户总数
     */
    private Long customerCount;

    /**
     * 年度发展集团客户数
     */
    private Long customerCountGroup;

    /**
     * 年度发展个人客户数
     */
    private Long customerCountIndividual;

    /**
     * 发展客户总数
     */
    private Long customerTotalCount;

    /**
     * 是否有对应年度的报告
     */
    private Boolean hasReport;
}