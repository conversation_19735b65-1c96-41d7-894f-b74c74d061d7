package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序首页热门产品
 *
 * <AUTHOR>
public class MiniProgramHomeSpu implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    private String id;

    /**
     * 主页id
     *
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    private String homeId;

    /**
     * spu_编码
     *
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    private String spuCode;

    /**
     * 排序
     *
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    private Integer sort;

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_spu.id
     *
     * @return the value of supply_chain..mini_program_home_spu.id
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public MiniProgramHomeSpu withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_spu.id
     *
     * @param id the value for supply_chain..mini_program_home_spu.id
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_spu.home_id
     *
     * @return the value of supply_chain..mini_program_home_spu.home_id
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public String getHomeId() {
        return homeId;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public MiniProgramHomeSpu withHomeId(String homeId) {
        this.setHomeId(homeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_spu.home_id
     *
     * @param homeId the value for supply_chain..mini_program_home_spu.home_id
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public void setHomeId(String homeId) {
        this.homeId = homeId == null ? null : homeId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_spu.spu_code
     *
     * @return the value of supply_chain..mini_program_home_spu.spu_code
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public MiniProgramHomeSpu withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_spu.spu_code
     *
     * @param spuCode the value for supply_chain..mini_program_home_spu.spu_code
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode == null ? null : spuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_spu.sort
     *
     * @return the value of supply_chain..mini_program_home_spu.sort
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public Integer getSort() {
        return sort;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public MiniProgramHomeSpu withSort(Integer sort) {
        this.setSort(sort);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_spu.sort
     *
     * @param sort the value for supply_chain..mini_program_home_spu.sort
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", homeId=").append(homeId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", sort=").append(sort);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramHomeSpu other = (MiniProgramHomeSpu) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getHomeId() == null ? other.getHomeId() == null : this.getHomeId().equals(other.getHomeId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()));
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getHomeId() == null) ? 0 : getHomeId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        homeId("home_id", "homeId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        sort("sort", "sort", "INTEGER", false);

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Dec 30 17:08:16 GMT+08:00 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}