package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 销售端小程序用户表
 *
 * <AUTHOR>
public class UserMiniProgram implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String id;

    /**
     * 用户ID
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String userId;

    /**
     * 推荐码，分销员为分销员推荐码，客户经理为从账号，渠道商为客户编码
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String code;

    /**
     * 分销员为空，客户经理为省工号，渠道商为渠道商编码
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String number;

    /**
     * 姓名
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String name;

    /**
     * 手机号
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String phone;

    /**
     * 省份编码
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String beId;

    /**
     * 省份名称
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String provinceName;

    /**
     * 地市编码
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String location;

    /**
     * 地市名称
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String cityName;

    /**
     * 区县编码
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String regionId;

    /**
     * 区县名称
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String regionName;

    /**
     * 0：普通用户  1：一级分销员  2：二级分销员 3:渠道商 4:客户经理
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String roleType;

    /**
     * 0：不可用 1：可用
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String status;

    /**
     * 头像url地址
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String headerImgUrl;

    /**
     * 审批状态 1--未审批 2--审批通过  3--审批未通过
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Integer auditStatus;

    /**
     * 审批原因
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String auditReason;

    /**
     * 头像保存的key
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private String fileKey;

    /**
     * 头像审批后是否提示过 1--未提示  2--已提示
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Integer auditHeaderNotice;

    /**
     * 签约状态 0-未签约 1-已签约
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Integer signStatus;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Date updateTime;

    /**
     * 最近登录时间
     *
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private Date latestLoginTime;

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.id
     *
     * @return the value of supply_chain..user_mini_program.id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.id
     *
     * @param id the value for supply_chain..user_mini_program.id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.user_id
     *
     * @return the value of supply_chain..user_mini_program.user_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.user_id
     *
     * @param userId the value for supply_chain..user_mini_program.user_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.code
     *
     * @return the value of supply_chain..user_mini_program.code
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getCode() {
        return code;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withCode(String code) {
        this.setCode(code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.code
     *
     * @param code the value for supply_chain..user_mini_program.code
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.number
     *
     * @return the value of supply_chain..user_mini_program.number
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getNumber() {
        return number;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withNumber(String number) {
        this.setNumber(number);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.number
     *
     * @param number the value for supply_chain..user_mini_program.number
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setNumber(String number) {
        this.number = number;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.name
     *
     * @return the value of supply_chain..user_mini_program.name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.name
     *
     * @param name the value for supply_chain..user_mini_program.name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.phone
     *
     * @return the value of supply_chain..user_mini_program.phone
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.phone
     *
     * @param phone the value for supply_chain..user_mini_program.phone
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.be_id
     *
     * @return the value of supply_chain..user_mini_program.be_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.be_id
     *
     * @param beId the value for supply_chain..user_mini_program.be_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.province_name
     *
     * @return the value of supply_chain..user_mini_program.province_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.province_name
     *
     * @param provinceName the value for supply_chain..user_mini_program.province_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.location
     *
     * @return the value of supply_chain..user_mini_program.location
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.location
     *
     * @param location the value for supply_chain..user_mini_program.location
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.city_name
     *
     * @return the value of supply_chain..user_mini_program.city_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.city_name
     *
     * @param cityName the value for supply_chain..user_mini_program.city_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.region_id
     *
     * @return the value of supply_chain..user_mini_program.region_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.region_id
     *
     * @param regionId the value for supply_chain..user_mini_program.region_id
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.region_name
     *
     * @return the value of supply_chain..user_mini_program.region_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.region_name
     *
     * @param regionName the value for supply_chain..user_mini_program.region_name
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.role_type
     *
     * @return the value of supply_chain..user_mini_program.role_type
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getRoleType() {
        return roleType;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withRoleType(String roleType) {
        this.setRoleType(roleType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.role_type
     *
     * @param roleType the value for supply_chain..user_mini_program.role_type
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.status
     *
     * @return the value of supply_chain..user_mini_program.status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withStatus(String status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.status
     *
     * @param status the value for supply_chain..user_mini_program.status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.header_img_url
     *
     * @return the value of supply_chain..user_mini_program.header_img_url
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getHeaderImgUrl() {
        return headerImgUrl;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withHeaderImgUrl(String headerImgUrl) {
        this.setHeaderImgUrl(headerImgUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.header_img_url
     *
     * @param headerImgUrl the value for supply_chain..user_mini_program.header_img_url
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setHeaderImgUrl(String headerImgUrl) {
        this.headerImgUrl = headerImgUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.audit_status
     *
     * @return the value of supply_chain..user_mini_program.audit_status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.audit_status
     *
     * @param auditStatus the value for supply_chain..user_mini_program.audit_status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.audit_reason
     *
     * @return the value of supply_chain..user_mini_program.audit_reason
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getAuditReason() {
        return auditReason;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withAuditReason(String auditReason) {
        this.setAuditReason(auditReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.audit_reason
     *
     * @param auditReason the value for supply_chain..user_mini_program.audit_reason
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.file_key
     *
     * @return the value of supply_chain..user_mini_program.file_key
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public String getFileKey() {
        return fileKey;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.file_key
     *
     * @param fileKey the value for supply_chain..user_mini_program.file_key
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.audit_header_notice
     *
     * @return the value of supply_chain..user_mini_program.audit_header_notice
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Integer getAuditHeaderNotice() {
        return auditHeaderNotice;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withAuditHeaderNotice(Integer auditHeaderNotice) {
        this.setAuditHeaderNotice(auditHeaderNotice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.audit_header_notice
     *
     * @param auditHeaderNotice the value for supply_chain..user_mini_program.audit_header_notice
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setAuditHeaderNotice(Integer auditHeaderNotice) {
        this.auditHeaderNotice = auditHeaderNotice;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.sign_status
     *
     * @return the value of supply_chain..user_mini_program.sign_status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Integer getSignStatus() {
        return signStatus;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withSignStatus(Integer signStatus) {
        this.setSignStatus(signStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.sign_status
     *
     * @param signStatus the value for supply_chain..user_mini_program.sign_status
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setSignStatus(Integer signStatus) {
        this.signStatus = signStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.create_time
     *
     * @return the value of supply_chain..user_mini_program.create_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.create_time
     *
     * @param createTime the value for supply_chain..user_mini_program.create_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.update_time
     *
     * @return the value of supply_chain..user_mini_program.update_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.update_time
     *
     * @param updateTime the value for supply_chain..user_mini_program.update_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_mini_program.latest_login_time
     *
     * @return the value of supply_chain..user_mini_program.latest_login_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public Date getLatestLoginTime() {
        return latestLoginTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public UserMiniProgram withLatestLoginTime(Date latestLoginTime) {
        this.setLatestLoginTime(latestLoginTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_mini_program.latest_login_time
     *
     * @param latestLoginTime the value for supply_chain..user_mini_program.latest_login_time
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public void setLatestLoginTime(Date latestLoginTime) {
        this.latestLoginTime = latestLoginTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", code=").append(code);
        sb.append(", number=").append(number);
        sb.append(", name=").append(name);
        sb.append(", phone=").append(phone);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", location=").append(location);
        sb.append(", cityName=").append(cityName);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", roleType=").append(roleType);
        sb.append(", status=").append(status);
        sb.append(", headerImgUrl=").append(headerImgUrl);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", auditReason=").append(auditReason);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", auditHeaderNotice=").append(auditHeaderNotice);
        sb.append(", signStatus=").append(signStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", latestLoginTime=").append(latestLoginTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserMiniProgram other = (UserMiniProgram) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getRoleType() == null ? other.getRoleType() == null : this.getRoleType().equals(other.getRoleType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getHeaderImgUrl() == null ? other.getHeaderImgUrl() == null : this.getHeaderImgUrl().equals(other.getHeaderImgUrl()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getAuditReason() == null ? other.getAuditReason() == null : this.getAuditReason().equals(other.getAuditReason()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getAuditHeaderNotice() == null ? other.getAuditHeaderNotice() == null : this.getAuditHeaderNotice().equals(other.getAuditHeaderNotice()))
            && (this.getSignStatus() == null ? other.getSignStatus() == null : this.getSignStatus().equals(other.getSignStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getLatestLoginTime() == null ? other.getLatestLoginTime() == null : this.getLatestLoginTime().equals(other.getLatestLoginTime()));
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getRoleType() == null) ? 0 : getRoleType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getHeaderImgUrl() == null) ? 0 : getHeaderImgUrl().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getAuditReason() == null) ? 0 : getAuditReason().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getAuditHeaderNotice() == null) ? 0 : getAuditHeaderNotice().hashCode());
        result = prime * result + ((getSignStatus() == null) ? 0 : getSignStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getLatestLoginTime() == null) ? 0 : getLatestLoginTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:14:42 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        code("code", "code", "VARCHAR", false),
        number("number", "number", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        roleType("role_type", "roleType", "VARCHAR", false),
        status("status", "status", "VARCHAR", false),
        headerImgUrl("header_img_url", "headerImgUrl", "VARCHAR", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        auditReason("audit_reason", "auditReason", "VARCHAR", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        auditHeaderNotice("audit_header_notice", "auditHeaderNotice", "INTEGER", false),
        signStatus("sign_status", "signStatus", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        latestLoginTime("latest_login_time", "latestLoginTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:14:42 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}