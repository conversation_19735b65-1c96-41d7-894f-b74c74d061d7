package com.chinamobile.retail.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 积分供应商关联商品列表表项
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierProductDTO {
    /**主键*/
    private String id;

    /**skuId*/
    private String skuId;

    /**产品名称spu*/
    private String spuName;

    /**规格名称sku*/
    private String skuName;

    /**实际名称*/
    private String realName;

    /**编码*/
    private String skuCode;

    /**销售价*/
    private Long price;

    /**是否生效，生效的不可删除*/
    private Boolean valid;

    private List<ProductRegionDTO> regions;
}
