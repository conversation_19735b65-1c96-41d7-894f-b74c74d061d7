package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EstewardReservationExample {
    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public EstewardReservationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public EstewardReservationExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public EstewardReservationExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        EstewardReservationExample example = new EstewardReservationExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public EstewardReservationExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public EstewardReservationExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdIsNull() {
            addCriterion("reservation_order_id is null");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdIsNotNull() {
            addCriterion("reservation_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdEqualTo(String value) {
            addCriterion("reservation_order_id =", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdNotEqualTo(String value) {
            addCriterion("reservation_order_id <>", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdGreaterThan(String value) {
            addCriterion("reservation_order_id >", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("reservation_order_id >=", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLessThan(String value) {
            addCriterion("reservation_order_id <", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLessThanOrEqualTo(String value) {
            addCriterion("reservation_order_id <=", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLike(String value) {
            addCriterion("reservation_order_id like", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdNotLike(String value) {
            addCriterion("reservation_order_id not like", value, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdIn(List<String> values) {
            addCriterion("reservation_order_id in", values, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdNotIn(List<String> values) {
            addCriterion("reservation_order_id not in", values, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdBetween(String value1, String value2) {
            addCriterion("reservation_order_id between", value1, value2, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdNotBetween(String value1, String value2) {
            addCriterion("reservation_order_id not between", value1, value2, "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductCountIsNull() {
            addCriterion("product_count is null");
            return (Criteria) this;
        }

        public Criteria andProductCountIsNotNull() {
            addCriterion("product_count is not null");
            return (Criteria) this;
        }

        public Criteria andProductCountEqualTo(Integer value) {
            addCriterion("product_count =", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountNotEqualTo(Integer value) {
            addCriterion("product_count <>", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountGreaterThan(Integer value) {
            addCriterion("product_count >", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_count >=", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountLessThan(Integer value) {
            addCriterion("product_count <", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountLessThanOrEqualTo(Integer value) {
            addCriterion("product_count <=", value, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("product_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCountIn(List<Integer> values) {
            addCriterion("product_count in", values, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountNotIn(List<Integer> values) {
            addCriterion("product_count not in", values, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountBetween(Integer value1, Integer value2) {
            addCriterion("product_count between", value1, value2, "productCount");
            return (Criteria) this;
        }

        public Criteria andProductCountNotBetween(Integer value1, Integer value2) {
            addCriterion("product_count not between", value1, value2, "productCount");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIsNull() {
            addCriterion("user_phone is null");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIsNotNull() {
            addCriterion("user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andUserPhoneEqualTo(String value) {
            addCriterion("user_phone =", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotEqualTo(String value) {
            addCriterion("user_phone <>", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThan(String value) {
            addCriterion("user_phone >", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("user_phone >=", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThan(String value) {
            addCriterion("user_phone <", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("user_phone <=", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserPhoneLike(String value) {
            addCriterion("user_phone like", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotLike(String value) {
            addCriterion("user_phone not like", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIn(List<String> values) {
            addCriterion("user_phone in", values, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotIn(List<String> values) {
            addCriterion("user_phone not in", values, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneBetween(String value1, String value2) {
            addCriterion("user_phone between", value1, value2, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotBetween(String value1, String value2) {
            addCriterion("user_phone not between", value1, value2, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserAddrIsNull() {
            addCriterion("user_addr is null");
            return (Criteria) this;
        }

        public Criteria andUserAddrIsNotNull() {
            addCriterion("user_addr is not null");
            return (Criteria) this;
        }

        public Criteria andUserAddrEqualTo(String value) {
            addCriterion("user_addr =", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrNotEqualTo(String value) {
            addCriterion("user_addr <>", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrGreaterThan(String value) {
            addCriterion("user_addr >", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrGreaterThanOrEqualTo(String value) {
            addCriterion("user_addr >=", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrLessThan(String value) {
            addCriterion("user_addr <", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrLessThanOrEqualTo(String value) {
            addCriterion("user_addr <=", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrLike(String value) {
            addCriterion("user_addr like", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrNotLike(String value) {
            addCriterion("user_addr not like", value, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrIn(List<String> values) {
            addCriterion("user_addr in", values, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrNotIn(List<String> values) {
            addCriterion("user_addr not in", values, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrBetween(String value1, String value2) {
            addCriterion("user_addr between", value1, value2, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrNotBetween(String value1, String value2) {
            addCriterion("user_addr not between", value1, value2, "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailIsNull() {
            addCriterion("user_addr_detail is null");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailIsNotNull() {
            addCriterion("user_addr_detail is not null");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailEqualTo(String value) {
            addCriterion("user_addr_detail =", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailNotEqualTo(String value) {
            addCriterion("user_addr_detail <>", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailGreaterThan(String value) {
            addCriterion("user_addr_detail >", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailGreaterThanOrEqualTo(String value) {
            addCriterion("user_addr_detail >=", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLessThan(String value) {
            addCriterion("user_addr_detail <", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLessThanOrEqualTo(String value) {
            addCriterion("user_addr_detail <=", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("user_addr_detail <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLike(String value) {
            addCriterion("user_addr_detail like", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailNotLike(String value) {
            addCriterion("user_addr_detail not like", value, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailIn(List<String> values) {
            addCriterion("user_addr_detail in", values, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailNotIn(List<String> values) {
            addCriterion("user_addr_detail not in", values, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailBetween(String value1, String value2) {
            addCriterion("user_addr_detail between", value1, value2, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailNotBetween(String value1, String value2) {
            addCriterion("user_addr_detail not between", value1, value2, "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIsNull() {
            addCriterion("is_hidden is null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIsNotNull() {
            addCriterion("is_hidden is not null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenEqualTo(String value) {
            addCriterion("is_hidden =", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotEqualTo(String value) {
            addCriterion("is_hidden <>", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThan(String value) {
            addCriterion("is_hidden >", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThanOrEqualTo(String value) {
            addCriterion("is_hidden >=", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThan(String value) {
            addCriterion("is_hidden <", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThanOrEqualTo(String value) {
            addCriterion("is_hidden <=", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("is_hidden <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenLike(String value) {
            addCriterion("is_hidden like", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotLike(String value) {
            addCriterion("is_hidden not like", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIn(List<String> values) {
            addCriterion("is_hidden in", values, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotIn(List<String> values) {
            addCriterion("is_hidden not in", values, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenBetween(String value1, String value2) {
            addCriterion("is_hidden between", value1, value2, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotBetween(String value1, String value2) {
            addCriterion("is_hidden not between", value1, value2, "isHidden");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeIsNull() {
            addCriterion("reservation_time is null");
            return (Criteria) this;
        }

        public Criteria andReservationTimeIsNotNull() {
            addCriterion("reservation_time is not null");
            return (Criteria) this;
        }

        public Criteria andReservationTimeEqualTo(Date value) {
            addCriterion("reservation_time =", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeNotEqualTo(Date value) {
            addCriterion("reservation_time <>", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeGreaterThan(Date value) {
            addCriterion("reservation_time >", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("reservation_time >=", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeLessThan(Date value) {
            addCriterion("reservation_time <", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeLessThanOrEqualTo(Date value) {
            addCriterion("reservation_time <=", value, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationTimeIn(List<Date> values) {
            addCriterion("reservation_time in", values, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeNotIn(List<Date> values) {
            addCriterion("reservation_time not in", values, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeBetween(Date value1, Date value2) {
            addCriterion("reservation_time between", value1, value2, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationTimeNotBetween(Date value1, Date value2) {
            addCriterion("reservation_time not between", value1, value2, "reservationTime");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdIsNull() {
            addCriterion("reservation_wf_id is null");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdIsNotNull() {
            addCriterion("reservation_wf_id is not null");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdEqualTo(String value) {
            addCriterion("reservation_wf_id =", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdNotEqualTo(String value) {
            addCriterion("reservation_wf_id <>", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdNotEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdGreaterThan(String value) {
            addCriterion("reservation_wf_id >", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdGreaterThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdGreaterThanOrEqualTo(String value) {
            addCriterion("reservation_wf_id >=", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdGreaterThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLessThan(String value) {
            addCriterion("reservation_wf_id <", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLessThanColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLessThanOrEqualTo(String value) {
            addCriterion("reservation_wf_id <=", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLessThanOrEqualToColumn(EstewardReservation.Column column) {
            addCriterion(new StringBuilder("reservation_wf_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLike(String value) {
            addCriterion("reservation_wf_id like", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdNotLike(String value) {
            addCriterion("reservation_wf_id not like", value, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdIn(List<String> values) {
            addCriterion("reservation_wf_id in", values, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdNotIn(List<String> values) {
            addCriterion("reservation_wf_id not in", values, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdBetween(String value1, String value2) {
            addCriterion("reservation_wf_id between", value1, value2, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdNotBetween(String value1, String value2) {
            addCriterion("reservation_wf_id not between", value1, value2, "reservationWfId");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andReservationOrderIdLikeInsensitive(String value) {
            addCriterion("upper(reservation_order_id) like", value.toUpperCase(), "reservationOrderId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andProductIdLikeInsensitive(String value) {
            addCriterion("upper(product_id) like", value.toUpperCase(), "productId");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameLikeInsensitive(String value) {
            addCriterion("upper(user_name) like", value.toUpperCase(), "userName");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(user_phone) like", value.toUpperCase(), "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserAddrLikeInsensitive(String value) {
            addCriterion("upper(user_addr) like", value.toUpperCase(), "userAddr");
            return (Criteria) this;
        }

        public Criteria andUserAddrDetailLikeInsensitive(String value) {
            addCriterion("upper(user_addr_detail) like", value.toUpperCase(), "userAddrDetail");
            return (Criteria) this;
        }

        public Criteria andIsHiddenLikeInsensitive(String value) {
            addCriterion("upper(is_hidden) like", value.toUpperCase(), "isHidden");
            return (Criteria) this;
        }

        public Criteria andReservationWfIdLikeInsensitive(String value) {
            addCriterion("upper(reservation_wf_id) like", value.toUpperCase(), "reservationWfId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Apr 24 09:35:08 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        private EstewardReservationExample example;

        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        protected Criteria(EstewardReservationExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        public EstewardReservationExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Apr 24 09:35:08 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Apr 24 09:35:08 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Apr 24 09:35:08 CST 2025
         */
        void example(com.chinamobile.retail.pojo.entity.EstewardReservationExample example);
    }
}