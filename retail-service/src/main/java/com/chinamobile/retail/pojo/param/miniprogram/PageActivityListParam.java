package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 16:47
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageActivityListParam extends BasePageQuery {

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-审核不通过，4-审核完成
     */
    private Integer auditStatus;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 活动省份
     */
    private List<RegionParam> regions;

    /**
     * 活动状态 0-待发布，1-待开始，2-进行中，3-结算中，4-已结束
     */
    private Integer status;

    /**
     * 启用/停用状态 true-启用，false-停用
     */
    private Boolean active;

    /**
     * 是否显示草稿，默认显示
     */
    private Boolean draft;
}
