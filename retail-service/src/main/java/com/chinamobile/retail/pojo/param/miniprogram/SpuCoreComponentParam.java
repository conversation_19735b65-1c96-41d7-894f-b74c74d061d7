package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售端小程序主页信息
 */
@Data
public class SpuCoreComponentParam implements Serializable {
    /**
     * 主键
     * */
    private String id;

    @NotNull(message = "操作类型不能为空")
    private Integer oprType;

    /**
     * spu编码
     *
     */
    private String spuCode;

    /**
     * spu编码
     *
     */
    private String spuName;

    /**
     * 核心部件名称
     */
    private String coreComponentName;

    /**
     * 核心部件头图
     */
    private String coreComponentImg;

    /**SKU核心部件配置*/
    private List<SkuCoreComponentParam> skuList;



}