package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class MiniProgramActivityRankExample {
    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public MiniProgramActivityRankExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public MiniProgramActivityRankExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public MiniProgramActivityRankExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramActivityRankExample example = new MiniProgramActivityRankExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public MiniProgramActivityRankExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public MiniProgramActivityRankExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(String value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(String value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(String value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(String value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(String value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(String value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("activity_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLike(String value) {
            addCriterion("activity_id like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotLike(String value) {
            addCriterion("activity_id not like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<String> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<String> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(String value1, String value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(String value1, String value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andDetailImgIsNull() {
            addCriterion("detail_img is null");
            return (Criteria) this;
        }

        public Criteria andDetailImgIsNotNull() {
            addCriterion("detail_img is not null");
            return (Criteria) this;
        }

        public Criteria andDetailImgEqualTo(String value) {
            addCriterion("detail_img =", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgNotEqualTo(String value) {
            addCriterion("detail_img <>", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgGreaterThan(String value) {
            addCriterion("detail_img >", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgGreaterThanOrEqualTo(String value) {
            addCriterion("detail_img >=", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgLessThan(String value) {
            addCriterion("detail_img <", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgLessThanOrEqualTo(String value) {
            addCriterion("detail_img <=", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("detail_img <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDetailImgLike(String value) {
            addCriterion("detail_img like", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgNotLike(String value) {
            addCriterion("detail_img not like", value, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgIn(List<String> values) {
            addCriterion("detail_img in", values, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgNotIn(List<String> values) {
            addCriterion("detail_img not in", values, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgBetween(String value1, String value2) {
            addCriterion("detail_img between", value1, value2, "detailImg");
            return (Criteria) this;
        }

        public Criteria andDetailImgNotBetween(String value1, String value2) {
            addCriterion("detail_img not between", value1, value2, "detailImg");
            return (Criteria) this;
        }

        public Criteria andRuleIsNull() {
            addCriterion("rule is null");
            return (Criteria) this;
        }

        public Criteria andRuleIsNotNull() {
            addCriterion("rule is not null");
            return (Criteria) this;
        }

        public Criteria andRuleEqualTo(String value) {
            addCriterion("rule =", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualTo(String value) {
            addCriterion("rule <>", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThan(String value) {
            addCriterion("rule >", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualTo(String value) {
            addCriterion("rule >=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLessThan(String value) {
            addCriterion("rule <", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualTo(String value) {
            addCriterion("rule <=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("rule <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLike(String value) {
            addCriterion("rule like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotLike(String value) {
            addCriterion("rule not like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleIn(List<String> values) {
            addCriterion("rule in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotIn(List<String> values) {
            addCriterion("rule not in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleBetween(String value1, String value2) {
            addCriterion("rule between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotBetween(String value1, String value2) {
            addCriterion("rule not between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("description <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andParticipationIsNull() {
            addCriterion("participation is null");
            return (Criteria) this;
        }

        public Criteria andParticipationIsNotNull() {
            addCriterion("participation is not null");
            return (Criteria) this;
        }

        public Criteria andParticipationEqualTo(String value) {
            addCriterion("participation =", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationNotEqualTo(String value) {
            addCriterion("participation <>", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationGreaterThan(String value) {
            addCriterion("participation >", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationGreaterThanOrEqualTo(String value) {
            addCriterion("participation >=", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationLessThan(String value) {
            addCriterion("participation <", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationLessThanOrEqualTo(String value) {
            addCriterion("participation <=", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("participation <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andParticipationLike(String value) {
            addCriterion("participation like", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationNotLike(String value) {
            addCriterion("participation not like", value, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationIn(List<String> values) {
            addCriterion("participation in", values, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationNotIn(List<String> values) {
            addCriterion("participation not in", values, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationBetween(String value1, String value2) {
            addCriterion("participation between", value1, value2, "participation");
            return (Criteria) this;
        }

        public Criteria andParticipationNotBetween(String value1, String value2) {
            addCriterion("participation not between", value1, value2, "participation");
            return (Criteria) this;
        }

        public Criteria andAwardIsNull() {
            addCriterion("award is null");
            return (Criteria) this;
        }

        public Criteria andAwardIsNotNull() {
            addCriterion("award is not null");
            return (Criteria) this;
        }

        public Criteria andAwardEqualTo(String value) {
            addCriterion("award =", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardNotEqualTo(String value) {
            addCriterion("award <>", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardGreaterThan(String value) {
            addCriterion("award >", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardGreaterThanOrEqualTo(String value) {
            addCriterion("award >=", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardLessThan(String value) {
            addCriterion("award <", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardLessThanOrEqualTo(String value) {
            addCriterion("award <=", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("award <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAwardLike(String value) {
            addCriterion("award like", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardNotLike(String value) {
            addCriterion("award not like", value, "award");
            return (Criteria) this;
        }

        public Criteria andAwardIn(List<String> values) {
            addCriterion("award in", values, "award");
            return (Criteria) this;
        }

        public Criteria andAwardNotIn(List<String> values) {
            addCriterion("award not in", values, "award");
            return (Criteria) this;
        }

        public Criteria andAwardBetween(String value1, String value2) {
            addCriterion("award between", value1, value2, "award");
            return (Criteria) this;
        }

        public Criteria andAwardNotBetween(String value1, String value2) {
            addCriterion("award not between", value1, value2, "award");
            return (Criteria) this;
        }

        public Criteria andAdditionalIsNull() {
            addCriterion("additional is null");
            return (Criteria) this;
        }

        public Criteria andAdditionalIsNotNull() {
            addCriterion("additional is not null");
            return (Criteria) this;
        }

        public Criteria andAdditionalEqualTo(String value) {
            addCriterion("additional =", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalNotEqualTo(String value) {
            addCriterion("additional <>", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalGreaterThan(String value) {
            addCriterion("additional >", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalGreaterThanOrEqualTo(String value) {
            addCriterion("additional >=", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalLessThan(String value) {
            addCriterion("additional <", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalLessThanOrEqualTo(String value) {
            addCriterion("additional <=", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("additional <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdditionalLike(String value) {
            addCriterion("additional like", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalNotLike(String value) {
            addCriterion("additional not like", value, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalIn(List<String> values) {
            addCriterion("additional in", values, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalNotIn(List<String> values) {
            addCriterion("additional not in", values, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalBetween(String value1, String value2) {
            addCriterion("additional between", value1, value2, "additional");
            return (Criteria) this;
        }

        public Criteria andAdditionalNotBetween(String value1, String value2) {
            addCriterion("additional not between", value1, value2, "additional");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultIsNull() {
            addCriterion("join_default is null");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultIsNotNull() {
            addCriterion("join_default is not null");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultEqualTo(Boolean value) {
            addCriterion("join_default =", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultNotEqualTo(Boolean value) {
            addCriterion("join_default <>", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultGreaterThan(Boolean value) {
            addCriterion("join_default >", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("join_default >=", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultLessThan(Boolean value) {
            addCriterion("join_default <", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultLessThanOrEqualTo(Boolean value) {
            addCriterion("join_default <=", value, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("join_default <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinDefaultIn(List<Boolean> values) {
            addCriterion("join_default in", values, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultNotIn(List<Boolean> values) {
            addCriterion("join_default not in", values, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultBetween(Boolean value1, Boolean value2) {
            addCriterion("join_default between", value1, value2, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andJoinDefaultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("join_default not between", value1, value2, "joinDefault");
            return (Criteria) this;
        }

        public Criteria andSortTypeIsNull() {
            addCriterion("sort_type is null");
            return (Criteria) this;
        }

        public Criteria andSortTypeIsNotNull() {
            addCriterion("sort_type is not null");
            return (Criteria) this;
        }

        public Criteria andSortTypeEqualTo(Integer value) {
            addCriterion("sort_type =", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeNotEqualTo(Integer value) {
            addCriterion("sort_type <>", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeNotEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeGreaterThan(Integer value) {
            addCriterion("sort_type >", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeGreaterThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort_type >=", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeGreaterThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeLessThan(Integer value) {
            addCriterion("sort_type <", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeLessThanColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sort_type <=", value, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeLessThanOrEqualToColumn(MiniProgramActivityRank.Column column) {
            addCriterion(new StringBuilder("sort_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSortTypeIn(List<Integer> values) {
            addCriterion("sort_type in", values, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeNotIn(List<Integer> values) {
            addCriterion("sort_type not in", values, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeBetween(Integer value1, Integer value2) {
            addCriterion("sort_type between", value1, value2, "sortType");
            return (Criteria) this;
        }

        public Criteria andSortTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sort_type not between", value1, value2, "sortType");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdLikeInsensitive(String value) {
            addCriterion("upper(activity_id) like", value.toUpperCase(), "activityId");
            return (Criteria) this;
        }

        public Criteria andDetailImgLikeInsensitive(String value) {
            addCriterion("upper(detail_img) like", value.toUpperCase(), "detailImg");
            return (Criteria) this;
        }

        public Criteria andRuleLikeInsensitive(String value) {
            addCriterion("upper(rule) like", value.toUpperCase(), "rule");
            return (Criteria) this;
        }

        public Criteria andDescriptionLikeInsensitive(String value) {
            addCriterion("upper(description) like", value.toUpperCase(), "description");
            return (Criteria) this;
        }

        public Criteria andParticipationLikeInsensitive(String value) {
            addCriterion("upper(participation) like", value.toUpperCase(), "participation");
            return (Criteria) this;
        }

        public Criteria andAwardLikeInsensitive(String value) {
            addCriterion("upper(award) like", value.toUpperCase(), "award");
            return (Criteria) this;
        }

        public Criteria andAdditionalLikeInsensitive(String value) {
            addCriterion("upper(additional) like", value.toUpperCase(), "additional");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Dec 03 09:19:36 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        private MiniProgramActivityRankExample example;

        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        protected Criteria(MiniProgramActivityRankExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        public MiniProgramActivityRankExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Dec 03 09:19:36 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Dec 03 09:19:36 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Dec 03 09:19:36 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramActivityRankExample example);
    }
}