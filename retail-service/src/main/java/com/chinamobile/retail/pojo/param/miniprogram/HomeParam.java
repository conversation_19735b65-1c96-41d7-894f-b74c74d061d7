package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeAd;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeBanner;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 销售端小程序资讯信息
 */
@Data
public class HomeParam implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 省编码
     *
     */
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    /**banner列表*/
    @NotEmpty(message = "banner列表不能为空")
    private List<MiniProgramHomeBanner> banners;

    /**广告列表*/
    @NotEmpty(message = "广告列表不能为空")
    private List<MiniProgramHomeAd> ads;

    /**热门产品列表*/
    @NotEmpty(message = "热门产品列表不能为空")
    private List<String> spuCodes;

    /**热门视频列表*/
    @NotEmpty(message = "热门视频列表不能为空")
    private List<PageInfoVO> videos;

    /**热门资讯列表*/
    @NotEmpty(message = "热门资讯列表不能为空")
    private List<PageInfoVO> infos;


}