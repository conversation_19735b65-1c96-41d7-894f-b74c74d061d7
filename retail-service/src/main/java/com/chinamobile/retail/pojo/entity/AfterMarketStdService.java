package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 售后服务包--标准服务 对应配置关系
 *
 * <AUTHOR>
public class AfterMarketStdService implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private String id;

    /**
     * 售后服务编码
     *
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private String afterMarketCode;

    /**
     * 标准服务id
     *
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private String stdServiceId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..after_market_std_service.id
     *
     * @return the value of supply_chain..after_market_std_service.id
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdService withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_std_service.id
     *
     * @param id the value for supply_chain..after_market_std_service.id
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_std_service.after_market_code
     *
     * @return the value of supply_chain..after_market_std_service.after_market_code
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public String getAfterMarketCode() {
        return afterMarketCode;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdService withAfterMarketCode(String afterMarketCode) {
        this.setAfterMarketCode(afterMarketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_std_service.after_market_code
     *
     * @param afterMarketCode the value for supply_chain..after_market_std_service.after_market_code
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setAfterMarketCode(String afterMarketCode) {
        this.afterMarketCode = afterMarketCode;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_std_service.std_service_id
     *
     * @return the value of supply_chain..after_market_std_service.std_service_id
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public String getStdServiceId() {
        return stdServiceId;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdService withStdServiceId(String stdServiceId) {
        this.setStdServiceId(stdServiceId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_std_service.std_service_id
     *
     * @param stdServiceId the value for supply_chain..after_market_std_service.std_service_id
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setStdServiceId(String stdServiceId) {
        this.stdServiceId = stdServiceId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_std_service.create_time
     *
     * @return the value of supply_chain..after_market_std_service.create_time
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdService withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_std_service.create_time
     *
     * @param createTime the value for supply_chain..after_market_std_service.create_time
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_std_service.update_time
     *
     * @return the value of supply_chain..after_market_std_service.update_time
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdService withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_std_service.update_time
     *
     * @param updateTime the value for supply_chain..after_market_std_service.update_time
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", afterMarketCode=").append(afterMarketCode);
        sb.append(", stdServiceId=").append(stdServiceId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AfterMarketStdService other = (AfterMarketStdService) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAfterMarketCode() == null ? other.getAfterMarketCode() == null : this.getAfterMarketCode().equals(other.getAfterMarketCode()))
            && (this.getStdServiceId() == null ? other.getStdServiceId() == null : this.getStdServiceId().equals(other.getStdServiceId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAfterMarketCode() == null) ? 0 : getAfterMarketCode().hashCode());
        result = prime * result + ((getStdServiceId() == null) ? 0 : getStdServiceId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        afterMarketCode("after_market_code", "afterMarketCode", "VARCHAR", false),
        stdServiceId("std_service_id", "stdServiceId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}