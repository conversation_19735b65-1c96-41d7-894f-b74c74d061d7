package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.dto.RequirementsQuestionAndAnswerDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 小程序场景需求
 *
 * <AUTHOR>
@Data
public class SceneRequirementVO implements Serializable {
    /**
     * 主键
     *
     */
    private String id;

    /**
     * 场景id
     */
    private String sceneId;

    /**
     * 场景id
     */
    private String sceneName;

    /**
     * 一级目录id
     *
     */
    private String firstDirectoryId;

    /**
     * 二级目录id
     */
    private String secondDirectoryId;

    /**
     * 需求描述
     *
     */
    private String description;

    /**
     * 附件url
     */
    private String attachmentFileUrl;

    /**
     * 省份名称
     *
     */
    private String provinceName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市名称
     *
     */
    private String cityName;

    /**
     * 城市编码
     *
     */
    private String cityCode;

    /**
     * 联系人
     *
     */
    private String contact;

    /**
     * 联系电话
     *
     */
    private String phone;

    /**
     * 创建人用户id
     *
     */
    private String createUid;

    /**
     * 创建人
     *
     */
    private String createUserName;

    /**
     * 商机经理用户id
     *
     */
    private String partnerBusinessId;

    /**
     * 商机经理名称
     *
     */
    private String partnerBusinessName;

    /**
     * 审核状态，0-待审核、1-已拒绝、2-已派发
     *
     */
    private Integer auditState;

    /**
     * 是否已删除，0-否，1-是
     *
     */
    private Boolean deleted;

    /**
     * 创建时间

     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private List<RequirementsQuestionAndAnswerDTO> answers;
}