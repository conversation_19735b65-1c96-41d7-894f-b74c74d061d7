package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 10:43
 * @description TODO
 */
@Data
public class ActivityWeeklyFunAwardCountParam {

    /**
     * 中奖记录id
     */
    private List<String> awardIds;

    /**开始时间*/
    private Date begin;

    /**结束时间*/
    private Date end;
}
