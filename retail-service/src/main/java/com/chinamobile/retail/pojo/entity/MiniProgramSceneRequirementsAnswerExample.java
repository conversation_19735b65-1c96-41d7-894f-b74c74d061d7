package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class MiniProgramSceneRequirementsAnswerExample {
    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswerExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswerExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramSceneRequirementsAnswerExample example = new MiniProgramSceneRequirementsAnswerExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswerExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswerExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRequirementIdIsNull() {
            addCriterion("requirement_id is null");
            return (Criteria) this;
        }

        public Criteria andRequirementIdIsNotNull() {
            addCriterion("requirement_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementIdEqualTo(String value) {
            addCriterion("requirement_id =", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdNotEqualTo(String value) {
            addCriterion("requirement_id <>", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdNotEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdGreaterThan(String value) {
            addCriterion("requirement_id >", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdGreaterThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_id >=", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdLessThan(String value) {
            addCriterion("requirement_id <", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdLessThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdLessThanOrEqualTo(String value) {
            addCriterion("requirement_id <=", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdLessThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("requirement_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequirementIdLike(String value) {
            addCriterion("requirement_id like", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdNotLike(String value) {
            addCriterion("requirement_id not like", value, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdIn(List<String> values) {
            addCriterion("requirement_id in", values, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdNotIn(List<String> values) {
            addCriterion("requirement_id not in", values, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdBetween(String value1, String value2) {
            addCriterion("requirement_id between", value1, value2, "requirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementIdNotBetween(String value1, String value2) {
            addCriterion("requirement_id not between", value1, value2, "requirementId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdIsNull() {
            addCriterion("question_id is null");
            return (Criteria) this;
        }

        public Criteria andQuestionIdIsNotNull() {
            addCriterion("question_id is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionIdEqualTo(String value) {
            addCriterion("question_id =", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdNotEqualTo(String value) {
            addCriterion("question_id <>", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdNotEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdGreaterThan(String value) {
            addCriterion("question_id >", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdGreaterThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdGreaterThanOrEqualTo(String value) {
            addCriterion("question_id >=", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdLessThan(String value) {
            addCriterion("question_id <", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdLessThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdLessThanOrEqualTo(String value) {
            addCriterion("question_id <=", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdLessThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("question_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionIdLike(String value) {
            addCriterion("question_id like", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdNotLike(String value) {
            addCriterion("question_id not like", value, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdIn(List<String> values) {
            addCriterion("question_id in", values, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdNotIn(List<String> values) {
            addCriterion("question_id not in", values, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdBetween(String value1, String value2) {
            addCriterion("question_id between", value1, value2, "questionId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdNotBetween(String value1, String value2) {
            addCriterion("question_id not between", value1, value2, "questionId");
            return (Criteria) this;
        }

        public Criteria andAnswerIsNull() {
            addCriterion("answer is null");
            return (Criteria) this;
        }

        public Criteria andAnswerIsNotNull() {
            addCriterion("answer is not null");
            return (Criteria) this;
        }

        public Criteria andAnswerEqualTo(String value) {
            addCriterion("answer =", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerNotEqualTo(String value) {
            addCriterion("answer <>", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerNotEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerGreaterThan(String value) {
            addCriterion("answer >", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerGreaterThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerGreaterThanOrEqualTo(String value) {
            addCriterion("answer >=", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerLessThan(String value) {
            addCriterion("answer <", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerLessThanColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerLessThanOrEqualTo(String value) {
            addCriterion("answer <=", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerLessThanOrEqualToColumn(MiniProgramSceneRequirementsAnswer.Column column) {
            addCriterion(new StringBuilder("answer <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAnswerLike(String value) {
            addCriterion("answer like", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerNotLike(String value) {
            addCriterion("answer not like", value, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerIn(List<String> values) {
            addCriterion("answer in", values, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerNotIn(List<String> values) {
            addCriterion("answer not in", values, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerBetween(String value1, String value2) {
            addCriterion("answer between", value1, value2, "answer");
            return (Criteria) this;
        }

        public Criteria andAnswerNotBetween(String value1, String value2) {
            addCriterion("answer not between", value1, value2, "answer");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andRequirementIdLikeInsensitive(String value) {
            addCriterion("upper(requirement_id) like", value.toUpperCase(), "requirementId");
            return (Criteria) this;
        }

        public Criteria andQuestionIdLikeInsensitive(String value) {
            addCriterion("upper(question_id) like", value.toUpperCase(), "questionId");
            return (Criteria) this;
        }

        public Criteria andAnswerLikeInsensitive(String value) {
            addCriterion("upper(answer) like", value.toUpperCase(), "answer");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Dec 16 10:38:41 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private MiniProgramSceneRequirementsAnswerExample example;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        protected Criteria(MiniProgramSceneRequirementsAnswerExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public MiniProgramSceneRequirementsAnswerExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Dec 16 10:38:41 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        void example(MiniProgramSceneRequirementsAnswerExample example);
    }
}