package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 10:43
 * @description TODO
 */
@Data
public class ActivityDataRankParam extends BasePageQuery {

    /**
     * 小程序活动id
     */
    @NotBlank(message = "活动id不能为空")
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 姓名
     */
    private String name;

}
