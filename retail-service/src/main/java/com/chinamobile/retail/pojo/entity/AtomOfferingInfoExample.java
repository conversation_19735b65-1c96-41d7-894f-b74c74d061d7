package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AtomOfferingInfoExample {
    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public AtomOfferingInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public AtomOfferingInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public AtomOfferingInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        AtomOfferingInfoExample example = new AtomOfferingInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public AtomOfferingInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public AtomOfferingInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("spu_id is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("spu_id is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(String value) {
            addCriterion("spu_id =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(String value) {
            addCriterion("spu_id <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(String value) {
            addCriterion("spu_id >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(String value) {
            addCriterion("spu_id >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(String value) {
            addCriterion("spu_id <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(String value) {
            addCriterion("spu_id <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLike(String value) {
            addCriterion("spu_id like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotLike(String value) {
            addCriterion("spu_id not like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<String> values) {
            addCriterion("spu_id in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<String> values) {
            addCriterion("spu_id not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(String value1, String value2) {
            addCriterion("spu_id between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(String value1, String value2) {
            addCriterion("spu_id not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(String value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(String value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(String value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(String value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(String value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLike(String value) {
            addCriterion("sku_id like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotLike(String value) {
            addCriterion("sku_id not like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<String> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<String> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(String value1, String value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(String value1, String value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNull() {
            addCriterion("offering_code is null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNotNull() {
            addCriterion("offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualTo(String value) {
            addCriterion("offering_code =", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualTo(String value) {
            addCriterion("offering_code <>", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThan(String value) {
            addCriterion("offering_code >", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_code >=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThan(String value) {
            addCriterion("offering_code <", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("offering_code <=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLike(String value) {
            addCriterion("offering_code like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotLike(String value) {
            addCriterion("offering_code not like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIn(List<String> values) {
            addCriterion("offering_code in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotIn(List<String> values) {
            addCriterion("offering_code not in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeBetween(String value1, String value2) {
            addCriterion("offering_code between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("offering_code not between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNull() {
            addCriterion("offering_name is null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNotNull() {
            addCriterion("offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualTo(String value) {
            addCriterion("offering_name =", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualTo(String value) {
            addCriterion("offering_name <>", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThan(String value) {
            addCriterion("offering_name >", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("offering_name >=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThan(String value) {
            addCriterion("offering_name <", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("offering_name <=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLike(String value) {
            addCriterion("offering_name like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotLike(String value) {
            addCriterion("offering_name not like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIn(List<String> values) {
            addCriterion("offering_name in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotIn(List<String> values) {
            addCriterion("offering_name not in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameBetween(String value1, String value2) {
            addCriterion("offering_name between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotBetween(String value1, String value2) {
            addCriterion("offering_name not between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIsNull() {
            addCriterion("offering_class is null");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIsNotNull() {
            addCriterion("offering_class is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingClassEqualTo(String value) {
            addCriterion("offering_class =", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotEqualTo(String value) {
            addCriterion("offering_class <>", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThan(String value) {
            addCriterion("offering_class >", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanOrEqualTo(String value) {
            addCriterion("offering_class >=", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThan(String value) {
            addCriterion("offering_class <", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanOrEqualTo(String value) {
            addCriterion("offering_class <=", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLike(String value) {
            addCriterion("offering_class like", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotLike(String value) {
            addCriterion("offering_class not like", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIn(List<String> values) {
            addCriterion("offering_class in", values, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotIn(List<String> values) {
            addCriterion("offering_class not in", values, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassBetween(String value1, String value2) {
            addCriterion("offering_class between", value1, value2, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotBetween(String value1, String value2) {
            addCriterion("offering_class not between", value1, value2, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Long value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Long value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Long value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Long value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Long value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Long> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Long> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Long value1, Long value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Long value1, Long value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIsNull() {
            addCriterion("ext_soft_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIsNotNull() {
            addCriterion("ext_soft_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeEqualTo(String value) {
            addCriterion("ext_soft_offering_code =", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotEqualTo(String value) {
            addCriterion("ext_soft_offering_code <>", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThan(String value) {
            addCriterion("ext_soft_offering_code >", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ext_soft_offering_code >=", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThan(String value) {
            addCriterion("ext_soft_offering_code <", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("ext_soft_offering_code <=", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLike(String value) {
            addCriterion("ext_soft_offering_code like", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotLike(String value) {
            addCriterion("ext_soft_offering_code not like", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIn(List<String> values) {
            addCriterion("ext_soft_offering_code in", values, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotIn(List<String> values) {
            addCriterion("ext_soft_offering_code not in", values, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeBetween(String value1, String value2) {
            addCriterion("ext_soft_offering_code between", value1, value2, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("ext_soft_offering_code not between", value1, value2, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIsNull() {
            addCriterion("ext_hard_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIsNotNull() {
            addCriterion("ext_hard_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeEqualTo(String value) {
            addCriterion("ext_hard_offering_code =", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotEqualTo(String value) {
            addCriterion("ext_hard_offering_code <>", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThan(String value) {
            addCriterion("ext_hard_offering_code >", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ext_hard_offering_code >=", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThan(String value) {
            addCriterion("ext_hard_offering_code <", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("ext_hard_offering_code <=", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLike(String value) {
            addCriterion("ext_hard_offering_code like", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotLike(String value) {
            addCriterion("ext_hard_offering_code not like", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIn(List<String> values) {
            addCriterion("ext_hard_offering_code in", values, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotIn(List<String> values) {
            addCriterion("ext_hard_offering_code not in", values, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeBetween(String value1, String value2) {
            addCriterion("ext_hard_offering_code between", value1, value2, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("ext_hard_offering_code not between", value1, value2, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNull() {
            addCriterion("settle_price is null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNotNull() {
            addCriterion("settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualTo(Long value) {
            addCriterion("settle_price =", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualTo(Long value) {
            addCriterion("settle_price <>", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThan(Long value) {
            addCriterion("settle_price >", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("settle_price >=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThan(Long value) {
            addCriterion("settle_price <", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("settle_price <=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceIn(List<Long> values) {
            addCriterion("settle_price in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotIn(List<Long> values) {
            addCriterion("settle_price not in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceBetween(Long value1, Long value2) {
            addCriterion("settle_price between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("settle_price not between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIsNull() {
            addCriterion("charge_code is null");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIsNotNull() {
            addCriterion("charge_code is not null");
            return (Criteria) this;
        }

        public Criteria andChargeCodeEqualTo(String value) {
            addCriterion("charge_code =", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotEqualTo(String value) {
            addCriterion("charge_code <>", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThan(String value) {
            addCriterion("charge_code >", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("charge_code >=", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThan(String value) {
            addCriterion("charge_code <", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanOrEqualTo(String value) {
            addCriterion("charge_code <=", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLike(String value) {
            addCriterion("charge_code like", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotLike(String value) {
            addCriterion("charge_code not like", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIn(List<String> values) {
            addCriterion("charge_code in", values, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotIn(List<String> values) {
            addCriterion("charge_code not in", values, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeBetween(String value1, String value2) {
            addCriterion("charge_code between", value1, value2, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotBetween(String value1, String value2) {
            addCriterion("charge_code not between", value1, value2, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeIdIsNull() {
            addCriterion("charge_id is null");
            return (Criteria) this;
        }

        public Criteria andChargeIdIsNotNull() {
            addCriterion("charge_id is not null");
            return (Criteria) this;
        }

        public Criteria andChargeIdEqualTo(String value) {
            addCriterion("charge_id =", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdNotEqualTo(String value) {
            addCriterion("charge_id <>", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThan(String value) {
            addCriterion("charge_id >", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanOrEqualTo(String value) {
            addCriterion("charge_id >=", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThan(String value) {
            addCriterion("charge_id <", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanOrEqualTo(String value) {
            addCriterion("charge_id <=", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("charge_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLike(String value) {
            addCriterion("charge_id like", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotLike(String value) {
            addCriterion("charge_id not like", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdIn(List<String> values) {
            addCriterion("charge_id in", values, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotIn(List<String> values) {
            addCriterion("charge_id not in", values, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdBetween(String value1, String value2) {
            addCriterion("charge_id between", value1, value2, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotBetween(String value1, String value2) {
            addCriterion("charge_id not between", value1, value2, "chargeId");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("color <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIsNull() {
            addCriterion("atom_sale_price is null");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIsNotNull() {
            addCriterion("atom_sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceEqualTo(Long value) {
            addCriterion("atom_sale_price =", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotEqualTo(Long value) {
            addCriterion("atom_sale_price <>", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThan(Long value) {
            addCriterion("atom_sale_price >", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_sale_price >=", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThan(Long value) {
            addCriterion("atom_sale_price <", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanOrEqualTo(Long value) {
            addCriterion("atom_sale_price <=", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_sale_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIn(List<Long> values) {
            addCriterion("atom_sale_price in", values, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotIn(List<Long> values) {
            addCriterion("atom_sale_price not in", values, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceBetween(Long value1, Long value2) {
            addCriterion("atom_sale_price between", value1, value2, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotBetween(Long value1, Long value2) {
            addCriterion("atom_sale_price not between", value1, value2, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNull() {
            addCriterion("inventory is null");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNotNull() {
            addCriterion("inventory is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualTo(Long value) {
            addCriterion("inventory =", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualTo(Long value) {
            addCriterion("inventory <>", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThan(Long value) {
            addCriterion("inventory >", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualTo(Long value) {
            addCriterion("inventory >=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThan(Long value) {
            addCriterion("inventory <", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualTo(Long value) {
            addCriterion("inventory <=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIn(List<Long> values) {
            addCriterion("inventory in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotIn(List<Long> values) {
            addCriterion("inventory not in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryBetween(Long value1, Long value2) {
            addCriterion("inventory between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotBetween(Long value1, Long value2) {
            addCriterion("inventory not between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIsNull() {
            addCriterion("reserve_inventory is null");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIsNotNull() {
            addCriterion("reserve_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryEqualTo(Long value) {
            addCriterion("reserve_inventory =", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotEqualTo(Long value) {
            addCriterion("reserve_inventory <>", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThan(Long value) {
            addCriterion("reserve_inventory >", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanOrEqualTo(Long value) {
            addCriterion("reserve_inventory >=", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThan(Long value) {
            addCriterion("reserve_inventory <", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanOrEqualTo(Long value) {
            addCriterion("reserve_inventory <=", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("reserve_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIn(List<Long> values) {
            addCriterion("reserve_inventory in", values, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotIn(List<Long> values) {
            addCriterion("reserve_inventory not in", values, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryBetween(Long value1, Long value2) {
            addCriterion("reserve_inventory between", value1, value2, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotBetween(Long value1, Long value2) {
            addCriterion("reserve_inventory not between", value1, value2, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryIsNull() {
            addCriterion("is_inventory is null");
            return (Criteria) this;
        }

        public Criteria andIsInventoryIsNotNull() {
            addCriterion("is_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andIsInventoryEqualTo(Boolean value) {
            addCriterion("is_inventory =", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotEqualTo(Boolean value) {
            addCriterion("is_inventory <>", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThan(Boolean value) {
            addCriterion("is_inventory >", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_inventory >=", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThan(Boolean value) {
            addCriterion("is_inventory <", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanOrEqualTo(Boolean value) {
            addCriterion("is_inventory <=", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryIn(List<Boolean> values) {
            addCriterion("is_inventory in", values, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotIn(List<Boolean> values) {
            addCriterion("is_inventory not in", values, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inventory between", value1, value2, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inventory not between", value1, value2, "isInventory");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNull() {
            addCriterion("inventory_threshold is null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNotNull() {
            addCriterion("inventory_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualTo(Long value) {
            addCriterion("inventory_threshold =", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualTo(Long value) {
            addCriterion("inventory_threshold <>", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThan(Long value) {
            addCriterion("inventory_threshold >", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualTo(Long value) {
            addCriterion("inventory_threshold >=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThan(Long value) {
            addCriterion("inventory_threshold <", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualTo(Long value) {
            addCriterion("inventory_threshold <=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIn(List<Long> values) {
            addCriterion("inventory_threshold in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotIn(List<Long> values) {
            addCriterion("inventory_threshold not in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdBetween(Long value1, Long value2) {
            addCriterion("inventory_threshold between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotBetween(Long value1, Long value2) {
            addCriterion("inventory_threshold not between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNull() {
            addCriterion("is_notice is null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNotNull() {
            addCriterion("is_notice is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualTo(Boolean value) {
            addCriterion("is_notice =", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualTo(Boolean value) {
            addCriterion("is_notice <>", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThan(Boolean value) {
            addCriterion("is_notice >", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_notice >=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThan(Boolean value) {
            addCriterion("is_notice <", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_notice <=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("is_notice <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeIn(List<Boolean> values) {
            addCriterion("is_notice in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotIn(List<Boolean> values) {
            addCriterion("is_notice not in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice not between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIsNull() {
            addCriterion("config_all_time is null");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIsNotNull() {
            addCriterion("config_all_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeEqualTo(Date value) {
            addCriterion("config_all_time =", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotEqualTo(Date value) {
            addCriterion("config_all_time <>", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThan(Date value) {
            addCriterion("config_all_time >", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("config_all_time >=", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThan(Date value) {
            addCriterion("config_all_time <", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanOrEqualTo(Date value) {
            addCriterion("config_all_time <=", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_all_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIn(List<Date> values) {
            addCriterion("config_all_time in", values, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotIn(List<Date> values) {
            addCriterion("config_all_time not in", values, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeBetween(Date value1, Date value2) {
            addCriterion("config_all_time between", value1, value2, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotBetween(Date value1, Date value2) {
            addCriterion("config_all_time not between", value1, value2, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeIsNull() {
            addCriterion("config_time is null");
            return (Criteria) this;
        }

        public Criteria andConfigTimeIsNotNull() {
            addCriterion("config_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfigTimeEqualTo(Date value) {
            addCriterion("config_time =", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotEqualTo(Date value) {
            addCriterion("config_time <>", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThan(Date value) {
            addCriterion("config_time >", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("config_time >=", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThan(Date value) {
            addCriterion("config_time <", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanOrEqualTo(Date value) {
            addCriterion("config_time <=", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("config_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeIn(List<Date> values) {
            addCriterion("config_time in", values, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotIn(List<Date> values) {
            addCriterion("config_time not in", values, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeBetween(Date value1, Date value2) {
            addCriterion("config_time between", value1, value2, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotBetween(Date value1, Date value2) {
            addCriterion("config_time not between", value1, value2, "configTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andNotifiedIsNull() {
            addCriterion("notified is null");
            return (Criteria) this;
        }

        public Criteria andNotifiedIsNotNull() {
            addCriterion("notified is not null");
            return (Criteria) this;
        }

        public Criteria andNotifiedEqualTo(Boolean value) {
            addCriterion("notified =", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedNotEqualTo(Boolean value) {
            addCriterion("notified <>", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThan(Boolean value) {
            addCriterion("notified >", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("notified >=", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThan(Boolean value) {
            addCriterion("notified <", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanOrEqualTo(Boolean value) {
            addCriterion("notified <=", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("notified <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedIn(List<Boolean> values) {
            addCriterion("notified in", values, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotIn(List<Boolean> values) {
            addCriterion("notified not in", values, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedBetween(Boolean value1, Boolean value2) {
            addCriterion("notified between", value1, value2, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("notified not between", value1, value2, "notified");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIsNull() {
            addCriterion("offeringSaleRegion is null");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIsNotNull() {
            addCriterion("offeringSaleRegion is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionEqualTo(String value) {
            addCriterion("offeringSaleRegion =", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotEqualTo(String value) {
            addCriterion("offeringSaleRegion <>", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThan(String value) {
            addCriterion("offeringSaleRegion >", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanOrEqualTo(String value) {
            addCriterion("offeringSaleRegion >=", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThan(String value) {
            addCriterion("offeringSaleRegion <", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanOrEqualTo(String value) {
            addCriterion("offeringSaleRegion <=", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLike(String value) {
            addCriterion("offeringSaleRegion like", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotLike(String value) {
            addCriterion("offeringSaleRegion not like", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIn(List<String> values) {
            addCriterion("offeringSaleRegion in", values, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotIn(List<String> values) {
            addCriterion("offeringSaleRegion not in", values, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionBetween(String value1, String value2) {
            addCriterion("offeringSaleRegion between", value1, value2, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotBetween(String value1, String value2) {
            addCriterion("offeringSaleRegion not between", value1, value2, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIsNull() {
            addCriterion("settlePricePartner is null");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIsNotNull() {
            addCriterion("settlePricePartner is not null");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerEqualTo(String value) {
            addCriterion("settlePricePartner =", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotEqualTo(String value) {
            addCriterion("settlePricePartner <>", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThan(String value) {
            addCriterion("settlePricePartner >", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanOrEqualTo(String value) {
            addCriterion("settlePricePartner >=", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThan(String value) {
            addCriterion("settlePricePartner <", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanOrEqualTo(String value) {
            addCriterion("settlePricePartner <=", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settlePricePartner <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLike(String value) {
            addCriterion("settlePricePartner like", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotLike(String value) {
            addCriterion("settlePricePartner not like", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIn(List<String> values) {
            addCriterion("settlePricePartner in", values, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotIn(List<String> values) {
            addCriterion("settlePricePartner not in", values, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerBetween(String value1, String value2) {
            addCriterion("settlePricePartner between", value1, value2, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotBetween(String value1, String value2) {
            addCriterion("settlePricePartner not between", value1, value2, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIsNull() {
            addCriterion("settleServiceName is null");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIsNotNull() {
            addCriterion("settleServiceName is not null");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameEqualTo(String value) {
            addCriterion("settleServiceName =", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotEqualTo(String value) {
            addCriterion("settleServiceName <>", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThan(String value) {
            addCriterion("settleServiceName >", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanOrEqualTo(String value) {
            addCriterion("settleServiceName >=", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThan(String value) {
            addCriterion("settleServiceName <", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanOrEqualTo(String value) {
            addCriterion("settleServiceName <=", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("settleServiceName <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLike(String value) {
            addCriterion("settleServiceName like", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotLike(String value) {
            addCriterion("settleServiceName not like", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIn(List<String> values) {
            addCriterion("settleServiceName in", values, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotIn(List<String> values) {
            addCriterion("settleServiceName not in", values, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameBetween(String value1, String value2) {
            addCriterion("settleServiceName between", value1, value2, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotBetween(String value1, String value2) {
            addCriterion("settleServiceName not between", value1, value2, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIsNull() {
            addCriterion("associated_offer is null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIsNotNull() {
            addCriterion("associated_offer is not null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferEqualTo(String value) {
            addCriterion("associated_offer =", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotEqualTo(String value) {
            addCriterion("associated_offer <>", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThan(String value) {
            addCriterion("associated_offer >", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanOrEqualTo(String value) {
            addCriterion("associated_offer >=", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThan(String value) {
            addCriterion("associated_offer <", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanOrEqualTo(String value) {
            addCriterion("associated_offer <=", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("associated_offer <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLike(String value) {
            addCriterion("associated_offer like", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotLike(String value) {
            addCriterion("associated_offer not like", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIn(List<String> values) {
            addCriterion("associated_offer in", values, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotIn(List<String> values) {
            addCriterion("associated_offer not in", values, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferBetween(String value1, String value2) {
            addCriterion("associated_offer between", value1, value2, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotBetween(String value1, String value2) {
            addCriterion("associated_offer not between", value1, value2, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIsNull() {
            addCriterion("validity_period is null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIsNotNull() {
            addCriterion("validity_period is not null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodEqualTo(String value) {
            addCriterion("validity_period =", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotEqualTo(String value) {
            addCriterion("validity_period <>", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThan(String value) {
            addCriterion("validity_period >", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("validity_period >=", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThan(String value) {
            addCriterion("validity_period <", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanOrEqualTo(String value) {
            addCriterion("validity_period <=", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("validity_period <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLike(String value) {
            addCriterion("validity_period like", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotLike(String value) {
            addCriterion("validity_period not like", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIn(List<String> values) {
            addCriterion("validity_period in", values, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotIn(List<String> values) {
            addCriterion("validity_period not in", values, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodBetween(String value1, String value2) {
            addCriterion("validity_period between", value1, value2, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotBetween(String value1, String value2) {
            addCriterion("validity_period not between", value1, value2, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxIsNull() {
            addCriterion("inventory_management_mode_kx is null");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxIsNotNull() {
            addCriterion("inventory_management_mode_kx is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxEqualTo(String value) {
            addCriterion("inventory_management_mode_kx =", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxNotEqualTo(String value) {
            addCriterion("inventory_management_mode_kx <>", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxGreaterThan(String value) {
            addCriterion("inventory_management_mode_kx >", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_management_mode_kx >=", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLessThan(String value) {
            addCriterion("inventory_management_mode_kx <", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLessThanOrEqualTo(String value) {
            addCriterion("inventory_management_mode_kx <=", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_management_mode_kx <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLike(String value) {
            addCriterion("inventory_management_mode_kx like", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxNotLike(String value) {
            addCriterion("inventory_management_mode_kx not like", value, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxIn(List<String> values) {
            addCriterion("inventory_management_mode_kx in", values, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxNotIn(List<String> values) {
            addCriterion("inventory_management_mode_kx not in", values, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxBetween(String value1, String value2) {
            addCriterion("inventory_management_mode_kx between", value1, value2, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxNotBetween(String value1, String value2) {
            addCriterion("inventory_management_mode_kx not between", value1, value2, "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryIdIsNull() {
            addCriterion("inventory_id is null");
            return (Criteria) this;
        }

        public Criteria andInventoryIdIsNotNull() {
            addCriterion("inventory_id is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryIdEqualTo(String value) {
            addCriterion("inventory_id =", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdNotEqualTo(String value) {
            addCriterion("inventory_id <>", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdGreaterThan(String value) {
            addCriterion("inventory_id >", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_id >=", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdLessThan(String value) {
            addCriterion("inventory_id <", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdLessThanOrEqualTo(String value) {
            addCriterion("inventory_id <=", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIdLike(String value) {
            addCriterion("inventory_id like", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdNotLike(String value) {
            addCriterion("inventory_id not like", value, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdIn(List<String> values) {
            addCriterion("inventory_id in", values, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdNotIn(List<String> values) {
            addCriterion("inventory_id not in", values, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdBetween(String value1, String value2) {
            addCriterion("inventory_id between", value1, value2, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andInventoryIdNotBetween(String value1, String value2) {
            addCriterion("inventory_id not between", value1, value2, "inventoryId");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIsNull() {
            addCriterion("get_order_way is null");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIsNotNull() {
            addCriterion("get_order_way is not null");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayEqualTo(Integer value) {
            addCriterion("get_order_way =", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotEqualTo(Integer value) {
            addCriterion("get_order_way <>", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThan(Integer value) {
            addCriterion("get_order_way >", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanOrEqualTo(Integer value) {
            addCriterion("get_order_way >=", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThan(Integer value) {
            addCriterion("get_order_way <", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanOrEqualTo(Integer value) {
            addCriterion("get_order_way <=", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("get_order_way <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIn(List<Integer> values) {
            addCriterion("get_order_way in", values, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotIn(List<Integer> values) {
            addCriterion("get_order_way not in", values, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayBetween(Integer value1, Integer value2) {
            addCriterion("get_order_way between", value1, value2, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotBetween(Integer value1, Integer value2) {
            addCriterion("get_order_way not between", value1, value2, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdIsNull() {
            addCriterion("inventory_main_id is null");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdIsNotNull() {
            addCriterion("inventory_main_id is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdEqualTo(String value) {
            addCriterion("inventory_main_id =", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdNotEqualTo(String value) {
            addCriterion("inventory_main_id <>", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdNotEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdGreaterThan(String value) {
            addCriterion("inventory_main_id >", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdGreaterThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_main_id >=", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdGreaterThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLessThan(String value) {
            addCriterion("inventory_main_id <", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLessThanColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLessThanOrEqualTo(String value) {
            addCriterion("inventory_main_id <=", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLessThanOrEqualToColumn(AtomOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_main_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLike(String value) {
            addCriterion("inventory_main_id like", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdNotLike(String value) {
            addCriterion("inventory_main_id not like", value, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdIn(List<String> values) {
            addCriterion("inventory_main_id in", values, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdNotIn(List<String> values) {
            addCriterion("inventory_main_id not in", values, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdBetween(String value1, String value2) {
            addCriterion("inventory_main_id between", value1, value2, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdNotBetween(String value1, String value2) {
            addCriterion("inventory_main_id not between", value1, value2, "inventoryMainId");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdLikeInsensitive(String value) {
            addCriterion("upper(spu_id) like", value.toUpperCase(), "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuIdLikeInsensitive(String value) {
            addCriterion("upper(sku_id) like", value.toUpperCase(), "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(offering_code) like", value.toUpperCase(), "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(offering_name) like", value.toUpperCase(), "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLikeInsensitive(String value) {
            addCriterion("upper(offering_class) like", value.toUpperCase(), "offeringClass");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(ext_soft_offering_code) like", value.toUpperCase(), "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(ext_hard_offering_code) like", value.toUpperCase(), "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLikeInsensitive(String value) {
            addCriterion("upper(charge_code) like", value.toUpperCase(), "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeIdLikeInsensitive(String value) {
            addCriterion("upper(charge_id) like", value.toUpperCase(), "chargeId");
            return (Criteria) this;
        }

        public Criteria andColorLikeInsensitive(String value) {
            addCriterion("upper(color) like", value.toUpperCase(), "color");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLikeInsensitive(String value) {
            addCriterion("upper(offeringSaleRegion) like", value.toUpperCase(), "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLikeInsensitive(String value) {
            addCriterion("upper(settlePricePartner) like", value.toUpperCase(), "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLikeInsensitive(String value) {
            addCriterion("upper(settleServiceName) like", value.toUpperCase(), "settleservicename");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLikeInsensitive(String value) {
            addCriterion("upper(associated_offer) like", value.toUpperCase(), "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLikeInsensitive(String value) {
            addCriterion("upper(validity_period) like", value.toUpperCase(), "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andInventoryManagementModeKxLikeInsensitive(String value) {
            addCriterion("upper(inventory_management_mode_kx) like", value.toUpperCase(), "inventoryManagementModeKx");
            return (Criteria) this;
        }

        public Criteria andInventoryIdLikeInsensitive(String value) {
            addCriterion("upper(inventory_id) like", value.toUpperCase(), "inventoryId");
            return (Criteria) this;
        }

        public Criteria andProductTypeLikeInsensitive(String value) {
            addCriterion("upper(product_type) like", value.toUpperCase(), "productType");
            return (Criteria) this;
        }

        public Criteria andInventoryMainIdLikeInsensitive(String value) {
            addCriterion("upper(inventory_main_id) like", value.toUpperCase(), "inventoryMainId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Sep 09 10:14:02 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        private AtomOfferingInfoExample example;

        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        protected Criteria(AtomOfferingInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        public AtomOfferingInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Sep 09 10:14:02 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Sep 09 10:14:02 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.AtomOfferingInfoExample example);
    }
}