package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 16:47
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageHomeListParam extends BasePageQuery {

    /**
     * 状态,0:已配置（未发布审核）、1:发布审核中、2:已驳回、3:已发布,4:已下线
     */
    private Integer status;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     */
    private Integer auditStatus;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     */
    private List<Integer> auditStatusList;




}
