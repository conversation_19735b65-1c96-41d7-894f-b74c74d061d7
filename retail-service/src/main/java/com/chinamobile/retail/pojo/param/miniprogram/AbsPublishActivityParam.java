package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:52
 * @description 发布活动请求参数
 */
@Data
public abstract class AbsPublishActivityParam {

    /**
     * 活动id
     */
    private String id;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String name;

    /**
     * 活动对象 客户经理1，分销员2，渠道商4
     */
    @NotEmpty(message = "活动对象不能为空")
    private List<Integer> targetList;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    private Date stopTime;

    /**
     * 奖品确认时间，该时间之后开始发放奖品
     */
    private Date confirmTime;

    /**
     * 结算时间
     */
    @NotNull(message = "结算时间不能为空")
    private Date settlementTime;

    /**
     * 活动类型 1-排位赛，2-即客周周乐
     */
    @NotNull(message = "活动类型不能为空")
    private Integer activityType;

    /**
     * 活动列表图片
     */
    private String listImg;

    /**
     * true-存草稿，false-发布审核，默认false
     */
    private Boolean draft;

    /**
     * 活动省份
     */
    @NotEmpty(message = "活动省份不能为空")
    private List<RegionParam> regions;

    /**
     * 产品范式
     */
    private List<String> offeringClasses;

    /**
     * SPU编码
     */
    private List<String> spuCodes;
    /**
     * 卡片宣传图
     */
    private String shareImg;
}
