package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeAd;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeBanner;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售端小程序主页信息
 */
@Data
public class SkuCoreComponentVO implements Serializable {


    /**
     * spu编码
     *
     */
    private String spuCode;

    /**
     * sku编码
     *
     */
    private String skuCode;


    /**
     * sku名称
     *
     */
    private String skuName;

    /**
     * 发布省份
     */

    private List<String> releaseProvinceName;

    /**
     * 发布城市
     */
    private List<String> releaseCityName;

    /**
     * 核心部件名称
     */
    private String coreComponentName;

    /**
     * 轮播图
     */
    private List<String> coreBannerList ;

    /**
     * 视频
     */
    private List<String> coreVideoList ;

    /**是否删除*/
    private Boolean isDelete;


}