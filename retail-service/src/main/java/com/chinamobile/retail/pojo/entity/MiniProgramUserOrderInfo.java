package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序用户销售订单
 *
 * <AUTHOR>
public class MiniProgramUserOrderInfo implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private String id;

    /**
     * 小程序用户id
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private String userId;

    /**
     * 订单id
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private String orderId;

    /**
     * 原子订单id
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private String atomOrderId;

    /**
     * 总价
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private Long amount;

    /**
     * 积分供应商id
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private String supplierId;

    /**
     * 积分
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private Long point;

    /**
     * 下单时间
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private Date orderCreateTime;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.id
     *
     * @return the value of supply_chain..mini_program_user_order_info.id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.id
     *
     * @param id the value for supply_chain..mini_program_user_order_info.id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.user_id
     *
     * @return the value of supply_chain..mini_program_user_order_info.user_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.user_id
     *
     * @param userId the value for supply_chain..mini_program_user_order_info.user_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.order_id
     *
     * @return the value of supply_chain..mini_program_user_order_info.order_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.order_id
     *
     * @param orderId the value for supply_chain..mini_program_user_order_info.order_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.atom_order_id
     *
     * @return the value of supply_chain..mini_program_user_order_info.atom_order_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..mini_program_user_order_info.atom_order_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.amount
     *
     * @return the value of supply_chain..mini_program_user_order_info.amount
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public Long getAmount() {
        return amount;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withAmount(Long amount) {
        this.setAmount(amount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.amount
     *
     * @param amount the value for supply_chain..mini_program_user_order_info.amount
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setAmount(Long amount) {
        this.amount = amount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.supplier_id
     *
     * @return the value of supply_chain..mini_program_user_order_info.supplier_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.supplier_id
     *
     * @param supplierId the value for supply_chain..mini_program_user_order_info.supplier_id
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.point
     *
     * @return the value of supply_chain..mini_program_user_order_info.point
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public Long getPoint() {
        return point;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withPoint(Long point) {
        this.setPoint(point);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.point
     *
     * @param point the value for supply_chain..mini_program_user_order_info.point
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setPoint(Long point) {
        this.point = point;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.order_create_time
     *
     * @return the value of supply_chain..mini_program_user_order_info.order_create_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withOrderCreateTime(Date orderCreateTime) {
        this.setOrderCreateTime(orderCreateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.order_create_time
     *
     * @param orderCreateTime the value for supply_chain..mini_program_user_order_info.order_create_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.create_time
     *
     * @return the value of supply_chain..mini_program_user_order_info.create_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.create_time
     *
     * @param createTime the value for supply_chain..mini_program_user_order_info.create_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_user_order_info.update_time
     *
     * @return the value of supply_chain..mini_program_user_order_info.update_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public MiniProgramUserOrderInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_user_order_info.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_user_order_info.update_time
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", orderId=").append(orderId);
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", amount=").append(amount);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", point=").append(point);
        sb.append(", orderCreateTime=").append(orderCreateTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramUserOrderInfo other = (MiniProgramUserOrderInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getPoint() == null ? other.getPoint() == null : this.getPoint().equals(other.getPoint()))
            && (this.getOrderCreateTime() == null ? other.getOrderCreateTime() == null : this.getOrderCreateTime().equals(other.getOrderCreateTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getPoint() == null) ? 0 : getPoint().hashCode());
        result = prime * result + ((getOrderCreateTime() == null) ? 0 : getOrderCreateTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        amount("amount", "amount", "BIGINT", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        point("point", "point", "BIGINT", false),
        orderCreateTime("order_create_time", "orderCreateTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Jul 26 14:37:35 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}