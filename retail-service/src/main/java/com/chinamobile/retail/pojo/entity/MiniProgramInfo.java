package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 销售端小程序资讯信息
 *
 * <AUTHOR>
public class MiniProgramInfo {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String id;

    /**
     * 资讯主题名称
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String name;

    /**
     * 素材类型 1-图文素材 2-视频素材 4-知识文档
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer contentType;

    /**
     * 活动状态 0-待发布，1-已发布，2-已下线
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer status;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer auditStatus;

    /**
     * 资讯内容
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String content;

    /**
     * 资讯关键词，拼接字符串；
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String keyWords;

    /**
     * 素材描述
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String description;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String createUid;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Date updateTime;

    /**
     * 是否为热门问答,0-否，1-是
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer isPopular;

    /**
     * 文档类型 0-产品资料，1-操作指南
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer wordType;

    /**
     * 发布区域。 1-资讯中心 2-营销素材 4-知识库
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer category;

    /**
     * 资讯类型，拼接字符串；1-产品推荐，2-产品评测，3-真实案例
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String infoType;

    /**
     * 资讯头图或者知识库头图url
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String headImgUrl1;

    /**
     * 营销素材头图url
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String headImgUrl2;

    /**
     * 知识库类型，拼接字符串；1-产品介绍，2-流程操作视频,3-FAQ问答对
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String knowledgeType;

    /**
     * 是否删除 0-未删除，1-已删除
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private Integer isDelete;

    /**
     * 专区名称
     *
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    private String activityName;

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.id
     *
     * @return the value of supply_chain..mini_program_info.id
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.id
     *
     * @param id the value for supply_chain..mini_program_info.id
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.name
     *
     * @return the value of supply_chain..mini_program_info.name
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.name
     *
     * @param name the value for supply_chain..mini_program_info.name
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.content_type
     *
     * @return the value of supply_chain..mini_program_info.content_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getContentType() {
        return contentType;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withContentType(Integer contentType) {
        this.setContentType(contentType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.content_type
     *
     * @param contentType the value for supply_chain..mini_program_info.content_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.status
     *
     * @return the value of supply_chain..mini_program_info.status
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.status
     *
     * @param status the value for supply_chain..mini_program_info.status
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.audit_status
     *
     * @return the value of supply_chain..mini_program_info.audit_status
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.audit_status
     *
     * @param auditStatus the value for supply_chain..mini_program_info.audit_status
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.content
     *
     * @return the value of supply_chain..mini_program_info.content
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getContent() {
        return content;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withContent(String content) {
        this.setContent(content);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.content
     *
     * @param content the value for supply_chain..mini_program_info.content
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.key_words
     *
     * @return the value of supply_chain..mini_program_info.key_words
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getKeyWords() {
        return keyWords;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withKeyWords(String keyWords) {
        this.setKeyWords(keyWords);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.key_words
     *
     * @param keyWords the value for supply_chain..mini_program_info.key_words
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords == null ? null : keyWords.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.description
     *
     * @return the value of supply_chain..mini_program_info.description
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withDescription(String description) {
        this.setDescription(description);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.description
     *
     * @param description the value for supply_chain..mini_program_info.description
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.create_uid
     *
     * @return the value of supply_chain..mini_program_info.create_uid
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getCreateUid() {
        return createUid;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withCreateUid(String createUid) {
        this.setCreateUid(createUid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.create_uid
     *
     * @param createUid the value for supply_chain..mini_program_info.create_uid
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setCreateUid(String createUid) {
        this.createUid = createUid == null ? null : createUid.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.create_time
     *
     * @return the value of supply_chain..mini_program_info.create_time
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.create_time
     *
     * @param createTime the value for supply_chain..mini_program_info.create_time
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.update_time
     *
     * @return the value of supply_chain..mini_program_info.update_time
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_info.update_time
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.is_popular
     *
     * @return the value of supply_chain..mini_program_info.is_popular
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getIsPopular() {
        return isPopular;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withIsPopular(Integer isPopular) {
        this.setIsPopular(isPopular);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.is_popular
     *
     * @param isPopular the value for supply_chain..mini_program_info.is_popular
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setIsPopular(Integer isPopular) {
        this.isPopular = isPopular;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.word_type
     *
     * @return the value of supply_chain..mini_program_info.word_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getWordType() {
        return wordType;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withWordType(Integer wordType) {
        this.setWordType(wordType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.word_type
     *
     * @param wordType the value for supply_chain..mini_program_info.word_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setWordType(Integer wordType) {
        this.wordType = wordType;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.category
     *
     * @return the value of supply_chain..mini_program_info.category
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getCategory() {
        return category;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withCategory(Integer category) {
        this.setCategory(category);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.category
     *
     * @param category the value for supply_chain..mini_program_info.category
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setCategory(Integer category) {
        this.category = category;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.info_type
     *
     * @return the value of supply_chain..mini_program_info.info_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getInfoType() {
        return infoType;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withInfoType(String infoType) {
        this.setInfoType(infoType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.info_type
     *
     * @param infoType the value for supply_chain..mini_program_info.info_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setInfoType(String infoType) {
        this.infoType = infoType == null ? null : infoType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.head_img_url_1
     *
     * @return the value of supply_chain..mini_program_info.head_img_url_1
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getHeadImgUrl1() {
        return headImgUrl1;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withHeadImgUrl1(String headImgUrl1) {
        this.setHeadImgUrl1(headImgUrl1);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.head_img_url_1
     *
     * @param headImgUrl1 the value for supply_chain..mini_program_info.head_img_url_1
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setHeadImgUrl1(String headImgUrl1) {
        this.headImgUrl1 = headImgUrl1 == null ? null : headImgUrl1.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.head_img_url_2
     *
     * @return the value of supply_chain..mini_program_info.head_img_url_2
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getHeadImgUrl2() {
        return headImgUrl2;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withHeadImgUrl2(String headImgUrl2) {
        this.setHeadImgUrl2(headImgUrl2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.head_img_url_2
     *
     * @param headImgUrl2 the value for supply_chain..mini_program_info.head_img_url_2
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setHeadImgUrl2(String headImgUrl2) {
        this.headImgUrl2 = headImgUrl2 == null ? null : headImgUrl2.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.knowledge_type
     *
     * @return the value of supply_chain..mini_program_info.knowledge_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getKnowledgeType() {
        return knowledgeType;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withKnowledgeType(String knowledgeType) {
        this.setKnowledgeType(knowledgeType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.knowledge_type
     *
     * @param knowledgeType the value for supply_chain..mini_program_info.knowledge_type
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setKnowledgeType(String knowledgeType) {
        this.knowledgeType = knowledgeType == null ? null : knowledgeType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.is_delete
     *
     * @return the value of supply_chain..mini_program_info.is_delete
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withIsDelete(Integer isDelete) {
        this.setIsDelete(isDelete);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.is_delete
     *
     * @param isDelete the value for supply_chain..mini_program_info.is_delete
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info.activity_name
     *
     * @return the value of supply_chain..mini_program_info.activity_name
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public String getActivityName() {
        return activityName;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public MiniProgramInfo withActivityName(String activityName) {
        this.setActivityName(activityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info.activity_name
     *
     * @param activityName the value for supply_chain..mini_program_info.activity_name
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", contentType=").append(contentType);
        sb.append(", status=").append(status);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", content=").append(content);
        sb.append(", keyWords=").append(keyWords);
        sb.append(", description=").append(description);
        sb.append(", createUid=").append(createUid);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isPopular=").append(isPopular);
        sb.append(", wordType=").append(wordType);
        sb.append(", category=").append(category);
        sb.append(", infoType=").append(infoType);
        sb.append(", headImgUrl1=").append(headImgUrl1);
        sb.append(", headImgUrl2=").append(headImgUrl2);
        sb.append(", knowledgeType=").append(knowledgeType);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", activityName=").append(activityName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramInfo other = (MiniProgramInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getContentType() == null ? other.getContentType() == null : this.getContentType().equals(other.getContentType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getContent() == null ? other.getContent() == null : this.getContent().equals(other.getContent()))
            && (this.getKeyWords() == null ? other.getKeyWords() == null : this.getKeyWords().equals(other.getKeyWords()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getCreateUid() == null ? other.getCreateUid() == null : this.getCreateUid().equals(other.getCreateUid()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsPopular() == null ? other.getIsPopular() == null : this.getIsPopular().equals(other.getIsPopular()))
            && (this.getWordType() == null ? other.getWordType() == null : this.getWordType().equals(other.getWordType()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getInfoType() == null ? other.getInfoType() == null : this.getInfoType().equals(other.getInfoType()))
            && (this.getHeadImgUrl1() == null ? other.getHeadImgUrl1() == null : this.getHeadImgUrl1().equals(other.getHeadImgUrl1()))
            && (this.getHeadImgUrl2() == null ? other.getHeadImgUrl2() == null : this.getHeadImgUrl2().equals(other.getHeadImgUrl2()))
            && (this.getKnowledgeType() == null ? other.getKnowledgeType() == null : this.getKnowledgeType().equals(other.getKnowledgeType()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getActivityName() == null ? other.getActivityName() == null : this.getActivityName().equals(other.getActivityName()));
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getContentType() == null) ? 0 : getContentType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getContent() == null) ? 0 : getContent().hashCode());
        result = prime * result + ((getKeyWords() == null) ? 0 : getKeyWords().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getCreateUid() == null) ? 0 : getCreateUid().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsPopular() == null) ? 0 : getIsPopular().hashCode());
        result = prime * result + ((getWordType() == null) ? 0 : getWordType().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getInfoType() == null) ? 0 : getInfoType().hashCode());
        result = prime * result + ((getHeadImgUrl1() == null) ? 0 : getHeadImgUrl1().hashCode());
        result = prime * result + ((getHeadImgUrl2() == null) ? 0 : getHeadImgUrl2().hashCode());
        result = prime * result + ((getKnowledgeType() == null) ? 0 : getKnowledgeType().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getActivityName() == null) ? 0 : getActivityName().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        contentType("content_type", "contentType", "INTEGER", false),
        status("status", "status", "INTEGER", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        content("content", "content", "VARCHAR", false),
        keyWords("key_words", "keyWords", "VARCHAR", false),
        description("description", "description", "VARCHAR", false),
        createUid("create_uid", "createUid", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        isPopular("is_popular", "isPopular", "INTEGER", false),
        wordType("word_type", "wordType", "INTEGER", false),
        category("category", "category", "INTEGER", false),
        infoType("info_type", "infoType", "VARCHAR", false),
        headImgUrl1("head_img_url_1", "headImgUrl1", "VARCHAR", false),
        headImgUrl2("head_img_url_2", "headImgUrl2", "VARCHAR", false),
        knowledgeType("knowledge_type", "knowledgeType", "VARCHAR", false),
        isDelete("is_delete", "isDelete", "INTEGER", false),
        activityName("activity_name", "activityName", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Dec 31 11:11:41 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}