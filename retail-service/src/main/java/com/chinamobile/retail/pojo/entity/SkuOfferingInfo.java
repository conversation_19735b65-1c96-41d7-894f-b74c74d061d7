package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 规格编码
 *
 * <AUTHOR>
public class SkuOfferingInfo implements Serializable {
    /**
     * sku主键id
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String id;

    /**
     * spu 主键id
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String spuId;

    /**
     * 商品组/销售商品编码
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String spuCode;

    /**
     * 规格编码
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String offeringCode;

    /**
     * 商品名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String offeringName;

    /**
     * sku商品状态 0：测试、1：发布、2：下架
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String offeringStatus;

    /**
     * sku商品状态变更时间
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Date offeringStatusTime;

    /**
     * 商品构成
offeringClass = A02时，本字段必填;
0:纯软件
1：软硬一体
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String composition;

    /**
     * 型号
composition=1时必填;
或者offeringClass = A03时必填;
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String model;

    /**
     * 部件数量
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Long quantity;

    /**
     * 尺寸
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String size;

    /**
     * 操作类型
A：新增
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String operType;

    /**
     * 建议零售价
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Long recommendPrice;

    /**
     * G:集团客户
P:个人客户
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String saleObject;

    /**
     * 销售目录价
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Long price;

    /**
     * 计量单位
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String unit;

    /**
     * 营销案名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String marketName;

    /**
     * 营销案编码
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String marketCode;

    /**
     * 供应商名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String supplierName;

    /**
     * X产品类型,1:5G CPE,2:5G 快线,3:千里眼,4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,7:OnePark独立服务,8:标准产品（OnePark）9：千里眼独立服务10：和对讲独立服务 11：云视讯独立服务
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String productType;

    /**
     * 是否需要合作伙伴接单,1：是,2：否
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String receiveOrder;

    /**
     * 卡服务商EC编码
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String custCode;

    /**
     * 卡服务商名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String custName;

    /**
     * 卡片类型,0：插拔卡,1：贴片卡,2：M2M芯片非空写卡,12：M2M芯片空写卡
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String cardType;

    /**
     * 主商品,01：物联卡个人、03：窄带网个人、04：和对讲个人 16：行车卫士个人
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String mainOfferingCode;

    /**
     * 模板名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String templateName;

    /**
     * 开卡模板名称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String templateId;

    /**
     * 销售模式1：省内融合 2：商城直销  当offeringClass=A11卡+X时必传
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String saleModel;

    /**
     * 项目信息
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String project;

    /**
     * 积分状态, 1-- 暂停  2-- 生效
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Integer pointStatus;

    /**
     * 合作伙伴id，关联user_partner表user_id
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String cooperatorId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Date updateTime;

    /**
     * 删除时间
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private Date deleteTime;

    /**
     * 规格简称
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String skuAbbreviation;

    /**
     * 接单人员名称，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String receiveOrderName;

    /**
     * 接单人员联系电话，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String receiveOrderPhone;

    /**
     * 交付人员名称，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String deliverName;

    /**
     * 交付人员联系电话，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String deliverPhone;

    /**
     * 售后人员名称，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String aftermarketName;

    /**
     * 售后人员联系电话，加密
     *
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private String aftermarketPhone;

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.id
     *
     * @return the value of supply_chain..sku_offering_info.id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.id
     *
     * @param id the value for supply_chain..sku_offering_info.id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.spu_id
     *
     * @return the value of supply_chain..sku_offering_info.spu_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSpuId(String spuId) {
        this.setSpuId(spuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.spu_id
     *
     * @param spuId the value for supply_chain..sku_offering_info.spu_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.spu_code
     *
     * @return the value of supply_chain..sku_offering_info.spu_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.spu_code
     *
     * @param spuCode the value for supply_chain..sku_offering_info.spu_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.offering_code
     *
     * @return the value of supply_chain..sku_offering_info.offering_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getOfferingCode() {
        return offeringCode;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withOfferingCode(String offeringCode) {
        this.setOfferingCode(offeringCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.offering_code
     *
     * @param offeringCode the value for supply_chain..sku_offering_info.offering_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setOfferingCode(String offeringCode) {
        this.offeringCode = offeringCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.offering_name
     *
     * @return the value of supply_chain..sku_offering_info.offering_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getOfferingName() {
        return offeringName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withOfferingName(String offeringName) {
        this.setOfferingName(offeringName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.offering_name
     *
     * @param offeringName the value for supply_chain..sku_offering_info.offering_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setOfferingName(String offeringName) {
        this.offeringName = offeringName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.offering_status
     *
     * @return the value of supply_chain..sku_offering_info.offering_status
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getOfferingStatus() {
        return offeringStatus;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withOfferingStatus(String offeringStatus) {
        this.setOfferingStatus(offeringStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.offering_status
     *
     * @param offeringStatus the value for supply_chain..sku_offering_info.offering_status
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setOfferingStatus(String offeringStatus) {
        this.offeringStatus = offeringStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.offering_status_time
     *
     * @return the value of supply_chain..sku_offering_info.offering_status_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Date getOfferingStatusTime() {
        return offeringStatusTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withOfferingStatusTime(Date offeringStatusTime) {
        this.setOfferingStatusTime(offeringStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.offering_status_time
     *
     * @param offeringStatusTime the value for supply_chain..sku_offering_info.offering_status_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setOfferingStatusTime(Date offeringStatusTime) {
        this.offeringStatusTime = offeringStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.composition
     *
     * @return the value of supply_chain..sku_offering_info.composition
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getComposition() {
        return composition;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withComposition(String composition) {
        this.setComposition(composition);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.composition
     *
     * @param composition the value for supply_chain..sku_offering_info.composition
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setComposition(String composition) {
        this.composition = composition;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.model
     *
     * @return the value of supply_chain..sku_offering_info.model
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.model
     *
     * @param model the value for supply_chain..sku_offering_info.model
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.quantity
     *
     * @return the value of supply_chain..sku_offering_info.quantity
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.quantity
     *
     * @param quantity the value for supply_chain..sku_offering_info.quantity
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.size
     *
     * @return the value of supply_chain..sku_offering_info.size
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSize() {
        return size;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSize(String size) {
        this.setSize(size);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.size
     *
     * @param size the value for supply_chain..sku_offering_info.size
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSize(String size) {
        this.size = size;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.oper_type
     *
     * @return the value of supply_chain..sku_offering_info.oper_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getOperType() {
        return operType;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withOperType(String operType) {
        this.setOperType(operType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.oper_type
     *
     * @param operType the value for supply_chain..sku_offering_info.oper_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setOperType(String operType) {
        this.operType = operType;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.recommend_price
     *
     * @return the value of supply_chain..sku_offering_info.recommend_price
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Long getRecommendPrice() {
        return recommendPrice;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withRecommendPrice(Long recommendPrice) {
        this.setRecommendPrice(recommendPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.recommend_price
     *
     * @param recommendPrice the value for supply_chain..sku_offering_info.recommend_price
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setRecommendPrice(Long recommendPrice) {
        this.recommendPrice = recommendPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.sale_object
     *
     * @return the value of supply_chain..sku_offering_info.sale_object
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSaleObject() {
        return saleObject;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSaleObject(String saleObject) {
        this.setSaleObject(saleObject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.sale_object
     *
     * @param saleObject the value for supply_chain..sku_offering_info.sale_object
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSaleObject(String saleObject) {
        this.saleObject = saleObject;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.price
     *
     * @return the value of supply_chain..sku_offering_info.price
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Long getPrice() {
        return price;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withPrice(Long price) {
        this.setPrice(price);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.price
     *
     * @param price the value for supply_chain..sku_offering_info.price
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setPrice(Long price) {
        this.price = price;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.unit
     *
     * @return the value of supply_chain..sku_offering_info.unit
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.unit
     *
     * @param unit the value for supply_chain..sku_offering_info.unit
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.market_name
     *
     * @return the value of supply_chain..sku_offering_info.market_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getMarketName() {
        return marketName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withMarketName(String marketName) {
        this.setMarketName(marketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.market_name
     *
     * @param marketName the value for supply_chain..sku_offering_info.market_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.market_code
     *
     * @return the value of supply_chain..sku_offering_info.market_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getMarketCode() {
        return marketCode;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withMarketCode(String marketCode) {
        this.setMarketCode(marketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.market_code
     *
     * @param marketCode the value for supply_chain..sku_offering_info.market_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.supplier_name
     *
     * @return the value of supply_chain..sku_offering_info.supplier_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSupplierName(String supplierName) {
        this.setSupplierName(supplierName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.supplier_name
     *
     * @param supplierName the value for supply_chain..sku_offering_info.supplier_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.product_type
     *
     * @return the value of supply_chain..sku_offering_info.product_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getProductType() {
        return productType;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withProductType(String productType) {
        this.setProductType(productType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.product_type
     *
     * @param productType the value for supply_chain..sku_offering_info.product_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.receive_order
     *
     * @return the value of supply_chain..sku_offering_info.receive_order
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getReceiveOrder() {
        return receiveOrder;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withReceiveOrder(String receiveOrder) {
        this.setReceiveOrder(receiveOrder);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.receive_order
     *
     * @param receiveOrder the value for supply_chain..sku_offering_info.receive_order
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setReceiveOrder(String receiveOrder) {
        this.receiveOrder = receiveOrder;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.cust_code
     *
     * @return the value of supply_chain..sku_offering_info.cust_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.cust_code
     *
     * @param custCode the value for supply_chain..sku_offering_info.cust_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.cust_name
     *
     * @return the value of supply_chain..sku_offering_info.cust_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.cust_name
     *
     * @param custName the value for supply_chain..sku_offering_info.cust_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.card_type
     *
     * @return the value of supply_chain..sku_offering_info.card_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getCardType() {
        return cardType;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withCardType(String cardType) {
        this.setCardType(cardType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.card_type
     *
     * @param cardType the value for supply_chain..sku_offering_info.card_type
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.main_offering_code
     *
     * @return the value of supply_chain..sku_offering_info.main_offering_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getMainOfferingCode() {
        return mainOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withMainOfferingCode(String mainOfferingCode) {
        this.setMainOfferingCode(mainOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.main_offering_code
     *
     * @param mainOfferingCode the value for supply_chain..sku_offering_info.main_offering_code
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setMainOfferingCode(String mainOfferingCode) {
        this.mainOfferingCode = mainOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.template_name
     *
     * @return the value of supply_chain..sku_offering_info.template_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withTemplateName(String templateName) {
        this.setTemplateName(templateName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.template_name
     *
     * @param templateName the value for supply_chain..sku_offering_info.template_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.template_id
     *
     * @return the value of supply_chain..sku_offering_info.template_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.template_id
     *
     * @param templateId the value for supply_chain..sku_offering_info.template_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.sale_model
     *
     * @return the value of supply_chain..sku_offering_info.sale_model
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSaleModel() {
        return saleModel;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSaleModel(String saleModel) {
        this.setSaleModel(saleModel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.sale_model
     *
     * @param saleModel the value for supply_chain..sku_offering_info.sale_model
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSaleModel(String saleModel) {
        this.saleModel = saleModel;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.project
     *
     * @return the value of supply_chain..sku_offering_info.project
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getProject() {
        return project;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withProject(String project) {
        this.setProject(project);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.project
     *
     * @param project the value for supply_chain..sku_offering_info.project
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setProject(String project) {
        this.project = project;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.point_status
     *
     * @return the value of supply_chain..sku_offering_info.point_status
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Integer getPointStatus() {
        return pointStatus;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withPointStatus(Integer pointStatus) {
        this.setPointStatus(pointStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.point_status
     *
     * @param pointStatus the value for supply_chain..sku_offering_info.point_status
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setPointStatus(Integer pointStatus) {
        this.pointStatus = pointStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.cooperator_id
     *
     * @return the value of supply_chain..sku_offering_info.cooperator_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..sku_offering_info.cooperator_id
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.create_time
     *
     * @return the value of supply_chain..sku_offering_info.create_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.create_time
     *
     * @param createTime the value for supply_chain..sku_offering_info.create_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.update_time
     *
     * @return the value of supply_chain..sku_offering_info.update_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.update_time
     *
     * @param updateTime the value for supply_chain..sku_offering_info.update_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.delete_time
     *
     * @return the value of supply_chain..sku_offering_info.delete_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.delete_time
     *
     * @param deleteTime the value for supply_chain..sku_offering_info.delete_time
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.sku_abbreviation
     *
     * @return the value of supply_chain..sku_offering_info.sku_abbreviation
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getSkuAbbreviation() {
        return skuAbbreviation;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withSkuAbbreviation(String skuAbbreviation) {
        this.setSkuAbbreviation(skuAbbreviation);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.sku_abbreviation
     *
     * @param skuAbbreviation the value for supply_chain..sku_offering_info.sku_abbreviation
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setSkuAbbreviation(String skuAbbreviation) {
        this.skuAbbreviation = skuAbbreviation;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.receive_order_name
     *
     * @return the value of supply_chain..sku_offering_info.receive_order_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getReceiveOrderName() {
        return receiveOrderName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withReceiveOrderName(String receiveOrderName) {
        this.setReceiveOrderName(receiveOrderName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.receive_order_name
     *
     * @param receiveOrderName the value for supply_chain..sku_offering_info.receive_order_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setReceiveOrderName(String receiveOrderName) {
        this.receiveOrderName = receiveOrderName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.receive_order_phone
     *
     * @return the value of supply_chain..sku_offering_info.receive_order_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getReceiveOrderPhone() {
        return receiveOrderPhone;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withReceiveOrderPhone(String receiveOrderPhone) {
        this.setReceiveOrderPhone(receiveOrderPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.receive_order_phone
     *
     * @param receiveOrderPhone the value for supply_chain..sku_offering_info.receive_order_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setReceiveOrderPhone(String receiveOrderPhone) {
        this.receiveOrderPhone = receiveOrderPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.deliver_name
     *
     * @return the value of supply_chain..sku_offering_info.deliver_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getDeliverName() {
        return deliverName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withDeliverName(String deliverName) {
        this.setDeliverName(deliverName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.deliver_name
     *
     * @param deliverName the value for supply_chain..sku_offering_info.deliver_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setDeliverName(String deliverName) {
        this.deliverName = deliverName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.deliver_phone
     *
     * @return the value of supply_chain..sku_offering_info.deliver_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getDeliverPhone() {
        return deliverPhone;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withDeliverPhone(String deliverPhone) {
        this.setDeliverPhone(deliverPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.deliver_phone
     *
     * @param deliverPhone the value for supply_chain..sku_offering_info.deliver_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setDeliverPhone(String deliverPhone) {
        this.deliverPhone = deliverPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.aftermarket_name
     *
     * @return the value of supply_chain..sku_offering_info.aftermarket_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getAftermarketName() {
        return aftermarketName;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withAftermarketName(String aftermarketName) {
        this.setAftermarketName(aftermarketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.aftermarket_name
     *
     * @param aftermarketName the value for supply_chain..sku_offering_info.aftermarket_name
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setAftermarketName(String aftermarketName) {
        this.aftermarketName = aftermarketName;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info.aftermarket_phone
     *
     * @return the value of supply_chain..sku_offering_info.aftermarket_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public String getAftermarketPhone() {
        return aftermarketPhone;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public SkuOfferingInfo withAftermarketPhone(String aftermarketPhone) {
        this.setAftermarketPhone(aftermarketPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info.aftermarket_phone
     *
     * @param aftermarketPhone the value for supply_chain..sku_offering_info.aftermarket_phone
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public void setAftermarketPhone(String aftermarketPhone) {
        this.aftermarketPhone = aftermarketPhone;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuId=").append(spuId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", offeringCode=").append(offeringCode);
        sb.append(", offeringName=").append(offeringName);
        sb.append(", offeringStatus=").append(offeringStatus);
        sb.append(", offeringStatusTime=").append(offeringStatusTime);
        sb.append(", composition=").append(composition);
        sb.append(", model=").append(model);
        sb.append(", quantity=").append(quantity);
        sb.append(", size=").append(size);
        sb.append(", operType=").append(operType);
        sb.append(", recommendPrice=").append(recommendPrice);
        sb.append(", saleObject=").append(saleObject);
        sb.append(", price=").append(price);
        sb.append(", unit=").append(unit);
        sb.append(", marketName=").append(marketName);
        sb.append(", marketCode=").append(marketCode);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", productType=").append(productType);
        sb.append(", receiveOrder=").append(receiveOrder);
        sb.append(", custCode=").append(custCode);
        sb.append(", custName=").append(custName);
        sb.append(", cardType=").append(cardType);
        sb.append(", mainOfferingCode=").append(mainOfferingCode);
        sb.append(", templateName=").append(templateName);
        sb.append(", templateId=").append(templateId);
        sb.append(", saleModel=").append(saleModel);
        sb.append(", project=").append(project);
        sb.append(", pointStatus=").append(pointStatus);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append(", skuAbbreviation=").append(skuAbbreviation);
        sb.append(", receiveOrderName=").append(receiveOrderName);
        sb.append(", receiveOrderPhone=").append(receiveOrderPhone);
        sb.append(", deliverName=").append(deliverName);
        sb.append(", deliverPhone=").append(deliverPhone);
        sb.append(", aftermarketName=").append(aftermarketName);
        sb.append(", aftermarketPhone=").append(aftermarketPhone);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SkuOfferingInfo other = (SkuOfferingInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getOfferingCode() == null ? other.getOfferingCode() == null : this.getOfferingCode().equals(other.getOfferingCode()))
            && (this.getOfferingName() == null ? other.getOfferingName() == null : this.getOfferingName().equals(other.getOfferingName()))
            && (this.getOfferingStatus() == null ? other.getOfferingStatus() == null : this.getOfferingStatus().equals(other.getOfferingStatus()))
            && (this.getOfferingStatusTime() == null ? other.getOfferingStatusTime() == null : this.getOfferingStatusTime().equals(other.getOfferingStatusTime()))
            && (this.getComposition() == null ? other.getComposition() == null : this.getComposition().equals(other.getComposition()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getSize() == null ? other.getSize() == null : this.getSize().equals(other.getSize()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getRecommendPrice() == null ? other.getRecommendPrice() == null : this.getRecommendPrice().equals(other.getRecommendPrice()))
            && (this.getSaleObject() == null ? other.getSaleObject() == null : this.getSaleObject().equals(other.getSaleObject()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getMarketName() == null ? other.getMarketName() == null : this.getMarketName().equals(other.getMarketName()))
            && (this.getMarketCode() == null ? other.getMarketCode() == null : this.getMarketCode().equals(other.getMarketCode()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getProductType() == null ? other.getProductType() == null : this.getProductType().equals(other.getProductType()))
            && (this.getReceiveOrder() == null ? other.getReceiveOrder() == null : this.getReceiveOrder().equals(other.getReceiveOrder()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getCardType() == null ? other.getCardType() == null : this.getCardType().equals(other.getCardType()))
            && (this.getMainOfferingCode() == null ? other.getMainOfferingCode() == null : this.getMainOfferingCode().equals(other.getMainOfferingCode()))
            && (this.getTemplateName() == null ? other.getTemplateName() == null : this.getTemplateName().equals(other.getTemplateName()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getSaleModel() == null ? other.getSaleModel() == null : this.getSaleModel().equals(other.getSaleModel()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getPointStatus() == null ? other.getPointStatus() == null : this.getPointStatus().equals(other.getPointStatus()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()))
            && (this.getSkuAbbreviation() == null ? other.getSkuAbbreviation() == null : this.getSkuAbbreviation().equals(other.getSkuAbbreviation()))
            && (this.getReceiveOrderName() == null ? other.getReceiveOrderName() == null : this.getReceiveOrderName().equals(other.getReceiveOrderName()))
            && (this.getReceiveOrderPhone() == null ? other.getReceiveOrderPhone() == null : this.getReceiveOrderPhone().equals(other.getReceiveOrderPhone()))
            && (this.getDeliverName() == null ? other.getDeliverName() == null : this.getDeliverName().equals(other.getDeliverName()))
            && (this.getDeliverPhone() == null ? other.getDeliverPhone() == null : this.getDeliverPhone().equals(other.getDeliverPhone()))
            && (this.getAftermarketName() == null ? other.getAftermarketName() == null : this.getAftermarketName().equals(other.getAftermarketName()))
            && (this.getAftermarketPhone() == null ? other.getAftermarketPhone() == null : this.getAftermarketPhone().equals(other.getAftermarketPhone()));
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getOfferingCode() == null) ? 0 : getOfferingCode().hashCode());
        result = prime * result + ((getOfferingName() == null) ? 0 : getOfferingName().hashCode());
        result = prime * result + ((getOfferingStatus() == null) ? 0 : getOfferingStatus().hashCode());
        result = prime * result + ((getOfferingStatusTime() == null) ? 0 : getOfferingStatusTime().hashCode());
        result = prime * result + ((getComposition() == null) ? 0 : getComposition().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getSize() == null) ? 0 : getSize().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getRecommendPrice() == null) ? 0 : getRecommendPrice().hashCode());
        result = prime * result + ((getSaleObject() == null) ? 0 : getSaleObject().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getMarketName() == null) ? 0 : getMarketName().hashCode());
        result = prime * result + ((getMarketCode() == null) ? 0 : getMarketCode().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getProductType() == null) ? 0 : getProductType().hashCode());
        result = prime * result + ((getReceiveOrder() == null) ? 0 : getReceiveOrder().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getCardType() == null) ? 0 : getCardType().hashCode());
        result = prime * result + ((getMainOfferingCode() == null) ? 0 : getMainOfferingCode().hashCode());
        result = prime * result + ((getTemplateName() == null) ? 0 : getTemplateName().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getSaleModel() == null) ? 0 : getSaleModel().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getPointStatus() == null) ? 0 : getPointStatus().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        result = prime * result + ((getSkuAbbreviation() == null) ? 0 : getSkuAbbreviation().hashCode());
        result = prime * result + ((getReceiveOrderName() == null) ? 0 : getReceiveOrderName().hashCode());
        result = prime * result + ((getReceiveOrderPhone() == null) ? 0 : getReceiveOrderPhone().hashCode());
        result = prime * result + ((getDeliverName() == null) ? 0 : getDeliverName().hashCode());
        result = prime * result + ((getDeliverPhone() == null) ? 0 : getDeliverPhone().hashCode());
        result = prime * result + ((getAftermarketName() == null) ? 0 : getAftermarketName().hashCode());
        result = prime * result + ((getAftermarketPhone() == null) ? 0 : getAftermarketPhone().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Feb 10 15:16:44 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuId("spu_id", "spuId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        offeringCode("offering_code", "offeringCode", "VARCHAR", false),
        offeringName("offering_name", "offeringName", "VARCHAR", false),
        offeringStatus("offering_status", "offeringStatus", "VARCHAR", false),
        offeringStatusTime("offering_status_time", "offeringStatusTime", "TIMESTAMP", false),
        composition("composition", "composition", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        size("size", "size", "VARCHAR", false),
        operType("oper_type", "operType", "VARCHAR", false),
        recommendPrice("recommend_price", "recommendPrice", "BIGINT", false),
        saleObject("sale_object", "saleObject", "VARCHAR", false),
        price("price", "price", "BIGINT", false),
        unit("unit", "unit", "VARCHAR", false),
        marketName("market_name", "marketName", "VARCHAR", false),
        marketCode("market_code", "marketCode", "VARCHAR", false),
        supplierName("supplier_name", "supplierName", "VARCHAR", false),
        productType("product_type", "productType", "VARCHAR", false),
        receiveOrder("receive_order", "receiveOrder", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        cardType("card_type", "cardType", "VARCHAR", false),
        mainOfferingCode("main_offering_code", "mainOfferingCode", "VARCHAR", false),
        templateName("template_name", "templateName", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        saleModel("sale_model", "saleModel", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        pointStatus("point_status", "pointStatus", "INTEGER", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false),
        skuAbbreviation("sku_abbreviation", "skuAbbreviation", "VARCHAR", false),
        receiveOrderName("receive_order_name", "receiveOrderName", "VARCHAR", false),
        receiveOrderPhone("receive_order_phone", "receiveOrderPhone", "VARCHAR", false),
        deliverName("deliver_name", "deliverName", "VARCHAR", false),
        deliverPhone("deliver_phone", "deliverPhone", "VARCHAR", false),
        aftermarketName("aftermarket_name", "aftermarketName", "VARCHAR", false),
        aftermarketPhone("aftermarket_phone", "aftermarketPhone", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Feb 10 15:16:44 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}