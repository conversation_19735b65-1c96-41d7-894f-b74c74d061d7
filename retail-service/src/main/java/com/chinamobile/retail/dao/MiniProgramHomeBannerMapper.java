package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeBanner;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeBannerExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramHomeBannerMapper {
    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    long countByExample(MiniProgramHomeBannerExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int deleteByExample(MiniProgramHomeBannerExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int insert(MiniProgramHomeBanner record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int insertSelective(MiniProgramHomeBanner record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    List<MiniProgramHomeBanner> selectByExample(MiniProgramHomeBannerExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    MiniProgramHomeBanner selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int updateByExampleSelective(@Param("record") MiniProgramHomeBanner record, @Param("example") MiniProgramHomeBannerExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int updateByExample(@Param("record") MiniProgramHomeBanner record, @Param("example") MiniProgramHomeBannerExample example);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int updateByPrimaryKeySelective(MiniProgramHomeBanner record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int updateByPrimaryKey(MiniProgramHomeBanner record);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int batchInsert(@Param("list") List<MiniProgramHomeBanner> list);

    /**
     *
     * @mbg.generated Thu Jan 02 10:01:46 CST 2025
     */
    int batchInsertSelective(@Param("list") List<MiniProgramHomeBanner> list, @Param("selective") MiniProgramHomeBanner.Column ... selective);
}