package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.EstewardReservationExtraTemplate;
import com.chinamobile.retail.pojo.entity.EstewardReservationExtraTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EstewardReservationExtraTemplateMapper {
    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    long countByExample(EstewardReservationExtraTemplateExample example);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int deleteByExample(EstewardReservationExtraTemplateExample example);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int insert(EstewardReservationExtraTemplate record);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int insertSelective(EstewardReservationExtraTemplate record);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    List<EstewardReservationExtraTemplate> selectByExample(EstewardReservationExtraTemplateExample example);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    EstewardReservationExtraTemplate selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int updateByExampleSelective(@Param("record") EstewardReservationExtraTemplate record, @Param("example") EstewardReservationExtraTemplateExample example);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int updateByExample(@Param("record") EstewardReservationExtraTemplate record, @Param("example") EstewardReservationExtraTemplateExample example);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int updateByPrimaryKeySelective(EstewardReservationExtraTemplate record);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int updateByPrimaryKey(EstewardReservationExtraTemplate record);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int batchInsert(@Param("list") List<EstewardReservationExtraTemplate> list);

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    int batchInsertSelective(@Param("list") List<EstewardReservationExtraTemplate> list, @Param("selective") EstewardReservationExtraTemplate.Column ... selective);
}