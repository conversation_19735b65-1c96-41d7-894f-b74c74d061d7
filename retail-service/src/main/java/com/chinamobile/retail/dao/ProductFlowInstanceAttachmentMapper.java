package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.ProductFlowInstanceAttachment;
import com.chinamobile.retail.pojo.entity.ProductFlowInstanceAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceAttachmentMapper {
    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    long countByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int deleteByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int insert(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int insertSelective(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    List<ProductFlowInstanceAttachment> selectByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    ProductFlowInstanceAttachment selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceAttachment record, @Param("example") ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int updateByExample(@Param("record") ProductFlowInstanceAttachment record, @Param("example") ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int updateByPrimaryKey(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceAttachment> list);

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceAttachment> list, @Param("selective") ProductFlowInstanceAttachment.Column ... selective);
}