package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.param.miniprogram.KnowledgeHomeParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageHomeListParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeSearchVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Mapper
public interface MiniProgramHomeMapperExt {

    List<HomeVO> pageHomeList(PageHomeListParam param);

    Long countHomeList(PageHomeListParam param);

    String getUserName(@Param("userId") String userId);
    String getProvinceName(@Param("provinceCode") String provinceCode);

    List<MiniProgramProductListVO> getHomeProduct(@Param("homeId") String homeId);
    List<PageInfoVO> getHomeInfo(@Param("homeId") String homeId,@Param("contentType") Integer contentType);

}
