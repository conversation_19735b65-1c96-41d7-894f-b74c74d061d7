package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.EstewardProduct;
import com.chinamobile.retail.pojo.entity.EstewardProductExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EstewardProductMapper {
    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    long countByExample(EstewardProductExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int deleteByExample(EstewardProductExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int insert(EstewardProduct record);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int insertSelective(EstewardProduct record);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    List<EstewardProduct> selectByExample(EstewardProductExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    EstewardProduct selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int updateByExampleSelective(@Param("record") EstewardProduct record, @Param("example") EstewardProductExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int updateByExample(@Param("record") EstewardProduct record, @Param("example") EstewardProductExample example);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int updateByPrimaryKeySelective(EstewardProduct record);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int updateByPrimaryKey(EstewardProduct record);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int batchInsert(@Param("list") List<EstewardProduct> list);

    /**
     *
     * @mbg.generated Tue Apr 22 09:02:44 CST 2025
     */
    int batchInsertSelective(@Param("list") List<EstewardProduct> list, @Param("selective") EstewardProduct.Column ... selective);
}