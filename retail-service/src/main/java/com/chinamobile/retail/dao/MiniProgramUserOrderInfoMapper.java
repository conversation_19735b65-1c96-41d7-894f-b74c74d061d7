package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfo;
import com.chinamobile.retail.pojo.entity.MiniProgramUserOrderInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramUserOrderInfoMapper {
    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    long countByExample(MiniProgramUserOrderInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int deleteByExample(MiniProgramUserOrderInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int insert(MiniProgramUserOrderInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int insertSelective(MiniProgramUserOrderInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    List<MiniProgramUserOrderInfo> selectByExample(MiniProgramUserOrderInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    MiniProgramUserOrderInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramUserOrderInfo record, @Param("example") MiniProgramUserOrderInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramUserOrderInfo record, @Param("example") MiniProgramUserOrderInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramUserOrderInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int updateByPrimaryKey(MiniProgramUserOrderInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramUserOrderInfo> list);

    /**
     *
     * @mbg.generated Fri Jul 26 14:37:35 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramUserOrderInfo> list, @Param("selective") MiniProgramUserOrderInfo.Column ... selective);
}