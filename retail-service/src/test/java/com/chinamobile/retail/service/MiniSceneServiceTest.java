package com.chinamobile.retail.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.dao.MiniProgramSceneSpuMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneSpu;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneSpuExample;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class MiniSceneServiceTest {

    @Resource
    private IMiniSceneService miniSceneService;

    @Resource
    private MiniProgramSceneSpuMapper miniProgramSceneSpuMapper;

    @Test
    void listSceneDirectory() {
        List<SceneDirectoryVO> result = miniSceneService.listSceneDirectory();
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void pageSceneFrontend() {
        PageSceneFrontendParam param = new PageSceneFrontendParam();
        PageData<SceneFrontendItemVO> result = miniSceneService.pageSceneFrontend(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void getSceneDetailFrontend() {
        SceneDetailFrontendParam param = new SceneDetailFrontendParam();
        param.setSceneId("");
        param.setUserId("");
        SceneDetailFrontendVO result = miniSceneService.getSceneDetailFrontend(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void submitSceneRequirement() {
        SceneRequirementSubmitParam param = new SceneRequirementSubmitParam();
        param.setSceneId("");
        param.setFirstDirectoryId("");
        param.setSecondDirectoryId("");
        param.setProvinceName("");
        param.setProvinceCode("");
        param.setCityName("");
        param.setCityCode("");
        param.setContact("");
        param.setPhone("");
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId("");
        miniSceneService.submitSceneRequirement(param, loginIfo4Redis);
    }

    @Test
    void pageRequirementList() {
        PageRequirementListParam param = new PageRequirementListParam();
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("1301198794070953984");
        loginIfo4Redis.setUserId("1309227669992554496");
        PageData<SceneRequirementVO> result = miniSceneService.pageRequirementList(param,loginIfo4Redis);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void testRequirementDetail() {
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("1301198794070953984");
        loginIfo4Redis.setUserId("1309227669992554496");
        SceneRequirementVO result = miniSceneService.getRequirementDetail("1319269771333140480",loginIfo4Redis);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void testEditScene() {
        String content = "{\"name\":\"120\",\"firstDirectoryId\":\"1321153749254250496\",\"secondDirectoryId\":\"1321164926403321856\",\"headImageUrl\":\"http://10.12.57.1/mallos/oss/mallos-test/testimage.png.png\",\"imageUrl\":\"http://10.12.57.1/mallos/oss/mallos-test/testImage4.png.png\",\"products\":[{\"x\":200,\"y\":422,\"spuCode\":\"1000007483\"},{\"x\":227,\"y\":790,\"spuCode\":\"1000007658\"},{\"x\":811,\"y\":374,\"spuCode\":\"1000007749\"},{\"x\":797,\"y\":626,\"spuCode\":\"1000010666\"},{\"x\":400,\"y\":400,\"spuCode\":\"1000010660\"}],\"oprType\":1,\"spuCode\":\"1000009582\",\"id\":\"1321170344160632832\"}";
        SceneParam param = JSON.parseObject(content,SceneParam.class);
        miniSceneService.edit(param,"1309227669992554496");
    }

    @Test
    void testSceneDetail() {
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("1301198794070953984");
        loginIfo4Redis.setUserId("1309227669992554496");
        SceneVO result = miniSceneService.getSceneDetail("1321170344160632832",loginIfo4Redis);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void testSceneSpu() {
        List<MiniProgramSceneSpu> result = miniProgramSceneSpuMapper.selectByExample(new MiniProgramSceneSpuExample().createCriteria().andSceneIdEqualTo("1321170344160632832").example());
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void testDeleteTemplate() {
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("1301198794070953984");
        loginIfo4Redis.setUserId("1309227669992554496");
        InfoOfflineParam param = new InfoOfflineParam();
        List<String> ids = new ArrayList<>();
        ids.add("1320810785772511232");
        param.setIds(ids);
        miniSceneService.deleteTemplate(param,loginIfo4Redis);
    }
}