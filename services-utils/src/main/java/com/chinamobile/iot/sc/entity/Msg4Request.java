package com.chinamobile.iot.sc.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * @Author: YSC
 * @Date: 2021/8/20 11:01
 * @Description:
 */
@Data
public class Msg4Request {
    @Size(min = 1,max = 99,message = "手机号至少存在一个，至多99个")
    private List<String> mobiles;
    @NotBlank(message = "模板Id不能为空")
    private String templateId;
    private Map<String,String> message;
}
