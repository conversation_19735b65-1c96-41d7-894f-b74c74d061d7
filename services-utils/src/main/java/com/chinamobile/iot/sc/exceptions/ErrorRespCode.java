package com.chinamobile.iot.sc.exceptions;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR> liang
 * @date : 2021/9/1 10:08
 * @description: 错误参数返回静态使用类
 **/
public class ErrorRespCode {

    public static final int INTEGER_NULL_ERROR = -999999999;

    public static final Date DATE_NULL_ERROR = null;

    public static final String STRING_NULL_ERROR = "";

    public static final LocalDate LOCALDATE_NULL_ERROR = null;
}
