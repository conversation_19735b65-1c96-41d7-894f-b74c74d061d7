package com.chinamobile.iot.sc.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/11
 * @description 商品产品类型枚举类
 */
public enum SkuProductTypeEnum {

    G5_CPE("1","5G CPE"),
    G5_LINE("2","5G 快线"),
    EYE("3","千里眼"),
    CONTRACT("4","合同履约"),
    ONENET_SERVICE("5","OneNET独立服务"),
    ONENET_PRODUCT("6","标准产品(OneNET)"),
    ONEPARK_SERVICE("7","OnePark独立服务"),
    ONEPARK_PRODUCT("8","标准产品(OnePark)"),
    EYE_SERVICE("9","千里眼独立服务"),
    SAY_SERVICE("10","和对讲独立服务"),
    CLOUD_SERVICE("11","云视讯独立服务"),
    ONECYBER_PRODUCT("12","标准产品(OneCyber)");

    /**
     * 产品类型
     */
    private String productType;
    /**
     * 中文描述
     */
    private String describe;

    SkuProductTypeEnum(String productType, String describe) {
        this.productType = productType;
        this.describe = describe;
    }

    public String getProductType() {
        return productType;
    }

    public static boolean contains(String productType){
        for (SkuProductTypeEnum value : SkuProductTypeEnum.values()) {
            if(value.productType.equals(productType)){
                return true;
            }
        }
        return false;
    }

    public static boolean containsList(List<String> productType){
        boolean varBool = true;
        SkuProductTypeEnum[] values = SkuProductTypeEnum.values();
        List<String> collect = Arrays.stream(values).map(SkuProductTypeEnum::getProductType).collect(Collectors.toList());
        for (String offeringClass : productType) {
            if (!collect.contains(offeringClass)){
                varBool = false;
                break;
            }
        }
        return varBool;
    }

    public static String getDescribe(String productType) {
        for (SkuProductTypeEnum value : SkuProductTypeEnum.values()) {
            if (value.productType.equals(productType)) {
                return value.describe;
            }
        }
        return null;
    }
    
}
