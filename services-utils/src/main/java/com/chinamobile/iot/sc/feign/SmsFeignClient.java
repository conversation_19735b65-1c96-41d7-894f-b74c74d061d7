package com.chinamobile.iot.sc.feign;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.Msg4Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: YSC
 * @Date: 2021/8/20 10:59
 * @Description:
 */
@Service
@FeignClient(name = "supply-chain-sms-svc")
public interface SmsFeignClient {

    @PostMapping("/sms/asySendMessage")
    BaseAnswer<Void> asySendMessage(@Validated @RequestBody Msg4Request request);
}
