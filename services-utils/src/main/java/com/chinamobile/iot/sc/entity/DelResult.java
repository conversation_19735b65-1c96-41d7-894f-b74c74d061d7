package com.chinamobile.iot.sc.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DelResult {
    List<String> failKeys;

    public DelResult setFailKey(String key){
        if (failKeys==null)
            failKeys = new ArrayList<>();
        failKeys.add(key);
        return this;
    }
}
