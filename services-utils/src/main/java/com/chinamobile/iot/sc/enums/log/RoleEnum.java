package com.chinamobile.iot.sc.enums.log;

/**
 * 用户角色枚举
 * <AUTHOR>
 */
public enum RoleEnum {

    /**操作枚举*/
    SUPER_ADMIN(0,"业务管理员"),
    OPERATION_ADMIN(1,"运营管理员"),
    CUSTOM_SERVICE_ADMIN(2,"客服管理员"),
    PARTNER_MASTER(3,"合作伙伴主"),
    PARTNER_SLAVE(4,"合作伙伴从"),
    PRODUCT_OPERATION_ADMIN(5,"产品运营管理员"),
    POINT_ADMIN(6,"积分管理员"),
    CLIENT_MANAGER(7,"客户经理"),
    RETAILER_FIRST(8,"一级分销员"),
    RETAILER_SECOND(9,"二级分销员"),
    KANBAN_SUPER_ADMIN(10,"大屏超管"),
    KANBAN_ADMIN(11,"大屏管理员"),
    KANBAN_PROVINCE(12,"大屏省账号"),
    H5_LOGIN(13,"售后服务H5账号"),
    PROVINCE_MANAGEMENT(14,"省业管员"),
    PARTNER_PROVINCE(15,"合作伙伴省管"),
    SYSTEM_ADMIN(16,"系统管理员"),

    PROVINCE_ORDER_MANAGER_ROLE(17,"采购管理员");
    

    public Integer code;
    public String name;

    RoleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RoleEnum fromCode(Integer code){
        RoleEnum[] values = RoleEnum.values();
        for (RoleEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }
}
