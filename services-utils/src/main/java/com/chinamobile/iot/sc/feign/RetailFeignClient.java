package com.chinamobile.iot.sc.feign;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.retail.FindMiniProgramUserParam;
import com.chinamobile.iot.sc.entity.retail.FindRetailUserParam;
import com.chinamobile.iot.sc.entity.retail.PointPercentBySkuAndRoleParam;
import com.chinamobile.iot.sc.entity.retail.SyncSkuPointParam;
import com.chinamobile.iot.sc.mode.PointPercentBySkuAndRoleVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/9/7 14:51
 */
@Service
@FeignClient(name = "supply-chain-retail-svc")
public interface RetailFeignClient {

    /**
     * 对于复杂参数的请求，即使用get请求,feign也会自动转化为post
     * @param param
     * @return
     */
    @PostMapping("/retail/product/pointPercentBySkuAndRole")
    BaseAnswer<PointPercentBySkuAndRoleVO> getPointPercentBySkuAndRole(@RequestBody @Valid PointPercentBySkuAndRoleParam param);

    @PostMapping("/retail/partner/point/syncSkuPoint")
    BaseAnswer<Void> syncSkuPoint(@RequestBody @Valid SyncSkuPointParam param);

    @PostMapping("/retail/user/findUserId")
    BaseAnswer<String> findRetailUserId(@RequestBody FindRetailUserParam param);

    @PostMapping("/retail/miniprogram/user/findUserId")
    BaseAnswer<String> findMiniProgramUserId(@RequestBody FindMiniProgramUserParam param);

    @PostMapping("/retail/product/addSkuRoleRelation")
    BaseAnswer<Void> addSkuRoleRelation(@RequestParam(value = "skuId") String skuId);

    @PostMapping("/retail/product/deleteSkuRoleRelation")

    BaseAnswer<Void> deleteSkuRoleRelation(@RequestParam(value = "skuId") String skuId);

    @PostMapping("/retail/supplier/product/syncSkuDelete")
    BaseAnswer<Void> syncSkuDelete(@RequestParam(value = "deleteSkuOfferingCodes") List<String> deleteSkuOfferingCodes);

    @PostMapping("/retail/product/pausePoint")
    BaseAnswer<Void> pausePoint(@RequestParam( value = "skuOfferingCode") String skuOfferingCode);

}
