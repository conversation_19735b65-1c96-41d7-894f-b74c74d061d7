package com.chinamobile.iot.sc.util.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.streaming.SXSSFSheet;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2023/7/28 10:40
 * @description: 导出单元格文本设置
 **/
public class CustomSheetWriteHandler  implements SheetWriteHandler {

    // 设置100列column
    private static final Integer COLUMN = 100;

    private Integer sheetNo;
    private String sheetName;

    public CustomSheetWriteHandler(Integer sheetNo, String sheetName) {
        this.sheetNo = sheetNo;
        this.sheetName = sheetName;
    }

    @Override
    public void beforeSheetCreate(SheetWriteHandlerContext context) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder,
                                 WriteSheetHolder writeSheetHolder) {
        for (int i = 0; i < COLUMN; i++) {
            // 设置为文本格式
            SXSSFSheet sxssfSheet = (SXSSFSheet) writeSheetHolder.getSheet();
            CellStyle cellStyle = writeWorkbookHolder.getCachedWorkbook().createCellStyle();
            // 49为文本格式
            cellStyle.setDataFormat((short) 49);
            // i为列，一整列设置为文本格式
            sxssfSheet.setDefaultColumnStyle(i, cellStyle);

        }
        if(StringUtils.isNotEmpty(sheetName) && sheetNo != null){
            //解决设置sheet名称不生效的BUG
            writeWorkbookHolder.getCachedWorkbook().setSheetName(sheetNo,sheetName);
        }
    }
}
