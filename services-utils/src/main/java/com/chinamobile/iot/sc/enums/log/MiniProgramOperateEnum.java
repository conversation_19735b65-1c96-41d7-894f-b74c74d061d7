package com.chinamobile.iot.sc.enums.log;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/24 14:55
 * @description TODO
 */
public enum MiniProgramOperateEnum {
    /**操作信息*/
    USER_AGREEMENT(0,"用户协议配置"),
    PRODUCT(1, "产品选型配置"),
    USER(2,"头像审批"),
    ACTIVITY(3, "活动中心配置"),
    INFO(4, "素材配置"),
    HOME(5,"首页配置"),
    SCENE(6,"小场景配置"),
    CORE_COMPONENT(7,"产品配置"),
    Material(8,"素材库");

    public Integer code;
    public String name;

    MiniProgramOperateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MiniProgramOperateEnum fromCode(Integer code){
        MiniProgramOperateEnum[] values = MiniProgramOperateEnum.values();
        for (MiniProgramOperateEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
