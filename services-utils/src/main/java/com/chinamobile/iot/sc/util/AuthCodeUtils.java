package com.chinamobile.iot.sc.util;

import com.chinamobile.iot.sc.mode.AuthCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/4/19 17:44
 */
public class AuthCodeUtils {

    /**
     * 递归获取所有的权限编码
     * @param authCodeList
     * @param authCodes
     */
    public static void getAllAuthCode(List<String> authCodeList, List<AuthCode> authCodes) {
        if(CollectionUtils.isEmpty(authCodes)){
            return;
        }else {
            for (AuthCode authCode : authCodes) {
                authCodeList.add(authCode.getCode());
                List<AuthCode> children = authCode.getChildren();
                getAllAuthCode(authCodeList,children);
            }
        }
    }
}
