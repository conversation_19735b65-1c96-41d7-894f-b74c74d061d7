package com.chinamobile.iot.sc.util.excel;

import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.exception.excel.ExcelExportException;
import cn.afterturn.easypoi.exception.excel.enums.ExcelExportEnum;
import cn.afterturn.easypoi.util.PoiPublicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/11
 * @description excel自定义工具类
 */
@Slf4j
public class ExcelCustomUtil {


    public static Workbook exportCustomExcel(ExportParams exportParams, Class<?> pojoClass, List<?> dataSet,ExportParamsCustom exportParamsCustom) {
        ExcelExportServiceCustom excelExportServiceCustom = new ExcelExportServiceCustom();
        XSSFWorkbook workbook = new XSSFWorkbook();
        if (workbook != null && exportParams != null && pojoClass != null && dataSet != null) {
            try {
                List<ExcelExportEntity> excelParams = new ArrayList();
                Field[] fileds = PoiPublicUtil.getClassFields(pojoClass);
                ExcelTarget excelTarget = pojoClass.getAnnotation(ExcelTarget.class);
                String targetId = excelTarget == null ? null : excelTarget.value();
                excelExportServiceCustom.getAllExcelField(exportParams.getExclusions(), targetId, fileds, excelParams, pojoClass, (List)null, (ExcelEntity)null);
                // 设置自定义参数
                excelExportServiceCustom.setExportParamsCustom(exportParamsCustom);

                excelExportServiceCustom.createSheetForMap(workbook, exportParams, excelParams, dataSet);
            } catch (Exception var9) {
                log.error(var9.getMessage(), var9);
                throw new ExcelExportException(ExcelExportEnum.EXPORT_ERROR, var9.getCause());
            }
        } else {
            throw new ExcelExportException(ExcelExportEnum.PARAMETER_ERROR);
        }
        return workbook;
    }

    public void createTitleRow(String title,Sheet sheet){

    }
}
