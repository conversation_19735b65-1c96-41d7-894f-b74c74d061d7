package com.chinamobile.iot.sc.enums.log;

/**
 * 保理融资子模块操作枚举
 */
public enum FinancingManageOperateEnum {
    AUTHENTICATION(0,"保理认证"),
    ORDER_MARK(1,"订单标记"),
    BAOLI_ORDER(2,"保理订单"),
    FINANCING(3,"融资管理"),
    ;

    public Integer code;
    public String name;

    FinancingManageOperateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FinancingManageOperateEnum fromCode(Integer code) {
        FinancingManageOperateEnum[] values = FinancingManageOperateEnum.values();
        for (FinancingManageOperateEnum value : values) {
            if (value.code.intValue() == code.intValue()) {
                return value;
            }
        }
        return null;
    }
}
