package com.chinamobile.iot.sc.entity.b2b;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 通用省侧同步参数类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/15
 * @description 通用省侧同步参数，支持自定义字段映射
 */
@Data
public class CommonProvinceSyncParam {

    /**
     * 订单主题
     * 规则为：IOT商城_[XX产品名称]_安装工单
     */
    private String title;

    /**
     * IOT商城订单编号
     * 商城订单唯一标识（20位），jkiot开头
     */
    private String orderNo;

    /**
     * 工单类别
     * 枚举：1-开通勘查工单、2-开通工单、3-变更勘查工单、4-变更工单、5-拆除工单（填写编码，目前仅有开通工单）
     */
    private String orderType;

    /**
     * 订购渠道
     * 枚举：13：商城os系统
     */
    private String orgId;

    /**
     * 产品名称
     * 例如：qly：IOT千里眼
     */
    private String productName;

    /**
     * 装维类型
     * 0：只装不维
     * 1：既装又维
     * （目前仅有只装不维）
     */
    private String businessType;

    /**
     * 工单处理时限
     * 派单时间+2天
     * 格式：yyyy-MM-dd HH:mm:ss，例如：2020-6-30 16:24:42
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date requiredTime;

    /**
     * 设备信息
     * 可能会传递多组设备信息,每组信息中包括设备型号和设备串号
     */
    private List<DeviceInfo> deviceInfo;

    /**
     * 客户联系人
     * 业务联系人的名称，例如：张三
     * 加密传输
     */
    private String customContact;

    /**
     * 客户联系电话
     * 业务联系人电话，例如：***********
     * 加密传输
     */
    private String customContactPhone;

    /**
     * 安装地址所属省份
     * 例如：HA
     * 加密传输
     */
    private String provinceA;

    /**
     * 安装地市所属地市
     * 见附录地市区县编码
     * 例如：01
     * 加密传输
     */
    private String cityA;

    /**
     * 安装地址所属区县
     * 见附录地市区县编码
     * 例如：0101
     * 加密传输
     */
    private String areaA;

    /**
     * 安装详细地址
     * 街道名称、大楼名称及需要通达的楼层
     * 加密传输
     */
    private String installAddr;

    /**
     * 客户编码
     * 客户编码（集团客户即为EC编码）
     */
    private String custCode;

    /**
     * 客户名称
     * 商城用户名
     */
    private String custName;

    /**
     * 客户经理名称
     * 取客户经理名称
     */
    private String customerManagerName;

    /**
     * 客户经理联系电话
     * 传客户经理从账号对应的手机号信息
     */
    private String createOperPhone;

    /**
     * 客户提单时间
     * 客户前台订购提单时间，格式：yyyy-MM-dd HH:mm:ss
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date creatTime;

    /**
     * 装维服务订购数量
     * 不一定是终端数量，订购数量
     */
    private String quantity;

    /**
     * 收货人姓名
     * 收货人联系人姓名
     * 加密传输，加密方式与省份对应
     */
    private String contactPersonName;

    /**
     * 收货人手机号
     * 收货人联系电话
     * 加密传输，加密方式与省份对应
     */
    private String contactPhone;

    /**
     * 收货地址省份
     * 收货地址所属省份编码
     * 加密传输，加密方式与省份对应
     */
    private String provinceB;

    /**
     * 收货地址地市
     * 收货地址所属地市编码
     * 加密传输，加密方式与省份对应
     */
    private String cityB;

    /**
     * 收货地址区县
     * 收货地址所属区县编码
     * 加密传输，加密方式与省份对应
     */
    private String areaB;

    /**
     * 收货地址乡镇
     * 收货地址所属乡镇编码
     * 加密传输，加密方式与省份对应
     */
    private String villageB;

    /**
     * 收货详细地址
     * 收货详细地址信息
     * 加密传输，加密方式与省份对应
     */
    private String usaddrB;

    /**
     * 物流供应商编码
     * 物流供应商编码信息
     */
    private String supplierCode;

    /**
     * 物流单号
     * 物流单号信息
     */
    private String logisCode;

    /**
     * 设备信息内部类
     */
    @Data
    public static class DeviceInfo {
        /**
         * 设备型号
         */
        private String model;

        /**
         * 设备串号
         */
        private String serialNumber;
    }
}
