package com.chinamobile.iot.sc.entity.iot;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description 订单和合作伙伴关系分组参数类
 */
@Data
public class OrderCooperatorInfoByGroupParam {

    /**
     * 原子订单id
     */
    private String atomOrderId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 合作伙伴名称
     */
    private String partnerName;

    /**
     * 合作伙伴用户名称
     */
    private String userName;

    /**
     * 合作伙伴id
     */
    private String cooperatorId;

}
