package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/10/22 17:18
 */
@Data
public class ActivityFeignDTO {

    /**
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String id;

    /**
     * 活动专区名称
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String activityName;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String userId;

    /**
     * 渠道类型，1 --PC; 2-- 移动
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Integer channelType;

    /**
     * 模板名称
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String templateName;

    /**
     * 发布省范围，null表示全国，有值表示省代码，对应contract_province_info
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String provinceCode;

    /**
     * 状态, 1-- 待发布； 2-- 已发布
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Integer status;

    /**
     * 页面标题
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String pageTitle;

    /**
     * 页面icon的url
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String pageIcon;

    /**
     * 背景颜色r,g,b ;如果使用默认颜色，则为null
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String bgcRgb;

    /**
     * 是否 显示搜索、导航栏
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Boolean isShowTabbar;

    /**
     * 是否 显示搜索框
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Boolean isShowSearchBox;

    /**
     * 是否 显示搜索导航栏logo1
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Boolean isShowTabLogo1;

    /**
     * 搜索导航栏logo1的url
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String tabLogo1;

    /**
     * 是否 显示搜索导航栏logo2
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Boolean isShowTabLogo2;

    /**
     * 搜索导航栏logo2的url
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private String tabLogo2;

    /**
     * 是否 显示轮播广告位
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Boolean isShowSlider;

    /**
     * 轮播时间长度，单位秒
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Integer sliderTime;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Sep 28 15:07:32 CST 2022
     */
    private Date updateTime;
}
