# 供应链系统


##  分支管理说明
- **master** 主干分支 
  主要用于生产环境，用于发布，和线上环境代码一致
  
- **develop** 日常开发分支 
  主要用于当前版本的开发，测试通过后，合并到master分支
  
- **hotfix** 线上bug修复分支 
  从master分支创建，对生产环境bug进行紧急修复，完成发布后，合并代码到master、同时合并到develop开发分支

- **{person_develop_name}** `(eg:zhansan_develop_wxPay)`  个人开发分支  
  用于多人开发时，个人建的分支，从develop开发分支创建，每日提交合并申请至develop(如**无特殊情况，不建议这种方式**)

##  标签（tag）管理说明
- **RELEASE**  标签
  一旦测试通过至发布上线阶段，由负责人给代码打上release的标签，同时在更新日志里面记录说明
  
- **命名规则**：`发布版本号.RELEASE`,举例：`4.0.0.RELEASE`



> **说明**：
> 1. 对上线后的 **hotfix**、以及 **个人开发分支** 要及时合并 并做相关清理（删除）
>

--- 

### 目录结构
   
   - supply-chain 
      

---
### 更新日志

 - 日期：2021-01-18

|服务|tag|升级描述|
|:-----|:-----|-----                          |
|b2b-service服务（版本号V1.0.x） |V1.0.0.RELEASE|`批量导入，查询订单，解密功能`|
|xx服务（版本号x.x.x） |x.x.x.RELEASE|`升级描述-------------`|

 - 日期：2022-01-18

|服务|tag|升级描述|
|:-----|:-----|-----                          |
|supply-chain-b2b-svc服务（版本号V1.0.x） |V1.4.0.RELEASE|`合同履约商品、订购、退货退款同步（基本复用联合销售类订单的流程）`|
|supply-chain-iot-svc服务（版本号V1.0.x） |V1.4.3.RELEASE|`新增：1.多物流同步与展示功能、2.合同履约商品、订购、退货退款同步（基本复用联合销售类订单的流程）`|
|supply-chain-user-svc服务（版本号V1.0.x） |V1.3.0.RELEASE|`新增：无`|
|supply-chain-sms-svc服务（版本号V1.0.x） |V1.3.0.RELEASE|`新增：无`|
|supply-chain-gateway-svc服务（版本号V1.0.x） |V1.3.0.RELEASE|`新增：1.外部电商服务模块路径`|
|supply-chain-estore-svc服务（版本号V1.0.x） |V1.4.0|`新增：1.外部电商订单查询服务`|
|xx服务（版本号x.x.x） |x.x.x.RELEASE|`升级描述-------------`|

##  数据库表设计
- **表名称、表字段** 
  1、全小写，多单词之间采用"_"分隔，做到精简同时见文知意。如：<font color='red'>supply_chain</font>
  2、表以及字段定义，需要添加说明信息，可枚举的字段必须列举枚举值并说明

## 开发代码说明
- **包结构** ：com.chinamobile.iot.sc
- **服务层目录结构** ：建议如下
  - 源码目录
<font color='red'>controller</font> : 定义接口服务目录
<font color='red'>service</font> : 接口服务逻辑实现目录
<font color='red'>config</font> : 配置相关目录
<font color='red'>mapper</font> : 数据库接口目录（mabatis工具 使用最好统一）
<font color='red'>pojo</font> : 数据库表对应pojo对象目录（xml自定义的对象也可以放此目录下面）

  - resource 目录
  <font color='red'>mapper</font> : 数据库xml配置文件目录
  <font color='red'>bootstrap.yml</font> :配置文件信息





